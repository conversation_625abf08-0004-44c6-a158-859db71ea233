"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07408492,0.03165539,-0.03061794,-0.01723807,-0.01750325,0.0081512,-0.00100196,0.0288383,0.03908015,0.01813638,0.04769555,-0.09057836,0.08859318,0.0577338,0.00824492,0.02048969,0.01184425,0.07919263,-0.02198429,0.02182079,0.02300671,-0.05203192,-0.00047358,-0.07682155,-0.01509612,0.02279168,0.01608867,0.02180688,-0.00607447,-0.18780367,0.00256879,0.04609599,0.02081583,0.04283834,0.01852868,-0.02740878,-0.02082675,0.06174734,0.01306115,0.01693974,0.03498307,0.03299409,-0.03978433,-0.03542414,-0.01903479,-0.06335516,-0.05861872,-0.02149636,0.03028126,-0.05096608,-0.03280021,-0.02711443,-0.0315519,-0.00140656,-0.01441566,-0.00210577,0.03575279,0.00840661,0.00853021,-0.00342121,0.01716002,0.03669361,-0.19519885,0.04304954,0.02421864,-0.01254126,-0.01085327,-0.00981955,-0.00171087,0.02871897,-0.03061685,0.00511254,-0.00682401,0.05025802,0.04623687,0.00679545,0.00807172,-0.04184389,-0.05481109,-0.05123003,-0.01732843,0.02635785,-0.04907302,-0.02180795,0.03351744,0.05002419,-0.02774434,-0.01966503,-0.01224974,0.04049216,-0.01389549,-0.05844449,0.04605386,0.05481202,0.00006777,0.03383002,0.03821288,0.03908104,-0.08763419,0.14243907,-0.04392393,-0.02069514,-0.03525812,-0.05004385,0.00507494,-0.02564213,-0.01516447,-0.04449775,-0.00795694,-0.01253711,-0.0026624,0.00960752,0.06445231,-0.0077004,0.03121078,0.03496264,-0.01171699,-0.00633816,-0.03221298,-0.0250004,-0.03820781,0.04011627,0.06689785,-0.02887124,0.00551905,-0.02085216,0.04811685,0.05995451,0.03205119,-0.02023528,0.04184784,-0.03118762,-0.05764209,-0.00086261,-0.03536892,-0.02697255,-0.066695,0.01780061,0.00742699,-0.05059047,-0.02243953,-0.05090528,0.03992581,-0.08952213,-0.05437455,0.02745344,-0.10692547,-0.03879613,0.04353054,-0.06238656,-0.01328673,0.01790303,-0.02056768,0.00260026,-0.00834399,-0.02676601,0.04086266,0.14020714,-0.02548462,-0.02726948,0.0096609,0.00628201,-0.08177169,0.18929102,0.0143281,-0.06251378,-0.04774497,0.01526046,-0.03296589,-0.01561101,0.03569724,-0.05814072,0.03672613,0.00536872,0.0257007,-0.01123094,-0.00453332,-0.00871828,-0.03256762,0.04777425,0.05025242,-0.0117002,-0.09780966,0.04301384,-0.00144459,-0.08968537,-0.07603554,-0.00559656,0.05938545,-0.05116812,-0.07345088,0.00693306,-0.03359913,0.00203845,-0.04670674,-0.05197108,0.04358456,-0.04657853,0.01170621,-0.05523046,0.0813265,0.01017669,-0.0383701,0.01231368,-0.03795192,-0.04723316,-0.0237303,-0.07258614,-0.00275196,0.04616383,0.00459265,0.00064385,-0.08179541,0.00792337,-0.01382103,0.04816267,0.00713314,0.04009387,0.06026433,0.07671909,0.02961084,-0.00247292,-0.03410574,-0.23414256,-0.06072494,-0.0278555,-0.01303993,0.00329447,-0.03106767,0.04662836,-0.02822118,0.08979213,0.03018211,0.0723439,0.01434538,-0.05252545,-0.03352733,0.01437881,-0.01402792,0.01206714,-0.01791369,0.00513692,0.01023668,-0.01814381,0.04492047,0.00366778,-0.00931204,0.01227214,-0.01062805,0.15273677,0.02655197,0.03821319,-0.03092408,0.01483418,0.04996184,-0.00781463,-0.07253835,0.06785082,0.02272847,-0.08356355,-0.00497481,-0.02897565,-0.0482062,0.01790737,0.00194079,-0.03085702,-0.07881346,-0.04670315,-0.00917097,0.03135608,0.01753271,-0.01515997,0.09019022,0.00938506,0.04217153,0.06216062,0.06731699,0.03495938,-0.07841497,-0.08413525,0.0053399,-0.00885838,0.01939136,0.05487514,-0.00752831,0.06393789,-0.03894104,0.01213361,0.00546374,0.01011973,-0.04675825,-0.05548931,-0.03057135,-0.02344623,0.18832241,0.02955071,-0.02451149,0.04824545,0.02060666,0.01273505,-0.04629298,-0.03063941,-0.00541914,0.02209048,0.03123759,0.05413403,0.03504186,0.0068624,0.03198502,0.03348969,0.0042912,0.1192641,-0.04139544,-0.0465996,-0.00808444,-0.05935388,0.01514228,0.11170604,-0.01445271,-0.27392364,0.03541985,0.00230299,0.01016771,0.01551777,0.02303163,0.05973357,-0.00693697,-0.05984797,0.03951458,-0.07799739,0.07734917,0.00482408,-0.05171952,-0.0468334,-0.04047593,0.05107301,-0.00403967,0.07930903,-0.01918883,0.01280867,0.09276146,0.20261115,0.01630339,0.04853062,0.01633809,0.02364455,0.05916464,0.02211137,0.01778773,-0.01876523,-0.03752974,0.04127451,0.00544226,0.0549953,0.01861701,-0.01619934,0.02338463,0.04573187,0.02015101,-0.01607073,0.05973737,-0.07979752,0.03791179,0.09136152,0.04061181,-0.01677167,-0.01290958,0.0009541,0.0526278,-0.00543106,-0.02143408,0.00238375,-0.02941221,0.03927987,0.04276284,0.0245587,-0.03652011,-0.03500111,-0.04162638,0.05113231,0.04693975,0.03753234,0.07924673,0.0037183],"last_embed":{"hash":"220ce9adf008e6ddfe7ae870c0c8a2644671315024ca23db75b754c5b172576f","tokens":432}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.03678006,0.01413448,-0.0017308,0.01525553,-0.00251727,0.01280959,0.02895597,-0.03760065,0.05754584,-0.0145589,0.0300058,-0.04199354,-0.02108563,-0.07100563,-0.00796315,-0.02801165,-0.01035243,0.02840847,0.04716415,-0.06259903,0.0031483,0.00156257,0.04212787,0.0508898,0.04145968,-0.04259261,-0.01920336,-0.02657325,0.04242167,0.00213629,0.07626078,-0.01893613,0.02304853,-0.05017168,-0.09035648,0.02125224,-0.01075028,-0.0394486,0.01109818,0.02682395,-0.05132947,0.00123227,-0.01643104,0.02820659,-0.04402152,-0.04815387,-0.00984147,0.01191444,0.0159527,0.03202647,0.01104863,-0.04074899,-0.00676788,0.01821015,-0.02907835,0.04787564,-0.03611449,-0.07129129,-0.03322036,0.01910597,0.05639406,-0.05173833,0.05974586,0.00771511,0.00775768,-0.00054594,0.02086645,-0.00386934,-0.03028287,0.01926143,-0.03761853,0.02942221,-0.01182439,0.0372324,0.05766873,-0.00774877,0.03411868,0.03647625,0.02277078,0.00618893,-0.05467604,0.04099285,0.02172974,0.00437271,0.03235727,-0.02202712,0.04817672,-0.03556524,-0.03702959,0.00872235,0.00665472,-0.01185576,0.00659019,-0.01368295,-0.00299805,-0.0828774,0.01861887,0.03704579,0.01839896,-0.01972216,0.01883629,-0.01386472,-0.05869819,0.0370493,-0.03334598,-0.04937116,-0.01631556,-0.04509349,-0.05670652,0.03132702,-0.00730053,-0.00709146,0.02827345,-0.04119396,-0.06787342,0.02667971,0.00491856,0.01170211,0.00034383,0.02858703,0.06914195,0.02623334,-0.00309177,-0.03582806,-0.0232284,0.01125665,0.01980247,-0.0210609,0.06240366,0.01878781,0.00602321,-0.00444479,-0.02480139,-0.03640322,0.04230168,0.02013993,0.02079677,-0.02934051,0.04870212,-0.03066988,-0.00843425,0.01442309,-0.02338428,0.01098668,0.00197574,0.05205972,-0.04291618,0.01401839,-0.03007621,-0.00382004,0.07988784,0.0541379,-0.0301325,-0.0403779,-0.02648641,0.0073868,0.00958475,-0.00657076,0.03254473,-0.00728087,0.00389257,-0.00028239,0.03120032,0.01669217,0.04780693,0.00758955,-0.0240062,0.05405213,-0.00937961,0.00433467,0.03669573,0.03985843,0.00735623,0.02604496,0.07189614,-0.04648345,0.05316208,0.02895796,-0.070336,0.01850295,0.01941686,-0.03180212,-0.01952213,-0.03133276,-0.00275268,0.01811315,0.00444039,-0.06622451,0.01134373,0.02816567,0.04605774,0.00496868,-0.02466287,0.01484952,-0.04221921,0.05649079,0.00543493,-0.01903208,-0.02329528,0.02388518,0.01379803,-0.00749634,-0.02905644,-0.12072881,0.0774902,0.01686796,-0.01876944,-0.02193544,-0.0766987,0.01259636,-0.01136758,0.01970164,0.01772708,-0.02595379,-0.02267418,-0.02846768,-0.05564369,0.04529534,0.02857043,-0.04259929,0.04210738,-0.02784142,-0.02975376,-0.00337016,-0.03866705,-0.07176253,-0.04013819,0.02549581,-0.00839199,0.00470901,0.00439781,0.04674769,-0.02037632,-0.02419573,-0.02814693,0.05299531,0.00597062,0.03905028,0.02133791,0.00884936,-0.01580206,0.03977994,0.02199169,0.01831502,0.00204018,-0.03985808,-0.07098445,0.03610271,0.00600572,0.05670016,0.0041562,-0.01062982,-0.04024429,0.00125386,0.01750275,0.00303217,-0.02090353,-0.03165212,0.04324565,-0.04948036,0.00419855,0.05301192,-0.03321793,0.00906118,-0.05361283,-0.0195937,-0.00398014,0.02230399,-0.07671691,0.07893719,-0.01569829,-0.02052017,0.03608301,0.03106646,0.0103834,-0.01631867,-0.03292608,0.02242241,-0.09219338,-0.00164831,-0.02596594,0.03096781,0.0127371,-0.00255731,-0.01040236,0.03595392,-0.01411527,-0.01336335,-0.0261071,-0.00505252,0.04231111,-0.00527166,-0.04859985,0.02179545,-0.07201588,-0.08824282,0.01690526,0.03268947,0.02186325,-0.04740576,-0.02854826,-0.02357476,-0.00260625,-0.04980075,-0.0227373,0.01581465,0.0006599,0.04763927,0.00577283,-0.02613441,0.01455464,-0.02444943,-0.00250832,0.00249365,0.03497216,0.0479732,0.01708601,0.03778538,-0.04917817,0.02659625,-0.05150255,0.02355874,-0.02611523,-0.05437804,-0.04550524,-0.07866921,-0.02976864,-0.01303481,0.03620625,0.05531429,-0.00590185,-0.00449563,0.00898132,-0.01880752,0.00563735,0.01375146,-0.04496937,-0.06752887,0.00390421,-0.03065373,0.00854167,-0.06159479,0.01584844,0.01111281,-0.01521486,0.01519581,-0.00918904,0.02530085,-0.01772658,-0.02001675,-0.04201316,0.02857988,-0.04082347,-0.00314386,-0.01113404,-0.07862221,-0.01846016,-0.03836873,0.02367275,0.00280959,0.00743932,-0.04443956,-0.05712602,-0.02515338,0.01540509,0.03706892,0.03423166,0.05138252,-0.01268302,0.00119857,0.05106917,-0.02705099,0.01771927,-0.00151421,0.01548854,-0.0032959,-0.00246731,-0.00095519,0.06852474,0.00620481,0.0159291,-0.02372685,-0.00896666,-0.03380154,-0.0506982,0.02963154,0.04585724,-0.04326859,-0.03780322,-0.04798305,-0.05310265,-0.01935237,-0.02199183,0.03449864,0.04812967,0.04054451,-0.00671465,-0.0254445,-0.05219871,-0.04417903,0.00258628,0.00548833,0.01913749,0.01857491,0.00637051,0.01606561,-0.0151985,0.0214253,-0.041594,0.00449282,0.05967313,-0.01688902,0.02316609,-0.00180345,0.0243875,-0.00210718,0.01824775,-0.06771216,-0.01591548,0.04229483,0.04244681,-0.05117146,0.03141723,-0.01329315,0.00261496,-0.02179835,-0.01917789,0.00001695,-0.01593144,0.01631688,-0.02073555,-0.01361938,-0.07479261,-0.01074985,-0.02005606,-0.00194884,0.04678207,-0.0309677,0.0279388,-0.00949986,-0.03865503,0.00777864,0.05751359,0.00308683,0.00585091,0.01817895,-0.01953663,-0.03948485,0.02554412,0.000484,-0.01799718,0.00664257,-0.0119916,-0.00179703,0.03632692,0.02797612,0.05114163,-0.03746803,-0.00225039,-0.03020135,-0.04710839,-0.05002186,0.05522276,-0.0333969,0.03791697,0.08874664,-0.01020254,-0.00069149,-0.04824691,0.03050607,-0.02079409,-0.01612768,0.03821608,0.02099098,0.06417775,0.05966999,-0.02852271,0.02708607,-0.00741337,-0.03052262,0.04765025,-0.00271546,0.0422548,-0.00334072,-0.01444415,0.00225839,0.00180195,0.05800171,0.03076104,-0.01320128,0.01631514,0.07358064,0.05637524,0.05679585,0.00611528,-0.07140297,-0.0214443,0.05053768,-0.00634569,0.06602024,-0.02530424,0.00695079,-0.04900244,0.06420089,0.0018992,0.01344766,-0.0417018,0.00189221,-0.006734,-0.00276431,-0.0356789,-0.01689852,0.09197471,0.10510074,-0.02841658,-0.02737762,-0.02750013,0.03193709,-0.02748217,-0.01751347,-0.01407124,0.03261,-0.01263491,0.02177268,0.04298143,-0.00867271,-0.04488201,0.00621494,-0.01313154,-0.00087497,-0.04938756,-0.0129789,0.01595961,-0.00095583,0.04115549,-0.06956955,-0.00333235,-0.02312997,0.00629354,0.05764434,0.05697675,0.02031163,-0.01490618,-0.01024702,-0.03228191,-0.01308014,0.08728351,-0.04623403,0.03784381,-0.05406093,0.00120884,-0.0130306,0.04005142,-0.02944645,-0.10042236,-0.00620102,0.05565868,-0.06740684,0.03023548,0.07555959,0.02418359,0.01291533,0.02993937,0.03964554,-0.0271257,0.02396653,-0.08140203,-0.00065484,0.03018487,0.06634577,-0.07046183,-0.02915938,0.04605744,-0.02440639,-0.04643731,0.00519347,0.03041962,-0.00876833,0.05759246,-0.04628901,0.01001314,-0.01079665,0.08550065,0.07152472,-0.00932247,0.05614273,0.07058,0.00671266,-0.00801097,-0.0005982,0.06161534,-0.05212016,0.00986855,-0.06905461,-0.05846385,0.009665,-0.01234544,0.03743402,0.01159661,-0.05821346,0.00151513,0.02607008,-0.01045264,-0.03572996,0.0569958,-0.07413317,0.00652559,-0.04254426,0.01211582,0.0654031,0.03163768,0.052462,-0.01728947,-0.01040052,-0.01150113,-0.0421434,-0.03875917,-0.02920789,0.00079273,-0.00416554,-0.08251961,-0.07655237,-0.05376882,-0.04069137,0.00285655,0.10394915,0.04594117,0.01073281,0.02306537,-0.01261888,-0.02778609,-0.02555868,-0.00818509,0.04505971,0.0158052,0.02945151,0.00373342,0.01626513,0.00879906,0.00514775,0.05091485,0.0675758,0.05875049,0.03256581,0.01816974,0.00436286,-0.03975016,0.0433834,0.01199096,0.02999508,-0.02123357,0.00712829,-0.02683323,-0.01003647,-0.01419517,0.02713801,0.01221636,-0.00893222,0.00605311,0.0903384,0.00805516,-0.01552518,0.0290057,-0.03193183,-0.0068368,-0.01055599,0.00605213,-0.05469966,-0.0091435,-0.01174434,-0.00797226,0.05125704,-0.02481308,0.01548116,0.04368917,0.01428602,0.00609467,0.01636003,-0.00720512,0.01204963,-0.04904184,0.02849013,-0.02657211,0.01366146,0.03487745,-0.02503159,0.05201409,0.00566899,-0.01927425,0.02344792,-0.05402478,0.06518444,-0.01488384,-0.03400868,-0.03140331,-0.07451043,0.06072715,-0.04749636,0.03378137,0.03413877,-0.06156454,-0.01820166,-0.04349428,0.00443935,0.00958872,0.00464356,0.0164196,0.04335175,0.05832012,0.05301699,-0.03689112,-0.02990885,-0.03799372,-0.02543415,0.06083606,-0.00337145,-0.02161638,0.0343222,-0.0982805,0.02064934,-0.01855737,-0.02354942,0.00712981,0.00181534,-0.06348693,-0.04164277,-0.04005191,0.03075477,-0.0058183,0.00906778,-0.00317454,-0.08741867,-0.00996591,0.0021363,0.04806827,-0.03874266,-0.01279063,0.03295043,0.05486607,-0.0084487,-0.0140772,-0.00202205,-0.01115734,-0.00940076,-0.00460508,0.00623505,0.0515066,0.00336598,0.01888321,0.01158623,-0.00476229,0.08413716,-0.01803546,0.06447709,0.02144886,0.03630483,-0.03901083,-0.00821266,7.4e-7,0.01403271,-0.01481785,0.0401565,-0.01280284,-0.00971405,-0.02464078,-0.07245574,-0.04425145,0.0383377],"last_embed":{"tokens":260,"hash":"12tb90r"}}},"last_read":{"hash":"12tb90r","at":1752940658880},"class_name":"SmartSource","outlinks":[{"title":"内存","target":"内存","line":10}],"metadata":{"aliases":["Race Condition Vulnerability"],"英文":"Race Condition Vulnerability","tags":["网络安全/漏洞"]},"blocks":{"#---frontmatter---":[1,7],"#简介":[8,25],"#简介#{1}":[9,11],"#简介#{2}":[12,12],"#简介#{3}":[13,14],"#简介#{4}":[15,16],"#简介#{5}":[17,19],"#简介#{6}":[20,23],"#简介#{7}":[24,25]},"last_import":{"mtime":1747536352426,"size":1132,"at":1749024987637,"hash":"12tb90r"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md","last_embed":{"hash":"12tb90r","at":1752940658880}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#---frontmatter---","lines":[1,7],"size":100,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介","lines":[8,25],"size":391,"outlinks":[{"title":"内存","target":"内存","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{1}","lines":[9,11],"size":105,"outlinks":[{"title":"内存","target":"内存","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{2}","lines":[12,12],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{3}","lines":[13,14],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{4}","lines":[15,16],"size":34,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{5}","lines":[17,19],"size":74,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{6}","lines":[20,23],"size":121,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md#简介#{7}","lines":[24,25],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07408492,0.03165539,-0.03061794,-0.01723807,-0.01750325,0.0081512,-0.00100196,0.0288383,0.03908015,0.01813638,0.04769555,-0.09057836,0.08859318,0.0577338,0.00824492,0.02048969,0.01184425,0.07919263,-0.02198429,0.02182079,0.02300671,-0.05203192,-0.00047358,-0.07682155,-0.01509612,0.02279168,0.01608867,0.02180688,-0.00607447,-0.18780367,0.00256879,0.04609599,0.02081583,0.04283834,0.01852868,-0.02740878,-0.02082675,0.06174734,0.01306115,0.01693974,0.03498307,0.03299409,-0.03978433,-0.03542414,-0.01903479,-0.06335516,-0.05861872,-0.02149636,0.03028126,-0.05096608,-0.03280021,-0.02711443,-0.0315519,-0.00140656,-0.01441566,-0.00210577,0.03575279,0.00840661,0.00853021,-0.00342121,0.01716002,0.03669361,-0.19519885,0.04304954,0.02421864,-0.01254126,-0.01085327,-0.00981955,-0.00171087,0.02871897,-0.03061685,0.00511254,-0.00682401,0.05025802,0.04623687,0.00679545,0.00807172,-0.04184389,-0.05481109,-0.05123003,-0.01732843,0.02635785,-0.04907302,-0.02180795,0.03351744,0.05002419,-0.02774434,-0.01966503,-0.01224974,0.04049216,-0.01389549,-0.05844449,0.04605386,0.05481202,0.00006777,0.03383002,0.03821288,0.03908104,-0.08763419,0.14243907,-0.04392393,-0.02069514,-0.03525812,-0.05004385,0.00507494,-0.02564213,-0.01516447,-0.04449775,-0.00795694,-0.01253711,-0.0026624,0.00960752,0.06445231,-0.0077004,0.03121078,0.03496264,-0.01171699,-0.00633816,-0.03221298,-0.0250004,-0.03820781,0.04011627,0.06689785,-0.02887124,0.00551905,-0.02085216,0.04811685,0.05995451,0.03205119,-0.02023528,0.04184784,-0.03118762,-0.05764209,-0.00086261,-0.03536892,-0.02697255,-0.066695,0.01780061,0.00742699,-0.05059047,-0.02243953,-0.05090528,0.03992581,-0.08952213,-0.05437455,0.02745344,-0.10692547,-0.03879613,0.04353054,-0.06238656,-0.01328673,0.01790303,-0.02056768,0.00260026,-0.00834399,-0.02676601,0.04086266,0.14020714,-0.02548462,-0.02726948,0.0096609,0.00628201,-0.08177169,0.18929102,0.0143281,-0.06251378,-0.04774497,0.01526046,-0.03296589,-0.01561101,0.03569724,-0.05814072,0.03672613,0.00536872,0.0257007,-0.01123094,-0.00453332,-0.00871828,-0.03256762,0.04777425,0.05025242,-0.0117002,-0.09780966,0.04301384,-0.00144459,-0.08968537,-0.07603554,-0.00559656,0.05938545,-0.05116812,-0.07345088,0.00693306,-0.03359913,0.00203845,-0.04670674,-0.05197108,0.04358456,-0.04657853,0.01170621,-0.05523046,0.0813265,0.01017669,-0.0383701,0.01231368,-0.03795192,-0.04723316,-0.0237303,-0.07258614,-0.00275196,0.04616383,0.00459265,0.00064385,-0.08179541,0.00792337,-0.01382103,0.04816267,0.00713314,0.04009387,0.06026433,0.07671909,0.02961084,-0.00247292,-0.03410574,-0.23414256,-0.06072494,-0.0278555,-0.01303993,0.00329447,-0.03106767,0.04662836,-0.02822118,0.08979213,0.03018211,0.0723439,0.01434538,-0.05252545,-0.03352733,0.01437881,-0.01402792,0.01206714,-0.01791369,0.00513692,0.01023668,-0.01814381,0.04492047,0.00366778,-0.00931204,0.01227214,-0.01062805,0.15273677,0.02655197,0.03821319,-0.03092408,0.01483418,0.04996184,-0.00781463,-0.07253835,0.06785082,0.02272847,-0.08356355,-0.00497481,-0.02897565,-0.0482062,0.01790737,0.00194079,-0.03085702,-0.07881346,-0.04670315,-0.00917097,0.03135608,0.01753271,-0.01515997,0.09019022,0.00938506,0.04217153,0.06216062,0.06731699,0.03495938,-0.07841497,-0.08413525,0.0053399,-0.00885838,0.01939136,0.05487514,-0.00752831,0.06393789,-0.03894104,0.01213361,0.00546374,0.01011973,-0.04675825,-0.05548931,-0.03057135,-0.02344623,0.18832241,0.02955071,-0.02451149,0.04824545,0.02060666,0.01273505,-0.04629298,-0.03063941,-0.00541914,0.02209048,0.03123759,0.05413403,0.03504186,0.0068624,0.03198502,0.03348969,0.0042912,0.1192641,-0.04139544,-0.0465996,-0.00808444,-0.05935388,0.01514228,0.11170604,-0.01445271,-0.27392364,0.03541985,0.00230299,0.01016771,0.01551777,0.02303163,0.05973357,-0.00693697,-0.05984797,0.03951458,-0.07799739,0.07734917,0.00482408,-0.05171952,-0.0468334,-0.04047593,0.05107301,-0.00403967,0.07930903,-0.01918883,0.01280867,0.09276146,0.20261115,0.01630339,0.04853062,0.01633809,0.02364455,0.05916464,0.02211137,0.01778773,-0.01876523,-0.03752974,0.04127451,0.00544226,0.0549953,0.01861701,-0.01619934,0.02338463,0.04573187,0.02015101,-0.01607073,0.05973737,-0.07979752,0.03791179,0.09136152,0.04061181,-0.01677167,-0.01290958,0.0009541,0.0526278,-0.00543106,-0.02143408,0.00238375,-0.02941221,0.03927987,0.04276284,0.0245587,-0.03652011,-0.03500111,-0.04162638,0.05113231,0.04693975,0.03753234,0.07924673,0.0037183],"last_embed":{"hash":"220ce9adf008e6ddfe7ae870c0c8a2644671315024ca23db75b754c5b172576f","tokens":432}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.03678006,0.01413448,-0.0017308,0.01525553,-0.00251727,0.01280959,0.02895597,-0.03760065,0.05754584,-0.0145589,0.0300058,-0.04199354,-0.02108563,-0.07100563,-0.00796315,-0.02801165,-0.01035243,0.02840847,0.04716415,-0.06259903,0.0031483,0.00156257,0.04212787,0.0508898,0.04145968,-0.04259261,-0.01920336,-0.02657325,0.04242167,0.00213629,0.07626078,-0.01893613,0.02304853,-0.05017168,-0.09035648,0.02125224,-0.01075028,-0.0394486,0.01109818,0.02682395,-0.05132947,0.00123227,-0.01643104,0.02820659,-0.04402152,-0.04815387,-0.00984147,0.01191444,0.0159527,0.03202647,0.01104863,-0.04074899,-0.00676788,0.01821015,-0.02907835,0.04787564,-0.03611449,-0.07129129,-0.03322036,0.01910597,0.05639406,-0.05173833,0.05974586,0.00771511,0.00775768,-0.00054594,0.02086645,-0.00386934,-0.03028287,0.01926143,-0.03761853,0.02942221,-0.01182439,0.0372324,0.05766873,-0.00774877,0.03411868,0.03647625,0.02277078,0.00618893,-0.05467604,0.04099285,0.02172974,0.00437271,0.03235727,-0.02202712,0.04817672,-0.03556524,-0.03702959,0.00872235,0.00665472,-0.01185576,0.00659019,-0.01368295,-0.00299805,-0.0828774,0.01861887,0.03704579,0.01839896,-0.01972216,0.01883629,-0.01386472,-0.05869819,0.0370493,-0.03334598,-0.04937116,-0.01631556,-0.04509349,-0.05670652,0.03132702,-0.00730053,-0.00709146,0.02827345,-0.04119396,-0.06787342,0.02667971,0.00491856,0.01170211,0.00034383,0.02858703,0.06914195,0.02623334,-0.00309177,-0.03582806,-0.0232284,0.01125665,0.01980247,-0.0210609,0.06240366,0.01878781,0.00602321,-0.00444479,-0.02480139,-0.03640322,0.04230168,0.02013993,0.02079677,-0.02934051,0.04870212,-0.03066988,-0.00843425,0.01442309,-0.02338428,0.01098668,0.00197574,0.05205972,-0.04291618,0.01401839,-0.03007621,-0.00382004,0.07988784,0.0541379,-0.0301325,-0.0403779,-0.02648641,0.0073868,0.00958475,-0.00657076,0.03254473,-0.00728087,0.00389257,-0.00028239,0.03120032,0.01669217,0.04780693,0.00758955,-0.0240062,0.05405213,-0.00937961,0.00433467,0.03669573,0.03985843,0.00735623,0.02604496,0.07189614,-0.04648345,0.05316208,0.02895796,-0.070336,0.01850295,0.01941686,-0.03180212,-0.01952213,-0.03133276,-0.00275268,0.01811315,0.00444039,-0.06622451,0.01134373,0.02816567,0.04605774,0.00496868,-0.02466287,0.01484952,-0.04221921,0.05649079,0.00543493,-0.01903208,-0.02329528,0.02388518,0.01379803,-0.00749634,-0.02905644,-0.12072881,0.0774902,0.01686796,-0.01876944,-0.02193544,-0.0766987,0.01259636,-0.01136758,0.01970164,0.01772708,-0.02595379,-0.02267418,-0.02846768,-0.05564369,0.04529534,0.02857043,-0.04259929,0.04210738,-0.02784142,-0.02975376,-0.00337016,-0.03866705,-0.07176253,-0.04013819,0.02549581,-0.00839199,0.00470901,0.00439781,0.04674769,-0.02037632,-0.02419573,-0.02814693,0.05299531,0.00597062,0.03905028,0.02133791,0.00884936,-0.01580206,0.03977994,0.02199169,0.01831502,0.00204018,-0.03985808,-0.07098445,0.03610271,0.00600572,0.05670016,0.0041562,-0.01062982,-0.04024429,0.00125386,0.01750275,0.00303217,-0.02090353,-0.03165212,0.04324565,-0.04948036,0.00419855,0.05301192,-0.03321793,0.00906118,-0.05361283,-0.0195937,-0.00398014,0.02230399,-0.07671691,0.07893719,-0.01569829,-0.02052017,0.03608301,0.03106646,0.0103834,-0.01631867,-0.03292608,0.02242241,-0.09219338,-0.00164831,-0.02596594,0.03096781,0.0127371,-0.00255731,-0.01040236,0.03595392,-0.01411527,-0.01336335,-0.0261071,-0.00505252,0.04231111,-0.00527166,-0.04859985,0.02179545,-0.07201588,-0.08824282,0.01690526,0.03268947,0.02186325,-0.04740576,-0.02854826,-0.02357476,-0.00260625,-0.04980075,-0.0227373,0.01581465,0.0006599,0.04763927,0.00577283,-0.02613441,0.01455464,-0.02444943,-0.00250832,0.00249365,0.03497216,0.0479732,0.01708601,0.03778538,-0.04917817,0.02659625,-0.05150255,0.02355874,-0.02611523,-0.05437804,-0.04550524,-0.07866921,-0.02976864,-0.01303481,0.03620625,0.05531429,-0.00590185,-0.00449563,0.00898132,-0.01880752,0.00563735,0.01375146,-0.04496937,-0.06752887,0.00390421,-0.03065373,0.00854167,-0.06159479,0.01584844,0.01111281,-0.01521486,0.01519581,-0.00918904,0.02530085,-0.01772658,-0.02001675,-0.04201316,0.02857988,-0.04082347,-0.00314386,-0.01113404,-0.07862221,-0.01846016,-0.03836873,0.02367275,0.00280959,0.00743932,-0.04443956,-0.05712602,-0.02515338,0.01540509,0.03706892,0.03423166,0.05138252,-0.01268302,0.00119857,0.05106917,-0.02705099,0.01771927,-0.00151421,0.01548854,-0.0032959,-0.00246731,-0.00095519,0.06852474,0.00620481,0.0159291,-0.02372685,-0.00896666,-0.03380154,-0.0506982,0.02963154,0.04585724,-0.04326859,-0.03780322,-0.04798305,-0.05310265,-0.01935237,-0.02199183,0.03449864,0.04812967,0.04054451,-0.00671465,-0.0254445,-0.05219871,-0.04417903,0.00258628,0.00548833,0.01913749,0.01857491,0.00637051,0.01606561,-0.0151985,0.0214253,-0.041594,0.00449282,0.05967313,-0.01688902,0.02316609,-0.00180345,0.0243875,-0.00210718,0.01824775,-0.06771216,-0.01591548,0.04229483,0.04244681,-0.05117146,0.03141723,-0.01329315,0.00261496,-0.02179835,-0.01917789,0.00001695,-0.01593144,0.01631688,-0.02073555,-0.01361938,-0.07479261,-0.01074985,-0.02005606,-0.00194884,0.04678207,-0.0309677,0.0279388,-0.00949986,-0.03865503,0.00777864,0.05751359,0.00308683,0.00585091,0.01817895,-0.01953663,-0.03948485,0.02554412,0.000484,-0.01799718,0.00664257,-0.0119916,-0.00179703,0.03632692,0.02797612,0.05114163,-0.03746803,-0.00225039,-0.03020135,-0.04710839,-0.05002186,0.05522276,-0.0333969,0.03791697,0.08874664,-0.01020254,-0.00069149,-0.04824691,0.03050607,-0.02079409,-0.01612768,0.03821608,0.02099098,0.06417775,0.05966999,-0.02852271,0.02708607,-0.00741337,-0.03052262,0.04765025,-0.00271546,0.0422548,-0.00334072,-0.01444415,0.00225839,0.00180195,0.05800171,0.03076104,-0.01320128,0.01631514,0.07358064,0.05637524,0.05679585,0.00611528,-0.07140297,-0.0214443,0.05053768,-0.00634569,0.06602024,-0.02530424,0.00695079,-0.04900244,0.06420089,0.0018992,0.01344766,-0.0417018,0.00189221,-0.006734,-0.00276431,-0.0356789,-0.01689852,0.09197471,0.10510074,-0.02841658,-0.02737762,-0.02750013,0.03193709,-0.02748217,-0.01751347,-0.01407124,0.03261,-0.01263491,0.02177268,0.04298143,-0.00867271,-0.04488201,0.00621494,-0.01313154,-0.00087497,-0.04938756,-0.0129789,0.01595961,-0.00095583,0.04115549,-0.06956955,-0.00333235,-0.02312997,0.00629354,0.05764434,0.05697675,0.02031163,-0.01490618,-0.01024702,-0.03228191,-0.01308014,0.08728351,-0.04623403,0.03784381,-0.05406093,0.00120884,-0.0130306,0.04005142,-0.02944645,-0.10042236,-0.00620102,0.05565868,-0.06740684,0.03023548,0.07555959,0.02418359,0.01291533,0.02993937,0.03964554,-0.0271257,0.02396653,-0.08140203,-0.00065484,0.03018487,0.06634577,-0.07046183,-0.02915938,0.04605744,-0.02440639,-0.04643731,0.00519347,0.03041962,-0.00876833,0.05759246,-0.04628901,0.01001314,-0.01079665,0.08550065,0.07152472,-0.00932247,0.05614273,0.07058,0.00671266,-0.00801097,-0.0005982,0.06161534,-0.05212016,0.00986855,-0.06905461,-0.05846385,0.009665,-0.01234544,0.03743402,0.01159661,-0.05821346,0.00151513,0.02607008,-0.01045264,-0.03572996,0.0569958,-0.07413317,0.00652559,-0.04254426,0.01211582,0.0654031,0.03163768,0.052462,-0.01728947,-0.01040052,-0.01150113,-0.0421434,-0.03875917,-0.02920789,0.00079273,-0.00416554,-0.08251961,-0.07655237,-0.05376882,-0.04069137,0.00285655,0.10394915,0.04594117,0.01073281,0.02306537,-0.01261888,-0.02778609,-0.02555868,-0.00818509,0.04505971,0.0158052,0.02945151,0.00373342,0.01626513,0.00879906,0.00514775,0.05091485,0.0675758,0.05875049,0.03256581,0.01816974,0.00436286,-0.03975016,0.0433834,0.01199096,0.02999508,-0.02123357,0.00712829,-0.02683323,-0.01003647,-0.01419517,0.02713801,0.01221636,-0.00893222,0.00605311,0.0903384,0.00805516,-0.01552518,0.0290057,-0.03193183,-0.0068368,-0.01055599,0.00605213,-0.05469966,-0.0091435,-0.01174434,-0.00797226,0.05125704,-0.02481308,0.01548116,0.04368917,0.01428602,0.00609467,0.01636003,-0.00720512,0.01204963,-0.04904184,0.02849013,-0.02657211,0.01366146,0.03487745,-0.02503159,0.05201409,0.00566899,-0.01927425,0.02344792,-0.05402478,0.06518444,-0.01488384,-0.03400868,-0.03140331,-0.07451043,0.06072715,-0.04749636,0.03378137,0.03413877,-0.06156454,-0.01820166,-0.04349428,0.00443935,0.00958872,0.00464356,0.0164196,0.04335175,0.05832012,0.05301699,-0.03689112,-0.02990885,-0.03799372,-0.02543415,0.06083606,-0.00337145,-0.02161638,0.0343222,-0.0982805,0.02064934,-0.01855737,-0.02354942,0.00712981,0.00181534,-0.06348693,-0.04164277,-0.04005191,0.03075477,-0.0058183,0.00906778,-0.00317454,-0.08741867,-0.00996591,0.0021363,0.04806827,-0.03874266,-0.01279063,0.03295043,0.05486607,-0.0084487,-0.0140772,-0.00202205,-0.01115734,-0.00940076,-0.00460508,0.00623505,0.0515066,0.00336598,0.01888321,0.01158623,-0.00476229,0.08413716,-0.01803546,0.06447709,0.02144886,0.03630483,-0.03901083,-0.00821266,7.4e-7,0.01403271,-0.01481785,0.0401565,-0.01280284,-0.00971405,-0.02464078,-0.07245574,-0.04425145,0.0383377],"last_embed":{"tokens":260,"hash":"12tb90r"}}},"last_read":{"hash":"12tb90r","at":1752940887588},"class_name":"SmartSource","outlinks":[{"title":"内存","target":"内存","line":10}],"metadata":{"aliases":["Race Condition Vulnerability"],"英文":"Race Condition Vulnerability","tags":["网络安全/漏洞"]},"blocks":{"#---frontmatter---":[1,7],"#简介":[8,25],"#简介#{1}":[9,11],"#简介#{2}":[12,12],"#简介#{3}":[13,14],"#简介#{4}":[15,16],"#简介#{5}":[17,19],"#简介#{6}":[20,23],"#简介#{7}":[24,25]},"last_import":{"mtime":1747536352426,"size":1132,"at":1749024987637,"hash":"12tb90r"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/条件竞争漏洞.md","last_embed":{"hash":"12tb90r","at":1752940887588}},