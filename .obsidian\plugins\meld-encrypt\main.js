/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var le=Object.defineProperty;var xe=Object.getOwnPropertyDescriptor;var Ce=Object.getOwnPropertyNames;var Se=Object.prototype.hasOwnProperty;var De=(g,t)=>{for(var e in t)le(g,e,{get:t[e],enumerable:!0})},Ae=(g,t,e,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of Ce(t))!Se.call(g,s)&&s!==e&&le(g,s,{get:()=>t[s],enumerable:!(n=xe(t,s))||n.enumerable});return g};var Ie=g=>Ae(le({},"__esModule",{value:!0}),g);var Ne={};De(Ne,{default:()=>ae});module.exports=Ie(Ne);var oe=require("obsidian");var S=require("obsidian");var ve=require("obsidian");var j=class{constructor(){this.values=new Map}put(t,e){this.values.set(t,e)}get(t,e){var n;return(n=this.values.get(t))!=null?n:e}getOrNull(t){var e;return(e=this.values.get(t))!=null?e:null}getFirst(t,e){for(let n=0;n<t.length;n++){let s=t[n];if(this.containsKey(s))return this.get(s,e)}return e}containsKey(t){return this.values.has(t)}getKeys(){return Array.from(this.values.keys())}removeKey(t){return this.values.delete(t)}clear(){this.values.clear()}};var ce=require("obsidian"),R=class{static getFilePathWithNewExtension(t,e){var n;return(0,ce.normalizePath)(`${(n=t.parent)==null?void 0:n.path}/${t.basename}.${e}`)}static getFilePathExcludingExtension(t){var e;return(0,ce.normalizePath)(`${(e=t.parent)==null?void 0:e.path}/${t.basename}`)}};var o=class o{static init(t){o.vaultFileAdapter=t}static setExternalFilePaths(t){o.externalFilePaths=t}static setActive(t){o.isActive=t,o.isActive||this.clear()}static setAutoExpire(t){o.baseMinutesToExpire=t!=null?t:0,o.updateExpiryTime()}static getLevel(){return o.level}static setLevel(t){if(o.level!=t){if(o.allLevels.contains(t)){o.level=t;return}o.level=o.LevelFilename,this.clear()}}static updateExpiryTime(){o.baseMinutesToExpire==0||o.baseMinutesToExpire==null?o.expiryTime=null:o.expiryTime=Date.now()+o.baseMinutesToExpire*1e3*60}static putByFile(t,e){if(!o.isActive)return;let n=o.getFileCacheKey(e);this.putByKey(n,t),o.updateExpiryTime()}static async getByFile(t){if(!o.isActive)return o.blankPasswordAndHint;this.clearIfExpired(),o.updateExpiryTime();let e=o.getFileCacheKey(t);return await this.getByKeyAsync(e,o.blankPasswordAndHint)}static putByPath(t,e){if(!o.isActive)return;let n=o.getPathCacheKey(e);this.putByKey(n,t),o.updateExpiryTime()}static getByPath(t){if(!o.isActive)return o.blankPasswordAndHint;this.clearIfExpired(),o.updateExpiryTime();let e=o.getPathCacheKey(t);return this.getByKey(e,o.blankPasswordAndHint)}static async getByPathAsync(t){if(!o.isActive)return o.blankPasswordAndHint;this.clearIfExpired(),o.updateExpiryTime();let e=o.getPathCacheKey(t);return await this.getByKeyAsync(e,o.blankPasswordAndHint)}static getPathCacheKey(t){return o.level==o.LevelExternalFile||o.level==o.LevelVault?"$"+o.level:o.level==o.LevelParentPath?t.split("/").slice(0,-1).join("/"):t}static getFileCacheKey(t){return o.level==o.LevelExternalFile||o.level==o.LevelVault?"$"+o.level:o.level==o.LevelParentPath?t.parent.path:R.getFilePathExcludingExtension(t)}static clearIfExpired(){o.expiryTime!=null&&(Date.now()<o.expiryTime||this.clear())}static clearForFile(t){let e=o.getFileCacheKey(t);this.cache.removeKey(e)}static clear(){let t=this.cache.getKeys().length;return this.cache.clear(),t}static putByKey(t,e){o.level!=o.LevelExternalFile&&this.cache.put(t,e)}static getByKey(t,e){return console.debug("SessionPasswordService.getByKey",{level:o.level,key:t,defaultValue:e}),this.cache.get(t,e)}static async getByKeyAsync(t,e){if(o.level==o.LevelExternalFile){for(let n=0;n<this.externalFilePaths.length;n++){let s=this.externalFilePaths[n];try{return{password:await this.fetchFileContents(s),hint:""}}catch(a){console.error(a,{relFilePath:s})}}return new ve.Notice("External password file not found",1e4),e}return this.cache.get(t,e)}static async canFetchContents(t){if(o.vaultFileAdapter==null)return!1;try{let e=await this.fetchFileContents(t);return!0}catch(e){return!1}}static async fetchFileContents(t){if(o.vaultFileAdapter==null)throw new Error("SessionPasswordService.vaultFileAdapter == null");let e=o.vaultFileAdapter.getResourcePath(t),s=await(await fetch(e)).text();if(s.length==0)throw new Error("File contents empty");return s}};o.vaultFileAdapter=null,o.isActive=!0,o.blankPasswordAndHint={password:"",hint:""},o.cache=new j,o.baseMinutesToExpire=0,o.expiryTime=null,o.LevelFilename="filename",o.LevelParentPath="parentPath",o.LevelVault="vault",o.LevelExternalFile="externalFile",o.allLevels=[o.LevelFilename,o.LevelParentPath,o.LevelVault,o.LevelExternalFile],o.level=o.LevelVault,o.externalFilePaths=[];var d=o;var U=class extends S.PluginSettingTab{constructor(t,e,n,s){super(t,e),this.plugin=e,this.settings=n,this.features=s}display(){let{containerEl:t}=this;t.empty(),new S.Setting(t).setName("Confirm password?").setDesc("Confirm password when encrypting. (Recommended)").addToggle(r=>{r.setValue(this.settings.confirmPassword).onChange(async i=>{this.settings.confirmPassword=i,await this.plugin.saveSettings()})});let e=()=>{if(!this.settings.rememberPassword){s.settingEl.hide(),n.settingEl.hide();return}this.settings.rememberPasswordLevel!=d.LevelExternalFile?(s.settingEl.show(),a.settingEl.hide()):(s.settingEl.hide(),a.settingEl.show()),n.settingEl.show();let r=this.settings.rememberPasswordTimeout,i=`For ${r} minutes`;r==0&&(i="Until Obsidian is closed"),s.setName(`Remember Password (${i})`)};new S.Setting(t).setName("Remember password?").setDesc("Remember the last used passwords when encrypting or decrypting.  Passwords are remembered until they timeout or Obsidian is closed").addToggle(r=>{r.setValue(this.settings.rememberPassword).onChange(async i=>{this.settings.rememberPassword=i,await this.plugin.saveSettings(),d.setActive(this.settings.rememberPassword),e()})});let n=new S.Setting(t).setName("Remember passwords by:").setDesc(this.buildRememberPasswordDescription()).addDropdown(r=>{r.addOption(d.LevelVault,"Vault").addOption(d.LevelParentPath,"Folder").addOption(d.LevelFilename,"File").addOption(d.LevelExternalFile,"External File").setValue(this.settings.rememberPasswordLevel).onChange(async i=>{console.debug("rememberPasswordLevelSetting.onChange",{value:i}),this.settings.rememberPasswordLevel=i,await this.plugin.saveSettings(),d.setLevel(this.settings.rememberPasswordLevel),e()})}),s=new S.Setting(t).setDesc("The number of minutes to remember passwords.").addSlider(r=>{r.setLimits(0,120,5).setValue(this.settings.rememberPasswordTimeout).onChange(async i=>{this.settings.rememberPasswordTimeout=i,await this.plugin.saveSettings(),d.setAutoExpire(this.settings.rememberPasswordTimeout),e()})}),a=new S.Setting(t).setName("External File Paths").setDesc("When needed the password is read from one of these filepaths. Paths must be relative to vault root").addTextArea(r=>{r.setValue(this.settings.rememberPasswordExternalFilePaths.join(`
`)).onChange(async i=>{this.settings.rememberPasswordExternalFilePaths=i.trim().split(`
`),await this.plugin.saveSettings(),d.setExternalFilePaths(this.settings.rememberPasswordExternalFilePaths)}),r.inputEl.placeholder="Enter one relative path per line",r.inputEl.style.whiteSpace="pre",r.inputEl.style.width="100%",r.inputEl.rows=4}).addButton(r=>{r.setIcon("check").setTooltip("Check Paths").onClick(async()=>{let i=this.settings.rememberPasswordExternalFilePaths;for(let l of i)await d.canFetchContents(l)?new S.Notice(`\u2714\uFE0F ${l}`):new S.Notice(`\u274C ${l}`)})});a.controlEl.style.width="80%",e(),this.features.forEach(r=>{r.buildSettingsUi(t,async()=>await this.plugin.saveSettings())})}buildRememberPasswordDescription(){let t=new DocumentFragment,e=t.createEl("table").createTBody(),n=e.createEl("tr");return n.createEl("th",{text:"Vault:",attr:{align:"right"}}),n.createEl("td",{text:"Typically, you'll use the same password every time."}),n=e.createEl("tr"),n.createEl("th",{text:"Folder:",attr:{align:"right"}}),n.createEl("td",{text:"Typically, you'll use the same password for each note within a folder."}),n=e.createEl("tr"),n.createEl("th",{text:"File:",attr:{align:"right"}}),n.createEl("td",{text:"Typically, each note will have a unique password."}),n=e.createEl("tr"),n.createEl("th",{text:"External File:",attr:{align:"right",style:"width:12em;"}}),n.createEl("td",{text:"When needed the password/key is read from one of these filepaths."}),t}};var m=require("obsidian");var B=require("obsidian"),V=class extends B.Modal{constructor(e,n,s=""){super(e);this.decryptInPlace=!1;this.save=!1;this.canDecryptInPlace=!0;this.titleEl.setText(n),this.text=s}onOpen(){var r;let{contentEl:e}=this;e.empty(),e.classList.add("meld-encrypt-decrypt-modal");let n;(r=new B.Setting(e).addTextArea(i=>{n=i,i.setValue(this.text),i.inputEl.setSelectionRange(0,0),i.inputEl.rows=10}).settingEl.querySelector(".setting-item-info"))==null||r.remove();let a=new B.Setting(e);a.addButton(i=>{i.setButtonText("Save").onClick(l=>{this.save=!0,this.text=n.getValue(),this.close()})}),a.addButton(i=>{i.setButtonText("Copy").onClick(l=>{navigator.clipboard.writeText(n.getValue()),new B.Notice("Copied!")})}),this.canDecryptInPlace&&a.addButton(i=>{i.setWarning().setButtonText("Decrypt in-place").onClick(l=>{this.decryptInPlace=!0,this.text=n.getValue(),this.close()})})}};var v=require("obsidian");var K=require("obsidian"),D=class{static isSettingsModalOpen(){return document.querySelector(".mod-settings")!==null}static buildPasswordSetting({container:t,name:e,desc:n="",autoFocus:s=!1,placeholder:a="",initialValue:r="",tabIndex:i,onChangeCallback:l,onEnterCallback:p}){let h=new K.Setting(t).setName(e).setDesc(n).addButton(f=>{f.buttonEl.tabIndex=-1,f.setIcon("reading-glasses").onClick(w=>{let y=h.components.find((c,u,E)=>c instanceof K.TextComponent);y instanceof K.TextComponent&&(y.inputEl.type=y.inputEl.type=="password"?"text":"password")})}).addText(f=>{f.inputEl.tabIndex=i!=null?i:f.inputEl.tabIndex,f.setPlaceholder(a),f.setValue(r),f.inputEl.type="password",l!=null&&f.onChange(l),p!=null&&(f.inputEl.onkeydown=w=>{w.key==="Enter"&&(w.preventDefault(),p(f.getValue()))}),s&&setTimeout(()=>f.inputEl.focus(),0)});return h}};var N=class extends v.Modal{constructor(e,n,s,a,r=null,i=null,l=!1){super(e);this.defaultPassword=null;this.resultConfirmed=!1;this.resultPassword=null;this.resultShowInReadingView=null;this.resultTextToEncrypt=null;this.defaultPassword=r,this.confirmPassword=s,this.showInReadingView=a,this.isEncrypting=n,this.defaultHint=i!=null?i:"",this.showTextToEncrypt=l}onOpen(){var y;let{contentEl:e}=this;e.empty(),e.classList.add("meld-encrypt-password-modal"),this.invalidate();let n=(y=this.defaultPassword)!=null?y:"",s="",a=this.defaultHint,r=this.showInReadingView,i="";new v.Setting(e).setHeading().setName(this.isEncrypting?"Encrypting":"Decrypting"),D.buildPasswordSetting({container:e,name:"Password:",placeholder:this.isEncrypting||a.length==0?"":`Hint: ${a}`,initialValue:n,autoFocus:!0,onChangeCallback:c=>{n=c,this.invalidate()},onEnterCallback:c=>{if(n=c,this.invalidate(),n.length>0)if(l.settingEl.isShown()){let u=l.components.find(E=>E instanceof v.TextComponent);u instanceof v.TextComponent&&u.inputEl.focus()}else if(p.settingEl.isShown()){let u=p.components.find(E=>E instanceof v.TextComponent);u instanceof v.TextComponent&&u.inputEl.focus()}else w()&&this.close()}});let l=D.buildPasswordSetting({container:e,name:"Confirm Password:",onChangeCallback:c=>{s=c,this.invalidate()},onEnterCallback:c=>{if(s=c,this.invalidate(),s.length>0&&w()&&p.settingEl.isShown()){let u=p.components.find(E=>E instanceof v.TextComponent);u instanceof v.TextComponent&&u.inputEl.focus()}}});this.confirmPassword||l.settingEl.hide();let p=new v.Setting(e).setName("Optional Password Hint").addText(c=>{c.inputEl.placeholder="Password Hint",c.setValue(a),c.onChange(u=>a=u),c.inputEl.on("keypress","*",(u,E)=>{u.key=="Enter"&&E instanceof HTMLInputElement&&E.value.length>0&&(u.preventDefault(),w()&&this.close())})});this.isEncrypting||p.settingEl.hide();let h=new v.Setting(e).setName("Show encrypted marker in Reading view").addToggle(c=>{c.setValue(r).onChange(u=>{r=u})});this.isEncrypting||h.settingEl.hide();let f=new v.Setting(e).setName("Text to encrypt").addTextArea(c=>{c.setValue("").onChange(u=>i=u),c.inputEl.rows=5,c.inputEl.style.width="100%"});this.showTextToEncrypt||f.settingEl.hide(),new v.Setting(e).addButton(c=>{c.setButtonText("Confirm").onClick(u=>{w()&&this.close()})});let w=()=>(this.invalidate(),l.setDesc(""),this.confirmPassword&&n!=s?(l.setDesc("Passwords don't match"),!1):(this.resultConfirmed=!0,this.resultPassword=n,this.resultHint=a,this.resultShowInReadingView=r,this.resultTextToEncrypt=i,!0))}invalidate(){this.resultConfirmed=!1,this.resultPassword=null,this.resultHint="",this.resultTextToEncrypt=""}};var pe=new TextEncoder,ke=new TextDecoder,Le=1e3,Me=pe.encode("XHWnDAT6ehMVY2zD"),W=class{async deriveKey(t){let e=pe.encode(t),n=await crypto.subtle.importKey("raw",e,{name:"PBKDF2"},!1,["deriveKey"]);return crypto.subtle.deriveKey({name:"PBKDF2",hash:{name:"SHA-256"},iterations:Le,salt:Me},n,{name:"AES-GCM",length:256},!1,["encrypt","decrypt"])}async encryptToBytes(t,e){let n=await this.deriveKey(e),s=pe.encode(t),a=crypto.getRandomValues(new Uint8Array(16)),r=new Uint8Array(await crypto.subtle.encrypt({name:"AES-GCM",iv:a},n,s)),i=new Uint8Array(a.byteLength+r.byteLength);return i.set(a,0),i.set(r,a.byteLength),i}convertToString(t){let e="";for(let n=0;n<t.length;n++)e+=String.fromCharCode(t[n]);return e}async encryptToBase64(t,e){let n=await this.encryptToBytes(t,e);return btoa(this.convertToString(n))}stringToArray(t){let e=[];for(let n=0;n<t.length;n++)e.push(t.charCodeAt(n));return new Uint8Array(e)}async decryptFromBytes(t,e){try{let n=t.slice(0,16),s=t.slice(16),a=await this.deriveKey(e),r=await crypto.subtle.decrypt({name:"AES-GCM",iv:n},a,s);return ke.decode(r)}catch(n){return null}}async decryptFromBase64(t,e){try{let n=this.stringToArray(atob(t));return await this.decryptFromBytes(n,e)}catch(n){return null}}};var J=class{constructor(t,e,n){this.vectorSize=t,this.saltSize=e,this.iterations=n}async deriveKey(t,e){let s=new TextEncoder().encode(t),a=await crypto.subtle.importKey("raw",s,"PBKDF2",!1,["deriveKey"]);try{return await crypto.subtle.deriveKey({name:"PBKDF2",hash:"SHA-512",salt:e,iterations:this.iterations},a,{name:"AES-GCM",length:256},!1,["encrypt","decrypt"])}finally{}}async encryptToBytes(t,e){let n=crypto.getRandomValues(new Uint8Array(this.saltSize)),s=await this.deriveKey(e,n),r=new TextEncoder().encode(t),i=crypto.getRandomValues(new Uint8Array(this.vectorSize)),l=new Uint8Array(await crypto.subtle.encrypt({name:"AES-GCM",iv:i},s,r)),p=new Uint8Array(i.byteLength+n.byteLength+l.byteLength);return p.set(i,0),p.set(n,i.byteLength),p.set(l,i.byteLength+n.byteLength),p}convertToString(t){let e="";for(let n=0;n<t.length;n++)e+=String.fromCharCode(t[n]);return e}async encryptToBase64(t,e){let n=await this.encryptToBytes(t,e);return btoa(this.convertToString(n))}stringToArray(t){let e=[];for(let n=0;n<t.length;n++)e.push(t.charCodeAt(n));return new Uint8Array(e)}async decryptFromBytes(t,e){try{let n,s;n=0,s=n+this.vectorSize;let a=t.slice(n,s);n=s,s=n+this.saltSize;let r=t.slice(n,s);n=s,s=void 0;let i=t.slice(n),l=await this.deriveKey(e,r),p=await crypto.subtle.decrypt({name:"AES-GCM",iv:a},l,i);return new TextDecoder().decode(p)}catch(n){return null}}async decryptFromBase64(t,e){try{let n=this.stringToArray(atob(t));return await this.decryptFromBytes(n,e)}catch(n){return null}}};var de={name:"AES-GCM",iv:new Uint8Array([196,190,240,190,188,78,41,132,15,220,84,211]),tagLength:128},q=class{async buildKey(t){let n=new TextEncoder().encode(t),s=await crypto.subtle.digest({name:"SHA-256"},n);return await crypto.subtle.importKey("raw",s,de,!1,["encrypt","decrypt"])}async encryptToBase64(t,e){let n=await this.buildKey(e),a=new TextEncoder().encode(t),r=new Uint8Array(await crypto.subtle.encrypt(de,n,a));return btoa(String.fromCharCode(...r))}stringToArray(t){let e=[];for(let n=0;n<t.length;n++)e.push(t.charCodeAt(n));return new Uint8Array(e)}async decryptFromBase64(t,e){try{let n=this.stringToArray(atob(t)),s=await this.buildKey(e),a=await crypto.subtle.decrypt(de,s,n);return new TextDecoder().decode(a)}catch(n){return null}}};var X=class X{static BuildDefault(){return this.cryptoHelper2304_v2}static BuildFromFileDataOrThrow(t){let e=X.BuildFromFileDataOrNull(t);if(e!=null)return e;throw new Error(`Unable to determine ICryptoHelper for File ver ${t.version}`)}static BuildFromFileDataOrNull(t){return t.version=="1.0"?new W:t.version=="2.0"?this.cryptoHelper2304_v2:null}static BuildFromDecryptableOrThrow(t){let e=X.BuildFromDecryptableOrNull(t);if(e!=null)return e;throw new Error(`Unable to determine ICryptoHelper for Decryptable ver ${t.version}`)}static BuildFromDecryptableOrNull(t){return t.version==0?new q:t.version==1?new W:t.version==2?this.cryptoHelper2304_v2:null}};X.cryptoHelper2304_v2=new J(16,16,21e4);var I=X;var Q=class{};var Pe="\u{1F510}",Z="%%\u{1F510}\u03B2 ",ee="\u{1F510}\u03B2 ",ue="%%\u{1F510}\u03B1 ",he="\u{1F510}\u03B1 ",fe="%%\u{1F510} ",ye="\u{1F510} ",be=Z,Te=ee,M=[Z,ee,ue,he,fe,ye],ge=" \u{1F510}%%",we=" \u{1F510}",H=[ge,we],k="\u{1F4A1}";var Y=class{constructor(t){this.process(t)}process(t){var e,n;if(this.processedText=t,this.isEmpty=t.length===0,this.prefix=(e=M.find(s=>t.startsWith(s)))!=null?e:"",this.suffix=(n=H.find(s=>t.endsWith(s)))!=null?n:"",this.hasEncryptedPrefix=this.prefix.length>0,this.hasEncryptedSuffix=this.suffix.length>0,this.hasObsoleteEncryptedPrefix=this.prefix===fe||this.prefix===ye,this.containsEncryptedMarkers=[...M,...H].some(s=>t.includes(s)),this.canDecrypt=this.hasEncryptedPrefix&&this.hasEncryptedSuffix,this.canEncrypt=!this.hasEncryptedPrefix&&!this.containsEncryptedMarkers,this.canDecrypt){let s=this.parseDecryptableContent(t);s!=null?this.decryptable=s:this.canDecrypt=!1}}parseDecryptableContent(t){let e=new Q;if(!this.hasEncryptedPrefix||!this.hasEncryptedSuffix)return null;this.hasObsoleteEncryptedPrefix?e.version=0:this.prefix==Z||this.prefix==ee?e.version=2:(this.prefix==ue||this.prefix==he)&&(e.version=1);let n=t.substring(this.prefix.length,t.length-this.suffix.length);if([...M,...H].some(s=>n.includes(s)))return null;if(n.substring(0,k.length)==k){let s=n.indexOf(k,k.length);if(s<0)return null;e.hint=n.substring(k.length,s),e.base64CipherText=n.substring(s+k.length)}else e.base64CipherText=n;return e.showInReadingView=!this.prefix.includes("%%"),e}};var _=class{async onload(t,e){this.plugin=t,this.pluginSettings=e,this.featureSettings=e.featureInplaceEncrypt,this.plugin.registerMarkdownPostProcessor((n,s)=>this.processEncryptedCodeBlockProcessor(n,s)),t.addCommand({id:"meld-encrypt-in-place-encrypt",name:"Encrypt Selection",icon:"lock-keyhole",editorCheckCallback:(n,s,a)=>this.processEncryptCommand(n,s)}),t.addCommand({id:"meld-encrypt-in-place-decrypt",name:"Decrypt",icon:"lock-keyhole-open",editorCheckCallback:(n,s,a)=>this.processDecryptCommand(n,s)}),this.plugin.addRibbonIcon("lock-keyhole","Encrypt Selection",n=>{let s=this.plugin.app.workspace.getActiveViewOfType(m.MarkdownView);if(s!=null)return this.processEncryptCommand(!1,s.editor)}),this.plugin.addRibbonIcon("lock-keyhole-open","Decrypt at Cursor",n=>{let s=this.plugin.app.workspace.getActiveViewOfType(m.MarkdownView);if(s!=null)return this.processDecryptCommand(!1,s.editor)})}onunload(){}replaceMarkersRecursive(t,e=0){if(t instanceof HTMLElement){for(let s of Array.from(t.childNodes)){var n=this.replaceMarkersRecursive(s,e+1);s.replaceWith(...n)}return[t]}if(t instanceof Text){let s=t.textContent;if(s==null)return[t];if(!s.contains("\u{1F510}"))return[t];let a=/🔐(.*?)🔐/g,r=s.split(a),i=[];for(let l=0;l<r.length;l++){let p=r[l];if(l%2!=0){let h=createSpan({cls:"meld-encrypt-inline-reading-marker",text:"\u{1F510}",attr:{"data-meld-encrypt-encrypted":`\u{1F510}${p}\u{1F510}`}});i.push(h)}else i.push(new Text(p))}return i}return[t]}async processEncryptedCodeBlockProcessor(t,e){let n=this.replaceMarkersRecursive(t);t.replaceWith(...n);let s=t.querySelectorAll(".meld-encrypt-inline-reading-marker");this.bindReadingIndicatorEventHandlers(e.sourcePath,s)}bindReadingIndicatorEventHandlers(t,e){e.forEach(n=>{let s=n;s!=null&&s.onClickEvent(async a=>{let r=a.target;if(r==null)return;let i=r.dataset.meldEncryptEncrypted;if(i==null)return;let l=new Y(i);await this.handleReadingIndicatorClick(t,l.decryptable)})})}async handleReadingIndicatorClick(t,e){if(e==null){new m.Notice("\u274C Decryption failed!");return}if(await this.showDecryptedTextIfPasswordKnown(t,e))return;let n=await this.fetchPasswordFromUser(e.hint);n!=null&&(await this.showDecryptedResultForPassword(e,n)?d.putByPath({password:n,hint:e.hint},t):new m.Notice("\u274C Decryption failed!"))}async showDecryptedResultForPassword(t,e){let s=await I.BuildFromDecryptableOrThrow(t).decryptFromBase64(t.base64CipherText,e);return s===null?!1:new Promise(a=>{let r=new V(this.plugin.app,"\u{1F513}",s);r.canDecryptInPlace=!1,r.onClose=()=>{a(!0)},r.open()})}async fetchPasswordFromUser(t){return new Promise(e=>{let n=new N(this.plugin.app,!1,!1,this.featureSettings.showMarkerWhenReadingDefault,"",t);n.onClose=()=>{e(n.resultPassword)},n.open()})}async showDecryptedTextIfPasswordKnown(t,e){let n=await d.getByPathAsync(t);return n.password==null?!1:await this.showDecryptedResultForPassword(e,n.password)}buildSettingsUi(t,e){new m.Setting(t).setHeading().setName("In-place encryption"),new m.Setting(t).setName("Expand selection to whole line?").setDesc("Partial selections will get expanded to the whole line.").addToggle(n=>{n.setValue(this.featureSettings.expandToWholeLines).onChange(async s=>{this.featureSettings.expandToWholeLines=s,await e()})}),new m.Setting(t).setName("Search limit for markers").setDesc("How far to look for markers when encrypting/decrypting.").addText(n=>{var s,a;n.setValue((a=(s=this.featureSettings.markerSearchLimit)==null?void 0:s.toString())!=null?a:"10000").onChange(async r=>{let i=parseInt(r);isNaN(i)||(this.featureSettings.markerSearchLimit=i,await e())}),n.inputEl.type="number",n.inputEl.min="1000",n.inputEl.max="9999999"}),new m.Setting(t).setName("By default, show encrypted marker when reading").setDesc("When encrypting inline text, should the default be to have a visible marker in Reading view?").addToggle(n=>{n.setValue(this.featureSettings.showMarkerWhenReadingDefault).onChange(async s=>{this.featureSettings.showMarkerWhenReadingDefault=s,await e()})})}processEncryptCommand(t,e){if(t&&D.isSettingsModalOpen())return!0;let n=e.getCursor("from"),s=e.getCursor("to");if(!e.somethingSelected())if(this.featureSettings.expandToWholeLines){n={line:n.line,ch:0};let h=s.line,f=e.getLine(h);s={line:h,ch:f.length}}else return t||new m.Notice("Please select text to encrypt."),!1;let r=this.getClosestPrefixCursorPos(e,n),i=this.getClosestSuffixCursorPos(e,n);if(r!=null&&i!=null&&r.line===i.line&&(n.line===r.line&&n.ch>=r.ch&&n.ch<i.ch||s.line===i.line&&s.ch>=r.ch&&s.ch<i.ch))return!1;let l=e.getRange(n,s);return l.includes(Pe)?!1:l.length===0?t||this.promptForTextToEncrypt(t,e,n):this.processSelection(t,e,l,n,s,"encrypt")}processDecryptCommand(t,e){if(t&&D.isSettingsModalOpen())return!0;let n=e.getCursor("from"),s=e.getCursor("to");if(!e.somethingSelected()){let i=this.getClosestPrefixCursorPos(e,n),l=this.getClosestSuffixCursorPos(e,n);if(i==null||l==null||n.line<i.line||s.line>l.line)return t||new m.Notice("Please select text to decrypt or place cursor on encrypted text."),!1;n=i,s=l}let r=e.getRange(n,s);return this.processSelection(t,e,r,n,s,"decrypt")}promptForTextToEncrypt(t,e,n){let s=this.plugin.app.workspace.getActiveFile();if(s==null)return!1;if(t)return!0;let a="",r="";if(this.pluginSettings.rememberPassword){let p=d.getByPath(s.path);a=p.password,r=p.hint}let i=this.pluginSettings.confirmPassword,l=new N(this.plugin.app,!0,i,this.featureSettings.showMarkerWhenReadingDefault,a,r,!0);return l.onClose=async()=>{var y,c,u,E;if(!l.resultConfirmed)return;let p=(y=l.resultPassword)!=null?y:"",h=(c=l.resultHint)!=null?c:"",f=(u=l.resultTextToEncrypt)!=null?u:"",w=new te;w.text=f,w.hint=h,this.encryptSelection(e,w,p,n,n,(E=l.resultShowInReadingView)!=null?E:this.featureSettings.showMarkerWhenReadingDefault),d.putByPath({password:p,hint:h},s.path)},l.open(),!1}getClosestPrefixCursorPos(t,e){let n=this.featureSettings.markerSearchLimit,s=M.reduce((i,l,p)=>p==0||l.length>i.length?l:i),a=t.posToOffset(e)+s.length,r=Math.max(a-n,0);for(let i=a;i>=r;i--){let l=t.offsetToPos(i);for(let p of M){let h=i-p.length,f=t.offsetToPos(h);if(t.getRange(f,l)==p)return t.offsetToPos(h)}}return null}getClosestSuffixCursorPos(t,e){let n=this.featureSettings.markerSearchLimit,s=M.reduce((l,p,h)=>h==0||p.length>l.length?p:l),a=t.posToOffset(e)-s.length+1,r=t.lastLine(),i=Math.min(a+n,t.posToOffset({line:r,ch:t.getLine(r).length}));for(let l=a;l<=i;l++){let p=t.offsetToPos(l);for(let h of H){let f=l+h.length,w=t.offsetToPos(f);if(t.getRange(p,w)==h)return w}}return null}processSelection(t,e,n,s,a,r){var y;let i=new Y(n);if(i.isEmpty)return t||new m.Notice(`Nothing to ${r=="encrypt"?"Encrypt":"Decrypt"}.`),!1;if(r=="encrypt"&&!i.canEncrypt)return t||new m.Notice("Unable to Encrypt that."),!1;if(r=="decrypt"&&!i.canDecrypt)return t||new m.Notice("Unable to Decrypt that."),!1;let l=this.plugin.app.workspace.getActiveFile();if(l==null)return!1;if(t)return!0;let p="",h=(y=i.decryptable)==null?void 0:y.hint;if(this.pluginSettings.rememberPassword){let c=d.getByPath(l.path);p=c.password,h=h!=null?h:c.hint}let f=i.canEncrypt&&this.pluginSettings.confirmPassword,w=new N(this.plugin.app,i.canEncrypt,f,this.featureSettings.showMarkerWhenReadingDefault,p,h);return w.onClose=async()=>{var E,me,Ee;if(!w.resultConfirmed)return;let c=(E=w.resultPassword)!=null?E:"",u=(me=w.resultHint)!=null?me:"";if(i.canEncrypt){let G=new te;G.text=n,G.hint=u,this.encryptSelection(e,G,c,s,a,(Ee=w.resultShowInReadingView)!=null?Ee:this.featureSettings.showMarkerWhenReadingDefault),d.putByPath({password:c,hint:u},l.path)}else i.decryptable&&await this.decryptSelection(e,i.decryptable,c,s,a)&&d.putByPath({password:c,hint:u},l.path)},w.open(),!0}async encryptSelection(t,e,n,s,a,r){let i=I.BuildDefault(),l=this.encodeEncryption(await i.encryptToBase64(e.text,n),e.hint,r);t.setSelection(s,a),t.replaceSelection(l)}async decryptSelection(t,e,n,s,a){let i=await I.BuildFromDecryptableOrThrow(e).decryptFromBase64(e.base64CipherText,n);if(i===null)return new m.Notice("\u274C Decryption failed!"),!1;{let l=new V(this.plugin.app,"\u{1F513}",i);l.onClose=async()=>{var p;if(t.focus(),l.decryptInPlace)t.setSelection(s,a),t.replaceSelection(l.text);else if(l.save){let h=I.BuildDefault(),f=this.encodeEncryption(await h.encryptToBase64(l.text,n),(p=e.hint)!=null?p:"",e.showInReadingView);t.setSelection(s,a),t.replaceSelection(f)}},l.open()}return!0}encodeEncryption(t,e,n){if(!M.some(s=>t.includes(s))&&!H.some(s=>t.includes(s))){let s=n?Te:be,a=n?we:ge;return e.length>0?s.concat(k,e,k,t,a):s.concat(t,a)}return t}},te=class{};var x=require("obsidian");var T=require("obsidian");var A=class extends T.Modal{constructor(e,n,s,a,r){super(e);this.resultConfirmed=!1;this.title=n,this.defaultPassword=r,this.confirmPassword=a,this.isEncrypting=s}onOpen(){var p,h,f,w;let{contentEl:e}=this;e.empty(),this.invalidate();let n=(h=(p=this.defaultPassword)==null?void 0:p.password)!=null?h:"",s="",a=(w=(f=this.defaultPassword)==null?void 0:f.hint)!=null?w:"";new T.Setting(e).setHeading().setName(this.title),D.buildPasswordSetting({container:e,tabIndex:0,name:"Password:",placeholder:this.isEncrypting?"":`Hint: ${a}`,initialValue:n,autoFocus:n=="",onChangeCallback:y=>{n=y,this.invalidate()},onEnterCallback:y=>{if(n=y,this.invalidate(),n.length>0)if(r.settingEl.isShown()){let c=r.components.find(u=>u instanceof T.TextComponent);c instanceof T.TextComponent&&c.inputEl.focus()}else if(i.settingEl.isShown()){let c=i.components.find(u=>u instanceof T.TextComponent);c instanceof T.TextComponent&&c.inputEl.focus()}else l()&&this.close()}});let r=D.buildPasswordSetting({container:e,name:"Confirm Password:",tabIndex:1,autoFocus:n!="",onChangeCallback:y=>{s=y,this.invalidate()},onEnterCallback:y=>{if(s=y,this.invalidate(),s.length>0&&l()&&i.settingEl.isShown()){let c=i.components.find(u=>u instanceof T.TextComponent);c instanceof T.TextComponent&&c.inputEl.focus()}}});this.confirmPassword||r.settingEl.hide();let i=new T.Setting(e).setName("Optional Password Hint").addText(y=>{y.inputEl.placeholder="Password Hint",y.inputEl.tabIndex=2,y.setValue(a),y.onChange(c=>a=c),y.inputEl.on("keypress","*",(c,u)=>{c.key=="Enter"&&u instanceof HTMLInputElement&&u.value.length>0&&(c.preventDefault(),l()&&this.close())})});this.isEncrypting||i.settingEl.hide(),new T.Setting(e).addButton(y=>{y.buttonEl.tabIndex=99,y.setButtonText("Confirm").onClick(c=>{l()&&this.close()})});let l=()=>(this.invalidate(),r.setDesc(""),this.confirmPassword&&n!=s?(r.setDesc("Passwords don't match"),!1):(this.resultConfirmed=!0,this.resultPassword={password:n,hint:a},!0))}open2Async(){return new Promise((e,n)=>{this.onClose=()=>{this.resultConfirmed==!0?e(this.resultPassword):e(null)},this.open()})}openAsync(){return new Promise((e,n)=>{this.onClose=()=>{this.resultConfirmed==!0?e(this.resultPassword):n()},this.open()})}invalidate(){this.resultConfirmed=!1,this.resultPassword={password:"",hint:""}}};var ne=class{constructor(t,e,n){this.version="1.0";this.version=t,this.hint=e,this.encodedData=n}},se=class se{static async encrypt(t,e,n){let a=await I.BuildDefault().encryptToBase64(n,t);return new ne(se.DEFAULT_VERSION,e,a)}static async decrypt(t,e){return t.encodedData==""?"":await I.BuildFromFileDataOrThrow(t).decryptFromBase64(t.encodedData,e)}};se.DEFAULT_VERSION="2.0";var F=se,b=class{static encode(t){return JSON.stringify(t,null,2)}static isEncoded(t){try{return JSON.parse(t),!0}catch(e){return!1}}static decode(t){return t===""?new ne(F.DEFAULT_VERSION,"",""):JSON.parse(t)}};var Be="encrypted",Fe="mdenc",ie=Fe,C=[Fe,Be];var O=require("obsidian");var re=class re extends O.MarkdownView{constructor(){super(...arguments);this.passwordAndHint=null;this.encryptedData=null;this.cachedUnencryptedData="";this.dataWasChangedSinceLastSave=!1;this.isSavingEnabled=!1;this.isLoadingFileInProgress=!1;this.isSavingInProgress=!1;this.allowNoFile=!1}getViewType(){return re.VIEW_TYPE}canAcceptExtension(e){return C.includes(e)}async onOpen(){await super.onOpen(),this.addAction("key-round","Change password",()=>this.changePassword()),this.addAction("lock","Lock & Close",()=>this.lockAndClose())}async onLoadFile(e){this.setViewBusy(!0);try{if(this.setUnencryptedViewData("",!0),!this.app.workspace.layoutReady){this.leaf.detach();return}let n=await this.app.vault.read(e);this.encryptedData=b.decode(n),this.passwordAndHint=await d.getByFile(e),this.passwordAndHint.hint=this.encryptedData.hint;let s=null;for(this.passwordAndHint.password.length>0&&(s=await F.decrypt(this.encryptedData,this.passwordAndHint.password));s==null;){if(this.passwordAndHint=await new A(this.app,`Decrypting "${e.basename}"`,!1,!1,{password:"",hint:this.encryptedData.hint}).open2Async(),this.passwordAndHint==null){this.leaf.detach();return}s=await F.decrypt(this.encryptedData,this.passwordAndHint.password),s==null&&new O.Notice("Decryption failed")}if(s==null){this.leaf.detach();return}this.passwordAndHint!=null&&d.putByFile(this.passwordAndHint,e),this.setUnencryptedViewData(s,!1),this.isLoadingFileInProgress=!0;try{this.origFile=e,await super.onLoadFile(e)}finally{this.isLoadingFileInProgress=!1,this.isSavingEnabled=!0}}finally{this.setViewBusy(!1)}}setViewBusy(e){e?this.contentEl.style.cursor="wait":this.contentEl.style.cursor="auto"}detachSafely(){this.save(),this.isSavingEnabled=!1,this.leaf.detach()}async onUnloadFile(e){this.passwordAndHint==null||this.encryptedData==null||(this.isSavingInProgress&&(console.info("Saving is in progress, but forcing another save because the file is being unloaded"),this.isSavingInProgress=!1,this.dataWasChangedSinceLastSave=!0),await super.onUnloadFile(e))}async onRename(e){this.origFile&&d.clearForFile(this.origFile),this.passwordAndHint!=null&&d.putByFile(this.passwordAndHint,e),await super.onRename(e)}getUnencryptedViewData(){return super.getViewData()}getViewData(){if(this.isSavingInProgress){if(this.encryptedData==null)throw new Error("encryptedData is unexpectedly null");return b.encode(this.encryptedData)}return this.getUnencryptedViewData()}setUnencryptedViewData(e,n){this.cachedUnencryptedData=e,super.setViewData(e,!1)}setViewData(e,n){if(this.file==null){console.info("View data will not be set because file is null");return}if(this.isLoadingFileInProgress)return;if(!b.isEncoded(e)){this.setUnencryptedViewData(e,n);return}if(console.info("View is being set with already encoded data, trying to decode",{data:e}),this.passwordAndHint==null){console.error("passwordAndHint == null");return}let s=b.decode(e);F.decrypt(s,this.passwordAndHint.password).then(a=>{if(a==null){console.info("View was being set with already encoceded data but the decryption failed, closing view"),this.isSavingEnabled=!1,this.leaf.detach();return}this.setUnencryptedViewData(a,n)})}async setState(e,n){e.mode=="preview"&&await this.save(),this.isSavingEnabled=!1;try{await super.setState(e,n),super.setViewData(this.cachedUnencryptedData,!1)}finally{this.isSavingEnabled=!0}}async save(e){if(console.debug("save",{clear:e}),this.isSavingInProgress){console.info("Saving was prevented because another save is in progress, Obsidian will try again later if the content changed.");return}this.isSavingInProgress=!0,this.setViewBusy(!0);try{if(this.file==null){console.info("Saving was prevented beacuse there is no file loaded in the view yet");return}if(!C.includes(this.file.extension)){console.info("Saving was prevented because the file is not an encrypted file");return}if(!this.isSavingEnabled){this.passwordAndHint==null?console.info("Saving was prevented because the file was not yet loaded with a password"):console.info("Saving was prevented because it was explicitly disabled");return}if(this.passwordAndHint==null){console.info("Saving was prevented beacuse there is no password set");return}let n=this.getUnencryptedViewData();if(b.isEncoded(n)){console.info("Saving was prevented beacuse the data was already encoded but it was expected to not be");return}if(!this.dataWasChangedSinceLastSave&&this.cachedUnencryptedData.length==n.length&&this.cachedUnencryptedData==n){console.info("Saving was prevented because the data was not changed");return}this.setUnencryptedViewData(n,!1),this.encryptedData=await F.encrypt(this.passwordAndHint.password,this.passwordAndHint.hint,n),await super.save(e),this.dataWasChangedSinceLastSave=!1}finally{this.isSavingInProgress=!1,this.setViewBusy(!1)}}lockAndClose(){this.detachSafely(),this.file!=null&&d.clearForFile(this.file)}async changePassword(){if(this.file==null){console.info("Unable to change password beacuse there is no file loaded in the view yet");return}let e=new A(this.app,`Change password for "${this.file.basename}"`,!0,!0,await d.getByFile(this.file));try{let n=await e.openAsync();this.passwordAndHint=n,d.putByFile(n,this.file),this.dataWasChangedSinceLastSave=!0,await this.save(),new O.Notice("Password changed")}catch(n){new O.Notice("Password wasn't changed")}}};re.VIEW_TYPE="meld-encrypted-view";var P=re;var $=class{async onload(t,e){this.plugin=t,this.plugin.addCommand({id:"meld-encrypt-convert-to-or-from-encrypted-note",name:"Convert to or from an Encrypted note",icon:"file-lock-2",checkCallback:n=>this.processCommandConvertActiveNote(n)}),this.plugin.addRibbonIcon("file-lock-2","Convert to or from an Encrypted note",n=>this.processCommandConvertActiveNote(!1)),this.plugin.registerEvent(this.plugin.app.workspace.on("file-menu",(n,s)=>{s instanceof x.TFile&&(s.extension=="md"&&n.addItem(a=>{a.setTitle("Encrypt note").setIcon("file-lock-2").onClick(()=>this.processCommandEncryptNote(s))}),C.contains(s.extension)&&n.addItem(a=>{a.setTitle("Decrypt note").setIcon("file").onClick(()=>this.processCommandDecryptNote(s))}))}))}onunload(){}buildSettingsUi(t,e){}checkCanEncryptFile(t){return t==null?!1:t.extension=="md"}checkCanDecryptFile(t){return t==null?!1:C.contains(t.extension)}processCommandEncryptNote(t){this.getPasswordAndEncryptFile(t).catch(e=>{e&&new x.Notice(e,1e4)})}processCommandDecryptNote(t){this.getPasswordAndDecryptFile(t).catch(e=>{e&&new x.Notice(e,1e4)})}processCommandConvertActiveNote(t){let e=this.plugin.app.workspace.getActiveFile();if(t)return this.checkCanEncryptFile(e)||this.checkCanDecryptFile(e);(e==null?void 0:e.extension)=="md"&&this.getPasswordAndEncryptFile(e).catch(n=>{n&&new x.Notice(n,1e4)}),e&&C.contains(e.extension)&&this.getPasswordAndDecryptFile(e).catch(n=>{n&&new x.Notice(n,1e4)})}async getPasswordAndEncryptFile(t){if(!this.checkCanEncryptFile(t))throw new Error("Unable to encrypt file");try{let e=await d.getByFile(t);e.password==""&&(e=await new A(this.plugin.app,"Encrypt Note",!0,!0,e).openAsync());let n=await this.encryptFile(t,e);await this.closeUpdateRememberPasswordThenReopen(t,ie,n,e),new x.Notice("\u{1F510} Note was encrypted \u{1F510}")}catch(e){e&&new x.Notice(e,1e4)}}async getPasswordAndDecryptFile(t){if(!this.checkCanDecryptFile(t))throw new Error("Unable to decrypt file");let e=await d.getByFile(t);if(e.password!=""){let r=await this.decryptFile(t,e.password);if(r!=null){await this.closeUpdateRememberPasswordThenReopen(t,"md",r,e);return}}let n=await this.plugin.app.vault.read(t),s=b.decode(n),a=new A(this.plugin.app,"Decrypt Note",!1,!1,{password:"",hint:s.hint});try{if(e=await a.openAsync(),!a.resultConfirmed)return;let r=await this.decryptFile(t,e.password);if(r==null)throw new Error("Decryption failed");await this.closeUpdateRememberPasswordThenReopen(t,"md",r,e),new x.Notice("\u{1F513} Note was decrypted \u{1F513}")}catch(r){r&&new x.Notice(r,1e4)}}async closeUpdateRememberPasswordThenReopen(t,e,n,s){let a=!1;this.plugin.app.workspace.iterateAllLeaves(r=>{r.view instanceof x.TextFileView&&r.view.file==t&&(r.view instanceof P?r.view.detachSafely():r.detach(),a=!0)});try{let r=R.getFilePathWithNewExtension(t,e);await this.plugin.app.fileManager.renameFile(t,r),await this.plugin.app.vault.modify(t,n),d.putByFile(s,t)}finally{a&&await this.plugin.app.workspace.getLeaf(!0).openFile(t)}}async encryptFile(t,e){let n=await this.plugin.app.vault.read(t),s=await F.encrypt(e.password,e.hint,n);return b.encode(s)}async decryptFile(t,e){let n=await this.plugin.app.vault.read(t),s=b.decode(n);return await F.decrypt(s,e)}};var L=require("obsidian");var z=class{async onload(t){this.plugin=t,this.plugin.addRibbonIcon("file-lock-2","New encrypted note",async e=>{await this.processCreateNewEncryptedNoteCommand(this.getDefaultFileFolder())}),this.plugin.addRibbonIcon("book-lock","Lock and Close all open encrypted notes",async e=>{await this.processLockAndCloseAllEncryptedNotesCommand()}),this.plugin.addCommand({id:"meld-encrypt-create-new-note",name:"Create new encrypted note",icon:"file-lock-2",callback:async()=>await this.processCreateNewEncryptedNoteCommand(this.getDefaultFileFolder())}),this.plugin.addCommand({id:"meld-encrypt-close-and-forget",name:"Lock and Close all open encrypted notes",icon:"book-lock",callback:async()=>await this.processLockAndCloseAllEncryptedNotesCommand()}),this.plugin.registerEvent(this.plugin.app.workspace.on("file-menu",(e,n)=>{n instanceof L.TFolder&&e.addItem(s=>{s.setTitle("New encrypted note").setIcon("file-lock-2").onClick(()=>this.processCreateNewEncryptedNoteCommand(n))})})),this.statusIndicator=this.plugin.addStatusBarItem(),this.statusIndicator.hide(),this.statusIndicator.setText("\u{1F510}"),this.plugin.registerEvent(this.plugin.app.workspace.on("editor-menu",(e,n,s)=>{s.file==null||!C.includes(s.file.extension)||s instanceof P&&(e.addItem(a=>{a.setTitle("Change Password").setIcon("key-round").onClick(async()=>await s.changePassword())}),e.addItem(a=>{a.setTitle("Lock & Close").setIcon("lock").onClick(()=>s.lockAndClose())}))})),this.plugin.registerEvent(this.plugin.app.workspace.on("file-menu",(e,n)=>{if(!(n instanceof L.TFile)||!C.includes(n.extension))return;let s=this.plugin.app.workspace.getActiveViewOfType(P);s==null||s.file!=n||(e.addItem(a=>{a.setTitle("Change Password").setIcon("key-round").onClick(async()=>await s.changePassword())}),e.addItem(a=>{a.setTitle("Lock & Close").setIcon("lock").onClick(()=>s.lockAndClose())}))})),this.plugin.registerView(P.VIEW_TYPE,e=>new P(e)),this.plugin.registerExtensions(C,P.VIEW_TYPE),this.plugin.registerEvent(this.plugin.app.workspace.on("active-leaf-change",()=>{if(this.plugin.app.workspace.getActiveViewOfType(P)==null){this.statusIndicator.hide();return}this.statusIndicator.show()})),this.plugin.registerEvent(this.plugin.app.workspace.on("active-leaf-change",async e=>{if(e!=null&&!(e.view instanceof P)&&e.view instanceof L.MarkdownView){let n=e.view.file;if(n==null)return;if(C.includes(n.extension)){let s=e.getViewState();s.type=P.VIEW_TYPE,await e.setViewState(s);return}}}))}async processLockAndCloseAllEncryptedNotesCommand(){let t=this.plugin.app.workspace.getLeavesOfType(P.VIEW_TYPE);for(let e of t){let n=e.view;n!=null&&n.lockAndClose()}}getDefaultFileFolder(){let t=this.plugin.app.workspace.getActiveFile();return t!=null?this.plugin.app.fileManager.getNewFileParent(t.path):this.plugin.app.fileManager.getNewFileParent("")}async processCreateNewEncryptedNoteCommand(t){let e=(0,L.moment)().format(`[Untitled] YYYYMMDD hhmmss[.${ie}]`),n=(0,L.normalizePath)(t.path+"/"+e),s;if(d.getLevel()==d.LevelExternalFile&&(s=await d.getByPathAsync(n)),!s){let p=new A(this.plugin.app,"Please provide a password for encryption",!0,!0,await d.getByPathAsync(n));try{s=await p.openAsync()}catch(h){return}}let a=await F.encrypt(s.password,s.hint,""),r=b.encode(a),i=await this.plugin.app.vault.create(n,r);d.putByFile(s,i),await this.plugin.app.workspace.getLeaf(!0).openFile(i)}onunload(){this.plugin.app.workspace.detachLeavesOfType(P.VIEW_TYPE)}buildSettingsUi(t,e){}};var ae=class extends oe.Plugin{constructor(){super(...arguments);this.enabledFeatures=[]}async onload(){d.init(this.app.vault.adapter),await this.loadSettings(),this.enabledFeatures.push(new z,new $,new _),this.addSettingTab(new U(this.app,this,this.settings,this.enabledFeatures)),this.addCommand({id:"meld-encrypt-clear-password-cache",name:"Clear Session Password Cache",icon:"shield-ellipsis",callback:()=>{let e=d.clear();new oe.Notice(`Items cleared: ${e}`)}}),this.enabledFeatures.forEach(async e=>{await e.onload(this,this.settings)})}onunload(){this.enabledFeatures.forEach(async e=>{e.onunload()}),super.onunload()}async loadSettings(){let e={confirmPassword:!0,rememberPassword:!0,rememberPasswordTimeout:30,rememberPasswordLevel:d.LevelVault,rememberPasswordExternalFilePaths:[],featureWholeNoteEncrypt:{},featureInplaceEncrypt:{expandToWholeLines:!1,markerSearchLimit:1e4,showMarkerWhenReadingDefault:!0}};this.settings=Object.assign(e,await this.loadData()),d.setActive(this.settings.rememberPassword),d.setAutoExpire(this.settings.rememberPasswordTimeout==0?null:this.settings.rememberPasswordTimeout),d.setLevel(this.settings.rememberPasswordLevel),d.setExternalFilePaths(this.settings.rememberPasswordExternalFilePaths)}async saveSettings(){await this.saveData(this.settings)}};

/* nosourcemap */