"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md","embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.04441181,-0.02124629,-0.00333184,-0.03570345,-0.0550703,0.02767582,-0.01045297,-0.01939703,-0.00861687,0.00662941,0.01905607,0.04816126,-0.0161664,-0.01601238,0.01348282,-0.00538072,-0.02783925,0.00806009,0.0279773,0.01204148,-0.02816498,0.00041348,0.02277087,0.04758377,0.01818645,-0.04565886,-0.03283561,-0.07848366,0.04296432,0.03853494,0.05499031,-0.01107902,0.03670383,0.01189101,0.0147448,0.00974585,0.02008667,-0.00104396,0.000559,-0.04470524,0.00881456,0.00654238,-0.06359442,0.01121001,-0.1242359,0.01900138,-0.04307963,-0.08684956,0.01876687,0.02375367,0.00637517,-0.00339326,-0.0095263,-0.04370194,-0.00432183,-0.03627486,-0.06739793,-0.02087305,0.0133534,-0.00855454,0.02454309,0.0759396,0.04694761,-0.04944407,0.03862749,0.00272031,0.03389155,-0.03636663,-0.0310958,-0.00329272,-0.00469708,-0.03650899,0.06828363,0.03785367,0.0259697,0.03214565,0.01742963,0.00023908,-0.05618465,0.01389573,-0.03485502,0.01096218,0.02787672,0.06039391,0.02606299,-0.01735539,0.01011916,-0.03851722,-0.05090823,-0.03391163,0.01687974,0.0062163,0.02207875,0.0033369,0.02009696,-0.04634536,-0.07042909,0.00845933,-0.00626835,-0.02938607,-0.03076351,0.01471185,-0.04260883,-0.00011353,-0.05081093,-0.05195053,-0.01064594,0.04612759,0.01089985,0.00332458,-0.00245826,0.00002562,-0.03472499,-0.04906492,-0.01072415,-0.02273699,0.00389371,0.05404257,0.00390674,0.06305312,0.01554814,-0.03812091,-0.0046412,0.00728675,-0.05779782,-0.00563448,0.01565691,-0.03247653,0.02214556,-0.06418608,-0.04699659,0.01583813,-0.0417784,-0.01495462,-0.01171086,-0.01745463,0.07368427,-0.07400184,0.02253512,-0.00074552,0.06109423,0.01406024,-0.01398087,-0.00298463,-0.02595857,0.00003589,-0.05625802,-0.00616501,0.04520561,0.00780804,0.03866301,-0.00456193,-0.06580733,-0.05948161,-0.00794935,0.056154,0.08199024,0.04701989,-0.01323924,-0.02450209,-0.0022528,0.0597626,-0.03457076,-0.04012563,0.0091211,-0.00459015,0.01092996,0.01635573,0.01744831,-0.00942933,0.03527283,0.02691853,-0.00130717,-0.01165854,0.02783385,-0.02894246,0.03957665,-0.02361341,-0.06550327,0.02939551,0.052288,0.03431313,0.01037509,0.03297894,-0.00699977,0.04418877,0.01121166,0.01565325,-0.04231017,0.00666587,0.03288118,-0.03405843,0.00362767,-0.0536673,0.01070462,0.04769229,-0.02314889,-0.04226977,0.0121136,0.01421347,0.03158756,0.08392516,-0.04140155,-0.08610295,0.01551067,0.00902357,-0.03291538,-0.04511222,-0.01234892,0.02224738,0.02851664,-0.00393095,0.11644812,0.0071639,0.0081725,0.0572648,0.0136129,0.0445256,-0.01439368,-0.01607793,0.02537744,-0.00663086,0.02545552,0.00313035,0.02422615,-0.04990432,0.01100791,0.00071172,0.04888288,0.06334534,-0.0037691,0.06349252,0.00287097,-0.05176841,-0.02715982,-0.00982099,0.03698228,0.00274061,-0.005355,0.02587388,-0.05187611,-0.02339184,0.01405256,0.05049833,0.03590214,-0.02156911,-0.09492502,0.01324969,-0.02977502,0.00055395,0.05858471,0.06377889,-0.00375718,0.01376072,0.03531917,-0.06125143,-0.06494302,0.02599871,0.00324688,-0.03852572,0.02277135,-0.01747881,0.02146249,0.03166952,0.01437167,0.0003586,-0.02610454,0.03105883,-0.00641615,0.05758671,0.00676558,-0.00351915,0.03471058,0.03853091,-0.02495762,-0.02209399,-0.00179612,0.00972931,0.00001648,-0.00489146,-0.00118213,-0.01436337,0.00238759,-0.00909569,-0.07276691,0.04819228,-0.00287255,-0.01164788,0.04773475,0.01757487,-0.026187,-0.03172838,-0.0078618,0.02365563,-0.03615611,-0.06697369,-0.02852391,0.04343264,0.03335927,-0.03406345,0.00435385,0.08422752,-0.03477783,-0.04926421,-0.0170418,-0.02212399,0.0022,0.00759628,-0.04088121,-0.01616911,0.01754341,-0.03752972,0.0059848,-0.02563951,0.00850243,0.03729197,0.06670659,0.00928043,-0.00559652,0.02877924,-0.0037795,0.01355254,-0.06457317,-0.01130064,0.00422342,-0.08412301,-0.04159651,0.00728318,-0.03169522,0.09360503,-0.04276978,-0.0249991,-0.02375223,-0.04013609,0.05259823,0.0164463,0.00289862,-0.00303213,0.01513453,-0.01946368,0.03775488,-0.04347741,-0.01836489,-0.08239877,-0.04009463,0.01587731,0.02324372,0.03509132,0.01411647,-0.0368227,-0.00304041,0.00575876,-0.04083987,0.02838294,-0.01590856,-0.00473796,-0.05041134,0.010788,-0.0275362,0.02596009,0.00837307,-0.01877042,-0.05033566,-0.07400221,-0.01700102,-0.01245891,0.04976095,0.04431497,-0.04390326,-0.02982565,-0.01729164,-0.04064893,0.01054123,-0.0002154,0.00268359,-0.0109477,-0.06597698,-0.0100229,0.0330567,-0.01308664,-0.03475624,-0.09977769,-0.04567883,-0.0008569,-0.0241251,-0.02966105,-0.00505079,0.00175488,0.08617339,-0.10469093,-0.01458165,0.01457547,-0.03776478,0.00550346,0.02341818,-0.05021984,-0.01892358,0.00125938,0.03427964,-0.03636661,0.04989282,0.05964895,-0.00116667,-0.03674161,-0.03013019,0.06419355,-0.05621974,0.03766899,-0.0312056,0.01625765,-0.03095282,0.05256905,-0.04195196,-0.01947656,0.01254804,0.00523246,0.03978112,-0.06471905,0.01304373,-0.03184263,0.02609687,-0.00647997,0.04248162,0.0506437,-0.06343411,-0.00516547,-0.04950061,0.01024048,0.02850123,0.03433821,-0.0282566,-0.05624178,0.00446558,-0.03124038,-0.00399112,-0.00010176,0.01564344,0.02928867,0.00110446,0.00661112,-0.04235262,-0.0588243,-0.04588758,0.01126832,0.01042026,0.06935026,0.01512227,0.00212932,0.00511559,0.03386357,0.0261137,-0.01394208,-0.08660968,-0.06740193,0.01975726,0.01187169,-0.00949608,-0.00014311,0.0433711,0.0332621,-0.02950053,0.0713393,0.05592003,-0.02506239,-0.02489796,0.02200535,0.01387124,-0.00432432,-0.10564564,0.01252779,-0.06021111,0.00531378,0.00973369,-0.05000005,0.05212956,0.01000425,0.00708697,-0.01404127,-0.06719913,0.04949461,-0.02634536,0.00455739,-0.03871232,-0.01662036,-0.0565059,0.0100033,-0.01556964,0.03485025,-0.01290994,-0.04766267,-0.03598786,0.05578491,0.01678086,-0.00780641,-0.04903236,0.03485309,0.04119173,0.03826128,-0.00444628,0.05662728,0.02645976,0.01196321,-0.06209104,0.03444717,0.05382612,0.02281843,-0.03120775,0.00407285,0.00912026,-0.03045653,-0.02269497,-0.019054,-0.02449588,0.06769584,-0.01880974,-0.02038465,0.05205458,0.03078612,-0.03154486,-0.04077853,-0.01884447,0.01879577,-0.06154998,-0.02256291,0.03688355,0.02064784,-0.00796345,0.02709822,0.04697562,0.05370813,0.03648232,-0.05141,0.00336855,-0.02796377,0.00018363,0.00118409,0.02316717,-0.00766067,-0.06761991,0.01949359,-0.00231471,0.01033669,-0.06187304,0.00577974,0.02284884,0.0409121,0.01274473,-0.054025,-0.00055343,-0.01141413,-0.00189376,0.02261677,0.03988474,0.03272923,-0.0077409,0.02554423,0.02839837,-0.03058024,-0.0397015,0.1028577,0.06414057,-0.00532512,0.05136146,0.00117056,0.04094916,0.02346908,-0.00766944,-0.00207322,0.03304784,0.03857997,-0.11403506,-0.00368285,0.09205802,0.02607693,-0.02586906,-0.01792724,-0.00233822,-0.03028915,0.08544457,-0.04632534,0.06022209,-0.00728462,0.06003719,-0.00794588,-0.04274713,0.01110761,-0.01975508,0.07427945,0.00555068,0.00126183,0.05698644,-0.01988052,-0.03063458,0.01938661,0.02584508,0.026359,-0.00745278,0.03170718,0.0046799,-0.01074748,0.02324869,0.01414753,-0.04777012,-0.00961506,0.02021422,0.00925759,0.02284495,-0.01714588,-0.00924333,0.04642827,0.00004582,0.08164095,-0.04794008,-0.02810161,0.01871184,0.01390069,0.04865066,0.00165426,-0.01515151,0.01962671,-0.04833997,-0.06109234,-0.02270564,-0.01777715,-0.03012442,-0.01226126,0.00453407,0.04361438,0.05026913,-0.04168609,0.00665731,-0.03913227,-0.03370699,-0.01242791,0.058356,0.02140819,-0.0025068,-0.02896598,0.01157819,0.00865224,0.02878125,-0.07588813,-0.01869649,0.03238474,0.0087086,-0.01767739,-0.04801599,-0.07722064,-0.02481468,0.01954659,0.03415971,-0.00323908,-0.01118753,-0.02824617,-0.01253211,0.00745431,0.02614869,0.02187947,0.02021026,0.05317605,-0.00200757,-0.01063586,0.00641514,0.06760909,0.00248446,-0.01462352,0.04175316,-0.04028817,-0.03681042,-0.09658051,0.00464517,0.00062127,0.00071073,0.01758156,-0.01171901,0.0492451,-0.01846372,0.03317243,-0.01147743,0.05317403,-0.0652344,-0.08543471,0.04444469,0.03104418,-0.01098721,-0.02432246,-0.03255034,-0.03049623,-0.00337737,0.00274998,-0.0483622,0.06799784,-0.02053085,-0.00276595,0.02423183,0.00832238,0.00831262,0.0571948,-0.01006304,0.00152918,-0.01224016,0.0146807,0.00419864,0.04199144,-0.06137561,0.00855142,0.01425976,0.05070343,0.03283959,0.00187169,-0.00331891,-0.0413302,-0.01580087,-0.08062554,0.04836047,-0.04563453,0.01150235,0.03275998,-0.02062572,0.02560561,0.00525408,0.02157429,0.0454945,0.04560015,-0.03744294,-0.00313463,-0.01350746,-0.00922074,0.0053948,0.03340163,-0.00166413,-0.06409562,-0.02244582,0.03370463,0.04659387,-0.0400959,0.01674599,-0.01530808,0.03149413,-0.02951856,0.0327161,-0.03566166,0.02385761,0.00029454,-0.00229159,0.0428814,-0.03074516,0.0437169,0.00532317,0.06077269,-0.03219259,0.00168606,0.01071122,0.05212338,-0.01879225,0.04460159,-0.01087955,-0.06433431,9e-7,0.03611455,-0.01117641,-0.01118125,-0.02642377,-0.00314386,-0.00237155,-0.00734691,0.02585956,0.02767682],"last_embed":{"hash":"11aqebv","tokens":537}}},"last_read":{"hash":"11aqebv","at":1752940659394},"class_name":"SmartSource","last_import":{"mtime":1731396396534,"size":1845,"at":1749002741368,"hash":"11aqebv"},"blocks":{"#---frontmatter---":[1,10],"#简介":[12,21],"#简介#{1}":[13,13],"#简介#{2}":[14,16],"#简介#{3}":[17,21],"#请求伪造":[22,42],"#请求伪造#[[Burp Suite]]替换规则":[24,42],"#请求伪造#[[Burp Suite]]替换规则#{1}":[25,25],"#请求伪造#[[Burp Suite]]替换规则#{2}":[26,27],"#请求伪造#[[Burp Suite]]替换规则#{3}":[28,28],"#请求伪造#[[Burp Suite]]替换规则#{4}":[29,30],"#请求伪造#[[Burp Suite]]替换规则#{5}":[31,36],"#请求伪造#[[Burp Suite]]替换规则#{6}":[34,36],"#请求伪造#[[Burp Suite]]替换规则#{7}":[37,37],"#请求伪造#[[Burp Suite]]替换规则#{8}":[38,38],"#请求伪造#[[Burp Suite]]替换规则#{9}":[39,39],"#请求伪造#[[Burp Suite]]替换规则#{10}":[40,40],"#请求伪造#[[Burp Suite]]替换规则#{11}":[41,42]},"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":7},{"title":"Burp Suite","target":"Burp Suite","line":9},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":13},{"title":"IP协议","target":"IP协议","line":14},{"title":"Burp Suite","target":"Burp Suite","line":24},{"title":"780","target":"Pasted image 20240402154555.png","line":25},{"title":"curl","target":"curl","line":26},{"title":"Me-and-My-Girlfriend","target":"Me-and-My-Girlfriend","line":26},{"title":"x-forwarded-for","target":"x-forwarded-for","line":26},{"title":"Pasted image 20240402155255.png","target":"Pasted image 20240402155255.png","line":28},{"title":"curl","target":"curl","line":30},{"title":"x-forwarded-for","target":"x-forwarded-for","line":30},{"title":"curl","target":"curl","line":31},{"title":"cookies","target":"cookies","line":32},{"title":"curl","target":"curl","line":33},{"title":"Me-and-My-Girlfriend","target":"Me-and-My-Girlfriend","line":33},{"title":"x-forwarded-for","target":"x-forwarded-for","line":38},{"title":"cookies","target":"cookies","line":39},{"title":"Burp Suite","target":"Burp Suite","line":41},{"title":"curl","target":"curl","line":41},{"title":"x-forwarded-for","target":"x-forwarded-for","line":41},{"title":"Burp Suite","target":"Burp Suite","line":42},{"title":"cookies","target":"cookies","line":42}],"metadata":{"tags":["互联网/浏览器/HTTP"],"aliases":["XFF"],"基础知识":["[[HTTP(S)协议]]"],"扩展知识":["[[Burp Suite]]"]},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md","last_embed":{"hash":"11aqebv","at":1752940659394}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#---frontmatter---","lines":[1,10],"size":100,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":7},{"title":"Burp Suite","target":"Burp Suite","line":9}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#简介","lines":[12,21],"size":230,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":2},{"title":"IP协议","target":"IP协议","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#简介#{1}","lines":[13,13],"size":48,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#简介#{2}","lines":[14,16],"size":114,"outlinks":[{"title":"IP协议","target":"IP协议","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#简介#{3}","lines":[17,21],"size":61,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造","lines":[22,42],"size":790,"outlinks":[{"title":"Burp Suite","target":"Burp Suite","line":3},{"title":"780","target":"Pasted image 20240402154555.png","line":4},{"title":"curl","target":"curl","line":5},{"title":"Me-and-My-Girlfriend","target":"Me-and-My-Girlfriend","line":5},{"title":"x-forwarded-for","target":"x-forwarded-for","line":5},{"title":"Pasted image 20240402155255.png","target":"Pasted image 20240402155255.png","line":7},{"title":"curl","target":"curl","line":9},{"title":"x-forwarded-for","target":"x-forwarded-for","line":9},{"title":"curl","target":"curl","line":10},{"title":"cookies","target":"cookies","line":11},{"title":"curl","target":"curl","line":12},{"title":"Me-and-My-Girlfriend","target":"Me-and-My-Girlfriend","line":12},{"title":"x-forwarded-for","target":"x-forwarded-for","line":17},{"title":"cookies","target":"cookies","line":18},{"title":"Burp Suite","target":"Burp Suite","line":20},{"title":"curl","target":"curl","line":20},{"title":"x-forwarded-for","target":"x-forwarded-for","line":20},{"title":"Burp Suite","target":"Burp Suite","line":21},{"title":"cookies","target":"cookies","line":21}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则","lines":[24,42],"size":781,"outlinks":[{"title":"Burp Suite","target":"Burp Suite","line":1},{"title":"780","target":"Pasted image 20240402154555.png","line":2},{"title":"curl","target":"curl","line":3},{"title":"Me-and-My-Girlfriend","target":"Me-and-My-Girlfriend","line":3},{"title":"x-forwarded-for","target":"x-forwarded-for","line":3},{"title":"Pasted image 20240402155255.png","target":"Pasted image 20240402155255.png","line":5},{"title":"curl","target":"curl","line":7},{"title":"x-forwarded-for","target":"x-forwarded-for","line":7},{"title":"curl","target":"curl","line":8},{"title":"cookies","target":"cookies","line":9},{"title":"curl","target":"curl","line":10},{"title":"Me-and-My-Girlfriend","target":"Me-and-My-Girlfriend","line":10},{"title":"x-forwarded-for","target":"x-forwarded-for","line":15},{"title":"cookies","target":"cookies","line":16},{"title":"Burp Suite","target":"Burp Suite","line":18},{"title":"curl","target":"curl","line":18},{"title":"x-forwarded-for","target":"x-forwarded-for","line":18},{"title":"Burp Suite","target":"Burp Suite","line":19},{"title":"cookies","target":"cookies","line":19}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{1}","lines":[25,25],"size":40,"outlinks":[{"title":"780","target":"Pasted image 20240402154555.png","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{2}","lines":[26,27],"size":94,"outlinks":[{"title":"curl","target":"curl","line":1},{"title":"Me-and-My-Girlfriend","target":"Me-and-My-Girlfriend","line":1},{"title":"x-forwarded-for","target":"x-forwarded-for","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{3}","lines":[28,28],"size":36,"outlinks":[{"title":"Pasted image 20240402155255.png","target":"Pasted image 20240402155255.png","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{4}","lines":[29,30],"size":79,"outlinks":[{"title":"curl","target":"curl","line":2},{"title":"x-forwarded-for","target":"x-forwarded-for","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{5}","lines":[31,36],"size":302,"outlinks":[{"title":"curl","target":"curl","line":1},{"title":"cookies","target":"cookies","line":2},{"title":"curl","target":"curl","line":3},{"title":"Me-and-My-Girlfriend","target":"Me-and-My-Girlfriend","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{6}","lines":[34,36],"size":163,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{7}","lines":[37,37],"size":24,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{8}","lines":[38,38],"size":31,"outlinks":[{"title":"x-forwarded-for","target":"x-forwarded-for","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{9}","lines":[39,39],"size":26,"outlinks":[{"title":"cookies","target":"cookies","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{10}","lines":[40,40],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{11}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md#请求伪造#[[Burp Suite]]替换规则#{11}","lines":[41,42],"size":114,"outlinks":[{"title":"Burp Suite","target":"Burp Suite","line":1},{"title":"curl","target":"curl","line":1},{"title":"x-forwarded-for","target":"x-forwarded-for","line":1},{"title":"Burp Suite","target":"Burp Suite","line":2},{"title":"cookies","target":"cookies","line":2}],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md","embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.04441181,-0.02124629,-0.00333184,-0.03570345,-0.0550703,0.02767582,-0.01045297,-0.01939703,-0.00861687,0.00662941,0.01905607,0.04816126,-0.0161664,-0.01601238,0.01348282,-0.00538072,-0.02783925,0.00806009,0.0279773,0.01204148,-0.02816498,0.00041348,0.02277087,0.04758377,0.01818645,-0.04565886,-0.03283561,-0.07848366,0.04296432,0.03853494,0.05499031,-0.01107902,0.03670383,0.01189101,0.0147448,0.00974585,0.02008667,-0.00104396,0.000559,-0.04470524,0.00881456,0.00654238,-0.06359442,0.01121001,-0.1242359,0.01900138,-0.04307963,-0.08684956,0.01876687,0.02375367,0.00637517,-0.00339326,-0.0095263,-0.04370194,-0.00432183,-0.03627486,-0.06739793,-0.02087305,0.0133534,-0.00855454,0.02454309,0.0759396,0.04694761,-0.04944407,0.03862749,0.00272031,0.03389155,-0.03636663,-0.0310958,-0.00329272,-0.00469708,-0.03650899,0.06828363,0.03785367,0.0259697,0.03214565,0.01742963,0.00023908,-0.05618465,0.01389573,-0.03485502,0.01096218,0.02787672,0.06039391,0.02606299,-0.01735539,0.01011916,-0.03851722,-0.05090823,-0.03391163,0.01687974,0.0062163,0.02207875,0.0033369,0.02009696,-0.04634536,-0.07042909,0.00845933,-0.00626835,-0.02938607,-0.03076351,0.01471185,-0.04260883,-0.00011353,-0.05081093,-0.05195053,-0.01064594,0.04612759,0.01089985,0.00332458,-0.00245826,0.00002562,-0.03472499,-0.04906492,-0.01072415,-0.02273699,0.00389371,0.05404257,0.00390674,0.06305312,0.01554814,-0.03812091,-0.0046412,0.00728675,-0.05779782,-0.00563448,0.01565691,-0.03247653,0.02214556,-0.06418608,-0.04699659,0.01583813,-0.0417784,-0.01495462,-0.01171086,-0.01745463,0.07368427,-0.07400184,0.02253512,-0.00074552,0.06109423,0.01406024,-0.01398087,-0.00298463,-0.02595857,0.00003589,-0.05625802,-0.00616501,0.04520561,0.00780804,0.03866301,-0.00456193,-0.06580733,-0.05948161,-0.00794935,0.056154,0.08199024,0.04701989,-0.01323924,-0.02450209,-0.0022528,0.0597626,-0.03457076,-0.04012563,0.0091211,-0.00459015,0.01092996,0.01635573,0.01744831,-0.00942933,0.03527283,0.02691853,-0.00130717,-0.01165854,0.02783385,-0.02894246,0.03957665,-0.02361341,-0.06550327,0.02939551,0.052288,0.03431313,0.01037509,0.03297894,-0.00699977,0.04418877,0.01121166,0.01565325,-0.04231017,0.00666587,0.03288118,-0.03405843,0.00362767,-0.0536673,0.01070462,0.04769229,-0.02314889,-0.04226977,0.0121136,0.01421347,0.03158756,0.08392516,-0.04140155,-0.08610295,0.01551067,0.00902357,-0.03291538,-0.04511222,-0.01234892,0.02224738,0.02851664,-0.00393095,0.11644812,0.0071639,0.0081725,0.0572648,0.0136129,0.0445256,-0.01439368,-0.01607793,0.02537744,-0.00663086,0.02545552,0.00313035,0.02422615,-0.04990432,0.01100791,0.00071172,0.04888288,0.06334534,-0.0037691,0.06349252,0.00287097,-0.05176841,-0.02715982,-0.00982099,0.03698228,0.00274061,-0.005355,0.02587388,-0.05187611,-0.02339184,0.01405256,0.05049833,0.03590214,-0.02156911,-0.09492502,0.01324969,-0.02977502,0.00055395,0.05858471,0.06377889,-0.00375718,0.01376072,0.03531917,-0.06125143,-0.06494302,0.02599871,0.00324688,-0.03852572,0.02277135,-0.01747881,0.02146249,0.03166952,0.01437167,0.0003586,-0.02610454,0.03105883,-0.00641615,0.05758671,0.00676558,-0.00351915,0.03471058,0.03853091,-0.02495762,-0.02209399,-0.00179612,0.00972931,0.00001648,-0.00489146,-0.00118213,-0.01436337,0.00238759,-0.00909569,-0.07276691,0.04819228,-0.00287255,-0.01164788,0.04773475,0.01757487,-0.026187,-0.03172838,-0.0078618,0.02365563,-0.03615611,-0.06697369,-0.02852391,0.04343264,0.03335927,-0.03406345,0.00435385,0.08422752,-0.03477783,-0.04926421,-0.0170418,-0.02212399,0.0022,0.00759628,-0.04088121,-0.01616911,0.01754341,-0.03752972,0.0059848,-0.02563951,0.00850243,0.03729197,0.06670659,0.00928043,-0.00559652,0.02877924,-0.0037795,0.01355254,-0.06457317,-0.01130064,0.00422342,-0.08412301,-0.04159651,0.00728318,-0.03169522,0.09360503,-0.04276978,-0.0249991,-0.02375223,-0.04013609,0.05259823,0.0164463,0.00289862,-0.00303213,0.01513453,-0.01946368,0.03775488,-0.04347741,-0.01836489,-0.08239877,-0.04009463,0.01587731,0.02324372,0.03509132,0.01411647,-0.0368227,-0.00304041,0.00575876,-0.04083987,0.02838294,-0.01590856,-0.00473796,-0.05041134,0.010788,-0.0275362,0.02596009,0.00837307,-0.01877042,-0.05033566,-0.07400221,-0.01700102,-0.01245891,0.04976095,0.04431497,-0.04390326,-0.02982565,-0.01729164,-0.04064893,0.01054123,-0.0002154,0.00268359,-0.0109477,-0.06597698,-0.0100229,0.0330567,-0.01308664,-0.03475624,-0.09977769,-0.04567883,-0.0008569,-0.0241251,-0.02966105,-0.00505079,0.00175488,0.08617339,-0.10469093,-0.01458165,0.01457547,-0.03776478,0.00550346,0.02341818,-0.05021984,-0.01892358,0.00125938,0.03427964,-0.03636661,0.04989282,0.05964895,-0.00116667,-0.03674161,-0.03013019,0.06419355,-0.05621974,0.03766899,-0.0312056,0.01625765,-0.03095282,0.05256905,-0.04195196,-0.01947656,0.01254804,0.00523246,0.03978112,-0.06471905,0.01304373,-0.03184263,0.02609687,-0.00647997,0.04248162,0.0506437,-0.06343411,-0.00516547,-0.04950061,0.01024048,0.02850123,0.03433821,-0.0282566,-0.05624178,0.00446558,-0.03124038,-0.00399112,-0.00010176,0.01564344,0.02928867,0.00110446,0.00661112,-0.04235262,-0.0588243,-0.04588758,0.01126832,0.01042026,0.06935026,0.01512227,0.00212932,0.00511559,0.03386357,0.0261137,-0.01394208,-0.08660968,-0.06740193,0.01975726,0.01187169,-0.00949608,-0.00014311,0.0433711,0.0332621,-0.02950053,0.0713393,0.05592003,-0.02506239,-0.02489796,0.02200535,0.01387124,-0.00432432,-0.10564564,0.01252779,-0.06021111,0.00531378,0.00973369,-0.05000005,0.05212956,0.01000425,0.00708697,-0.01404127,-0.06719913,0.04949461,-0.02634536,0.00455739,-0.03871232,-0.01662036,-0.0565059,0.0100033,-0.01556964,0.03485025,-0.01290994,-0.04766267,-0.03598786,0.05578491,0.01678086,-0.00780641,-0.04903236,0.03485309,0.04119173,0.03826128,-0.00444628,0.05662728,0.02645976,0.01196321,-0.06209104,0.03444717,0.05382612,0.02281843,-0.03120775,0.00407285,0.00912026,-0.03045653,-0.02269497,-0.019054,-0.02449588,0.06769584,-0.01880974,-0.02038465,0.05205458,0.03078612,-0.03154486,-0.04077853,-0.01884447,0.01879577,-0.06154998,-0.02256291,0.03688355,0.02064784,-0.00796345,0.02709822,0.04697562,0.05370813,0.03648232,-0.05141,0.00336855,-0.02796377,0.00018363,0.00118409,0.02316717,-0.00766067,-0.06761991,0.01949359,-0.00231471,0.01033669,-0.06187304,0.00577974,0.02284884,0.0409121,0.01274473,-0.054025,-0.00055343,-0.01141413,-0.00189376,0.02261677,0.03988474,0.03272923,-0.0077409,0.02554423,0.02839837,-0.03058024,-0.0397015,0.1028577,0.06414057,-0.00532512,0.05136146,0.00117056,0.04094916,0.02346908,-0.00766944,-0.00207322,0.03304784,0.03857997,-0.11403506,-0.00368285,0.09205802,0.02607693,-0.02586906,-0.01792724,-0.00233822,-0.03028915,0.08544457,-0.04632534,0.06022209,-0.00728462,0.06003719,-0.00794588,-0.04274713,0.01110761,-0.01975508,0.07427945,0.00555068,0.00126183,0.05698644,-0.01988052,-0.03063458,0.01938661,0.02584508,0.026359,-0.00745278,0.03170718,0.0046799,-0.01074748,0.02324869,0.01414753,-0.04777012,-0.00961506,0.02021422,0.00925759,0.02284495,-0.01714588,-0.00924333,0.04642827,0.00004582,0.08164095,-0.04794008,-0.02810161,0.01871184,0.01390069,0.04865066,0.00165426,-0.01515151,0.01962671,-0.04833997,-0.06109234,-0.02270564,-0.01777715,-0.03012442,-0.01226126,0.00453407,0.04361438,0.05026913,-0.04168609,0.00665731,-0.03913227,-0.03370699,-0.01242791,0.058356,0.02140819,-0.0025068,-0.02896598,0.01157819,0.00865224,0.02878125,-0.07588813,-0.01869649,0.03238474,0.0087086,-0.01767739,-0.04801599,-0.07722064,-0.02481468,0.01954659,0.03415971,-0.00323908,-0.01118753,-0.02824617,-0.01253211,0.00745431,0.02614869,0.02187947,0.02021026,0.05317605,-0.00200757,-0.01063586,0.00641514,0.06760909,0.00248446,-0.01462352,0.04175316,-0.04028817,-0.03681042,-0.09658051,0.00464517,0.00062127,0.00071073,0.01758156,-0.01171901,0.0492451,-0.01846372,0.03317243,-0.01147743,0.05317403,-0.0652344,-0.08543471,0.04444469,0.03104418,-0.01098721,-0.02432246,-0.03255034,-0.03049623,-0.00337737,0.00274998,-0.0483622,0.06799784,-0.02053085,-0.00276595,0.02423183,0.00832238,0.00831262,0.0571948,-0.01006304,0.00152918,-0.01224016,0.0146807,0.00419864,0.04199144,-0.06137561,0.00855142,0.01425976,0.05070343,0.03283959,0.00187169,-0.00331891,-0.0413302,-0.01580087,-0.08062554,0.04836047,-0.04563453,0.01150235,0.03275998,-0.02062572,0.02560561,0.00525408,0.02157429,0.0454945,0.04560015,-0.03744294,-0.00313463,-0.01350746,-0.00922074,0.0053948,0.03340163,-0.00166413,-0.06409562,-0.02244582,0.03370463,0.04659387,-0.0400959,0.01674599,-0.01530808,0.03149413,-0.02951856,0.0327161,-0.03566166,0.02385761,0.00029454,-0.00229159,0.0428814,-0.03074516,0.0437169,0.00532317,0.06077269,-0.03219259,0.00168606,0.01071122,0.05212338,-0.01879225,0.04460159,-0.01087955,-0.06433431,9e-7,0.03611455,-0.01117641,-0.01118125,-0.02642377,-0.00314386,-0.00237155,-0.00734691,0.02585956,0.02767682],"last_embed":{"hash":"11aqebv","tokens":537}}},"last_read":{"hash":"11aqebv","at":1752940888165},"class_name":"SmartSource","last_import":{"mtime":1731396396534,"size":1845,"at":1749002741368,"hash":"11aqebv"},"blocks":{"#---frontmatter---":[1,10],"#简介":[12,21],"#简介#{1}":[13,13],"#简介#{2}":[14,16],"#简介#{3}":[17,21],"#请求伪造":[22,42],"#请求伪造#[[Burp Suite]]替换规则":[24,42],"#请求伪造#[[Burp Suite]]替换规则#{1}":[25,25],"#请求伪造#[[Burp Suite]]替换规则#{2}":[26,27],"#请求伪造#[[Burp Suite]]替换规则#{3}":[28,28],"#请求伪造#[[Burp Suite]]替换规则#{4}":[29,30],"#请求伪造#[[Burp Suite]]替换规则#{5}":[31,36],"#请求伪造#[[Burp Suite]]替换规则#{6}":[34,36],"#请求伪造#[[Burp Suite]]替换规则#{7}":[37,37],"#请求伪造#[[Burp Suite]]替换规则#{8}":[38,38],"#请求伪造#[[Burp Suite]]替换规则#{9}":[39,39],"#请求伪造#[[Burp Suite]]替换规则#{10}":[40,40],"#请求伪造#[[Burp Suite]]替换规则#{11}":[41,42]},"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":7},{"title":"Burp Suite","target":"Burp Suite","line":9},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":13},{"title":"IP协议","target":"IP协议","line":14},{"title":"Burp Suite","target":"Burp Suite","line":24},{"title":"780","target":"Pasted image 20240402154555.png","line":25},{"title":"curl","target":"curl","line":26},{"title":"Me-and-My-Girlfriend","target":"Me-and-My-Girlfriend","line":26},{"title":"x-forwarded-for","target":"x-forwarded-for","line":26},{"title":"Pasted image 20240402155255.png","target":"Pasted image 20240402155255.png","line":28},{"title":"curl","target":"curl","line":30},{"title":"x-forwarded-for","target":"x-forwarded-for","line":30},{"title":"curl","target":"curl","line":31},{"title":"cookies","target":"cookies","line":32},{"title":"curl","target":"curl","line":33},{"title":"Me-and-My-Girlfriend","target":"Me-and-My-Girlfriend","line":33},{"title":"x-forwarded-for","target":"x-forwarded-for","line":38},{"title":"cookies","target":"cookies","line":39},{"title":"Burp Suite","target":"Burp Suite","line":41},{"title":"curl","target":"curl","line":41},{"title":"x-forwarded-for","target":"x-forwarded-for","line":41},{"title":"Burp Suite","target":"Burp Suite","line":42},{"title":"cookies","target":"cookies","line":42}],"metadata":{"tags":["互联网/浏览器/HTTP"],"aliases":["XFF"],"基础知识":["[[HTTP(S)协议]]"],"扩展知识":["[[Burp Suite]]"]},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/x-forwarded-for.md","last_embed":{"hash":"11aqebv","at":1752940888165}},