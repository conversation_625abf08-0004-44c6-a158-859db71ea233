{"version": "0.7.52", "endpoint": "https://api.deepseek.com/v1", "models": [], "api_key": "", "encrypt_keys": false, "selectedProvider": "OpenAI Chat (Langchain)", "max_tokens": 500, "temperature": 0.7, "frequency_penalty": 0.5, "showStatusBar": true, "outputToBlockQuote": false, "freeCursorOnStreaming": false, "allowJavascriptRun": true, "experiment": false, "promptsPath": "textgenerator/templates", "textGenPath": "textgenerator/", "prefix": "\n\n", "tgSelectionLimiter": "^\\*\\*\\*", "stream": true, "context": {"customInstructEnabled": true, "includeClipboard": true, "customInstruct": "Title: {{title}}\n  \nStarred Blocks: {{starredBlocks}}\n\t  \n{{tg_selection}}", "contextTemplate": "Title: {{title}}\n\t\nStarred Blocks: {{starredBlocks}}\n\t  \n{{tg_selection}}\n\n"}, "requestTimeout": 300000, "options": {"generate-text": true, "generate-text-with-metadata": true, "insert-generated-text-From-template": true, "create-generated-text-From-template": false, "search-results-batch-generate-from-template": true, "insert-text-From-template": false, "create-text-From-template": false, "show-modal-From-template": true, "open-template-as-tool": true, "open-playground": true, "set_max_tokens": true, "set-llm": true, "set-model": true, "packageManager": true, "create-template": false, "get-title": true, "generated-text-to-clipboard-From-template": false, "calculate-tokens": true, "calculate-tokens-for-template": true, "text-extractor-tool": true, "stop-stream": true, "custom-instruct": true, "generate-in-right-click-menu": false, "batch-generate-in-right-click-files-menu": true, "tg-block-processor": true, "reload": true, "disable-ribbon-icons": false}, "advancedOptions": {"generateTitleInstructEnabled": false, "generateTitleInstruct": "Generate a title for the current document (do not use * \" \\ / < > : | ? .):\n{{substring content 0 255}}", "includeAttachmentsInRequest": false}, "autoSuggestOptions": {"customInstructEnabled": true, "customInstruct": "Continue the follwing text:\nTitle: {{title}}\n{{query}}", "systemPrompt": "", "isEnabled": false, "allowInNewLine": false, "delay": 300, "numberOfSuggestions": 5, "triggerPhrase": "  ", "stop": ".", "showStatus": true, "customProvider": false, "inlineSuggestions": false, "overrideTrigger": " "}, "slashSuggestOptions": {"isEnabled": false, "triggerPhrase": "/"}, "extractorsOptions": {"PDFExtractor": true, "WebPageExtractor": true, "YoutubeExtractor": true, "AudioExtractor": false, "ImageExtractorEmbded": true, "ImageExtractor": true}, "displayErrorInEditor": false, "LLMProviderProfiles": {"OpenAI Chat (Langchain) 1": {"extends": "OpenAI Chat (Langchain)", "name": "OpenAI Chat 1"}, "OpenAI Instruct (Langchain) 1": {"extends": "OpenAI Instruct (Langchain)", "name": "Deepseek"}}, "LLMProviderOptions": {"whisper": {"base_path": "https://api.openai.com/v1", "model": "whisper-1", "api_key": "", "basePath": "https://api.gptsapi.net/v1"}, "OpenAI Chat (Langchain)": {"basePath": "https://api.gptsapi.net/v1", "api_key": "", "model": "gpt-4o"}, "OpenAI Instruct (Langchain)": {"basePath": "https://api.openai.com/v1", "model": "deepseek chat", "api_key": ""}, "OpenAI Chat (Langchain) 1": {"basePath": "https://api.gptsapi.net/v1", "api_key": "", "model": "gpt-4o"}, "OpenAI Agent (Langchain)": {"basePath": "https://api.openai.com/v1", "model": "o1-mini"}, "Huggingface (Langchain)": {}, "Chat Anthropic (Langchain)": {"basePath": "https://api.anthropic.com/", "model": "claude-3-5-sonnet-latest"}, "OpenAI Instruct (Langchain) 1": {"basePath": "https://api.deepseek.com/v1", "model": "ada", "api_key": ""}}, "LLMProviderOptionsKeysHashed": {"whisper.api_key": "__@#key_prefix#@__", "OpenAI Chat (Langchain).api_key": "__@#key_prefix#@__sk-8xR56d4a7f5997a23fe78da67b9118ccb73bb2eb509iEhBV", "OpenAI Instruct (Langchain).api_key": "__@#key_prefix#@__sk-Yp43999dc648b76e5cc50d8f7dfb1280add7006e6abeFTQc", "OpenAI Chat (Langchain) 1.api_key": "__@#key_prefix#@__", "OpenAI Instruct (Langchain) 1.api_key": "__@#key_prefix#@__sk-150aad65a81a41c28a1b7cdcb2be1403"}, "api_key_encrypted": "__@#key_prefix#@__sk-150aad65a81a41c28a1b7cdcb2be1403"}