/* 網格視圖容器 Grid View Container */
.ge-grid-view-container {
    padding: 0px;
    height: 100%;
}

/* --------------------------------------------------------------------------- */

/* 網格容器 Grid Container */
.ge-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(var(--grid-item-width, 300px), 1fr));
    gap: 12px;
    padding: 12px !important;
    align-items: start;
    align-content: start;
    background: var(--background-secondary) !important;
    flex: 1;
    overflow-y: auto;
}

/* 行動裝置隱藏滾動條 Mobile hidden Scrollbar */
.is-mobile .ge-grid-container::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
}

/* 網格項目 Grid Item */
.ge-grid-item {
    background-color: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: var(--button-radius);
    padding: 10px;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    gap: 14px;
    height: var(--grid-item-height);
}

.ge-grid-item:hover {
    transform: translateY(-2px);
    background-color: var(--text-selection) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ge-grid-item:hover p {
    color: var(--text-muted);
}

/* 摘要區域 Content Area */
.ge-content-area {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

/* 標題容器 Title Container */
.ge-title-container {
    display: flex;
    align-items: center;
    gap: 0px;
    width: 100%;
}

/* 標題圖示 Icon Container */
.ge-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    color: var(--text-muted);
}

/* 圖片圖示 Image Icon */
.ge-icon-container.ge-img {
    color: var(--color-blue);
}

/* 影片圖示 Video Icon */
.ge-icon-container.ge-video {
    color: var(--color-red);
}

/* 音樂圖示 Audio Icon */
.ge-icon-container.ge-audio {
    color: var(--color-purple);
}

/* PDF圖示 PDF Icon */
.ge-icon-container.ge-pdf {
    color: var(--color-orange);
}

/* Canvas圖示 Canvas Icon */
.ge-icon-container.ge-canvas {
    color: var(--color-green);
}

/* 基礎圖示 Base Icon */
.ge-icon-container.ge-base {
    color: var(--color-cyan);
}

/* 標題 Title */
.ge-title {
    margin: 0 0 0 3px;
    font-size: var(--title-font-size);
    color: var(--text-normal);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    width: 100%;
}

/* 多行標題支援 Multiline Title */
.ge-multiline-title {
    white-space: normal !important;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 摘要p Content Area p */
.ge-content-area p {
    margin: 5px 0 0 0;
    color: var(--text-faint);
    font-size: 0.85em;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    height: 100%;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
}

.ge-content-area p:empty {
    margin: 0;
}

.ge-content-area-p-field,
.ge-content-area .ge-content-area-p-field {
    display: block;
    -webkit-line-clamp: unset !important;
    line-clamp: unset !important;
    -webkit-box-orient: unset !important;
    white-space: pre-wrap;
    overflow: visible;
    height: auto;
}

/* 圖片區域 Image Area */
.ge-image-area {
    width: var(--image-area-width);
    height: var(--image-area-height);
    flex-shrink: 0;
}

.ge-image-area img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.ge-image-area video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

/* 直向卡片容器 Vertical Card Container */
.ge-vertical-card .ge-grid-container {
    /* 使用既有的欄寬變數，僅改 gap 與 padding 以符合卡片視覺 */
    gap: 16px;
    padding: 16px !important;
}

/* 直向卡片 Vertical Card Item */
.ge-vertical-card .ge-grid-item {
    /* 垂直排列 */
    flex-direction: column;
    align-items: stretch;
    height: var(--grid-item-height); /* 與一般網格一致，保持同排等高 */
    gap: 0px;
    padding: 0px !important;
}

/* 摘要文字 Content Area */
.ge-vertical-card .ge-content-area {
    order: 1;
    flex: 1 1 auto; /* 使摘要區撐開，標籤可貼底 */
    flex-grow: 1; /* Add this line */
    padding: 10px !important;
}

/* 圖片置中，滿版顯示 Image Area */
.ge-vertical-card .ge-image-area {
    order: 3;
    width: 100%;
    height: var(--image-area-height);  
}

/* 標籤列置底 Tags Container */
.ge-vertical-card .ge-tags-container {
    order: 4;
    margin-top: 6px;
}

/* 按設定切換直向卡片圖片位置 */
.ge-vertical-card.ge-image-top .ge-image-area {
    order: 1;
}

.ge-vertical-card.ge-image-top .ge-content-area {
    order: 3;
}

.ge-vertical-card.ge-image-top .ge-image-area img,
.ge-vertical-card.ge-image-top .ge-image-area video {
    border-radius: calc(var(--button-radius) - var(--border-width)) calc(var(--button-radius) - var(--border-width)) 0px 0px !important;
} 

/* 預設 bottom 可維持原有順序，以下為明確設置 */
.ge-vertical-card.ge-image-bottom .ge-image-area {
    order: 3;
}

.ge-vertical-card.ge-image-bottom .ge-content-area {
    order: 1;
}

.ge-vertical-card.ge-image-bottom .ge-image-area img,
.ge-vertical-card.ge-image-bottom .ge-image-area video {
    border-radius: 0px 0px calc(var(--button-radius) - var(--border-width)) calc(var(--button-radius) - var(--border-width)) !important;
} 

/* 鼠標懸停效果：整張卡片微抬升 */
.ge-vertical-card .ge-grid-item:hover {
    transform: translateY(-2px);
}

/* 僅圖片/影片的直向卡片 Media Card */
.ge-vertical-card .ge-media-card {
    padding: 0; /* 移除內距，讓媒體填滿 */
    border: none;
    gap: 0;
    height: var(--grid-item-height);
}

.ge-vertical-card .ge-media-card .ge-image-area {
    order: 1;
    width: 100%;
    height: var(--image-area-height);
    flex: 1 1 auto;
}

.ge-vertical-card .ge-media-card .ge-image-area img,
.ge-vertical-card .ge-media-card .ge-image-area video {
    border-radius: 4px !important;
}

.ge-vertical-card .ge-media-card .ge-content-area,
.ge-vertical-card .ge-media-card .ge-tags-container {
    display: none !important; /* 隱藏標題、摘要、標籤 */
}

/* 標籤容器 Tags Container */
.ge-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
    margin-bottom: 2px;
}

/* 標籤樣式 Tag Style */
.ge-tag {
    display: inline-flex;
    align-items: center;
    padding: 0 6px;
    font-size: 0.8em;
    border: 1px solid var(--background-modifier-border);
    border-radius: 5px;
    background-color: var(--background-secondary-alt);
    color: var(--text-accent);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 影片縮圖樣式 */
.ge-video-thumbnail {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background-secondary);
    border-radius: 4px;
    border: 1px solid var(--background-modifier-border);
}

.ge-video-thumbnail svg {
    width: 40px;
    height: 40px;
    color: var(--text-accent);
}

/* 選中項目的樣式 Selected Item */
.ge-selected-item {
    outline: 1px solid var(--vault-profile-color-hover) !important;
    outline-offset: -1px;
    position: relative;
    z-index: 1;
}

/* 拖曳相關樣式 Dragging */
.ge-grid-item.ge-dragging {
    opacity: 0.5;
    transform: scale(0.95);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.ge-folder-item.ge-dragover {
    border: 2px dashed var(--interactive-accent);
    transform: scale(1.05);
}

/* --------------------------------------------------------------------------- */

/* 資料夾項目 Folder Item */
.ge-grid-item.ge-folder-item {
    background-color: var(--background-primary-alt);
    border: 2px solid var(--background-modifier-border);
    padding: 7px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    height: 100%;
}

.ge-grid-item.ge-folder-item:hover {
    background-color: var(--background-modifier-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 直向卡片模式下的資料夾項目補正 */
.ge-vertical-card .ge-folder-item .ge-content-area {
    padding: 7px !important;
}

.ge-grid-item.ge-folder-item .ge-icon-container {
    color: var(--interactive-accent);
}

.ge-grid-item.ge-folder-item .ge-title {
    color: var(--text-normal);
}

.ge-grid-item.ge-folder-item .ge-content-area {
    min-height: 0px; /* 設定最小高度 */
    justify-content: center;
}

.ge-grid-item.ge-folder-item .ge-title-container {
    align-items: center;
}

/* 資料夾和筆記顏色 Folder & Note Colors */
.ge-grid-item.ge-folder-item.ge-folder-color-red,
.ge-grid-item.ge-note-color-red { 
    background-color: rgba(var(--color-red-rgb), 0.2); 
    border-color: rgba(var(--color-red-rgb), 0.5);
}
.ge-grid-item.ge-folder-item.ge-folder-color-orange,
.ge-grid-item.ge-note-color-orange { 
    background-color: rgba(var(--color-orange-rgb), 0.2); 
    border-color: rgba(var(--color-orange-rgb), 0.5); 
}
.ge-grid-item.ge-folder-item.ge-folder-color-yellow,
.ge-grid-item.ge-note-color-yellow { 
    background-color: rgba(var(--color-yellow-rgb), 0.2); 
    border-color: rgba(var(--color-yellow-rgb), 0.5); 
}
.ge-grid-item.ge-folder-item.ge-folder-color-green,
.ge-grid-item.ge-note-color-green { 
    background-color: rgba(var(--color-green-rgb), 0.2); 
    border-color: rgba(var(--color-green-rgb), 0.5); 
}
.ge-grid-item.ge-folder-item.ge-folder-color-cyan,
.ge-grid-item.ge-note-color-cyan { 
    background-color: rgba(var(--color-cyan-rgb), 0.2); 
    border-color: rgba(var(--color-cyan-rgb), 0.5); 
}
.ge-grid-item.ge-folder-item.ge-folder-color-blue,
.ge-grid-item.ge-note-color-blue { 
    background-color: rgba(var(--color-blue-rgb), 0.2); 
    border-color: rgba(var(--color-blue-rgb), 0.5); 
}
.ge-grid-item.ge-folder-item.ge-folder-color-purple,
.ge-grid-item.ge-note-color-purple { 
    background-color: rgba(var(--color-purple-rgb), 0.2); 
    border-color: rgba(var(--color-purple-rgb), 0.5); 
}
.ge-grid-item.ge-folder-item.ge-folder-color-pink,
.ge-grid-item.ge-note-color-pink { 
    background-color: rgba(var(--color-pink-rgb), 0.2); 
    border-color: rgba(var(--color-pink-rgb), 0.5); 
}

/* 筆記文字顏色 Note Text Color */
.ge-note-color-red-text { 
    color: rgba(var(--color-red-rgb), 0.7) !important;
}
.ge-note-color-orange-text { 
    color: rgba(var(--color-orange-rgb), 0.7) !important;
}
.ge-note-color-yellow-text { 
    color: rgba(var(--color-yellow-rgb), 0.7) !important;
}
.ge-note-color-green-text { 
    color: rgba(var(--color-green-rgb), 0.7) !important;
}
.ge-note-color-cyan-text { 
    color: rgba(var(--color-cyan-rgb), 0.7) !important;
}
.ge-note-color-blue-text { 
    color: rgba(var(--color-blue-rgb), 0.7) !important;
}
.ge-note-color-purple-text { 
    color: rgba(var(--color-purple-rgb), 0.7) !important;
}
.ge-note-color-pink-text { 
    color: rgba(var(--color-pink-rgb), 0.7) !important;
}

/* 資料夾上的資料夾筆記按鈕 Folder Note Button */
.ge-foldernote-button {
    color: var(--text-muted);
    cursor: pointer;
    margin-left: 8px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
    background: none;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
}

.ge-foldernote-button:hover {
    opacity: 1;
    color: var(--text-accent);
}

.ge-foldernote-button svg {
    width: 18px;
    height: 18px;
}

/* --------------------------------------------------------------------------- */

/* 分隔線 Break */
.ge-break {
    grid-column: 1 / -1;
    height: 0;
    margin: 0;
    padding: 0;
    border-top: 1px solid var(--background-modifier-border);
    border-bottom: 1px solid var(--background-primary);
}

/* 日期分隔器樣式 */
.ge-date-divider {
    width: 100%;
    text-align: left;
    color: var(--text-normal);
    margin-left: 8px;
    grid-column: 1 / -1;
    pointer-events: none;
    user-select: none;
    margin-top: 4px;
    margin-bottom: -2px;
}

/* 置頂分隔器圖示 
.ge-date-divider::before {
    content: "📆";
    margin-right: 6px;
    margin-left: 2px;
} */

/* Pin分隔器樣式 */
.ge-pin-divider {
    width: 100%;
    text-align: left;
    color: var(--text-normal);
    margin-left: 8px;
    grid-column: 1 / -1;
    pointer-events: none;
    user-select: none;
    margin-top: -1px;
    margin-bottom: -2px;
}

/* 置頂分隔器圖示 */
.ge-pin-divider::before {
    content: "📌";
    margin-right: 6px;
    margin-left: 2px;
}

/* 搜尋載入中 Loading Indicator */
.ge-loading-indicator {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    font-size: 1.2em;
    color: var(--text-muted);
}

/* 沒有找到文件 No Files Found */
.ge-no-files {
    grid-column: 1 / -1;
    text-align: center;
    padding: 2em;
    color: var(--text-muted);
    font-size: 1.2em;
}

/* --------------------------------------------------------------------------- */

/* 頂部按鈕區域樣式 Header Buttons */
.ge-header-buttons {
    display: flex;
    gap: 8px;
    padding: 10px;
    background: var(--background-primary);
    border-bottom: 1px solid var(--background-modifier-border);
    flex-shrink: 0;
    justify-content: center;
}

.ge-header-buttons button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background-color: var(--interactive-normal);
    border-radius: var(--button-radius);
    color: var(--text-normal);
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
}

.is-tablet .ge-header-buttons button:not(.clickable-icon) {
    padding: 6px 12px;
}

.ge-header-buttons button:hover {
    background-color: var(--interactive-hover);
    transform: translateY(-1px);
}

.ge-header-buttons button:active {
    transform: translateY(0);
}

.ge-header-buttons .sort-button,
.ge-header-buttons .reselect-button,
.ge-header-buttons .refresh-button,
.ge-header-buttons .up-button {
    display: inline-flex;
    align-items: center;
}

/* --------------------------------------------------------------------------- */

/* 模式名稱容器 */
.ge-mode-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--background-secondary);
    border-bottom: 1px solid var(--background-modifier-border);
}

/* 模式名稱區域 */
.ge-modename-content {
    flex: 1;
    padding: 7px 12px;
    font-size: var(--font-ui-medium);
    display: flex;
    align-items: center;
    overflow: hidden;
}

/* 右側操作按鈕區域 */
.ge-right-actions {
    display: flex;
    align-items: center;
    padding-right: 8px;
}

/* 排序按鈕樣式 */
.ge-sort-button {
    background: none;
    border: none;
    color: var(--text-muted);
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease;
}

.ge-sort-button:hover {
    opacity: 1;
    background-color: var(--background-modifier-hover);
}

.ge-sort-button svg {
    width: 16px;
    height: 16px;
}

.ge-mode-title {
    text-decoration: none;
    padding: 3px 8px;
    white-space: nowrap;
    overflow: hidden;
    color: var(--text-normal);
    border: 1px solid var(--background-secondary) !important;
    font-size: var(--font-ui-small);
    display: inline-flex;
    align-items: center;
    border: none;
    margin-right: 6px;
}

a.ge-mode-title {
    border: 1px solid var(--background-modifier-border) !important;
    border-radius: 4px;
    cursor: pointer;
}

a.ge-mode-title:hover {
    text-decoration: none;
    background-color: var(--background-modifier-hover);
}

.ge-sub-option {
    text-decoration: none;
    padding: 3px 8px;
    white-space: nowrap;
    overflow: hidden;
    max-width: 200px;
    color: var(--text-normal);
    font-size: var(--font-ui-small);
    display: inline-flex;
    align-items: center;
    border: none;
    border-radius: 4px;
    border: 1px solid var(--background-modifier-hover);
    cursor: pointer;
}

.ge-sub-option:hover {
    border-radius: 4px;
    background-color: var(--background-modifier-hover);
    text-decoration: none;
}

/* 路徑容器樣式 */
.ge-path-container {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow: hidden;
    white-space: nowrap;
}

/* 上層資料夾連結樣式 Parent Folder Link */
.ge-parent-folder-link {
    text-decoration: none;
    padding: 3px 8px;
    white-space: nowrap;
    overflow: hidden;
    max-width: 200px;
    border-radius: 4px;
    border: 1px solid var(--background-modifier-hover);
    color: var(--text-normal);
    font-size: var(--font-ui-small);
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    margin-right: 6px;
}

.ge-parent-folder-link:hover {
    text-decoration: none;
    background-color: var(--background-modifier-hover);
}

.ge-parent-folder-link.ge-dragover {
    border: 1px dashed var(--interactive-accent);
    background-color: var(--background-modifier-hover);
}

/* 當前資料夾樣式 */
.ge-current-folder {
    font-size: var(--font-ui-small);
    padding: 4px 9px 4px 0px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    color: var(--text-normal);
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
}

a.ge-current-folder {
    text-decoration: none;
    padding: 3px 8px !important;
    border: 1px solid var(--background-modifier-hover);
}

a.ge-current-folder:hover {
    text-decoration: none;
    background-color: var(--background-modifier-hover);
}

/* --------------------------------------------------------------------------- */

/* 搜尋對話框樣式 Search Container */
.ge-search-container {
    margin-bottom: 8px;
    position: relative;
    display: flex;
    align-items: center;
}

.ge-search-input-wrapper {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.ge-input-container {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
}

.ge-search-clear-button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    color: var(--text-muted);
    cursor: pointer;
    z-index: 10;
}

.ge-search-clear-button:hover {
    color: var(--text-accent);
}

.ge-search-clear-button svg {
    width: 16px;
    height: 16px;
}

/* 標籤顯示區域 Search Tag Display Area */
.ge-search-tag-display-area {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding: 5px;
    margin-bottom: 10px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-primary);
    align-items: center;
}

.ge-search-tag-button {
    display: inline-flex;
    align-items: center;
    padding: 3px 7px;
    background-color: var(--background-modifier-active-hover);
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    padding-right: 24px;
    height: 24px;
    border: none;
    font-size: var(--font-ui-small);
}

.ge-search-tag-delete-button {
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: transparent;
    cursor: pointer;
    transition: background-color 0.2s;
    opacity: 0.8;
}

.ge-search-tag-delete-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    opacity: 1;
}

.ge-search-tag-delete-button svg {
    width: 22px;
    height: 22px;
}

/* 標籤自動完成下拉選單 Search Tag Suggestions */
.ge-search-tag-suggestions {
    position: relative;
    margin-top: 4px;
    background-color: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    max-height: 150px;
    overflow-y: auto;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    font-size: var(--font-ui-small);
}

.ge-search-tag-suggestion-item {
    padding: 4px 8px;
    cursor: pointer;
    white-space: nowrap;
}

.ge-search-tag-suggestion-item:hover,
.ge-search-tag-suggestion-item.is-selected {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent, white);
}

.ge-search-container input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-primary);
    color: var(--text-normal);
    font-size: var(--font-ui-medium);
    outline: none;
    transition: border-color 0.2s;
    padding-right: 25px;
}

.ge-search-container input:focus {
    border-color: var(--interactive-accent);
}

.ge-search-container input::placeholder {
    color: var(--text-muted);
}

/* 搜尋選項容器 Search Options Container */
.ge-search-options-container {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin: 4px 0;
}

/* 搜尋範圍設定樣式 Search Scope Container */
.ge-search-scope-container {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.ge-search-scope-checkbox {
    cursor: pointer;
}

.ge-search-scope-label {
    cursor: pointer;
    user-select: none;
    color: var(--text-normal);
    font-size: var(--font-ui-small);
}

/* 搜尋媒體檔案設定樣式 Search Media Files Container */
.ge-search-media-files-container {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.ge-search-media-files-checkbox {
    cursor: pointer;
}

.ge-search-media-files-label {
    cursor: pointer;
    user-select: none;
    color: var(--text-normal);
    font-size: var(--font-ui-small);
}

.ge-button-container {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* 搜尋視窗內的按鈕容器 Search Modal Button Container */
.ge-button-container button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
}

.ge-button-container button:first-child {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}

.ge-button-container button:last-child {
    background-color: var(--interactive-normal);
    color: var(--text-normal);
}

.ge-button-container button:hover {
    transform: translateY(-1px);
}

.ge-button-container button:first-child:hover {
    background-color: var(--interactive-accent-hover);
}

.ge-button-container button:last-child:hover {
    background-color: var(--interactive-hover);
}

.ge-button-container button:active {
    transform: translateY(0);
}

/* 搜尋視窗內的清除按鈕 Search Modal Clear Button */
.ge-search-clear-button {
    color: var(--text-muted);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    position: absolute;
    right: 5px;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    padding: 3px;
}

.ge-search-clear-button:hover {
    color: var(--text-normal);
    background-color: var(--background-modifier-hover);
}

/* 頂部欄搜尋按鈕容器 Header Search Button Container */
.ge-search-button-container {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--background-primary);
    border-radius: 4px;
    padding: 0;
}

/* 搜尋按鈕啟用狀態 Header Search Button Active */
.ge-search-button-container button.active {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}

/* 搜尋文字容器 Header Search Text Container */
.ge-search-text-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-color: var(--background-modifier-active-hover);
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    transition: border-color 0.15s ease;
}

.ge-search-text-container:hover {
    border-color: var(--interactive-accent);
}

/* 搜尋文字 Header Search Text */
.ge-search-text {
    font-size: 14px;
    color: var(--text-normal);
    flex-grow: 1; /* 讓文字填滿剩餘空間 */
    min-width: 0; /* 防止内容溢出 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 8px;
}

/* 取消按鈕 Header Search Clear Button */
.ge-clear-button {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    color: var(--text-muted);
    font-size: 14px;
    margin-right:4px;
    flex-shrink: 0;
}

.ge-clear-button:hover {
    color: #FFF;
    background-color: var(--background-modifier-error-hover);
}

.ge-clear-button svg {
    width: 18px;
    height: 18px;
}

/* --------------------------------------------------------------------------- */

/* 資料夾選擇器的搜尋輸入框容器 */
.ge-folder-search-container {
    margin-bottom: 16px;
    padding: 0 4px;
}

/* 資料夾選擇器的搜尋輸入框 */
.ge-folder-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-primary);
    color: var(--text-normal);
    font-size: var(--font-ui-small);
}

.ge-folder-search-input:focus {
    border-color: var(--interactive-accent);
    box-shadow: 0 0 0 2px rgba(var(--interactive-accent-rgb), 0.2);
    outline: none;
}

/* 資料夾選擇器的項目容器 */
.ge-folder-options-container {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 4px;
}

/* 資料夾選擇器的項目樣式 */
.ge-grid-view-folder-option {
    cursor: pointer;
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
}

.ge-grid-view-folder-option:hover {
    background-color: var(--background-modifier-hover);
}

/* ascii tree prefix */
.ge-folder-tree-prefix {
    font-family: monospace;
    white-space: pre;
    color: var(--text-muted);
}

/* 鍵盤導航選中項的樣式 */
.ge-selected-option {
    background-color: var(--background-modifier-hover);
    border-left: 3px solid var(--interactive-accent) !important;
    padding-left: 5px !important;
}

/* --------------------------------------------------------------------------- */

/* Media Modal */
.ge-media-modal {
    padding: 0 !important;
    background-color: rgba(0, 0, 0, 0.9) !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    max-width: 100% !important;
    max-height: 100% !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
}

.ge-media-modal .modal-header {
    margin: 0 !important;
}

.ge-media-modal .modal-close-button {
    display: none !important;
}

.ge-media-modal-content {
    background-color: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 100%;
    height: 100%;
}

/* Media View */
.ge-media-view {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ge-media-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
}

.ge-fullscreen-image {
    max-width: 100vw;
    max-height: 100vh;
    object-fit: contain;
}

.ge-fullscreen-video {
    max-width: 100vw;
    max-height: 100vh;
}

.ge-media-container video {
    margin: 0 auto;
}

.ge-fullscreen-file-name {
    position: fixed;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1007;
    white-space: nowrap;
}

.ge-media-close-button {
    position: fixed;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1010;
}

.ge-media-close-button:hover {
    background-color: rgba(255, 0, 0, 0.7);
}

/* 左右切換區域 */
.ge-media-prev-area,
.ge-media-next-area {
    position: absolute;
    top: 15%;
    height: 70%;
    width: 10%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1005;
    cursor: pointer;
}

.ge-media-prev-area {
    left: 0;
}

.ge-media-next-area {
    right: 0;
}

/* --------------------------------------------------------------------------- */

/* 音樂播放器 Floating Audio Player */
.ge-floating-audio-player {
    position: fixed;
    width: 300px;
    background-color: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 1100;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: box-shadow 0.3s ease;
}

.ge-floating-audio-player:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.ge-audio-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-normal);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 24px;
    cursor: default;
    margin: 2px; 
}

.ge-floating-audio-player audio {
    width: 100%;
    outline: none;
}

.ge-audio-close-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: 50%;
    transition: background-color 0.2s ease, color 0.2s ease;
    z-index: 1102; 
}

.ge-audio-close-button:hover {
    background-color: var(--background-modifier-error-hover);
    color: white;
}

.ge-audio-handle {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 30px;
    cursor: move;
    border-radius: 8px 8px 0 0;
    z-index: 1101; 
}

.ge-audio-dragging {
    opacity: 0.9;
    user-select: none;
}

/* --------------------------------------------------------------------------- */

/* 設定描述區域樣式 Setting Description */
.ge-setting-desc {
    display: flex;
    align-items: center;
    gap: 10px;
}

.ge-setting-number-input {
    width: 60px;
}

/* 忽略資料夾設定 Ignored Folders Setting */
.ge-ignored-folders-container {
    margin-bottom: 16px;
}

/* 忽略資料夾列表 Ignored Folders List */
.ge-ignored-folders-list {
    list-style: none;
    padding: 0;
}

/* 忽略資料夾項目 Ignored Folder Item */
.ge-ignored-folder-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    margin-bottom: 4px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-primary);
}

/* 忽略資料夾路徑 Ignored Folder Path */
.ge-ignored-folder-path {
    flex-grow: 1;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 忽略資料夾刪除按鈕 Ignored Folder Remove */
.ge-ignored-folder-remove {
    background-color: var(--interactive-normal);
    color: var(--text-normal);
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.ge-ignored-folder-remove:hover {
    background-color: var(--interactive-hover);
}

/* 忽略資料夾模式的容器 Ignored Folder Patterns Container */
.ignored-folder-patterns-container {
    margin-bottom: 16px;
}

/* 忽略資料夾模式列表 Ignored Folder Patterns List */
.ge-ignored-folder-patterns-list {
    list-style: none;
    padding: 0;
}

/* 拖曳預覽 Drag Preview */
.ge-custom-drag-preview {
    position: absolute;
    top: -1000px; /* 隱藏在視窗外 */
    left: -1000px;
    font-size: var(--font-ui-small);
    white-space: nowrap;
    color: rgba(var(--mono-rgb-0), 0.9);
    background: rgba(var(--mono-rgb-100), 0.9);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    pointer-events: none;
    z-index: 1000;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Custom Mode Options */
.ge-custommode-option-container {
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    padding: 0 12px;
    margin-bottom: 12px;
    background-color: var(--background-secondary);
}

.ge-custommode-option-container .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
    width: 100%;
}

.ge-custommode-option-container .setting-item-control {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
}

/* --------------------------------------------------------------------------- */

/* Popup Modal Styles */
.ge-popup-modal {
    background: transparent !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    border: none !important;
}

.ge-popup-modal-reset {
    position: fixed;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    transform: none;
    max-width: 300px;
    width: auto;
    background-color: transparent;
    box-shadow: none;
    border: none;
    border-radius: 0;
    padding: 0;
}

.ge-popup-modal-reset .modal-close-button {
    display: none;
}

.ge-popup-modal-reset .modal-header {
    display: none;
}

.is-phone .ge-popup-modal-reset.modal {
    max-width: fit-content;
    max-height: fit-content;
}

.ge-popup-content {
    background-color: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    padding: 10px 8px;
    position: relative;
    max-height: 400px;
    overflow-y: auto;
    min-width: 250px;
    max-width: 300px;
}

/* Popup modal 中的資料夾選項樣式 */
.ge-popup-content .ge-grid-view-folder-option {
    margin: 2px 0;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: var(--font-ui-medium);
    line-height: 1.4;
}

.ge-popup-content .ge-grid-view-folder-option:hover {
    background-color: var(--background-modifier-hover);
}

.ge-popup-content .ge-grid-view-folder-option.selected {
    background-color: var(--background-modifier-active);
}

/* Popup modal 中的搜尋框樣式 */
.ge-popup-content .ge-folder-search-container {
    margin-bottom: 8px;
}

.ge-popup-content .ge-folder-search-input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-primary);
    color: var(--text-normal);
    font-size: var(--font-ui-medium);
}

.ge-popup-content .ge-folder-search-input:focus {
    outline: none;
    border-color: var(--interactive-accent);
    box-shadow: 0 0 0 2px rgba(var(--interactive-accent-rgb), 0.2);
}

.ge-popup-content .ge-folder-options-container {
    max-height: 300px;
    overflow-y: auto;
}

/* 搜尋 modal popup 樣式 */
.ge-popup-content .ge-search-container {
    margin-top: 0;
}

.ge-popup-content .ge-search-input-wrapper {
    position: relative;
}

.ge-popup-content .ge-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-primary);
    color: var(--text-normal);
    font-size: var(--font-ui-medium);
}

.ge-popup-content .ge-search-input:focus {
    outline: none;
    border-color: var(--interactive-accent);
    box-shadow: 0 0 0 2px rgba(var(--interactive-accent-rgb), 0.2);
}

.ge-popup-content .ge-search-clear-button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    color: var(--text-muted);
}

.ge-popup-content .ge-search-clear-button:hover {
    background-color: var(--background-modifier-hover);
    color: var(--text-normal);
}

.ge-popup-content .ge-search-tag-suggestions {
    margin-top: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.ge-popup-content .setting-item {
    border: none;
    padding: 8px 0;
}

.ge-popup-content .setting-item-name {
    font-size: var(--font-ui-medium);
    font-weight: 500;
}

/* --------------------------------------------------------------------------- */

/* 筆記檢視容器 Note View Container */
.ge-note-view-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background-primary);
    z-index: 100;
    overflow: auto;
}

.ge-note-close-button {
    position: fixed;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: var(--background-secondary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.2s ease;
    color: var(--text-normal);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0.5;
}

.ge-note-close-button:hover {
    background: var(--background-modifier-hover);
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    opacity: 1;
}

.ge-note-close-button:active {
    transform: scale(0.98);
}

.ge-note-edit-button {
    position: fixed;
    top: 65px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: var(--background-secondary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.2s ease;
    color: var(--text-normal);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0.5;
}

.ge-note-edit-button:hover {
    background: var(--background-modifier-hover);
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    opacity: 1;
}

.ge-note-edit-button:active {
    transform: scale(0.98);
}

.ge-note-content-container {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--file-margins);
    min-height: 100%;
}

.ge-note-title {
    font-size: var(--inline-title-size);
    font-weight: var(--inline-title-weight);
    line-height: var(--inline-title-line-height);
    font-style: var(--inline-title-style);
    font-variant: var(--inline-title-variant);
    font-family: var(--inline-title-font);
    letter-spacing: -0.015em;
    color: var(--inline-title-color);
}

.ge-note-content {
    line-height: 1.6;
    color: var(--text-normal);
    font-size: var(--font-text-size);
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    pointer-events: auto;
}

.ge-note-content h1,
.ge-note-content h2,
.ge-note-content h3,
.ge-note-content h4,
.ge-note-content h5,
.ge-note-content h6 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    color: var(--text-normal);
}

.ge-note-content pre {
    background: var(--background-secondary);
    border: 1px solid var(--background-modifier-border);
    border-radius: var(--radius-s);
    padding: 1em;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.ge-note-content code {
    background: var(--background-code);
    padding: 0.2em 0.4em;
    border-radius: var(--radius-s);
    font-family: var(--font-monospace);
    font-size: 90%;
    color: var(--text-code);
}
    
.ge-note-content a {
    color: var(--text-accent);
    text-decoration: none;
}

.ge-note-content a:hover {
    text-decoration: underline;
}

.ge-note-content blockquote {
    border-left: 3px solid var(--text-accent);
    margin: 1em 0;
    padding-left: 1em;
    color: var(--text-muted);
    background: var(--background-secondary);
    border-radius: 0 var(--radius-s) var(--radius-s) 0;
}

.ge-note-content ul,
.ge-note-content ol {
    margin: 1em 0;
    padding-left: 2em;
}

.ge-note-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
}

.ge-note-content th,
.ge-note-content td {
    border: 1px solid var(--background-modifier-border);
    padding: 0.5em;
    text-align: left;
}

.ge-note-content th {
    background: var(--background-secondary);
    font-weight: 600;
}

.ge-note-content hr {
    border: none;
    border-top: 1px solid;
    border-color: var(--horizontal-line);
}

/* 隱藏網格內容 */
.ge-grid-container.ge-hidden {
    display: none;
}

/* 行動裝置適配 Mobile adaptation */
@media (max-width: 768px) {
    .ge-note-title {
        font-size: 1.5em;
    }
}