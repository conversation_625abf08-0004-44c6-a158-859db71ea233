{"main": {"id": "5324373015726ba8", "type": "split", "children": [{"id": "4509724f8bf84da7", "type": "tabs", "children": [{"id": "6b5f653f6fe6b50b", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Master_Home🧬.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "Master_Home🧬"}}, {"id": "5d6e7800be286809", "type": "leaf", "state": {"type": "components-file-view", "state": {"file": "附件/影视数据.components"}, "icon": "gantt-chart", "title": "影视数据"}}, {"id": "54c7c0bd022f2c60", "type": "leaf", "state": {"type": "markdown", "state": {"file": "数据分析/青年旅社的居住体验.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "青年旅社的居住体验"}}, {"id": "233e45b5a3f79c21", "type": "leaf", "state": {"type": "components-file-view", "state": {"file": "CPS组件/健身知识数据库.components"}, "icon": "gantt-chart", "title": "健身知识数据库"}}, {"id": "b7094bc7b2d8a859", "type": "leaf", "state": {"type": "markdown", "state": {"file": "数据分析/ATP.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "ATP"}}, {"id": "9837988fe1ad51f9", "type": "leaf", "state": {"type": "markdown", "state": {"file": "数据分析/健身时间的选择.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "健身时间的选择"}}], "currentTab": 4}], "direction": "vertical"}, "left": {"id": "7c070e31e5cee322", "type": "split", "children": [{"id": "60299ce8bf32597b", "type": "tabs", "children": [{"id": "cb870ec1f56abf9c", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "592a64f4506227ad", "type": "leaf", "state": {"type": "search", "state": {"query": "甲亢", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "526031984897ebc4", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}, {"id": "d869e08963d6095f", "type": "leaf", "state": {"type": "calendar", "state": {}, "icon": "calendar-with-checkmark", "title": "Calendar"}}, {"id": "cf4ee81f9fec35b9", "type": "leaf", "state": {"type": "runjs-codelist-view", "state": {}, "icon": "js-dark-icon", "title": "RunJS Codelist"}}], "currentTab": 3}, {"id": "41f1e5231174c9d8", "type": "tabs", "children": [{"id": "000fa9b94c8ca7e1", "type": "leaf", "state": {"type": "chronology-calendar-view", "state": {}, "icon": "clock", "title": "Chronology"}}]}], "direction": "horizontal", "width": 387.5}, "right": {"id": "71db69ebb85d4c35", "type": "split", "children": [{"id": "bdc55f1aa67ee0ce", "type": "tabs", "dimension": 58.09716599190283, "children": [{"id": "2b772baaf2f43dc9", "type": "leaf", "state": {"type": "backlink", "state": {"file": "数据分析/合理化机制.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": false}, "icon": "links-coming-in", "title": "合理化机制 的反向链接列表"}}, {"id": "1b85f637bd661b19", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "数据分析/ATP.md", "linksCollapsed": false, "unlinkedCollapsed": false}, "icon": "links-going-out", "title": "ATP 的出链列表"}}, {"id": "2232ea2c4847df5d", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "4691db7677b0624f", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency", "showSearch": true, "searchQuery": "GDP"}, "icon": "lucide-archive", "title": "添加笔记属性"}}, {"id": "65b0566ebe83afe2", "type": "leaf", "state": {"type": "outline", "state": {"file": "National/Branch Pack/地理-国家-城市-民族/城市/成都相关/县城/大邑县相关/西岭雪山风景名胜区.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "西岭雪山风景名胜区 的大纲"}}, {"id": "74a89a0a0ea89332", "type": "leaf", "state": {"type": "smart-connections-view", "state": {}, "icon": "smart-connections", "title": "Smart Connections"}}, {"id": "8605187543444c65", "type": "leaf", "pinned": true, "state": {"type": "markdown", "state": {"file": "地理数据/笔记创建面板.md", "mode": "preview", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "pinned": true, "icon": "lucide-file", "title": "笔记创建面板"}}, {"id": "401aa37b441c57f8", "type": "leaf", "state": {"type": "infio-chat-view", "state": {}, "icon": "wand-sparkles", "title": "Infio chat"}}, {"id": "97850688bd9efdd9", "type": "leaf", "state": {"type": "comment-view", "state": {}, "icon": "highlighter", "title": "<PERSON><PERSON><PERSON>"}}], "currentTab": 1}, {"id": "f3d59534947e831f", "type": "tabs", "dimension": 41.90283400809717, "children": [{"id": "c26c7b43fcf8aa2c", "type": "leaf", "state": {"type": "quiet-outline", "state": {}, "icon": "lines-of-text", "title": "Quiet Outline"}}, {"id": "c16168380a0cc543", "type": "leaf", "state": {"type": "reminder-list", "state": {}, "icon": "clock", "title": "Reminders"}}, {"id": "c33cb4975429252e", "type": "leaf", "state": {"type": "file-properties", "state": {"file": "数据分析/ATP.md"}, "icon": "lucide-info", "title": "ATP的笔记属性"}}]}], "direction": "horizontal", "width": 547.5}, "left-ribbon": {"hiddenItems": {"switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "markdown-importer:打开 Markdown 格式转换器": false, "random-note:开始漫游笔记": false, "workspaces:管理工作区布局": false, "omnisearch:Omnisearch": false, "homepage:Open homepage": false, "infio-copilot:打开 Infio Copilot": false, "obsidian-leaflet-plugin:打开 Leaflet 地图": false, "smart-connections:Open: Smart Chat": false, "smart-connections:Open: View Smart Connections": false, "callout-manager:Insert Callout": false, "chronology:Open Chronology": false, "obsidian-day-planner:Open Timeline": false, "obsidian-day-planner:Open Multi-Day View": false, "obsidian-kanban:创建新看板": false, "media-extended:Open media": false, "obsidian-textgenerator-plugin:Generate Text!": false, "obsidian-textgenerator-plugin:Text Generator: Templates Packages Manager": false, "obsidian-weread-plugin:同步微信读书笔记": false, "meld-encrypt:New encrypted note": false, "meld-encrypt:Convert to or from an Encrypted note": false, "yearly-glance:打开年度概览": false, "runjs:RunJS": false, "quickadd:QuickAdd": false, "meld-encrypt:Encrypt Selection": false, "obsidian42-brat:BRAT": false, "meld-encrypt:Lock and Close all open encrypted notes": false, "meld-encrypt:Decrypt at Cursor": false, "templater-obsidian:Templater": false, "hi-note:HiNote": false, "obsidian-task-progress-bar:打开 Task Genius 视图": false, "breadcrumbs:Breadcrumbs Visualisation": false}}, "active": "b7094bc7b2d8a859", "lastOpenFiles": ["数据分析/热量的本质是什么.md", "CPS组件/健身知识数据库.components", "数据分析/健身时间的选择.md", "数据分析/青年旅社的居住体验.md", "附件/影视数据.components", "CPS组件/库布里克作品数据库.components", "aliyun-backup/分支仓库/生物科技/物质化学/人体物质/胰腺.md", "aliyun-backup/分支仓库/生物科技/物质化学/人体物质/胰岛素.md", "地理数据/基础设施/中国省份/深圳市.md", "aliyun-backup/分支仓库/生物科技/库欣综合征.md", "aliyun-backup/分支仓库/生物科技/物质化学/人体物质/皮质醇.md", "aliyun-backup/分支仓库/生物科技/化学-医学-物质/解剖学/上肢/肱二头肌.md", "数据分析/高位下拉.md", "豆瓣数据/movie/movie/全金属外壳.md", "Persona/Social network/斯坦利·库布里克.md", "豆瓣数据/movie/movie/闪灵.md", "豆瓣数据/movie/movie/奇爱博士.md", "aliyun-backup/分支仓库/生物科技/物质化学/未命名/自由基.md", "附件/p835946292.webp", "Master_Home🧬.md", "地理数据/笔记创建面板.md", "数据分析/辅酶Q10.md", "aliyun-backup/分支仓库/生物科技/营养学/肌酸.md", "地理数据/基础设施/广东省.md", "HowToCook-master/dishes/meat_dish/水煮牛肉/水煮牛肉.md", "Persona/Social network/彼得·蒂尔.md", "数据分析/双向情感障碍.md", "aliyun-backup/分支仓库/生物科技/心理学/嫉妒心理.md", "components/File/成都历任市长清单.md", "附件/Pasted image 20250721141326.png", "附件/Pasted image 20250721141302.png", "附件/Pasted image 20250721141130.png", "附件/Pasted image 20250721140957.png", "National/Branch Pack/地理-国家-城市-民族/附件/国家数据库.components", "CPS组件/兴趣点打卡数据库.components", "地理数据/基础设施/附件/成都区域数据库.components", "CPS组件/成都兴趣点数据库.components", "附件/Pasted image 20250720144857.png", "附件/Pasted image 20250720144838.png", "附件/Pasted image 20250720123207.png", "附件/Pasted image 20250720123150.png", "附件/Pasted image 20250720123129.png", "附件/人物信息管理面板.components", "CPS组件/云南省子单位数据库.components", "CPS组件/韩国电影数据库.components", "画布进阶/火线-第一季可视化(专案组线).canvas", "画布进阶/火线 - 第三季可视化(街头黑帮线).canvas", "画布进阶/火线第四季 - 街头线(斯坦菲尔德组织).canvas", "画布进阶/MCP协议逻辑可视化.canvas", "画布进阶/未命名 1.canvas", "画布进阶/关系网络搭建思路图.canvas", "画布进阶/睾酮合成可视化.canvas", "画布进阶/计算机科技树可视化.canvas", "画布进阶/男性的生理分析可视化.canvas", "画布进阶/火线 - 第三季可视化(社会实验线).canvas"]}