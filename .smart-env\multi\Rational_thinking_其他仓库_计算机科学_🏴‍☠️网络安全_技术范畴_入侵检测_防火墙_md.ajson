"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md","last_embed":{"hash":"xdzhdo","at":1752940642434},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09676993,0.00342223,0.01002813,-0.05537734,0.03993372,-0.01181047,-0.01358618,0.03836221,0.04479728,0.00103803,-0.00756134,-0.07001887,0.04939612,0.06271807,0.05211655,0.01646355,-0.02239446,0.01650263,-0.0000608,0.00640119,0.02706691,-0.03886275,0.00629717,-0.07448644,-0.0112937,0.0099165,0.02160849,-0.0299591,-0.01036196,-0.14904712,-0.00423826,0.05015796,0.02100559,0.0300232,-0.00982573,-0.06008773,0.02053769,0.05429094,-0.01093943,0.01899751,0.03395975,0.05290129,0.03616602,-0.02313843,0.01161135,-0.03206614,-0.01933757,-0.02480925,0.0024061,-0.08465358,-0.02652821,-0.05667157,-0.03068144,0.00613158,-0.0706519,0.02182915,0.00967243,0.02149354,0.0551048,0.01543025,-0.00269616,0.01660436,-0.2265424,0.04897317,0.0184147,-0.07313418,-0.01562234,0.00073559,-0.01224108,0.05735011,-0.05198009,0.02440626,-0.04167912,0.06139359,0.08508383,-0.00258385,-0.01338916,0.01369101,-0.04334306,-0.05251748,-0.01389999,0.03919076,-0.02993849,0.01499791,-0.01096609,0.06103918,0.02255203,-0.04330018,0.01568804,0.03366999,-0.00774602,-0.04900332,-0.01319285,0.03445582,-0.00401259,-0.05508543,0.01809819,0.06409186,-0.08733085,0.12216181,-0.05669429,-0.05639956,-0.00984808,-0.0364291,0.00889019,0.01259966,0.00677368,-0.03331196,-0.03721723,-0.01840758,-0.05483052,-0.02501736,0.04601074,-0.01835833,0.05329465,-0.03547836,0.03229344,-0.02986804,-0.01685752,-0.00443319,-0.00989578,0.00791161,0.04950446,-0.03093088,-0.01722685,-0.04263484,0.03589485,0.05892606,0.02163506,0.0548521,0.04690864,-0.02011531,-0.03278126,-0.01178365,-0.02064672,0.00924859,-0.03638081,0.03779003,-0.0134584,-0.04411453,0.01343138,-0.02963187,0.01923546,-0.09031345,-0.0512509,0.06164484,-0.09352977,-0.01047558,0.01917374,-0.03529939,0.02398398,0.02498084,0.00727702,-0.00207592,0.01260015,0.01000817,0.08151438,0.1732446,-0.01091641,-0.05161874,0.02266848,0.01441418,-0.08156393,0.14735663,-0.01425819,-0.07532831,-0.00956157,-0.01431653,-0.03767595,-0.05761618,-0.01382488,-0.00705299,-0.01028484,0.03883463,0.03454845,-0.01973144,0.01775977,-0.01529793,0.02733776,0.05092029,0.06616829,-0.03946154,-0.08819569,0.04960106,0.01713286,-0.10692751,-0.07753965,-0.04946731,-0.00368696,-0.04796619,-0.14112683,0.05488079,-0.01397945,0.01938768,-0.0791803,-0.05107927,0.06572785,-0.02019722,0.03048707,-0.06554532,0.1016966,0.00712085,-0.03726109,-0.01207569,-0.03767096,-0.01930559,0.02764124,0.00913407,0.02403145,-0.00271946,-0.01272029,0.01816216,-0.0186847,0.02393153,0.00660479,-0.01563804,0.03802134,0.05034038,0.01484065,0.08091626,-0.00239232,0.03053803,-0.0736065,-0.23664792,-0.04043268,0.02747042,-0.05104786,0.00161286,-0.02634219,0.03320171,0.01575598,0.06179662,0.10798547,0.10941794,0.05259676,-0.07913181,-0.03751763,0.0206888,0.00403568,0.01447239,-0.02274323,-0.04079486,0.03010013,0.04687517,0.04679796,-0.04354902,-0.00920012,0.02890915,-0.02179364,0.12417165,0.01956158,0.03835463,0.03199429,0.03910276,0.02406144,-0.01207869,-0.08322749,0.02421867,0.04912291,-0.00411124,-0.01881788,-0.00080214,-0.01269071,-0.00368075,0.04911518,-0.02843674,-0.09020152,-0.01377658,-0.03489415,-0.02259752,-0.00626144,-0.0009508,0.0087469,0.00727057,0.0208657,0.01533798,0.0466048,0.00734543,-0.02250711,-0.06003021,-0.01036205,-0.0462627,0.02178283,0.01706685,0.01963546,0.03715272,-0.01204407,0.03902952,0.01047903,-0.02163243,-0.00318532,-0.05930994,0.00566526,-0.05235314,0.16012302,0.01088575,-0.03934648,0.02659989,0.02137126,0.01513527,-0.02562028,0.01056343,-0.0028052,0.04775223,-0.00831005,0.06312543,0.01758436,-0.0128855,0.03274125,0.00683694,-0.03613026,0.07948697,-0.05399095,-0.07644559,0.00563527,-0.0117304,0.03860645,0.06612094,-0.0354823,-0.30305582,0.01045859,0.00452641,-0.02901549,0.05257628,0.02782845,0.04701664,0.00947067,-0.05150118,0.05645687,-0.01742904,0.04648213,-0.01661175,-0.02951866,-0.03678525,-0.06535299,0.05314755,-0.00173545,0.04884499,0.00104196,0.01105581,0.03386483,0.22137548,-0.02427072,0.07294348,-0.022072,-0.02695636,0.05595457,0.03537027,0.03950404,0.02023123,-0.0719264,0.0374419,-0.02137472,0.05294961,0.04024006,-0.03352473,0.00663672,0.01839599,0.01737595,-0.04957635,0.06496953,-0.06628405,0.05017838,0.06865676,0.02442073,-0.0432499,-0.0134827,0.00080675,0.00900275,0.00547556,0.0390077,-0.00239527,0.00738325,0.03016464,0.05624136,0.00869376,-0.03371188,-0.08169597,0.00008132,0.00931073,0.02797035,0.08553032,0.082385,0.07549562],"last_embed":{"hash":"6cd8f53265def0659df7308a1fb1e07cc8b1af71069039aa74d9a255571ef29d","tokens":440}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.008077,0.00746244,0.05801528,0.03134271,-0.0022506,0.0379574,0.09005065,-0.03215289,0.00219921,-0.03680732,0.01459826,0.03061069,-0.00568296,-0.07782453,0.00523839,-0.02188151,0.0615304,0.00254004,0.06708094,-0.04501338,-0.09820996,0.05883688,0.01106625,0.00421346,0.06725807,-0.04595078,-0.04681095,-0.09503086,0.0347654,0.0271242,0.08002685,-0.01419627,0.03705626,-0.00775208,0.02484509,0.06165636,0.00821092,-0.03464048,0.03944554,0.00806144,-0.0216148,0.05165402,-0.01137714,-0.0125314,-0.05141209,0.00749194,0.05488418,-0.09436177,-0.01180316,0.00109732,0.04145792,-0.01502201,0.03612779,0.00961408,0.03304767,0.02603528,-0.02184134,-0.04855681,-0.05575855,-0.02318113,-0.02487571,-0.05519316,0.02457066,-0.02316517,0.06273627,-0.00289028,0.01530801,-0.03929781,-0.06479168,0.00966223,-0.00689662,0.03258479,0.0267371,0.01857114,0.00607593,0.02444363,0.03577151,-0.03512982,-0.06023211,-0.0000218,-0.05669196,0.04967583,0.06452852,0.03824129,0.04619964,-0.02735432,0.08084396,-0.01084368,-0.03516376,0.02335377,0.02016426,-0.00855857,-0.06513072,-0.05374943,0.0484007,-0.0040033,0.00264093,0.00017468,-0.04091258,-0.02675228,-0.00825751,-0.00745661,0.00567489,0.00339761,0.00822594,0.00611831,-0.00334475,0.02040455,-0.0236377,-0.00227679,0.00697406,-0.00977402,-0.09107091,-0.04056317,-0.02435365,0.03038339,0.01428392,0.04272828,0.01160028,-0.02377933,0.00233209,0.03467967,-0.04796178,-0.01743429,-0.00716769,0.0369542,-0.00877506,0.02226541,-0.02431748,-0.0243623,-0.03316486,0.03063504,-0.05568873,-0.03921834,0.03208492,0.06429818,0.01648713,-0.06757497,-0.04555976,-0.04460947,0.06569984,-0.01849596,0.00137954,0.00021208,-0.03199895,0.03598012,0.02090565,-0.03582451,-0.00436324,0.00954936,0.03739168,0.07610437,-0.07092576,-0.00739572,-0.01859353,-0.02647433,0.01553259,0.02294062,0.03500045,-0.05140869,-0.00572797,0.07185663,-0.00208982,0.01638582,0.03249298,-0.0089933,-0.06534516,0.01620442,0.02724745,0.00397771,-0.0255211,0.01092373,-0.04287491,-0.03759781,0.02521293,-0.02227943,0.02160287,0.02865114,-0.05557689,0.03402522,0.02936726,-0.04135634,-0.01097526,-0.02085036,0.01507887,0.03112076,0.00953239,-0.00542577,0.02869218,0.02709344,0.02788151,-0.00342806,-0.01778854,0.00794813,-0.0057195,0.08756258,-0.03064531,-0.05786736,0.00245673,0.01920566,-0.04146791,-0.04027051,-0.02484206,-0.08100476,0.06427806,0.00667558,-0.08360992,-0.01860351,-0.03457663,0.03367183,0.0198481,0.05848287,0.02092604,-0.00441136,-0.03265474,-0.00325126,0.00833966,0.02619123,-0.03306775,-0.05833017,0.03731322,-0.00292431,-0.03688341,-0.0498123,0.04414468,-0.05240972,-0.006279,0.01553808,0.02032497,-0.03334782,0.03236256,0.0715824,-0.00519354,-0.03407951,-0.05847771,0.01303957,-0.01681449,0.00183923,0.06813264,0.00621236,-0.05377946,-0.0378833,-0.00903869,0.05005683,-0.0032258,-0.00841447,-0.04377914,-0.0042421,0.05300399,0.01789238,0.04749355,-0.00855236,-0.01006884,0.06970819,-0.00409989,0.01122377,-0.00067377,0.01876364,-0.0084388,-0.03191163,0.0415732,-0.03870802,-0.02922394,0.03321005,-0.07771631,-0.02130298,-0.01008954,0.01785672,0.00234898,-0.00448764,0.04110734,-0.01596515,0.00380265,0.02027011,0.02099144,0.00691826,0.00640131,0.03255135,-0.02478336,-0.0322683,0.00780762,-0.01259546,0.05386068,-0.03795926,0.01535098,-0.00830227,-0.0153305,-0.04309428,-0.01504418,0.04264145,0.02703171,-0.02283863,-0.04977142,-0.00851399,-0.0548265,-0.03636784,0.02879123,-0.00500434,0.03044467,-0.06560081,-0.01199458,0.03044297,-0.02152232,-0.03972007,0.0472271,0.05296475,0.00474402,0.00263925,0.00085719,-0.01484107,-0.0143995,-0.03534472,-0.01165021,-0.00422555,0.03376448,0.01641939,0.05196404,0.00844409,-0.03934923,-0.0058667,-0.00938497,0.014865,-0.07884289,0.03354882,-0.04741327,-0.06001563,-0.09915355,0.02035704,0.02964355,0.04322879,-0.03006984,-0.01682959,-0.06748589,-0.0428127,0.0399027,0.00753674,0.02841649,-0.03394865,0.0145794,0.00518184,0.0089106,-0.01894873,0.0217774,-0.00618297,-0.04932391,0.05942564,0.01288392,-0.03067029,0.00184518,0.00801636,-0.00722518,0.00427883,-0.04327061,-0.03463508,-0.01653278,0.07232694,-0.02197437,0.01376633,0.03234584,0.03887236,-0.01231633,0.00680149,0.01862331,0.03286179,-0.03153613,0.01725765,0.0131901,0.0368105,-0.00495072,-0.01106759,0.00395156,0.02150553,0.0860443,0.05756059,0.04177906,-0.0122691,-0.00059606,0.02404713,0.07142665,-0.00245859,-0.01794267,0.00187097,-0.03430487,-0.01960964,0.00724997,-0.02870837,0.00225373,-0.01712272,0.06313771,-0.02301086,0.00646781,0.00741801,-0.02050841,-0.05117228,0.05759056,0.02875013,0.02007814,-0.03916871,0.00900405,-0.00835761,0.02900514,0.04692063,-0.03358071,-0.0083874,0.02170946,0.05917994,-0.04115246,-0.0387844,-0.05538422,-0.03283385,0.00571956,-0.02873484,0.02893851,-0.00381517,-0.03558383,-0.01508839,-0.01230385,-0.05319828,-0.02808829,0.04518021,-0.03248295,-0.00901235,0.02915544,0.01342527,-0.02779595,0.09181882,-0.02053064,-0.0116625,0.0386074,0.02443029,-0.03347335,-0.02817248,-0.06978232,-0.01104587,-0.08423521,0.00921985,0.03064582,0.01610598,-0.03921062,0.02162587,0.01515058,0.03232268,0.00739545,-0.05772397,-0.01326478,-0.06054462,-0.00692645,-0.08658294,0.07303102,0.03422415,0.03117382,-0.00506313,-0.08230913,-0.00575371,0.04180802,0.02520361,-0.03494502,0.0475181,0.04315754,0.01046468,-0.05763151,0.00151909,-0.04016724,-0.05213876,0.02509134,0.05785484,0.05362205,0.01202868,-0.07993595,0.03006707,-0.04896253,-0.0328062,0.03959361,0.00157355,0.01027822,0.02976686,0.02646489,0.02505116,-0.03464009,0.05112502,-0.00140904,-0.02788648,-0.02920771,0.01783919,-0.03876955,0.0217999,-0.02772991,0.0119291,0.07071359,-0.02224604,0.00881298,-0.00921299,0.05138259,0.02819117,-0.05536202,-0.06237937,-0.00440289,-0.00931918,0.0068383,0.03468369,-0.00364075,-0.01530922,-0.02260765,-0.01095494,-0.02357412,0.01230078,-0.02231762,0.01822717,-0.02024305,-0.0261588,0.03218863,-0.03999231,0.04347568,-0.03999655,-0.05820038,-0.05345797,-0.04420195,0.09278985,-0.01225869,0.03856037,0.0529432,0.0535003,-0.00197421,0.02313836,-0.03116625,-0.03790296,-0.00554319,-0.02357451,0.02640739,-0.06321177,-0.01985294,0.00720336,-0.02114631,-0.04525895,0.03349915,-0.04572302,0.01278351,-0.03122574,-0.07098336,0.01511184,0.01332705,-0.01023583,-0.03323305,0.03249714,-0.00312866,0.0560566,0.0349816,-0.01643224,-0.01099751,0.05623705,0.00774038,-0.03911861,0.01619182,0.02273223,-0.043419,-0.00946337,0.00586188,-0.02211649,-0.00714597,-0.00496607,-0.00050272,0.05580993,0.02908472,0.02347051,-0.00760277,0.02510479,-0.02149642,-0.02064343,0.04005413,0.00276662,-0.06607306,0.01051627,-0.01626491,0.01490837,-0.03454149,0.01515419,-0.0402953,-0.02816261,0.01759494,-0.04630064,0.04968143,-0.00159437,0.0247777,-0.03464719,-0.00797741,0.03973718,-0.00151178,-0.02175676,0.02499895,-0.04393951,0.00963808,-0.01353539,-0.00518654,0.01276091,0.01039882,0.00894562,-0.02673915,0.02589082,0.08675253,-0.01739845,0.03530705,-0.00342591,-0.01146367,-0.01772057,0.04384045,-0.03473769,0.01377823,0.02232319,0.01394997,-0.01615424,0.00534154,0.00454185,-0.02618854,0.00639181,-0.01063412,0.00664403,0.04367708,-0.00576421,-0.02324639,0.03189936,-0.01053333,-0.06556449,-0.09409954,-0.02697071,-0.02868771,0.04979888,0.02836673,0.01129176,0.02167721,-0.00242441,-0.01642683,-0.0630494,-0.01258749,0.05477823,0.01428296,-0.04331126,-0.06446774,-0.01870051,0.00792399,0.02686273,0.05013524,0.03288336,0.09143332,-0.02642219,-0.00578903,-0.07369516,-0.05735848,0.02625851,0.05239108,0.0264121,0.01349958,-0.04019415,-0.01790355,-0.05313143,-0.03771846,0.00360486,0.00213437,-0.03101422,-0.04408608,0.05758389,0.01225414,0.04402903,0.01255786,-0.00702177,0.03406464,-0.00673612,0.05532805,-0.00804893,-0.0621038,-0.00782283,0.02935337,0.04898527,-0.06156771,-0.02577452,-0.04097684,-0.00844947,0.01540783,0.03425824,-0.03473032,-0.05878947,-0.07793674,-0.0046925,0.04147844,0.05465682,0.0016899,-0.03802655,-0.01401954,-0.00536568,-0.01982559,0.00433992,-0.04096818,0.04648831,-0.00965448,0.03064422,0.01980625,0.02498103,-0.00735705,0.0474089,0.02445777,-0.05461697,-0.06252525,-0.0131377,-0.07410586,0.02268357,0.02223526,0.03136155,-0.00670807,-0.02570552,-0.0575232,0.04055222,0.03409294,0.01657207,-0.00320363,-0.01180608,0.03495448,-0.0115435,0.01749756,0.01065751,-0.08681647,-0.00317834,0.0045413,-0.05084002,0.00985777,0.04624253,-0.09270975,-0.0297823,-0.02665503,-0.01257422,-0.03353328,0.00466979,-0.00367515,-0.05706126,-0.00395908,-0.01219807,0.02514284,0.03498383,0.07331936,-0.02087223,-0.01931748,0.05173459,0.00813906,0.03871279,0.02140448,-0.01443561,-0.05168815,0.04124219,0.00273475,0.0075859,0.04358074,0.03093987,-0.04868189,-0.01588389,-0.00544276,0.08742968,0.00321764,0.04253672,-0.02782216,-0.04143411,0.00000108,0.0384892,0.00016687,-0.01988472,-0.02007818,-0.01643755,0.02159759,-0.02334271,-0.03004122,0.02896122],"last_embed":{"tokens":966,"hash":"xdzhdo"}}},"last_read":{"hash":"xdzhdo","at":1752940642434},"class_name":"SmartSource","outlinks":[{"title":"计算机网络大纲笔记","target":"计算机网络大纲笔记","line":24},{"title":"深度包检测","target":"深度包检测","line":51},{"title":"VPN","target":"VPN","line":68},{"title":"VPN","target":"VPN","line":69},{"title":"恶意软件","target":"Malware","line":71},{"title":"Cisco","target":"思科公司","line":87},{"title":"深度包检测","target":"深度包检测","line":98}],"metadata":{"aliases":["Firewall"],"英文":"Firewall","tags":["网络安全/防火墙"],"cssclasses":["editor-full"],"笔记类型":"重要紧急"},"blocks":{"#---frontmatter---":[1,10],"#网络基础设施":[11,13],"#网络基础设施#{1}":[13,13],"#简介":[14,30],"#简介#{1}":[15,22],"#简介#{2}":[23,24],"#简介#{3}":[25,29],"#简介#{4}":[30,30],"#防火墙分类":[31,54],"#防火墙分类#{1}":[32,37],"#防火墙分类#技术实现类别":[38,54],"#防火墙分类#技术实现类别#{1}":[39,39],"#防火墙分类#技术实现类别#{2}":[40,43],"#防火墙分类#技术实现类别#{3}":[44,46],"#防火墙分类#技术实现类别#{4}":[47,49],"#防火墙分类#技术实现类别#{5}":[50,53],"#防火墙分类#技术实现类别#{6}":[54,54],"#主要功能":[55,73],"#主要功能#{1}":[56,60],"#主要功能#{2}":[61,62],"#主要功能#{3}":[63,65],"#主要功能#{4}":[66,67],"#主要功能#{5}":[68,69],"#主要功能#{6}":[70,72],"#主要功能#{7}":[73,73],"#包过滤机制":[74,84],"#包过滤机制#{1}":[76,76],"#包过滤机制#{2}":[77,77],"#包过滤机制#{3}":[78,78],"#包过滤机制#{4}":[79,81],"#包过滤机制#{5}":[82,84],"#常见防火墙厂商":[85,93],"#常见防火墙厂商#{1}":[87,87],"#常见防火墙厂商#{2}":[88,88],"#常见防火墙厂商#{3}":[89,89],"#常见防火墙厂商#{4}":[90,90],"#常见防火墙厂商#{5}":[91,92],"#常见防火墙厂商#{6}":[93,93],"#笔记":[94,98],"#笔记#{1}":[95,95],"#笔记#{2}":[96,96],"#笔记#{3}":[97,97],"#笔记#{4}":[98,98]},"last_import":{"mtime":1747038474721,"size":4510,"at":1749024987320,"hash":"xdzhdo"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#---frontmatter---","lines":[1,10],"size":100,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#网络基础设施": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#网络基础设施","lines":[11,13],"size":12,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#网络基础设施#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#网络基础设施#{1}","lines":[13,13],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#简介","lines":[14,30],"size":562,"outlinks":[{"title":"计算机网络大纲笔记","target":"计算机网络大纲笔记","line":11}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#简介#{1}","lines":[15,22],"size":262,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#简介#{2}","lines":[23,24],"size":67,"outlinks":[{"title":"计算机网络大纲笔记","target":"计算机网络大纲笔记","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#简介#{3}","lines":[25,29],"size":222,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#简介#{4}","lines":[30,30],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类","lines":[31,54],"size":487,"outlinks":[{"title":"深度包检测","target":"深度包检测","line":21}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#{1}","lines":[32,37],"size":236,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别","lines":[38,54],"size":242,"outlinks":[{"title":"深度包检测","target":"深度包检测","line":14}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{1}","lines":[39,39],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{2}","lines":[40,43],"size":71,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{3}","lines":[44,46],"size":41,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{4}","lines":[47,49],"size":49,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{5}","lines":[50,53],"size":52,"outlinks":[{"title":"深度包检测","target":"深度包检测","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#防火墙分类#技术实现类别#{6}","lines":[54,54],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能","lines":[55,73],"size":385,"outlinks":[{"title":"VPN","target":"VPN","line":14},{"title":"VPN","target":"VPN","line":15},{"title":"恶意软件","target":"Malware","line":17}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{1}","lines":[56,60],"size":161,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{2}","lines":[61,62],"size":30,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{3}","lines":[63,65],"size":55,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{4}","lines":[66,67],"size":37,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{5}","lines":[68,69],"size":44,"outlinks":[{"title":"VPN","target":"VPN","line":1},{"title":"VPN","target":"VPN","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{6}","lines":[70,72],"size":42,"outlinks":[{"title":"恶意软件","target":"Malware","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#主要功能#{7}","lines":[73,73],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制","lines":[74,84],"size":186,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制#{1}","lines":[76,76],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制#{2}","lines":[77,77],"size":43,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制#{3}","lines":[78,78],"size":38,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制#{4}","lines":[79,81],"size":47,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#包过滤机制#{5}","lines":[82,84],"size":5,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商","lines":[85,93],"size":128,"outlinks":[{"title":"Cisco","target":"思科公司","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{1}","lines":[87,87],"size":20,"outlinks":[{"title":"Cisco","target":"思科公司","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{2}","lines":[88,88],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{3}","lines":[89,89],"size":24,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{4}","lines":[90,90],"size":27,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{5}","lines":[91,92],"size":24,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#常见防火墙厂商#{6}","lines":[93,93],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#笔记": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#笔记","lines":[94,98],"size":63,"outlinks":[{"title":"深度包检测","target":"深度包检测","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#笔记#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#笔记#{1}","lines":[95,95],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#笔记#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#笔记#{2}","lines":[96,96],"size":12,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#笔记#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#笔记#{3}","lines":[97,97],"size":17,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#笔记#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md#笔记#{4}","lines":[98,98],"size":15,"outlinks":[{"title":"深度包检测","target":"深度包检测","line":1}],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md","last_embed":{"hash":"xdzhdo","at":1752940870536},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09676993,0.00342223,0.01002813,-0.05537734,0.03993372,-0.01181047,-0.01358618,0.03836221,0.04479728,0.00103803,-0.00756134,-0.07001887,0.04939612,0.06271807,0.05211655,0.01646355,-0.02239446,0.01650263,-0.0000608,0.00640119,0.02706691,-0.03886275,0.00629717,-0.07448644,-0.0112937,0.0099165,0.02160849,-0.0299591,-0.01036196,-0.14904712,-0.00423826,0.05015796,0.02100559,0.0300232,-0.00982573,-0.06008773,0.02053769,0.05429094,-0.01093943,0.01899751,0.03395975,0.05290129,0.03616602,-0.02313843,0.01161135,-0.03206614,-0.01933757,-0.02480925,0.0024061,-0.08465358,-0.02652821,-0.05667157,-0.03068144,0.00613158,-0.0706519,0.02182915,0.00967243,0.02149354,0.0551048,0.01543025,-0.00269616,0.01660436,-0.2265424,0.04897317,0.0184147,-0.07313418,-0.01562234,0.00073559,-0.01224108,0.05735011,-0.05198009,0.02440626,-0.04167912,0.06139359,0.08508383,-0.00258385,-0.01338916,0.01369101,-0.04334306,-0.05251748,-0.01389999,0.03919076,-0.02993849,0.01499791,-0.01096609,0.06103918,0.02255203,-0.04330018,0.01568804,0.03366999,-0.00774602,-0.04900332,-0.01319285,0.03445582,-0.00401259,-0.05508543,0.01809819,0.06409186,-0.08733085,0.12216181,-0.05669429,-0.05639956,-0.00984808,-0.0364291,0.00889019,0.01259966,0.00677368,-0.03331196,-0.03721723,-0.01840758,-0.05483052,-0.02501736,0.04601074,-0.01835833,0.05329465,-0.03547836,0.03229344,-0.02986804,-0.01685752,-0.00443319,-0.00989578,0.00791161,0.04950446,-0.03093088,-0.01722685,-0.04263484,0.03589485,0.05892606,0.02163506,0.0548521,0.04690864,-0.02011531,-0.03278126,-0.01178365,-0.02064672,0.00924859,-0.03638081,0.03779003,-0.0134584,-0.04411453,0.01343138,-0.02963187,0.01923546,-0.09031345,-0.0512509,0.06164484,-0.09352977,-0.01047558,0.01917374,-0.03529939,0.02398398,0.02498084,0.00727702,-0.00207592,0.01260015,0.01000817,0.08151438,0.1732446,-0.01091641,-0.05161874,0.02266848,0.01441418,-0.08156393,0.14735663,-0.01425819,-0.07532831,-0.00956157,-0.01431653,-0.03767595,-0.05761618,-0.01382488,-0.00705299,-0.01028484,0.03883463,0.03454845,-0.01973144,0.01775977,-0.01529793,0.02733776,0.05092029,0.06616829,-0.03946154,-0.08819569,0.04960106,0.01713286,-0.10692751,-0.07753965,-0.04946731,-0.00368696,-0.04796619,-0.14112683,0.05488079,-0.01397945,0.01938768,-0.0791803,-0.05107927,0.06572785,-0.02019722,0.03048707,-0.06554532,0.1016966,0.00712085,-0.03726109,-0.01207569,-0.03767096,-0.01930559,0.02764124,0.00913407,0.02403145,-0.00271946,-0.01272029,0.01816216,-0.0186847,0.02393153,0.00660479,-0.01563804,0.03802134,0.05034038,0.01484065,0.08091626,-0.00239232,0.03053803,-0.0736065,-0.23664792,-0.04043268,0.02747042,-0.05104786,0.00161286,-0.02634219,0.03320171,0.01575598,0.06179662,0.10798547,0.10941794,0.05259676,-0.07913181,-0.03751763,0.0206888,0.00403568,0.01447239,-0.02274323,-0.04079486,0.03010013,0.04687517,0.04679796,-0.04354902,-0.00920012,0.02890915,-0.02179364,0.12417165,0.01956158,0.03835463,0.03199429,0.03910276,0.02406144,-0.01207869,-0.08322749,0.02421867,0.04912291,-0.00411124,-0.01881788,-0.00080214,-0.01269071,-0.00368075,0.04911518,-0.02843674,-0.09020152,-0.01377658,-0.03489415,-0.02259752,-0.00626144,-0.0009508,0.0087469,0.00727057,0.0208657,0.01533798,0.0466048,0.00734543,-0.02250711,-0.06003021,-0.01036205,-0.0462627,0.02178283,0.01706685,0.01963546,0.03715272,-0.01204407,0.03902952,0.01047903,-0.02163243,-0.00318532,-0.05930994,0.00566526,-0.05235314,0.16012302,0.01088575,-0.03934648,0.02659989,0.02137126,0.01513527,-0.02562028,0.01056343,-0.0028052,0.04775223,-0.00831005,0.06312543,0.01758436,-0.0128855,0.03274125,0.00683694,-0.03613026,0.07948697,-0.05399095,-0.07644559,0.00563527,-0.0117304,0.03860645,0.06612094,-0.0354823,-0.30305582,0.01045859,0.00452641,-0.02901549,0.05257628,0.02782845,0.04701664,0.00947067,-0.05150118,0.05645687,-0.01742904,0.04648213,-0.01661175,-0.02951866,-0.03678525,-0.06535299,0.05314755,-0.00173545,0.04884499,0.00104196,0.01105581,0.03386483,0.22137548,-0.02427072,0.07294348,-0.022072,-0.02695636,0.05595457,0.03537027,0.03950404,0.02023123,-0.0719264,0.0374419,-0.02137472,0.05294961,0.04024006,-0.03352473,0.00663672,0.01839599,0.01737595,-0.04957635,0.06496953,-0.06628405,0.05017838,0.06865676,0.02442073,-0.0432499,-0.0134827,0.00080675,0.00900275,0.00547556,0.0390077,-0.00239527,0.00738325,0.03016464,0.05624136,0.00869376,-0.03371188,-0.08169597,0.00008132,0.00931073,0.02797035,0.08553032,0.082385,0.07549562],"last_embed":{"hash":"6cd8f53265def0659df7308a1fb1e07cc8b1af71069039aa74d9a255571ef29d","tokens":440}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.008077,0.00746244,0.05801528,0.03134271,-0.0022506,0.0379574,0.09005065,-0.03215289,0.00219921,-0.03680732,0.01459826,0.03061069,-0.00568296,-0.07782453,0.00523839,-0.02188151,0.0615304,0.00254004,0.06708094,-0.04501338,-0.09820996,0.05883688,0.01106625,0.00421346,0.06725807,-0.04595078,-0.04681095,-0.09503086,0.0347654,0.0271242,0.08002685,-0.01419627,0.03705626,-0.00775208,0.02484509,0.06165636,0.00821092,-0.03464048,0.03944554,0.00806144,-0.0216148,0.05165402,-0.01137714,-0.0125314,-0.05141209,0.00749194,0.05488418,-0.09436177,-0.01180316,0.00109732,0.04145792,-0.01502201,0.03612779,0.00961408,0.03304767,0.02603528,-0.02184134,-0.04855681,-0.05575855,-0.02318113,-0.02487571,-0.05519316,0.02457066,-0.02316517,0.06273627,-0.00289028,0.01530801,-0.03929781,-0.06479168,0.00966223,-0.00689662,0.03258479,0.0267371,0.01857114,0.00607593,0.02444363,0.03577151,-0.03512982,-0.06023211,-0.0000218,-0.05669196,0.04967583,0.06452852,0.03824129,0.04619964,-0.02735432,0.08084396,-0.01084368,-0.03516376,0.02335377,0.02016426,-0.00855857,-0.06513072,-0.05374943,0.0484007,-0.0040033,0.00264093,0.00017468,-0.04091258,-0.02675228,-0.00825751,-0.00745661,0.00567489,0.00339761,0.00822594,0.00611831,-0.00334475,0.02040455,-0.0236377,-0.00227679,0.00697406,-0.00977402,-0.09107091,-0.04056317,-0.02435365,0.03038339,0.01428392,0.04272828,0.01160028,-0.02377933,0.00233209,0.03467967,-0.04796178,-0.01743429,-0.00716769,0.0369542,-0.00877506,0.02226541,-0.02431748,-0.0243623,-0.03316486,0.03063504,-0.05568873,-0.03921834,0.03208492,0.06429818,0.01648713,-0.06757497,-0.04555976,-0.04460947,0.06569984,-0.01849596,0.00137954,0.00021208,-0.03199895,0.03598012,0.02090565,-0.03582451,-0.00436324,0.00954936,0.03739168,0.07610437,-0.07092576,-0.00739572,-0.01859353,-0.02647433,0.01553259,0.02294062,0.03500045,-0.05140869,-0.00572797,0.07185663,-0.00208982,0.01638582,0.03249298,-0.0089933,-0.06534516,0.01620442,0.02724745,0.00397771,-0.0255211,0.01092373,-0.04287491,-0.03759781,0.02521293,-0.02227943,0.02160287,0.02865114,-0.05557689,0.03402522,0.02936726,-0.04135634,-0.01097526,-0.02085036,0.01507887,0.03112076,0.00953239,-0.00542577,0.02869218,0.02709344,0.02788151,-0.00342806,-0.01778854,0.00794813,-0.0057195,0.08756258,-0.03064531,-0.05786736,0.00245673,0.01920566,-0.04146791,-0.04027051,-0.02484206,-0.08100476,0.06427806,0.00667558,-0.08360992,-0.01860351,-0.03457663,0.03367183,0.0198481,0.05848287,0.02092604,-0.00441136,-0.03265474,-0.00325126,0.00833966,0.02619123,-0.03306775,-0.05833017,0.03731322,-0.00292431,-0.03688341,-0.0498123,0.04414468,-0.05240972,-0.006279,0.01553808,0.02032497,-0.03334782,0.03236256,0.0715824,-0.00519354,-0.03407951,-0.05847771,0.01303957,-0.01681449,0.00183923,0.06813264,0.00621236,-0.05377946,-0.0378833,-0.00903869,0.05005683,-0.0032258,-0.00841447,-0.04377914,-0.0042421,0.05300399,0.01789238,0.04749355,-0.00855236,-0.01006884,0.06970819,-0.00409989,0.01122377,-0.00067377,0.01876364,-0.0084388,-0.03191163,0.0415732,-0.03870802,-0.02922394,0.03321005,-0.07771631,-0.02130298,-0.01008954,0.01785672,0.00234898,-0.00448764,0.04110734,-0.01596515,0.00380265,0.02027011,0.02099144,0.00691826,0.00640131,0.03255135,-0.02478336,-0.0322683,0.00780762,-0.01259546,0.05386068,-0.03795926,0.01535098,-0.00830227,-0.0153305,-0.04309428,-0.01504418,0.04264145,0.02703171,-0.02283863,-0.04977142,-0.00851399,-0.0548265,-0.03636784,0.02879123,-0.00500434,0.03044467,-0.06560081,-0.01199458,0.03044297,-0.02152232,-0.03972007,0.0472271,0.05296475,0.00474402,0.00263925,0.00085719,-0.01484107,-0.0143995,-0.03534472,-0.01165021,-0.00422555,0.03376448,0.01641939,0.05196404,0.00844409,-0.03934923,-0.0058667,-0.00938497,0.014865,-0.07884289,0.03354882,-0.04741327,-0.06001563,-0.09915355,0.02035704,0.02964355,0.04322879,-0.03006984,-0.01682959,-0.06748589,-0.0428127,0.0399027,0.00753674,0.02841649,-0.03394865,0.0145794,0.00518184,0.0089106,-0.01894873,0.0217774,-0.00618297,-0.04932391,0.05942564,0.01288392,-0.03067029,0.00184518,0.00801636,-0.00722518,0.00427883,-0.04327061,-0.03463508,-0.01653278,0.07232694,-0.02197437,0.01376633,0.03234584,0.03887236,-0.01231633,0.00680149,0.01862331,0.03286179,-0.03153613,0.01725765,0.0131901,0.0368105,-0.00495072,-0.01106759,0.00395156,0.02150553,0.0860443,0.05756059,0.04177906,-0.0122691,-0.00059606,0.02404713,0.07142665,-0.00245859,-0.01794267,0.00187097,-0.03430487,-0.01960964,0.00724997,-0.02870837,0.00225373,-0.01712272,0.06313771,-0.02301086,0.00646781,0.00741801,-0.02050841,-0.05117228,0.05759056,0.02875013,0.02007814,-0.03916871,0.00900405,-0.00835761,0.02900514,0.04692063,-0.03358071,-0.0083874,0.02170946,0.05917994,-0.04115246,-0.0387844,-0.05538422,-0.03283385,0.00571956,-0.02873484,0.02893851,-0.00381517,-0.03558383,-0.01508839,-0.01230385,-0.05319828,-0.02808829,0.04518021,-0.03248295,-0.00901235,0.02915544,0.01342527,-0.02779595,0.09181882,-0.02053064,-0.0116625,0.0386074,0.02443029,-0.03347335,-0.02817248,-0.06978232,-0.01104587,-0.08423521,0.00921985,0.03064582,0.01610598,-0.03921062,0.02162587,0.01515058,0.03232268,0.00739545,-0.05772397,-0.01326478,-0.06054462,-0.00692645,-0.08658294,0.07303102,0.03422415,0.03117382,-0.00506313,-0.08230913,-0.00575371,0.04180802,0.02520361,-0.03494502,0.0475181,0.04315754,0.01046468,-0.05763151,0.00151909,-0.04016724,-0.05213876,0.02509134,0.05785484,0.05362205,0.01202868,-0.07993595,0.03006707,-0.04896253,-0.0328062,0.03959361,0.00157355,0.01027822,0.02976686,0.02646489,0.02505116,-0.03464009,0.05112502,-0.00140904,-0.02788648,-0.02920771,0.01783919,-0.03876955,0.0217999,-0.02772991,0.0119291,0.07071359,-0.02224604,0.00881298,-0.00921299,0.05138259,0.02819117,-0.05536202,-0.06237937,-0.00440289,-0.00931918,0.0068383,0.03468369,-0.00364075,-0.01530922,-0.02260765,-0.01095494,-0.02357412,0.01230078,-0.02231762,0.01822717,-0.02024305,-0.0261588,0.03218863,-0.03999231,0.04347568,-0.03999655,-0.05820038,-0.05345797,-0.04420195,0.09278985,-0.01225869,0.03856037,0.0529432,0.0535003,-0.00197421,0.02313836,-0.03116625,-0.03790296,-0.00554319,-0.02357451,0.02640739,-0.06321177,-0.01985294,0.00720336,-0.02114631,-0.04525895,0.03349915,-0.04572302,0.01278351,-0.03122574,-0.07098336,0.01511184,0.01332705,-0.01023583,-0.03323305,0.03249714,-0.00312866,0.0560566,0.0349816,-0.01643224,-0.01099751,0.05623705,0.00774038,-0.03911861,0.01619182,0.02273223,-0.043419,-0.00946337,0.00586188,-0.02211649,-0.00714597,-0.00496607,-0.00050272,0.05580993,0.02908472,0.02347051,-0.00760277,0.02510479,-0.02149642,-0.02064343,0.04005413,0.00276662,-0.06607306,0.01051627,-0.01626491,0.01490837,-0.03454149,0.01515419,-0.0402953,-0.02816261,0.01759494,-0.04630064,0.04968143,-0.00159437,0.0247777,-0.03464719,-0.00797741,0.03973718,-0.00151178,-0.02175676,0.02499895,-0.04393951,0.00963808,-0.01353539,-0.00518654,0.01276091,0.01039882,0.00894562,-0.02673915,0.02589082,0.08675253,-0.01739845,0.03530705,-0.00342591,-0.01146367,-0.01772057,0.04384045,-0.03473769,0.01377823,0.02232319,0.01394997,-0.01615424,0.00534154,0.00454185,-0.02618854,0.00639181,-0.01063412,0.00664403,0.04367708,-0.00576421,-0.02324639,0.03189936,-0.01053333,-0.06556449,-0.09409954,-0.02697071,-0.02868771,0.04979888,0.02836673,0.01129176,0.02167721,-0.00242441,-0.01642683,-0.0630494,-0.01258749,0.05477823,0.01428296,-0.04331126,-0.06446774,-0.01870051,0.00792399,0.02686273,0.05013524,0.03288336,0.09143332,-0.02642219,-0.00578903,-0.07369516,-0.05735848,0.02625851,0.05239108,0.0264121,0.01349958,-0.04019415,-0.01790355,-0.05313143,-0.03771846,0.00360486,0.00213437,-0.03101422,-0.04408608,0.05758389,0.01225414,0.04402903,0.01255786,-0.00702177,0.03406464,-0.00673612,0.05532805,-0.00804893,-0.0621038,-0.00782283,0.02935337,0.04898527,-0.06156771,-0.02577452,-0.04097684,-0.00844947,0.01540783,0.03425824,-0.03473032,-0.05878947,-0.07793674,-0.0046925,0.04147844,0.05465682,0.0016899,-0.03802655,-0.01401954,-0.00536568,-0.01982559,0.00433992,-0.04096818,0.04648831,-0.00965448,0.03064422,0.01980625,0.02498103,-0.00735705,0.0474089,0.02445777,-0.05461697,-0.06252525,-0.0131377,-0.07410586,0.02268357,0.02223526,0.03136155,-0.00670807,-0.02570552,-0.0575232,0.04055222,0.03409294,0.01657207,-0.00320363,-0.01180608,0.03495448,-0.0115435,0.01749756,0.01065751,-0.08681647,-0.00317834,0.0045413,-0.05084002,0.00985777,0.04624253,-0.09270975,-0.0297823,-0.02665503,-0.01257422,-0.03353328,0.00466979,-0.00367515,-0.05706126,-0.00395908,-0.01219807,0.02514284,0.03498383,0.07331936,-0.02087223,-0.01931748,0.05173459,0.00813906,0.03871279,0.02140448,-0.01443561,-0.05168815,0.04124219,0.00273475,0.0075859,0.04358074,0.03093987,-0.04868189,-0.01588389,-0.00544276,0.08742968,0.00321764,0.04253672,-0.02782216,-0.04143411,0.00000108,0.0384892,0.00016687,-0.01988472,-0.02007818,-0.01643755,0.02159759,-0.02334271,-0.03004122,0.02896122],"last_embed":{"tokens":966,"hash":"xdzhdo"}}},"last_read":{"hash":"xdzhdo","at":1752940870536},"class_name":"SmartSource","outlinks":[{"title":"计算机网络大纲笔记","target":"计算机网络大纲笔记","line":24},{"title":"深度包检测","target":"深度包检测","line":51},{"title":"VPN","target":"VPN","line":68},{"title":"VPN","target":"VPN","line":69},{"title":"恶意软件","target":"Malware","line":71},{"title":"Cisco","target":"思科公司","line":87},{"title":"深度包检测","target":"深度包检测","line":98}],"metadata":{"aliases":["Firewall"],"英文":"Firewall","tags":["网络安全/防火墙"],"cssclasses":["editor-full"],"笔记类型":"重要紧急"},"blocks":{"#---frontmatter---":[1,10],"#网络基础设施":[11,13],"#网络基础设施#{1}":[13,13],"#简介":[14,30],"#简介#{1}":[15,22],"#简介#{2}":[23,24],"#简介#{3}":[25,29],"#简介#{4}":[30,30],"#防火墙分类":[31,54],"#防火墙分类#{1}":[32,37],"#防火墙分类#技术实现类别":[38,54],"#防火墙分类#技术实现类别#{1}":[39,39],"#防火墙分类#技术实现类别#{2}":[40,43],"#防火墙分类#技术实现类别#{3}":[44,46],"#防火墙分类#技术实现类别#{4}":[47,49],"#防火墙分类#技术实现类别#{5}":[50,53],"#防火墙分类#技术实现类别#{6}":[54,54],"#主要功能":[55,73],"#主要功能#{1}":[56,60],"#主要功能#{2}":[61,62],"#主要功能#{3}":[63,65],"#主要功能#{4}":[66,67],"#主要功能#{5}":[68,69],"#主要功能#{6}":[70,72],"#主要功能#{7}":[73,73],"#包过滤机制":[74,84],"#包过滤机制#{1}":[76,76],"#包过滤机制#{2}":[77,77],"#包过滤机制#{3}":[78,78],"#包过滤机制#{4}":[79,81],"#包过滤机制#{5}":[82,84],"#常见防火墙厂商":[85,93],"#常见防火墙厂商#{1}":[87,87],"#常见防火墙厂商#{2}":[88,88],"#常见防火墙厂商#{3}":[89,89],"#常见防火墙厂商#{4}":[90,90],"#常见防火墙厂商#{5}":[91,92],"#常见防火墙厂商#{6}":[93,93],"#笔记":[94,98],"#笔记#{1}":[95,95],"#笔记#{2}":[96,96],"#笔记#{3}":[97,97],"#笔记#{4}":[98,98]},"last_import":{"mtime":1747038474721,"size":4510,"at":1749024987320,"hash":"xdzhdo"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/防火墙.md"},