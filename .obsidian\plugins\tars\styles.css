summary.tars-setting-h4 {
	font-variant: var(--h4-variant);
	line-height: var(--h4-line-height);
	font-size: var(--h4-size);
	color: var(--h4-color);
	font-weight: var(--h4-weight);
	font-style: var(--h4-style);
	font-family: var(--h4-font);
	margin-block-start: var(--p-spacing);
	margin-block-end: var(--p-spacing);
}

.fuzzy-match-highlight {
	font-weight: var(--font-extrabold);
}

.capability-tags-container {
	margin-top: var(--size-2-3);
	display: flex;
	flex-wrap: wrap;
	gap: var(--size-2-2);
}

.capability-tag {
	display: inline-flex;
	align-items: center;
	display: inline-block;
	padding: var(--size-2-1) var(--size-2-3);
	font-size: var(--font-ui-smaller);
	background-color: var(--background-modifier-border);
	color: var(--text-muted);
	border-radius: var(--radius-m);
	border: var(--border-width) solid var(--background-modifier-border-hover);
	white-space: nowrap;
}

.syntax-error-textarea {
	width: 100%;
}

/* Generation Stats Modal Styles */
.generation-stats {
	display: flex;
	flex-direction: column;
	gap: var(--size-4-2);
}

.generation-stats p {
	margin: 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: var(--size-2-1) var(--size-2-3);
	background-color: var(--background-secondary);
	border-radius: var(--radius-s);
	font-family: var(--font-monospace);
	font-size: var(--font-ui-small);
}

/* Error Detail Modal Styles */
.error-details {
	display: flex;
	flex-direction: column;
	gap: var(--size-4-2);
}

.error-details p {
	margin: 0;
	padding: var(--size-2-1) var(--size-2-3);
	background-color: var(--background-secondary);
	border-radius: var(--radius-s);
	font-family: var(--font-monospace);
	font-size: var(--font-ui-small);
	word-break: break-word;
}

.stack-trace {
	margin-top: var(--size-4-2);
}

.stack-trace h3 {
	margin: 0 0 var(--size-4-1) 0;
	color: var(--text-error);
	font-size: var(--font-ui-medium);
}

.stack-trace-pre {
	background: var(--background-secondary);
	padding: var(--size-4-2);
	border-radius: var(--radius-s);
	font-size: var(--font-ui-smaller);
	overflow: auto;
	max-height: 200px;
	margin: 0;
}

.error-modal-buttons {
	margin-top: var(--size-4-3);
	display: flex;
	gap: var(--size-4-1);
	justify-content: flex-end;
}

.error-modal-buttons button {
	padding: var(--size-2-2) var(--size-4-2);
	border-radius: var(--radius-m);
	border: 1px solid var(--background-modifier-border);
	background-color: var(--interactive-normal);
	color: var(--text-normal);
	cursor: pointer;
}

.error-modal-buttons button:hover {
	background-color: var(--interactive-hover);
}

.error-modal-buttons button:active {
	background-color: var(--interactive-active);
}
