/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Oy=Object.create;var di=Object.defineProperty;var My=Object.getOwnPropertyDescriptor;var Ly=Object.getOwnPropertyNames;var Ny=Object.getPrototypeOf,Dy=Object.prototype.hasOwnProperty;var j=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),td=(r,e)=>{for(var t in e)di(r,t,{get:e[t],enumerable:!0})},rd=(r,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of Ly(e))!Dy.call(r,s)&&s!==t&&di(r,s,{get:()=>e[s],enumerable:!(n=My(e,s))||n.enumerable});return r};var By=(r,e,t)=>(t=r!=null?Oy(Ny(r)):{},rd(e||!r||!r.__esModule?di(t,"default",{value:r,enumerable:!0}):t,r)),Fy=r=>rd(di({},"__esModule",{value:!0}),r);var Ee=j(Oe=>{"use strict";Oe.__esModule=!0;Oe.extend=Um;Oe.indexOf=lE;Oe.escapeExpression=cE;Oe.isEmpty=uE;Oe.createFrame=fE;Oe.blockParams=dE;Oe.appendContextPath=hE;var sE={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},oE=/[&<>"'`=]/g,iE=/[&<>"'`=]/;function aE(r){return sE[r]}function Um(r){for(var e=1;e<arguments.length;e++)for(var t in arguments[e])Object.prototype.hasOwnProperty.call(arguments[e],t)&&(r[t]=arguments[e][t]);return r}var rf=Object.prototype.toString;Oe.toString=rf;var tf=function(e){return typeof e=="function"};tf(/x/)&&(Oe.isFunction=tf=function(r){return typeof r=="function"&&rf.call(r)==="[object Function]"});Oe.isFunction=tf;var qm=Array.isArray||function(r){return r&&typeof r=="object"?rf.call(r)==="[object Array]":!1};Oe.isArray=qm;function lE(r,e){for(var t=0,n=r.length;t<n;t++)if(r[t]===e)return t;return-1}function cE(r){if(typeof r!="string"){if(r&&r.toHTML)return r.toHTML();if(r==null)return"";if(!r)return r+"";r=""+r}return iE.test(r)?r.replace(oE,aE):r}function uE(r){return!r&&r!==0?!0:!!(qm(r)&&r.length===0)}function fE(r){var e=Um({},r);return e._parent=r,e}function dE(r,e){return r.path=e,r}function hE(r,e){return(r?r+".":"")+e}});var qe=j((Za,$m)=>{"use strict";Za.__esModule=!0;var nf=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];function sf(r,e){var t=e&&e.loc,n=void 0,s=void 0,o=void 0,i=void 0;t&&(n=t.start.line,s=t.end.line,o=t.start.column,i=t.end.column,r+=" - "+n+":"+o);for(var a=Error.prototype.constructor.call(this,r),c=0;c<nf.length;c++)this[nf[c]]=a[nf[c]];Error.captureStackTrace&&Error.captureStackTrace(this,sf);try{t&&(this.lineNumber=n,this.endLineNumber=s,Object.defineProperty?(Object.defineProperty(this,"column",{value:o,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:i,enumerable:!0})):(this.column=o,this.endColumn=i))}catch(l){}}sf.prototype=new Error;Za.default=sf;$m.exports=Za.default});var jm=j((el,Hm)=>{"use strict";el.__esModule=!0;var of=Ee();el.default=function(r){r.registerHelper("blockHelperMissing",function(e,t){var n=t.inverse,s=t.fn;if(e===!0)return s(this);if(e===!1||e==null)return n(this);if(of.isArray(e))return e.length>0?(t.ids&&(t.ids=[t.name]),r.helpers.each(e,t)):n(this);if(t.data&&t.ids){var o=of.createFrame(t.data);o.contextPath=of.appendContextPath(t.data.contextPath,t.name),t={data:o}}return s(e,t)})};Hm.exports=el.default});var Vm=j((tl,Wm)=>{"use strict";tl.__esModule=!0;function pE(r){return r&&r.__esModule?r:{default:r}}var Yo=Ee(),mE=qe(),gE=pE(mE);tl.default=function(r){r.registerHelper("each",function(e,t){if(!t)throw new gE.default("Must pass iterator to #each");var n=t.fn,s=t.inverse,o=0,i="",a=void 0,c=void 0;t.data&&t.ids&&(c=Yo.appendContextPath(t.data.contextPath,t.ids[0])+"."),Yo.isFunction(e)&&(e=e.call(this)),t.data&&(a=Yo.createFrame(t.data));function l(p,y,g){a&&(a.key=p,a.index=y,a.first=y===0,a.last=!!g,c&&(a.contextPath=c+p)),i=i+n(e[p],{data:a,blockParams:Yo.blockParams([e[p],p],[c+p,null])})}if(e&&typeof e=="object")if(Yo.isArray(e))for(var f=e.length;o<f;o++)o in e&&l(o,o,o===e.length-1);else if(typeof Symbol=="function"&&e[Symbol.iterator]){for(var u=[],d=e[Symbol.iterator](),h=d.next();!h.done;h=d.next())u.push(h.value);e=u;for(var f=e.length;o<f;o++)l(o,o,o===e.length-1)}else(function(){var p=void 0;Object.keys(e).forEach(function(y){p!==void 0&&l(p,o-1),p=y,o++}),p!==void 0&&l(p,o-1,!0)})();return o===0&&(i=s(this)),i})};Wm.exports=tl.default});var Gm=j((rl,Km)=>{"use strict";rl.__esModule=!0;function yE(r){return r&&r.__esModule?r:{default:r}}var bE=qe(),wE=yE(bE);rl.default=function(r){r.registerHelper("helperMissing",function(){if(arguments.length!==1)throw new wE.default('Missing helper: "'+arguments[arguments.length-1].name+'"')})};Km.exports=rl.default});var Ym=j((nl,Xm)=>{"use strict";nl.__esModule=!0;function _E(r){return r&&r.__esModule?r:{default:r}}var Jm=Ee(),SE=qe(),zm=_E(SE);nl.default=function(r){r.registerHelper("if",function(e,t){if(arguments.length!=2)throw new zm.default("#if requires exactly one argument");return Jm.isFunction(e)&&(e=e.call(this)),!t.hash.includeZero&&!e||Jm.isEmpty(e)?t.inverse(this):t.fn(this)}),r.registerHelper("unless",function(e,t){if(arguments.length!=2)throw new zm.default("#unless requires exactly one argument");return r.helpers.if.call(this,e,{fn:t.inverse,inverse:t.fn,hash:t.hash})})};Xm.exports=nl.default});var Zm=j((sl,Qm)=>{"use strict";sl.__esModule=!0;sl.default=function(r){r.registerHelper("log",function(){for(var e=[void 0],t=arguments[arguments.length-1],n=0;n<arguments.length-1;n++)e.push(arguments[n]);var s=1;t.hash.level!=null?s=t.hash.level:t.data&&t.data.level!=null&&(s=t.data.level),e[0]=s,r.log.apply(r,e)})};Qm.exports=sl.default});var tg=j((ol,eg)=>{"use strict";ol.__esModule=!0;ol.default=function(r){r.registerHelper("lookup",function(e,t,n){return e&&n.lookupProperty(e,t)})};eg.exports=ol.default});var ng=j((il,rg)=>{"use strict";il.__esModule=!0;function xE(r){return r&&r.__esModule?r:{default:r}}var Qo=Ee(),EE=qe(),vE=xE(EE);il.default=function(r){r.registerHelper("with",function(e,t){if(arguments.length!=2)throw new vE.default("#with requires exactly one argument");Qo.isFunction(e)&&(e=e.call(this));var n=t.fn;if(Qo.isEmpty(e))return t.inverse(this);var s=t.data;return t.data&&t.ids&&(s=Qo.createFrame(t.data),s.contextPath=Qo.appendContextPath(t.data.contextPath,t.ids[0])),n(e,{data:s,blockParams:Qo.blockParams([e],[s&&s.contextPath])})})};rg.exports=il.default});var af=j(al=>{"use strict";al.__esModule=!0;al.registerDefaultHelpers=UE;al.moveHelperToHooks=qE;function tn(r){return r&&r.__esModule?r:{default:r}}var AE=jm(),CE=tn(AE),PE=Vm(),RE=tn(PE),TE=Gm(),IE=tn(TE),kE=Ym(),OE=tn(kE),ME=Zm(),LE=tn(ME),NE=tg(),DE=tn(NE),BE=ng(),FE=tn(BE);function UE(r){CE.default(r),RE.default(r),IE.default(r),OE.default(r),LE.default(r),DE.default(r),FE.default(r)}function qE(r,e,t){r.helpers[e]&&(r.hooks[e]=r.helpers[e],t||delete r.helpers[e])}});var og=j((ll,sg)=>{"use strict";ll.__esModule=!0;var $E=Ee();ll.default=function(r){r.registerDecorator("inline",function(e,t,n,s){var o=e;return t.partials||(t.partials={},o=function(i,a){var c=n.partials;n.partials=$E.extend({},c,t.partials);var l=e(i,a);return n.partials=c,l}),t.partials[s.args[0]]=s.fn,o})};sg.exports=ll.default});var ig=j(lf=>{"use strict";lf.__esModule=!0;lf.registerDefaultDecorators=VE;function HE(r){return r&&r.__esModule?r:{default:r}}var jE=og(),WE=HE(jE);function VE(r){WE.default(r)}});var cf=j((cl,ag)=>{"use strict";cl.__esModule=!0;var KE=Ee(),Is={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(e){if(typeof e=="string"){var t=KE.indexOf(Is.methodMap,e.toLowerCase());t>=0?e=t:e=parseInt(e,10)}return e},log:function(e){if(e=Is.lookupLevel(e),typeof console!="undefined"&&Is.lookupLevel(Is.level)<=e){var t=Is.methodMap[e];console[t]||(t="log");for(var n=arguments.length,s=Array(n>1?n-1:0),o=1;o<n;o++)s[o-1]=arguments[o];console[t].apply(console,s)}}};cl.default=Is;ag.exports=cl.default});var lg=j(uf=>{"use strict";uf.__esModule=!0;uf.createNewLookupObject=JE;var GE=Ee();function JE(){for(var r=arguments.length,e=Array(r),t=0;t<r;t++)e[t]=arguments[t];return GE.extend.apply(void 0,[Object.create(null)].concat(e))}});var ff=j(Zo=>{"use strict";Zo.__esModule=!0;Zo.createProtoAccessControl=QE;Zo.resultIsAllowed=ZE;Zo.resetLoggedProperties=t2;function zE(r){return r&&r.__esModule?r:{default:r}}var cg=lg(),XE=cf(),YE=zE(XE),ul=Object.create(null);function QE(r){var e=Object.create(null);e.constructor=!1,e.__defineGetter__=!1,e.__defineSetter__=!1,e.__lookupGetter__=!1;var t=Object.create(null);return t.__proto__=!1,{properties:{whitelist:cg.createNewLookupObject(t,r.allowedProtoProperties),defaultValue:r.allowProtoPropertiesByDefault},methods:{whitelist:cg.createNewLookupObject(e,r.allowedProtoMethods),defaultValue:r.allowProtoMethodsByDefault}}}function ZE(r,e,t){return ug(typeof r=="function"?e.methods:e.properties,t)}function ug(r,e){return r.whitelist[e]!==void 0?r.whitelist[e]===!0:r.defaultValue!==void 0?r.defaultValue:(e2(e),!1)}function e2(r){ul[r]!==!0&&(ul[r]=!0,YE.default.log("error",'Handlebars: Access has been denied to resolve the property "'+r+`" because it is not an "own property" of its parent.
You can add a runtime option to disable the check or this warning:
See https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details`))}function t2(){Object.keys(ul).forEach(function(r){delete ul[r]})}});var dl=j(yt=>{"use strict";yt.__esModule=!0;yt.HandlebarsEnvironment=pf;function fg(r){return r&&r.__esModule?r:{default:r}}var rn=Ee(),r2=qe(),df=fg(r2),n2=af(),s2=ig(),o2=cf(),fl=fg(o2),i2=ff(),a2="4.7.8";yt.VERSION=a2;var l2=8;yt.COMPILER_REVISION=l2;var c2=7;yt.LAST_COMPATIBLE_COMPILER_REVISION=c2;var u2={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};yt.REVISION_CHANGES=u2;var hf="[object Object]";function pf(r,e,t){this.helpers=r||{},this.partials=e||{},this.decorators=t||{},n2.registerDefaultHelpers(this),s2.registerDefaultDecorators(this)}pf.prototype={constructor:pf,logger:fl.default,log:fl.default.log,registerHelper:function(e,t){if(rn.toString.call(e)===hf){if(t)throw new df.default("Arg not supported with multiple helpers");rn.extend(this.helpers,e)}else this.helpers[e]=t},unregisterHelper:function(e){delete this.helpers[e]},registerPartial:function(e,t){if(rn.toString.call(e)===hf)rn.extend(this.partials,e);else{if(typeof t=="undefined")throw new df.default('Attempting to register a partial called "'+e+'" as undefined');this.partials[e]=t}},unregisterPartial:function(e){delete this.partials[e]},registerDecorator:function(e,t){if(rn.toString.call(e)===hf){if(t)throw new df.default("Arg not supported with multiple decorators");rn.extend(this.decorators,e)}else this.decorators[e]=t},unregisterDecorator:function(e){delete this.decorators[e]},resetLoggedPropertyAccesses:function(){i2.resetLoggedProperties()}};var f2=fl.default.log;yt.log=f2;yt.createFrame=rn.createFrame;yt.logger=fl.default});var hg=j((hl,dg)=>{"use strict";hl.__esModule=!0;function mf(r){this.string=r}mf.prototype.toString=mf.prototype.toHTML=function(){return""+this.string};hl.default=mf;dg.exports=hl.default});var pg=j(gf=>{"use strict";gf.__esModule=!0;gf.wrapHelper=d2;function d2(r,e){if(typeof r!="function")return r;var t=function(){var s=arguments[arguments.length-1];return arguments[arguments.length-1]=e(s),r.apply(this,arguments)};return t}});var wg=j(pr=>{"use strict";pr.__esModule=!0;pr.checkRevision=b2;pr.template=w2;pr.wrapProgram=pl;pr.resolvePartial=_2;pr.invokePartial=S2;pr.noop=yg;function h2(r){return r&&r.__esModule?r:{default:r}}function p2(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t]);return e.default=r,e}var m2=Ee(),Dt=p2(m2),g2=qe(),Bt=h2(g2),Ft=dl(),mg=af(),y2=pg(),gg=ff();function b2(r){var e=r&&r[0]||1,t=Ft.COMPILER_REVISION;if(!(e>=Ft.LAST_COMPATIBLE_COMPILER_REVISION&&e<=Ft.COMPILER_REVISION))if(e<Ft.LAST_COMPATIBLE_COMPILER_REVISION){var n=Ft.REVISION_CHANGES[t],s=Ft.REVISION_CHANGES[e];throw new Bt.default("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+n+") or downgrade your runtime to an older version ("+s+").")}else throw new Bt.default("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+r[1]+").")}function w2(r,e){if(!e)throw new Bt.default("No environment passed to template");if(!r||!r.main)throw new Bt.default("Unknown template object: "+typeof r);r.main.decorator=r.main_d,e.VM.checkRevision(r.compiler);var t=r.compiler&&r.compiler[0]===7;function n(i,a,c){c.hash&&(a=Dt.extend({},a,c.hash),c.ids&&(c.ids[0]=!0)),i=e.VM.resolvePartial.call(this,i,a,c);var l=Dt.extend({},c,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),f=e.VM.invokePartial.call(this,i,a,l);if(f==null&&e.compile&&(c.partials[c.name]=e.compile(i,r.compilerOptions,e),f=c.partials[c.name](a,l)),f!=null){if(c.indent){for(var u=f.split(`
`),d=0,h=u.length;d<h&&!(!u[d]&&d+1===h);d++)u[d]=c.indent+u[d];f=u.join(`
`)}return f}else throw new Bt.default("The partial "+c.name+" could not be compiled when running in runtime-only mode")}var s={strict:function(a,c,l){if(!a||!(c in a))throw new Bt.default('"'+c+'" not defined in '+a,{loc:l});return s.lookupProperty(a,c)},lookupProperty:function(a,c){var l=a[c];if(l==null||Object.prototype.hasOwnProperty.call(a,c)||gg.resultIsAllowed(l,s.protoAccessControl,c))return l},lookup:function(a,c){for(var l=a.length,f=0;f<l;f++){var u=a[f]&&s.lookupProperty(a[f],c);if(u!=null)return a[f][c]}},lambda:function(a,c){return typeof a=="function"?a.call(c):a},escapeExpression:Dt.escapeExpression,invokePartial:n,fn:function(a){var c=r[a];return c.decorator=r[a+"_d"],c},programs:[],program:function(a,c,l,f,u){var d=this.programs[a],h=this.fn(a);return c||u||f||l?d=pl(this,a,h,c,l,f,u):d||(d=this.programs[a]=pl(this,a,h)),d},data:function(a,c){for(;a&&c--;)a=a._parent;return a},mergeIfNeeded:function(a,c){var l=a||c;return a&&c&&a!==c&&(l=Dt.extend({},c,a)),l},nullContext:Object.seal({}),noop:e.VM.noop,compilerInfo:r.compiler};function o(i){var a=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],c=a.data;o._setup(a),!a.partial&&r.useData&&(c=x2(i,c));var l=void 0,f=r.useBlockParams?[]:void 0;r.useDepths&&(a.depths?l=i!=a.depths[0]?[i].concat(a.depths):a.depths:l=[i]);function u(d){return""+r.main(s,d,s.helpers,s.partials,c,f,l)}return u=bg(r.main,u,s,a.depths||[],c,f),u(i,a)}return o.isTop=!0,o._setup=function(i){if(i.partial)s.protoAccessControl=i.protoAccessControl,s.helpers=i.helpers,s.partials=i.partials,s.decorators=i.decorators,s.hooks=i.hooks;else{var a=Dt.extend({},e.helpers,i.helpers);E2(a,s),s.helpers=a,r.usePartial&&(s.partials=s.mergeIfNeeded(i.partials,e.partials)),(r.usePartial||r.useDecorators)&&(s.decorators=Dt.extend({},e.decorators,i.decorators)),s.hooks={},s.protoAccessControl=gg.createProtoAccessControl(i);var c=i.allowCallsToHelperMissing||t;mg.moveHelperToHooks(s,"helperMissing",c),mg.moveHelperToHooks(s,"blockHelperMissing",c)}},o._child=function(i,a,c,l){if(r.useBlockParams&&!c)throw new Bt.default("must pass block params");if(r.useDepths&&!l)throw new Bt.default("must pass parent depths");return pl(s,i,r[i],a,0,c,l)},o}function pl(r,e,t,n,s,o,i){function a(c){var l=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],f=i;return i&&c!=i[0]&&!(c===r.nullContext&&i[0]===null)&&(f=[c].concat(i)),t(r,c,r.helpers,r.partials,l.data||n,o&&[l.blockParams].concat(o),f)}return a=bg(t,a,r,i,n,o),a.program=e,a.depth=i?i.length:0,a.blockParams=s||0,a}function _2(r,e,t){return r?!r.call&&!t.name&&(t.name=r,r=t.partials[r]):t.name==="@partial-block"?r=t.data["partial-block"]:r=t.partials[t.name],r}function S2(r,e,t){var n=t.data&&t.data["partial-block"];t.partial=!0,t.ids&&(t.data.contextPath=t.ids[0]||t.data.contextPath);var s=void 0;if(t.fn&&t.fn!==yg&&function(){t.data=Ft.createFrame(t.data);var o=t.fn;s=t.data["partial-block"]=function(a){var c=arguments.length<=1||arguments[1]===void 0?{}:arguments[1];return c.data=Ft.createFrame(c.data),c.data["partial-block"]=n,o(a,c)},o.partials&&(t.partials=Dt.extend({},t.partials,o.partials))}(),r===void 0&&s&&(r=s),r===void 0)throw new Bt.default("The partial "+t.name+" could not be found");if(r instanceof Function)return r(e,t)}function yg(){return""}function x2(r,e){return(!e||!("root"in e))&&(e=e?Ft.createFrame(e):{},e.root=r),e}function bg(r,e,t,n,s,o){if(r.decorator){var i={};e=r.decorator(e,i,t,n&&n[0],s,o,n),Dt.extend(e,i)}return e}function E2(r,e){Object.keys(r).forEach(function(t){var n=r[t];r[t]=v2(n,e)})}function v2(r,e){var t=e.lookupProperty;return y2.wrapHelper(r,function(n){return Dt.extend({lookupProperty:t},n)})}});var yf=j((ml,_g)=>{"use strict";ml.__esModule=!0;ml.default=function(r){(function(){typeof globalThis!="object"&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__)})();var e=globalThis.Handlebars;r.noConflict=function(){return globalThis.Handlebars===r&&(globalThis.Handlebars=e),r}};_g.exports=ml.default});var Ag=j((gl,vg)=>{"use strict";gl.__esModule=!0;function wf(r){return r&&r.__esModule?r:{default:r}}function _f(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t]);return e.default=r,e}var A2=dl(),Sg=_f(A2),C2=hg(),P2=wf(C2),R2=qe(),T2=wf(R2),I2=Ee(),bf=_f(I2),k2=wg(),xg=_f(k2),O2=yf(),M2=wf(O2);function Eg(){var r=new Sg.HandlebarsEnvironment;return bf.extend(r,Sg),r.SafeString=P2.default,r.Exception=T2.default,r.Utils=bf,r.escapeExpression=bf.escapeExpression,r.VM=xg,r.template=function(e){return xg.template(e,r)},r}var ei=Eg();ei.create=Eg;M2.default(ei);ei.default=ei;gl.default=ei;vg.exports=gl.default});var Sf=j((yl,Pg)=>{"use strict";yl.__esModule=!0;var Cg={helpers:{helperExpression:function(e){return e.type==="SubExpression"||(e.type==="MustacheStatement"||e.type==="BlockStatement")&&!!(e.params&&e.params.length||e.hash)},scopedId:function(e){return/^\.|this\b/.test(e.original)},simpleId:function(e){return e.parts.length===1&&!Cg.helpers.scopedId(e)&&!e.depth}}};yl.default=Cg;Pg.exports=yl.default});var Tg=j((bl,Rg)=>{"use strict";bl.__esModule=!0;var L2=function(){var r={trace:function(){},yy:{},symbols_:{error:2,root:3,program:4,EOF:5,program_repetition0:6,statement:7,mustache:8,block:9,rawBlock:10,partial:11,partialBlock:12,content:13,COMMENT:14,CONTENT:15,openRawBlock:16,rawBlock_repetition0:17,END_RAW_BLOCK:18,OPEN_RAW_BLOCK:19,helperName:20,openRawBlock_repetition0:21,openRawBlock_option0:22,CLOSE_RAW_BLOCK:23,openBlock:24,block_option0:25,closeBlock:26,openInverse:27,block_option1:28,OPEN_BLOCK:29,openBlock_repetition0:30,openBlock_option0:31,openBlock_option1:32,CLOSE:33,OPEN_INVERSE:34,openInverse_repetition0:35,openInverse_option0:36,openInverse_option1:37,openInverseChain:38,OPEN_INVERSE_CHAIN:39,openInverseChain_repetition0:40,openInverseChain_option0:41,openInverseChain_option1:42,inverseAndProgram:43,INVERSE:44,inverseChain:45,inverseChain_option0:46,OPEN_ENDBLOCK:47,OPEN:48,mustache_repetition0:49,mustache_option0:50,OPEN_UNESCAPED:51,mustache_repetition1:52,mustache_option1:53,CLOSE_UNESCAPED:54,OPEN_PARTIAL:55,partialName:56,partial_repetition0:57,partial_option0:58,openPartialBlock:59,OPEN_PARTIAL_BLOCK:60,openPartialBlock_repetition0:61,openPartialBlock_option0:62,param:63,sexpr:64,OPEN_SEXPR:65,sexpr_repetition0:66,sexpr_option0:67,CLOSE_SEXPR:68,hash:69,hash_repetition_plus0:70,hashSegment:71,ID:72,EQUALS:73,blockParams:74,OPEN_BLOCK_PARAMS:75,blockParams_repetition_plus0:76,CLOSE_BLOCK_PARAMS:77,path:78,dataName:79,STRING:80,NUMBER:81,BOOLEAN:82,UNDEFINED:83,NULL:84,DATA:85,pathSegments:86,SEP:87,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"COMMENT",15:"CONTENT",18:"END_RAW_BLOCK",19:"OPEN_RAW_BLOCK",23:"CLOSE_RAW_BLOCK",29:"OPEN_BLOCK",33:"CLOSE",34:"OPEN_INVERSE",39:"OPEN_INVERSE_CHAIN",44:"INVERSE",47:"OPEN_ENDBLOCK",48:"OPEN",51:"OPEN_UNESCAPED",54:"CLOSE_UNESCAPED",55:"OPEN_PARTIAL",60:"OPEN_PARTIAL_BLOCK",65:"OPEN_SEXPR",68:"CLOSE_SEXPR",72:"ID",73:"EQUALS",75:"OPEN_BLOCK_PARAMS",77:"CLOSE_BLOCK_PARAMS",80:"STRING",81:"NUMBER",82:"BOOLEAN",83:"UNDEFINED",84:"NULL",85:"DATA",87:"SEP"},productions_:[0,[3,2],[4,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[13,1],[10,3],[16,5],[9,4],[9,4],[24,6],[27,6],[38,6],[43,2],[45,3],[45,1],[26,3],[8,5],[8,5],[11,5],[12,3],[59,5],[63,1],[63,1],[64,5],[69,1],[71,3],[74,3],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[56,1],[56,1],[79,2],[78,1],[86,3],[86,1],[6,0],[6,2],[17,0],[17,2],[21,0],[21,2],[22,0],[22,1],[25,0],[25,1],[28,0],[28,1],[30,0],[30,2],[31,0],[31,1],[32,0],[32,1],[35,0],[35,2],[36,0],[36,1],[37,0],[37,1],[40,0],[40,2],[41,0],[41,1],[42,0],[42,1],[46,0],[46,1],[49,0],[49,2],[50,0],[50,1],[52,0],[52,2],[53,0],[53,1],[57,0],[57,2],[58,0],[58,1],[61,0],[61,2],[62,0],[62,1],[66,0],[66,2],[67,0],[67,1],[70,1],[70,2],[76,1],[76,2]],performAction:function(s,o,i,a,c,l,f){var u=l.length-1;switch(c){case 1:return l[u-1];case 2:this.$=a.prepareProgram(l[u]);break;case 3:this.$=l[u];break;case 4:this.$=l[u];break;case 5:this.$=l[u];break;case 6:this.$=l[u];break;case 7:this.$=l[u];break;case 8:this.$=l[u];break;case 9:this.$={type:"CommentStatement",value:a.stripComment(l[u]),strip:a.stripFlags(l[u],l[u]),loc:a.locInfo(this._$)};break;case 10:this.$={type:"ContentStatement",original:l[u],value:l[u],loc:a.locInfo(this._$)};break;case 11:this.$=a.prepareRawBlock(l[u-2],l[u-1],l[u],this._$);break;case 12:this.$={path:l[u-3],params:l[u-2],hash:l[u-1]};break;case 13:this.$=a.prepareBlock(l[u-3],l[u-2],l[u-1],l[u],!1,this._$);break;case 14:this.$=a.prepareBlock(l[u-3],l[u-2],l[u-1],l[u],!0,this._$);break;case 15:this.$={open:l[u-5],path:l[u-4],params:l[u-3],hash:l[u-2],blockParams:l[u-1],strip:a.stripFlags(l[u-5],l[u])};break;case 16:this.$={path:l[u-4],params:l[u-3],hash:l[u-2],blockParams:l[u-1],strip:a.stripFlags(l[u-5],l[u])};break;case 17:this.$={path:l[u-4],params:l[u-3],hash:l[u-2],blockParams:l[u-1],strip:a.stripFlags(l[u-5],l[u])};break;case 18:this.$={strip:a.stripFlags(l[u-1],l[u-1]),program:l[u]};break;case 19:var d=a.prepareBlock(l[u-2],l[u-1],l[u],l[u],!1,this._$),h=a.prepareProgram([d],l[u-1].loc);h.chained=!0,this.$={strip:l[u-2].strip,program:h,chain:!0};break;case 20:this.$=l[u];break;case 21:this.$={path:l[u-1],strip:a.stripFlags(l[u-2],l[u])};break;case 22:this.$=a.prepareMustache(l[u-3],l[u-2],l[u-1],l[u-4],a.stripFlags(l[u-4],l[u]),this._$);break;case 23:this.$=a.prepareMustache(l[u-3],l[u-2],l[u-1],l[u-4],a.stripFlags(l[u-4],l[u]),this._$);break;case 24:this.$={type:"PartialStatement",name:l[u-3],params:l[u-2],hash:l[u-1],indent:"",strip:a.stripFlags(l[u-4],l[u]),loc:a.locInfo(this._$)};break;case 25:this.$=a.preparePartialBlock(l[u-2],l[u-1],l[u],this._$);break;case 26:this.$={path:l[u-3],params:l[u-2],hash:l[u-1],strip:a.stripFlags(l[u-4],l[u])};break;case 27:this.$=l[u];break;case 28:this.$=l[u];break;case 29:this.$={type:"SubExpression",path:l[u-3],params:l[u-2],hash:l[u-1],loc:a.locInfo(this._$)};break;case 30:this.$={type:"Hash",pairs:l[u],loc:a.locInfo(this._$)};break;case 31:this.$={type:"HashPair",key:a.id(l[u-2]),value:l[u],loc:a.locInfo(this._$)};break;case 32:this.$=a.id(l[u-1]);break;case 33:this.$=l[u];break;case 34:this.$=l[u];break;case 35:this.$={type:"StringLiteral",value:l[u],original:l[u],loc:a.locInfo(this._$)};break;case 36:this.$={type:"NumberLiteral",value:Number(l[u]),original:Number(l[u]),loc:a.locInfo(this._$)};break;case 37:this.$={type:"BooleanLiteral",value:l[u]==="true",original:l[u]==="true",loc:a.locInfo(this._$)};break;case 38:this.$={type:"UndefinedLiteral",original:void 0,value:void 0,loc:a.locInfo(this._$)};break;case 39:this.$={type:"NullLiteral",original:null,value:null,loc:a.locInfo(this._$)};break;case 40:this.$=l[u];break;case 41:this.$=l[u];break;case 42:this.$=a.preparePath(!0,l[u],this._$);break;case 43:this.$=a.preparePath(!1,l[u],this._$);break;case 44:l[u-2].push({part:a.id(l[u]),original:l[u],separator:l[u-1]}),this.$=l[u-2];break;case 45:this.$=[{part:a.id(l[u]),original:l[u]}];break;case 46:this.$=[];break;case 47:l[u-1].push(l[u]);break;case 48:this.$=[];break;case 49:l[u-1].push(l[u]);break;case 50:this.$=[];break;case 51:l[u-1].push(l[u]);break;case 58:this.$=[];break;case 59:l[u-1].push(l[u]);break;case 64:this.$=[];break;case 65:l[u-1].push(l[u]);break;case 70:this.$=[];break;case 71:l[u-1].push(l[u]);break;case 78:this.$=[];break;case 79:l[u-1].push(l[u]);break;case 82:this.$=[];break;case 83:l[u-1].push(l[u]);break;case 86:this.$=[];break;case 87:l[u-1].push(l[u]);break;case 90:this.$=[];break;case 91:l[u-1].push(l[u]);break;case 94:this.$=[];break;case 95:l[u-1].push(l[u]);break;case 98:this.$=[l[u]];break;case 99:l[u-1].push(l[u]);break;case 100:this.$=[l[u]];break;case 101:l[u-1].push(l[u]);break}},table:[{3:1,4:2,5:[2,46],6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{1:[3]},{5:[1,4]},{5:[2,2],7:5,8:6,9:7,10:8,11:9,12:10,13:11,14:[1,12],15:[1,20],16:17,19:[1,23],24:15,27:16,29:[1,21],34:[1,22],39:[2,2],44:[2,2],47:[2,2],48:[1,13],51:[1,14],55:[1,18],59:19,60:[1,24]},{1:[2,1]},{5:[2,47],14:[2,47],15:[2,47],19:[2,47],29:[2,47],34:[2,47],39:[2,47],44:[2,47],47:[2,47],48:[2,47],51:[2,47],55:[2,47],60:[2,47]},{5:[2,3],14:[2,3],15:[2,3],19:[2,3],29:[2,3],34:[2,3],39:[2,3],44:[2,3],47:[2,3],48:[2,3],51:[2,3],55:[2,3],60:[2,3]},{5:[2,4],14:[2,4],15:[2,4],19:[2,4],29:[2,4],34:[2,4],39:[2,4],44:[2,4],47:[2,4],48:[2,4],51:[2,4],55:[2,4],60:[2,4]},{5:[2,5],14:[2,5],15:[2,5],19:[2,5],29:[2,5],34:[2,5],39:[2,5],44:[2,5],47:[2,5],48:[2,5],51:[2,5],55:[2,5],60:[2,5]},{5:[2,6],14:[2,6],15:[2,6],19:[2,6],29:[2,6],34:[2,6],39:[2,6],44:[2,6],47:[2,6],48:[2,6],51:[2,6],55:[2,6],60:[2,6]},{5:[2,7],14:[2,7],15:[2,7],19:[2,7],29:[2,7],34:[2,7],39:[2,7],44:[2,7],47:[2,7],48:[2,7],51:[2,7],55:[2,7],60:[2,7]},{5:[2,8],14:[2,8],15:[2,8],19:[2,8],29:[2,8],34:[2,8],39:[2,8],44:[2,8],47:[2,8],48:[2,8],51:[2,8],55:[2,8],60:[2,8]},{5:[2,9],14:[2,9],15:[2,9],19:[2,9],29:[2,9],34:[2,9],39:[2,9],44:[2,9],47:[2,9],48:[2,9],51:[2,9],55:[2,9],60:[2,9]},{20:25,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:36,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:37,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{4:38,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{15:[2,48],17:39,18:[2,48]},{20:41,56:40,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:44,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{5:[2,10],14:[2,10],15:[2,10],18:[2,10],19:[2,10],29:[2,10],34:[2,10],39:[2,10],44:[2,10],47:[2,10],48:[2,10],51:[2,10],55:[2,10],60:[2,10]},{20:45,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:46,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:47,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:41,56:48,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[2,78],49:49,65:[2,78],72:[2,78],80:[2,78],81:[2,78],82:[2,78],83:[2,78],84:[2,78],85:[2,78]},{23:[2,33],33:[2,33],54:[2,33],65:[2,33],68:[2,33],72:[2,33],75:[2,33],80:[2,33],81:[2,33],82:[2,33],83:[2,33],84:[2,33],85:[2,33]},{23:[2,34],33:[2,34],54:[2,34],65:[2,34],68:[2,34],72:[2,34],75:[2,34],80:[2,34],81:[2,34],82:[2,34],83:[2,34],84:[2,34],85:[2,34]},{23:[2,35],33:[2,35],54:[2,35],65:[2,35],68:[2,35],72:[2,35],75:[2,35],80:[2,35],81:[2,35],82:[2,35],83:[2,35],84:[2,35],85:[2,35]},{23:[2,36],33:[2,36],54:[2,36],65:[2,36],68:[2,36],72:[2,36],75:[2,36],80:[2,36],81:[2,36],82:[2,36],83:[2,36],84:[2,36],85:[2,36]},{23:[2,37],33:[2,37],54:[2,37],65:[2,37],68:[2,37],72:[2,37],75:[2,37],80:[2,37],81:[2,37],82:[2,37],83:[2,37],84:[2,37],85:[2,37]},{23:[2,38],33:[2,38],54:[2,38],65:[2,38],68:[2,38],72:[2,38],75:[2,38],80:[2,38],81:[2,38],82:[2,38],83:[2,38],84:[2,38],85:[2,38]},{23:[2,39],33:[2,39],54:[2,39],65:[2,39],68:[2,39],72:[2,39],75:[2,39],80:[2,39],81:[2,39],82:[2,39],83:[2,39],84:[2,39],85:[2,39]},{23:[2,43],33:[2,43],54:[2,43],65:[2,43],68:[2,43],72:[2,43],75:[2,43],80:[2,43],81:[2,43],82:[2,43],83:[2,43],84:[2,43],85:[2,43],87:[1,50]},{72:[1,35],86:51},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{52:52,54:[2,82],65:[2,82],72:[2,82],80:[2,82],81:[2,82],82:[2,82],83:[2,82],84:[2,82],85:[2,82]},{25:53,38:55,39:[1,57],43:56,44:[1,58],45:54,47:[2,54]},{28:59,43:60,44:[1,58],47:[2,56]},{13:62,15:[1,20],18:[1,61]},{33:[2,86],57:63,65:[2,86],72:[2,86],80:[2,86],81:[2,86],82:[2,86],83:[2,86],84:[2,86],85:[2,86]},{33:[2,40],65:[2,40],72:[2,40],80:[2,40],81:[2,40],82:[2,40],83:[2,40],84:[2,40],85:[2,40]},{33:[2,41],65:[2,41],72:[2,41],80:[2,41],81:[2,41],82:[2,41],83:[2,41],84:[2,41],85:[2,41]},{20:64,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:65,47:[1,66]},{30:67,33:[2,58],65:[2,58],72:[2,58],75:[2,58],80:[2,58],81:[2,58],82:[2,58],83:[2,58],84:[2,58],85:[2,58]},{33:[2,64],35:68,65:[2,64],72:[2,64],75:[2,64],80:[2,64],81:[2,64],82:[2,64],83:[2,64],84:[2,64],85:[2,64]},{21:69,23:[2,50],65:[2,50],72:[2,50],80:[2,50],81:[2,50],82:[2,50],83:[2,50],84:[2,50],85:[2,50]},{33:[2,90],61:70,65:[2,90],72:[2,90],80:[2,90],81:[2,90],82:[2,90],83:[2,90],84:[2,90],85:[2,90]},{20:74,33:[2,80],50:71,63:72,64:75,65:[1,43],69:73,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{72:[1,79]},{23:[2,42],33:[2,42],54:[2,42],65:[2,42],68:[2,42],72:[2,42],75:[2,42],80:[2,42],81:[2,42],82:[2,42],83:[2,42],84:[2,42],85:[2,42],87:[1,50]},{20:74,53:80,54:[2,84],63:81,64:75,65:[1,43],69:82,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:83,47:[1,66]},{47:[2,55]},{4:84,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{47:[2,20]},{20:85,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:86,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{26:87,47:[1,66]},{47:[2,57]},{5:[2,11],14:[2,11],15:[2,11],19:[2,11],29:[2,11],34:[2,11],39:[2,11],44:[2,11],47:[2,11],48:[2,11],51:[2,11],55:[2,11],60:[2,11]},{15:[2,49],18:[2,49]},{20:74,33:[2,88],58:88,63:89,64:75,65:[1,43],69:90,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{65:[2,94],66:91,68:[2,94],72:[2,94],80:[2,94],81:[2,94],82:[2,94],83:[2,94],84:[2,94],85:[2,94]},{5:[2,25],14:[2,25],15:[2,25],19:[2,25],29:[2,25],34:[2,25],39:[2,25],44:[2,25],47:[2,25],48:[2,25],51:[2,25],55:[2,25],60:[2,25]},{20:92,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,31:93,33:[2,60],63:94,64:75,65:[1,43],69:95,70:76,71:77,72:[1,78],75:[2,60],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,66],36:96,63:97,64:75,65:[1,43],69:98,70:76,71:77,72:[1,78],75:[2,66],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,22:99,23:[2,52],63:100,64:75,65:[1,43],69:101,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,92],62:102,63:103,64:75,65:[1,43],69:104,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,105]},{33:[2,79],65:[2,79],72:[2,79],80:[2,79],81:[2,79],82:[2,79],83:[2,79],84:[2,79],85:[2,79]},{33:[2,81]},{23:[2,27],33:[2,27],54:[2,27],65:[2,27],68:[2,27],72:[2,27],75:[2,27],80:[2,27],81:[2,27],82:[2,27],83:[2,27],84:[2,27],85:[2,27]},{23:[2,28],33:[2,28],54:[2,28],65:[2,28],68:[2,28],72:[2,28],75:[2,28],80:[2,28],81:[2,28],82:[2,28],83:[2,28],84:[2,28],85:[2,28]},{23:[2,30],33:[2,30],54:[2,30],68:[2,30],71:106,72:[1,107],75:[2,30]},{23:[2,98],33:[2,98],54:[2,98],68:[2,98],72:[2,98],75:[2,98]},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],73:[1,108],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{23:[2,44],33:[2,44],54:[2,44],65:[2,44],68:[2,44],72:[2,44],75:[2,44],80:[2,44],81:[2,44],82:[2,44],83:[2,44],84:[2,44],85:[2,44],87:[2,44]},{54:[1,109]},{54:[2,83],65:[2,83],72:[2,83],80:[2,83],81:[2,83],82:[2,83],83:[2,83],84:[2,83],85:[2,83]},{54:[2,85]},{5:[2,13],14:[2,13],15:[2,13],19:[2,13],29:[2,13],34:[2,13],39:[2,13],44:[2,13],47:[2,13],48:[2,13],51:[2,13],55:[2,13],60:[2,13]},{38:55,39:[1,57],43:56,44:[1,58],45:111,46:110,47:[2,76]},{33:[2,70],40:112,65:[2,70],72:[2,70],75:[2,70],80:[2,70],81:[2,70],82:[2,70],83:[2,70],84:[2,70],85:[2,70]},{47:[2,18]},{5:[2,14],14:[2,14],15:[2,14],19:[2,14],29:[2,14],34:[2,14],39:[2,14],44:[2,14],47:[2,14],48:[2,14],51:[2,14],55:[2,14],60:[2,14]},{33:[1,113]},{33:[2,87],65:[2,87],72:[2,87],80:[2,87],81:[2,87],82:[2,87],83:[2,87],84:[2,87],85:[2,87]},{33:[2,89]},{20:74,63:115,64:75,65:[1,43],67:114,68:[2,96],69:116,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,117]},{32:118,33:[2,62],74:119,75:[1,120]},{33:[2,59],65:[2,59],72:[2,59],75:[2,59],80:[2,59],81:[2,59],82:[2,59],83:[2,59],84:[2,59],85:[2,59]},{33:[2,61],75:[2,61]},{33:[2,68],37:121,74:122,75:[1,120]},{33:[2,65],65:[2,65],72:[2,65],75:[2,65],80:[2,65],81:[2,65],82:[2,65],83:[2,65],84:[2,65],85:[2,65]},{33:[2,67],75:[2,67]},{23:[1,123]},{23:[2,51],65:[2,51],72:[2,51],80:[2,51],81:[2,51],82:[2,51],83:[2,51],84:[2,51],85:[2,51]},{23:[2,53]},{33:[1,124]},{33:[2,91],65:[2,91],72:[2,91],80:[2,91],81:[2,91],82:[2,91],83:[2,91],84:[2,91],85:[2,91]},{33:[2,93]},{5:[2,22],14:[2,22],15:[2,22],19:[2,22],29:[2,22],34:[2,22],39:[2,22],44:[2,22],47:[2,22],48:[2,22],51:[2,22],55:[2,22],60:[2,22]},{23:[2,99],33:[2,99],54:[2,99],68:[2,99],72:[2,99],75:[2,99]},{73:[1,108]},{20:74,63:125,64:75,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,23],14:[2,23],15:[2,23],19:[2,23],29:[2,23],34:[2,23],39:[2,23],44:[2,23],47:[2,23],48:[2,23],51:[2,23],55:[2,23],60:[2,23]},{47:[2,19]},{47:[2,77]},{20:74,33:[2,72],41:126,63:127,64:75,65:[1,43],69:128,70:76,71:77,72:[1,78],75:[2,72],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,24],14:[2,24],15:[2,24],19:[2,24],29:[2,24],34:[2,24],39:[2,24],44:[2,24],47:[2,24],48:[2,24],51:[2,24],55:[2,24],60:[2,24]},{68:[1,129]},{65:[2,95],68:[2,95],72:[2,95],80:[2,95],81:[2,95],82:[2,95],83:[2,95],84:[2,95],85:[2,95]},{68:[2,97]},{5:[2,21],14:[2,21],15:[2,21],19:[2,21],29:[2,21],34:[2,21],39:[2,21],44:[2,21],47:[2,21],48:[2,21],51:[2,21],55:[2,21],60:[2,21]},{33:[1,130]},{33:[2,63]},{72:[1,132],76:131},{33:[1,133]},{33:[2,69]},{15:[2,12],18:[2,12]},{14:[2,26],15:[2,26],19:[2,26],29:[2,26],34:[2,26],47:[2,26],48:[2,26],51:[2,26],55:[2,26],60:[2,26]},{23:[2,31],33:[2,31],54:[2,31],68:[2,31],72:[2,31],75:[2,31]},{33:[2,74],42:134,74:135,75:[1,120]},{33:[2,71],65:[2,71],72:[2,71],75:[2,71],80:[2,71],81:[2,71],82:[2,71],83:[2,71],84:[2,71],85:[2,71]},{33:[2,73],75:[2,73]},{23:[2,29],33:[2,29],54:[2,29],65:[2,29],68:[2,29],72:[2,29],75:[2,29],80:[2,29],81:[2,29],82:[2,29],83:[2,29],84:[2,29],85:[2,29]},{14:[2,15],15:[2,15],19:[2,15],29:[2,15],34:[2,15],39:[2,15],44:[2,15],47:[2,15],48:[2,15],51:[2,15],55:[2,15],60:[2,15]},{72:[1,137],77:[1,136]},{72:[2,100],77:[2,100]},{14:[2,16],15:[2,16],19:[2,16],29:[2,16],34:[2,16],44:[2,16],47:[2,16],48:[2,16],51:[2,16],55:[2,16],60:[2,16]},{33:[1,138]},{33:[2,75]},{33:[2,32]},{72:[2,101],77:[2,101]},{14:[2,17],15:[2,17],19:[2,17],29:[2,17],34:[2,17],39:[2,17],44:[2,17],47:[2,17],48:[2,17],51:[2,17],55:[2,17],60:[2,17]}],defaultActions:{4:[2,1],54:[2,55],56:[2,20],60:[2,57],73:[2,81],82:[2,85],86:[2,18],90:[2,89],101:[2,53],104:[2,93],110:[2,19],111:[2,77],116:[2,97],119:[2,63],122:[2,69],135:[2,75],136:[2,32]},parseError:function(s,o){throw new Error(s)},parse:function(s){var o=this,i=[0],a=[null],c=[],l=this.table,f="",u=0,d=0,h=0,p=2,y=1;this.lexer.setInput(s),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,this.yy.parser=this,typeof this.lexer.yylloc=="undefined"&&(this.lexer.yylloc={});var g=this.lexer.yylloc;c.push(g);var S=this.lexer.options&&this.lexer.options.ranges;typeof this.yy.parseError=="function"&&(this.parseError=this.yy.parseError);function _(Le){i.length=i.length-2*Le,a.length=a.length-Le,c.length=c.length-Le}function A(){var Le;return Le=o.lexer.lex()||1,typeof Le!="number"&&(Le=o.symbols_[Le]||Le),Le}for(var P,x,R,k,M,q,F={},V,H,re,ve;;){if(R=i[i.length-1],this.defaultActions[R]?k=this.defaultActions[R]:((P===null||typeof P=="undefined")&&(P=A()),k=l[R]&&l[R][P]),typeof k=="undefined"||!k.length||!k[0]){var ue="";if(!h){ve=[];for(V in l[R])this.terminals_[V]&&V>2&&ve.push("'"+this.terminals_[V]+"'");this.lexer.showPosition?ue="Parse error on line "+(u+1)+`:
`+this.lexer.showPosition()+`
Expecting `+ve.join(", ")+", got '"+(this.terminals_[P]||P)+"'":ue="Parse error on line "+(u+1)+": Unexpected "+(P==1?"end of input":"'"+(this.terminals_[P]||P)+"'"),this.parseError(ue,{text:this.lexer.match,token:this.terminals_[P]||P,line:this.lexer.yylineno,loc:g,expected:ve})}}if(k[0]instanceof Array&&k.length>1)throw new Error("Parse Error: multiple actions possible at state: "+R+", token: "+P);switch(k[0]){case 1:i.push(P),a.push(this.lexer.yytext),c.push(this.lexer.yylloc),i.push(k[1]),P=null,x?(P=x,x=null):(d=this.lexer.yyleng,f=this.lexer.yytext,u=this.lexer.yylineno,g=this.lexer.yylloc,h>0&&h--);break;case 2:if(H=this.productions_[k[1]][1],F.$=a[a.length-H],F._$={first_line:c[c.length-(H||1)].first_line,last_line:c[c.length-1].last_line,first_column:c[c.length-(H||1)].first_column,last_column:c[c.length-1].last_column},S&&(F._$.range=[c[c.length-(H||1)].range[0],c[c.length-1].range[1]]),q=this.performAction.call(F,f,d,u,this.yy,k[1],a,c),typeof q!="undefined")return q;H&&(i=i.slice(0,-1*H*2),a=a.slice(0,-1*H),c=c.slice(0,-1*H)),i.push(this.productions_[k[1]][0]),a.push(F.$),c.push(F._$),re=l[i[i.length-2]][i[i.length-1]],i.push(re);break;case 3:return!0}}return!0}},e=function(){var n={EOF:1,parseError:function(o,i){if(this.yy.parser)this.yy.parser.parseError(o,i);else throw new Error(o)},setInput:function(o){return this._input=o,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var o=this._input[0];this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o;var i=o.match(/(?:\r\n?|\n).*/g);return i?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},unput:function(o){var i=o.length,a=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-i-1),this.offset-=i;var c=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var l=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===c.length?this.yylloc.first_column:0)+c[c.length-a.length].length-a[0].length:this.yylloc.first_column-i},this.options.ranges&&(this.yylloc.range=[l[0],l[0]+this.yyleng-i]),this},more:function(){return this._more=!0,this},less:function(o){this.unput(this.match.slice(o))},pastInput:function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var o=this.pastInput(),i=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+i+"^"},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var o,i,a,c,l,f;this._more||(this.yytext="",this.match="");for(var u=this._currentRules(),d=0;d<u.length&&(a=this._input.match(this.rules[u[d]]),!(a&&(!i||a[0].length>i[0].length)&&(i=a,c=d,!this.options.flex)));d++);return i?(f=i[0].match(/(?:\r\n?|\n).*/g),f&&(this.yylineno+=f.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:f?f[f.length-1].length-f[f.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+i[0].length},this.yytext+=i[0],this.match+=i[0],this.matches=i,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(i[0].length),this.matched+=i[0],o=this.performAction.call(this,this.yy,this,u[c],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),o||void 0):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var o=this.next();return typeof o!="undefined"?o:this.lex()},begin:function(o){this.conditionStack.push(o)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(o){this.begin(o)}};return n.options={},n.performAction=function(o,i,a,c){function l(u,d){return i.yytext=i.yytext.substring(u,i.yyleng-d+u)}var f=c;switch(a){case 0:if(i.yytext.slice(-2)==="\\\\"?(l(0,1),this.begin("mu")):i.yytext.slice(-1)==="\\"?(l(0,1),this.begin("emu")):this.begin("mu"),i.yytext)return 15;break;case 1:return 15;case 2:return this.popState(),15;break;case 3:return this.begin("raw"),15;break;case 4:return this.popState(),this.conditionStack[this.conditionStack.length-1]==="raw"?15:(l(5,9),"END_RAW_BLOCK");case 5:return 15;case 6:return this.popState(),14;break;case 7:return 65;case 8:return 68;case 9:return 19;case 10:return this.popState(),this.begin("raw"),23;break;case 11:return 55;case 12:return 60;case 13:return 29;case 14:return 47;case 15:return this.popState(),44;break;case 16:return this.popState(),44;break;case 17:return 34;case 18:return 39;case 19:return 51;case 20:return 48;case 21:this.unput(i.yytext),this.popState(),this.begin("com");break;case 22:return this.popState(),14;break;case 23:return 48;case 24:return 73;case 25:return 72;case 26:return 72;case 27:return 87;case 28:break;case 29:return this.popState(),54;break;case 30:return this.popState(),33;break;case 31:return i.yytext=l(1,2).replace(/\\"/g,'"'),80;break;case 32:return i.yytext=l(1,2).replace(/\\'/g,"'"),80;break;case 33:return 85;case 34:return 82;case 35:return 82;case 36:return 83;case 37:return 84;case 38:return 81;case 39:return 75;case 40:return 77;case 41:return 72;case 42:return i.yytext=i.yytext.replace(/\\([\\\]])/g,"$1"),72;break;case 43:return"INVALID";case 44:return 5}},n.rules=[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|\\\{\{|\\\\\{\{|$)))/,/^(?:\{\{\{\{(?=[^/]))/,/^(?:\{\{\{\{\/[^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=[=}\s\/.])\}\}\}\})/,/^(?:[^\x00]+?(?=(\{\{\{\{)))/,/^(?:[\s\S]*?--(~)?\}\})/,/^(?:\()/,/^(?:\))/,/^(?:\{\{\{\{)/,/^(?:\}\}\}\})/,/^(?:\{\{(~)?>)/,/^(?:\{\{(~)?#>)/,/^(?:\{\{(~)?#\*?)/,/^(?:\{\{(~)?\/)/,/^(?:\{\{(~)?\^\s*(~)?\}\})/,/^(?:\{\{(~)?\s*else\s*(~)?\}\})/,/^(?:\{\{(~)?\^)/,/^(?:\{\{(~)?\s*else\b)/,/^(?:\{\{(~)?\{)/,/^(?:\{\{(~)?&)/,/^(?:\{\{(~)?!--)/,/^(?:\{\{(~)?![\s\S]*?\}\})/,/^(?:\{\{(~)?\*?)/,/^(?:=)/,/^(?:\.\.)/,/^(?:\.(?=([=~}\s\/.)|])))/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}(~)?\}\})/,/^(?:(~)?\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=([~}\s)])))/,/^(?:false(?=([~}\s)])))/,/^(?:undefined(?=([~}\s)])))/,/^(?:null(?=([~}\s)])))/,/^(?:-?[0-9]+(?:\.[0-9]+)?(?=([~}\s)])))/,/^(?:as\s+\|)/,/^(?:\|)/,/^(?:([^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=([=~}\s\/.)|]))))/,/^(?:\[(\\\]|[^\]])*\])/,/^(?:.)/,/^(?:$)/],n.conditions={mu:{rules:[7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44],inclusive:!1},emu:{rules:[2],inclusive:!1},com:{rules:[6],inclusive:!1},raw:{rules:[3,4,5],inclusive:!1},INITIAL:{rules:[0,1,44],inclusive:!0}},n}();r.lexer=e;function t(){this.yy={}}return t.prototype=r,r.Parser=t,new t}();bl.default=L2;Rg.exports=bl.default});var Ef=j((Sl,Og)=>{"use strict";Sl.__esModule=!0;function N2(r){return r&&r.__esModule?r:{default:r}}var D2=qe(),xf=N2(D2);function wl(){this.parents=[]}wl.prototype={constructor:wl,mutating:!1,acceptKey:function(e,t){var n=this.accept(e[t]);if(this.mutating){if(n&&!wl.prototype[n.type])throw new xf.default('Unexpected node type "'+n.type+'" found when accepting '+t+" on "+e.type);e[t]=n}},acceptRequired:function(e,t){if(this.acceptKey(e,t),!e[t])throw new xf.default(e.type+" requires "+t)},acceptArray:function(e){for(var t=0,n=e.length;t<n;t++)this.acceptKey(e,t),e[t]||(e.splice(t,1),t--,n--)},accept:function(e){if(e){if(!this[e.type])throw new xf.default("Unknown type: "+e.type,e);this.current&&this.parents.unshift(this.current),this.current=e;var t=this[e.type](e);if(this.current=this.parents.shift(),!this.mutating||t)return t;if(t!==!1)return e}},Program:function(e){this.acceptArray(e.body)},MustacheStatement:_l,Decorator:_l,BlockStatement:Ig,DecoratorBlock:Ig,PartialStatement:kg,PartialBlockStatement:function(e){kg.call(this,e),this.acceptKey(e,"program")},ContentStatement:function(){},CommentStatement:function(){},SubExpression:_l,PathExpression:function(){},StringLiteral:function(){},NumberLiteral:function(){},BooleanLiteral:function(){},UndefinedLiteral:function(){},NullLiteral:function(){},Hash:function(e){this.acceptArray(e.pairs)},HashPair:function(e){this.acceptRequired(e,"value")}};function _l(r){this.acceptRequired(r,"path"),this.acceptArray(r.params),this.acceptKey(r,"hash")}function Ig(r){_l.call(this,r),this.acceptKey(r,"program"),this.acceptKey(r,"inverse")}function kg(r){this.acceptRequired(r,"name"),this.acceptArray(r.params),this.acceptKey(r,"hash")}Sl.default=wl;Og.exports=Sl.default});var Lg=j((xl,Mg)=>{"use strict";xl.__esModule=!0;function B2(r){return r&&r.__esModule?r:{default:r}}var F2=Ef(),U2=B2(F2);function bt(){var r=arguments.length<=0||arguments[0]===void 0?{}:arguments[0];this.options=r}bt.prototype=new U2.default;bt.prototype.Program=function(r){var e=!this.options.ignoreStandalone,t=!this.isRootSeen;this.isRootSeen=!0;for(var n=r.body,s=0,o=n.length;s<o;s++){var i=n[s],a=this.accept(i);if(a){var c=vf(n,s,t),l=Af(n,s,t),f=a.openStandalone&&c,u=a.closeStandalone&&l,d=a.inlineStandalone&&c&&l;a.close&&nn(n,s,!0),a.open&&mr(n,s,!0),e&&d&&(nn(n,s),mr(n,s)&&i.type==="PartialStatement"&&(i.indent=/([ \t]+$)/.exec(n[s-1].original)[1])),e&&f&&(nn((i.program||i.inverse).body),mr(n,s)),e&&u&&(nn(n,s),mr((i.inverse||i.program).body))}}return r};bt.prototype.BlockStatement=bt.prototype.DecoratorBlock=bt.prototype.PartialBlockStatement=function(r){this.accept(r.program),this.accept(r.inverse);var e=r.program||r.inverse,t=r.program&&r.inverse,n=t,s=t;if(t&&t.chained)for(n=t.body[0].program;s.chained;)s=s.body[s.body.length-1].program;var o={open:r.openStrip.open,close:r.closeStrip.close,openStandalone:Af(e.body),closeStandalone:vf((n||e).body)};if(r.openStrip.close&&nn(e.body,null,!0),t){var i=r.inverseStrip;i.open&&mr(e.body,null,!0),i.close&&nn(n.body,null,!0),r.closeStrip.open&&mr(s.body,null,!0),!this.options.ignoreStandalone&&vf(e.body)&&Af(n.body)&&(mr(e.body),nn(n.body))}else r.closeStrip.open&&mr(e.body,null,!0);return o};bt.prototype.Decorator=bt.prototype.MustacheStatement=function(r){return r.strip};bt.prototype.PartialStatement=bt.prototype.CommentStatement=function(r){var e=r.strip||{};return{inlineStandalone:!0,open:e.open,close:e.close}};function vf(r,e,t){e===void 0&&(e=r.length);var n=r[e-1],s=r[e-2];if(!n)return t;if(n.type==="ContentStatement")return(s||!t?/\r?\n\s*?$/:/(^|\r?\n)\s*?$/).test(n.original)}function Af(r,e,t){e===void 0&&(e=-1);var n=r[e+1],s=r[e+2];if(!n)return t;if(n.type==="ContentStatement")return(s||!t?/^\s*?\r?\n/:/^\s*?(\r?\n|$)/).test(n.original)}function nn(r,e,t){var n=r[e==null?0:e+1];if(!(!n||n.type!=="ContentStatement"||!t&&n.rightStripped)){var s=n.value;n.value=n.value.replace(t?/^\s+/:/^[ \t]*\r?\n?/,""),n.rightStripped=n.value!==s}}function mr(r,e,t){var n=r[e==null?r.length-1:e-1];if(!(!n||n.type!=="ContentStatement"||!t&&n.leftStripped)){var s=n.value;return n.value=n.value.replace(t?/\s+$/:/[ \t]+$/,""),n.leftStripped=n.value!==s,n.leftStripped}}xl.default=bt;Mg.exports=xl.default});var Ng=j($e=>{"use strict";$e.__esModule=!0;$e.SourceLocation=H2;$e.id=j2;$e.stripFlags=W2;$e.stripComment=V2;$e.preparePath=K2;$e.prepareMustache=G2;$e.prepareRawBlock=J2;$e.prepareBlock=z2;$e.prepareProgram=X2;$e.preparePartialBlock=Y2;function q2(r){return r&&r.__esModule?r:{default:r}}var $2=qe(),Cf=q2($2);function Pf(r,e){if(e=e.path?e.path.original:e,r.path.original!==e){var t={loc:r.path.loc};throw new Cf.default(r.path.original+" doesn't match "+e,t)}}function H2(r,e){this.source=r,this.start={line:e.first_line,column:e.first_column},this.end={line:e.last_line,column:e.last_column}}function j2(r){return/^\[.*\]$/.test(r)?r.substring(1,r.length-1):r}function W2(r,e){return{open:r.charAt(2)==="~",close:e.charAt(e.length-3)==="~"}}function V2(r){return r.replace(/^\{\{~?!-?-?/,"").replace(/-?-?~?\}\}$/,"")}function K2(r,e,t){t=this.locInfo(t);for(var n=r?"@":"",s=[],o=0,i=0,a=e.length;i<a;i++){var c=e[i].part,l=e[i].original!==c;if(n+=(e[i].separator||"")+c,!l&&(c===".."||c==="."||c==="this")){if(s.length>0)throw new Cf.default("Invalid path: "+n,{loc:t});c===".."&&o++}else s.push(c)}return{type:"PathExpression",data:r,depth:o,parts:s,original:n,loc:t}}function G2(r,e,t,n,s,o){var i=n.charAt(3)||n.charAt(2),a=i!=="{"&&i!=="&",c=/\*/.test(n);return{type:c?"Decorator":"MustacheStatement",path:r,params:e,hash:t,escaped:a,strip:s,loc:this.locInfo(o)}}function J2(r,e,t,n){Pf(r,t),n=this.locInfo(n);var s={type:"Program",body:e,strip:{},loc:n};return{type:"BlockStatement",path:r.path,params:r.params,hash:r.hash,program:s,openStrip:{},inverseStrip:{},closeStrip:{},loc:n}}function z2(r,e,t,n,s,o){n&&n.path&&Pf(r,n);var i=/\*/.test(r.open);e.blockParams=r.blockParams;var a=void 0,c=void 0;if(t){if(i)throw new Cf.default("Unexpected inverse block on decorator",t);t.chain&&(t.program.body[0].closeStrip=n.strip),c=t.strip,a=t.program}return s&&(s=a,a=e,e=s),{type:i?"DecoratorBlock":"BlockStatement",path:r.path,params:r.params,hash:r.hash,program:e,inverse:a,openStrip:r.strip,inverseStrip:c,closeStrip:n&&n.strip,loc:this.locInfo(o)}}function X2(r,e){if(!e&&r.length){var t=r[0].loc,n=r[r.length-1].loc;t&&n&&(e={source:t.source,start:{line:t.start.line,column:t.start.column},end:{line:n.end.line,column:n.end.column}})}return{type:"Program",body:r,strip:{},loc:e}}function Y2(r,e,t,n){return Pf(r,t),{type:"PartialBlockStatement",name:r.path,params:r.params,hash:r.hash,program:e,openStrip:r.strip,closeStrip:t&&t.strip,loc:this.locInfo(n)}}});var Fg=j(ti=>{"use strict";ti.__esModule=!0;ti.parseWithoutProcessing=Bg;ti.parse=o0;function Q2(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t]);return e.default=r,e}function Dg(r){return r&&r.__esModule?r:{default:r}}var Z2=Tg(),Rf=Dg(Z2),e0=Lg(),t0=Dg(e0),r0=Ng(),n0=Q2(r0),s0=Ee();ti.parser=Rf.default;var El={};s0.extend(El,n0);function Bg(r,e){if(r.type==="Program")return r;Rf.default.yy=El,El.locInfo=function(n){return new El.SourceLocation(e&&e.srcName,n)};var t=Rf.default.parse(r);return t}function o0(r,e){var t=Bg(r,e),n=new t0.default(e);return n.accept(t)}});var Hg=j(oi=>{"use strict";oi.__esModule=!0;oi.Compiler=Tf;oi.precompile=c0;oi.compile=u0;function qg(r){return r&&r.__esModule?r:{default:r}}var i0=qe(),ni=qg(i0),si=Ee(),a0=Sf(),ri=qg(a0),l0=[].slice;function Tf(){}Tf.prototype={compiler:Tf,equals:function(e){var t=this.opcodes.length;if(e.opcodes.length!==t)return!1;for(var n=0;n<t;n++){var s=this.opcodes[n],o=e.opcodes[n];if(s.opcode!==o.opcode||!$g(s.args,o.args))return!1}t=this.children.length;for(var n=0;n<t;n++)if(!this.children[n].equals(e.children[n]))return!1;return!0},guid:0,compile:function(e,t){return this.sourceNode=[],this.opcodes=[],this.children=[],this.options=t,this.stringParams=t.stringParams,this.trackIds=t.trackIds,t.blockParams=t.blockParams||[],t.knownHelpers=si.extend(Object.create(null),{helperMissing:!0,blockHelperMissing:!0,each:!0,if:!0,unless:!0,with:!0,log:!0,lookup:!0},t.knownHelpers),this.accept(e)},compileProgram:function(e){var t=new this.compiler,n=t.compile(e,this.options),s=this.guid++;return this.usePartial=this.usePartial||n.usePartial,this.children[s]=n,this.useDepths=this.useDepths||n.useDepths,s},accept:function(e){if(!this[e.type])throw new ni.default("Unknown type: "+e.type,e);this.sourceNode.unshift(e);var t=this[e.type](e);return this.sourceNode.shift(),t},Program:function(e){this.options.blockParams.unshift(e.blockParams);for(var t=e.body,n=t.length,s=0;s<n;s++)this.accept(t[s]);return this.options.blockParams.shift(),this.isSimple=n===1,this.blockParams=e.blockParams?e.blockParams.length:0,this},BlockStatement:function(e){Ug(e);var t=e.program,n=e.inverse;t=t&&this.compileProgram(t),n=n&&this.compileProgram(n);var s=this.classifySexpr(e);s==="helper"?this.helperSexpr(e,t,n):s==="simple"?(this.simpleSexpr(e),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("blockValue",e.path.original)):(this.ambiguousSexpr(e,t,n),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("ambiguousBlockValue")),this.opcode("append")},DecoratorBlock:function(e){var t=e.program&&this.compileProgram(e.program),n=this.setupFullMustacheParams(e,t,void 0),s=e.path;this.useDecorators=!0,this.opcode("registerDecorator",n.length,s.original)},PartialStatement:function(e){this.usePartial=!0;var t=e.program;t&&(t=this.compileProgram(e.program));var n=e.params;if(n.length>1)throw new ni.default("Unsupported number of partial arguments: "+n.length,e);n.length||(this.options.explicitPartialContext?this.opcode("pushLiteral","undefined"):n.push({type:"PathExpression",parts:[],depth:0}));var s=e.name.original,o=e.name.type==="SubExpression";o&&this.accept(e.name),this.setupFullMustacheParams(e,t,void 0,!0);var i=e.indent||"";this.options.preventIndent&&i&&(this.opcode("appendContent",i),i=""),this.opcode("invokePartial",o,s,i),this.opcode("append")},PartialBlockStatement:function(e){this.PartialStatement(e)},MustacheStatement:function(e){this.SubExpression(e),e.escaped&&!this.options.noEscape?this.opcode("appendEscaped"):this.opcode("append")},Decorator:function(e){this.DecoratorBlock(e)},ContentStatement:function(e){e.value&&this.opcode("appendContent",e.value)},CommentStatement:function(){},SubExpression:function(e){Ug(e);var t=this.classifySexpr(e);t==="simple"?this.simpleSexpr(e):t==="helper"?this.helperSexpr(e):this.ambiguousSexpr(e)},ambiguousSexpr:function(e,t,n){var s=e.path,o=s.parts[0],i=t!=null||n!=null;this.opcode("getContext",s.depth),this.opcode("pushProgram",t),this.opcode("pushProgram",n),s.strict=!0,this.accept(s),this.opcode("invokeAmbiguous",o,i)},simpleSexpr:function(e){var t=e.path;t.strict=!0,this.accept(t),this.opcode("resolvePossibleLambda")},helperSexpr:function(e,t,n){var s=this.setupFullMustacheParams(e,t,n),o=e.path,i=o.parts[0];if(this.options.knownHelpers[i])this.opcode("invokeKnownHelper",s.length,i);else{if(this.options.knownHelpersOnly)throw new ni.default("You specified knownHelpersOnly, but used the unknown helper "+i,e);o.strict=!0,o.falsy=!0,this.accept(o),this.opcode("invokeHelper",s.length,o.original,ri.default.helpers.simpleId(o))}},PathExpression:function(e){this.addDepth(e.depth),this.opcode("getContext",e.depth);var t=e.parts[0],n=ri.default.helpers.scopedId(e),s=!e.depth&&!n&&this.blockParamIndex(t);s?this.opcode("lookupBlockParam",s,e.parts):t?e.data?(this.options.data=!0,this.opcode("lookupData",e.depth,e.parts,e.strict)):this.opcode("lookupOnContext",e.parts,e.falsy,e.strict,n):this.opcode("pushContext")},StringLiteral:function(e){this.opcode("pushString",e.value)},NumberLiteral:function(e){this.opcode("pushLiteral",e.value)},BooleanLiteral:function(e){this.opcode("pushLiteral",e.value)},UndefinedLiteral:function(){this.opcode("pushLiteral","undefined")},NullLiteral:function(){this.opcode("pushLiteral","null")},Hash:function(e){var t=e.pairs,n=0,s=t.length;for(this.opcode("pushHash");n<s;n++)this.pushParam(t[n].value);for(;n--;)this.opcode("assignToHash",t[n].key);this.opcode("popHash")},opcode:function(e){this.opcodes.push({opcode:e,args:l0.call(arguments,1),loc:this.sourceNode[0].loc})},addDepth:function(e){e&&(this.useDepths=!0)},classifySexpr:function(e){var t=ri.default.helpers.simpleId(e.path),n=t&&!!this.blockParamIndex(e.path.parts[0]),s=!n&&ri.default.helpers.helperExpression(e),o=!n&&(s||t);if(o&&!s){var i=e.path.parts[0],a=this.options;a.knownHelpers[i]?s=!0:a.knownHelpersOnly&&(o=!1)}return s?"helper":o?"ambiguous":"simple"},pushParams:function(e){for(var t=0,n=e.length;t<n;t++)this.pushParam(e[t])},pushParam:function(e){var t=e.value!=null?e.value:e.original||"";if(this.stringParams)t.replace&&(t=t.replace(/^(\.?\.\/)*/g,"").replace(/\//g,".")),e.depth&&this.addDepth(e.depth),this.opcode("getContext",e.depth||0),this.opcode("pushStringParam",t,e.type),e.type==="SubExpression"&&this.accept(e);else{if(this.trackIds){var n=void 0;if(e.parts&&!ri.default.helpers.scopedId(e)&&!e.depth&&(n=this.blockParamIndex(e.parts[0])),n){var s=e.parts.slice(1).join(".");this.opcode("pushId","BlockParam",n,s)}else t=e.original||t,t.replace&&(t=t.replace(/^this(?:\.|$)/,"").replace(/^\.\//,"").replace(/^\.$/,"")),this.opcode("pushId",e.type,t)}this.accept(e)}},setupFullMustacheParams:function(e,t,n,s){var o=e.params;return this.pushParams(o),this.opcode("pushProgram",t),this.opcode("pushProgram",n),e.hash?this.accept(e.hash):this.opcode("emptyHash",s),o},blockParamIndex:function(e){for(var t=0,n=this.options.blockParams.length;t<n;t++){var s=this.options.blockParams[t],o=s&&si.indexOf(s,e);if(s&&o>=0)return[t,o]}}};function c0(r,e,t){if(r==null||typeof r!="string"&&r.type!=="Program")throw new ni.default("You must pass a string or Handlebars AST to Handlebars.precompile. You passed "+r);e=e||{},"data"in e||(e.data=!0),e.compat&&(e.useDepths=!0);var n=t.parse(r,e),s=new t.Compiler().compile(n,e);return new t.JavaScriptCompiler().compile(s,e)}function u0(r,e,t){if(e===void 0&&(e={}),r==null||typeof r!="string"&&r.type!=="Program")throw new ni.default("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+r);e=si.extend({},e),"data"in e||(e.data=!0),e.compat&&(e.useDepths=!0);var n=void 0;function s(){var i=t.parse(r,e),a=new t.Compiler().compile(i,e),c=new t.JavaScriptCompiler().compile(a,e,void 0,!0);return t.template(c)}function o(i,a){return n||(n=s()),n.call(this,i,a)}return o._setup=function(i){return n||(n=s()),n._setup(i)},o._child=function(i,a,c,l){return n||(n=s()),n._child(i,a,c,l)},o}function $g(r,e){if(r===e)return!0;if(si.isArray(r)&&si.isArray(e)&&r.length===e.length){for(var t=0;t<r.length;t++)if(!$g(r[t],e[t]))return!1;return!0}}function Ug(r){if(!r.path.parts){var e=r.path;r.path={type:"PathExpression",data:!1,depth:0,parts:[e.original+""],original:e.original+"",loc:e.loc}}}});var Wg=j(If=>{var jg="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");If.encode=function(r){if(0<=r&&r<jg.length)return jg[r];throw new TypeError("Must be between 0 and 63: "+r)};If.decode=function(r){var e=65,t=90,n=97,s=122,o=48,i=57,a=43,c=47,l=26,f=52;return e<=r&&r<=t?r-e:n<=r&&r<=s?r-n+l:o<=r&&r<=i?r-o+f:r==a?62:r==c?63:-1}});var Mf=j(Of=>{var Vg=Wg(),kf=5,Kg=1<<kf,Gg=Kg-1,Jg=Kg;function f0(r){return r<0?(-r<<1)+1:(r<<1)+0}function d0(r){var e=(r&1)===1,t=r>>1;return e?-t:t}Of.encode=function(e){var t="",n,s=f0(e);do n=s&Gg,s>>>=kf,s>0&&(n|=Jg),t+=Vg.encode(n);while(s>0);return t};Of.decode=function(e,t,n){var s=e.length,o=0,i=0,a,c;do{if(t>=s)throw new Error("Expected more digits in base 64 VLQ value.");if(c=Vg.decode(e.charCodeAt(t++)),c===-1)throw new Error("Invalid base64 digit: "+e.charAt(t-1));a=!!(c&Jg),c&=Gg,o=o+(c<<i),i+=kf}while(a);n.value=d0(o),n.rest=t}});var Ms=j(be=>{function h0(r,e,t){if(e in r)return r[e];if(arguments.length===3)return t;throw new Error('"'+e+'" is a required argument.')}be.getArg=h0;var zg=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,p0=/^data:.+\,.+$/;function ii(r){var e=r.match(zg);return e?{scheme:e[1],auth:e[2],host:e[3],port:e[4],path:e[5]}:null}be.urlParse=ii;function ks(r){var e="";return r.scheme&&(e+=r.scheme+":"),e+="//",r.auth&&(e+=r.auth+"@"),r.host&&(e+=r.host),r.port&&(e+=":"+r.port),r.path&&(e+=r.path),e}be.urlGenerate=ks;function Lf(r){var e=r,t=ii(r);if(t){if(!t.path)return r;e=t.path}for(var n=be.isAbsolute(e),s=e.split(/\/+/),o,i=0,a=s.length-1;a>=0;a--)o=s[a],o==="."?s.splice(a,1):o===".."?i++:i>0&&(o===""?(s.splice(a+1,i),i=0):(s.splice(a,2),i--));return e=s.join("/"),e===""&&(e=n?"/":"."),t?(t.path=e,ks(t)):e}be.normalize=Lf;function Xg(r,e){r===""&&(r="."),e===""&&(e=".");var t=ii(e),n=ii(r);if(n&&(r=n.path||"/"),t&&!t.scheme)return n&&(t.scheme=n.scheme),ks(t);if(t||e.match(p0))return e;if(n&&!n.host&&!n.path)return n.host=e,ks(n);var s=e.charAt(0)==="/"?e:Lf(r.replace(/\/+$/,"")+"/"+e);return n?(n.path=s,ks(n)):s}be.join=Xg;be.isAbsolute=function(r){return r.charAt(0)==="/"||zg.test(r)};function m0(r,e){r===""&&(r="."),r=r.replace(/\/$/,"");for(var t=0;e.indexOf(r+"/")!==0;){var n=r.lastIndexOf("/");if(n<0||(r=r.slice(0,n),r.match(/^([^\/]+:\/)?\/*$/)))return e;++t}return Array(t+1).join("../")+e.substr(r.length+1)}be.relative=m0;var Yg=function(){var r=Object.create(null);return!("__proto__"in r)}();function Qg(r){return r}function g0(r){return Zg(r)?"$"+r:r}be.toSetString=Yg?Qg:g0;function y0(r){return Zg(r)?r.slice(1):r}be.fromSetString=Yg?Qg:y0;function Zg(r){if(!r)return!1;var e=r.length;if(e<9||r.charCodeAt(e-1)!==95||r.charCodeAt(e-2)!==95||r.charCodeAt(e-3)!==111||r.charCodeAt(e-4)!==116||r.charCodeAt(e-5)!==111||r.charCodeAt(e-6)!==114||r.charCodeAt(e-7)!==112||r.charCodeAt(e-8)!==95||r.charCodeAt(e-9)!==95)return!1;for(var t=e-10;t>=0;t--)if(r.charCodeAt(t)!==36)return!1;return!0}function b0(r,e,t){var n=Os(r.source,e.source);return n!==0||(n=r.originalLine-e.originalLine,n!==0)||(n=r.originalColumn-e.originalColumn,n!==0||t)||(n=r.generatedColumn-e.generatedColumn,n!==0)||(n=r.generatedLine-e.generatedLine,n!==0)?n:Os(r.name,e.name)}be.compareByOriginalPositions=b0;function w0(r,e,t){var n=r.generatedLine-e.generatedLine;return n!==0||(n=r.generatedColumn-e.generatedColumn,n!==0||t)||(n=Os(r.source,e.source),n!==0)||(n=r.originalLine-e.originalLine,n!==0)||(n=r.originalColumn-e.originalColumn,n!==0)?n:Os(r.name,e.name)}be.compareByGeneratedPositionsDeflated=w0;function Os(r,e){return r===e?0:r===null?1:e===null?-1:r>e?1:-1}function _0(r,e){var t=r.generatedLine-e.generatedLine;return t!==0||(t=r.generatedColumn-e.generatedColumn,t!==0)||(t=Os(r.source,e.source),t!==0)||(t=r.originalLine-e.originalLine,t!==0)||(t=r.originalColumn-e.originalColumn,t!==0)?t:Os(r.name,e.name)}be.compareByGeneratedPositionsInflated=_0;function S0(r){return JSON.parse(r.replace(/^\)]}'[^\n]*\n/,""))}be.parseSourceMapInput=S0;function x0(r,e,t){if(e=e||"",r&&(r[r.length-1]!=="/"&&e[0]!=="/"&&(r+="/"),e=r+e),t){var n=ii(t);if(!n)throw new Error("sourceMapURL could not be parsed");if(n.path){var s=n.path.lastIndexOf("/");s>=0&&(n.path=n.path.substring(0,s+1))}e=Xg(ks(n),e)}return Lf(e)}be.computeSourceURL=x0});var Bf=j(ey=>{var Nf=Ms(),Df=Object.prototype.hasOwnProperty,sn=typeof Map!="undefined";function Ut(){this._array=[],this._set=sn?new Map:Object.create(null)}Ut.fromArray=function(e,t){for(var n=new Ut,s=0,o=e.length;s<o;s++)n.add(e[s],t);return n};Ut.prototype.size=function(){return sn?this._set.size:Object.getOwnPropertyNames(this._set).length};Ut.prototype.add=function(e,t){var n=sn?e:Nf.toSetString(e),s=sn?this.has(e):Df.call(this._set,n),o=this._array.length;(!s||t)&&this._array.push(e),s||(sn?this._set.set(e,o):this._set[n]=o)};Ut.prototype.has=function(e){if(sn)return this._set.has(e);var t=Nf.toSetString(e);return Df.call(this._set,t)};Ut.prototype.indexOf=function(e){if(sn){var t=this._set.get(e);if(t>=0)return t}else{var n=Nf.toSetString(e);if(Df.call(this._set,n))return this._set[n]}throw new Error('"'+e+'" is not in the set.')};Ut.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)};Ut.prototype.toArray=function(){return this._array.slice()};ey.ArraySet=Ut});var ny=j(ry=>{var ty=Ms();function E0(r,e){var t=r.generatedLine,n=e.generatedLine,s=r.generatedColumn,o=e.generatedColumn;return n>t||n==t&&o>=s||ty.compareByGeneratedPositionsInflated(r,e)<=0}function vl(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}vl.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)};vl.prototype.add=function(e){E0(this._last,e)?(this._last=e,this._array.push(e)):(this._sorted=!1,this._array.push(e))};vl.prototype.toArray=function(){return this._sorted||(this._array.sort(ty.compareByGeneratedPositionsInflated),this._sorted=!0),this._array};ry.MappingList=vl});var Ff=j(sy=>{var ai=Mf(),te=Ms(),Al=Bf().ArraySet,v0=ny().MappingList;function He(r){r||(r={}),this._file=te.getArg(r,"file",null),this._sourceRoot=te.getArg(r,"sourceRoot",null),this._skipValidation=te.getArg(r,"skipValidation",!1),this._sources=new Al,this._names=new Al,this._mappings=new v0,this._sourcesContents=null}He.prototype._version=3;He.fromSourceMap=function(e){var t=e.sourceRoot,n=new He({file:e.file,sourceRoot:t});return e.eachMapping(function(s){var o={generated:{line:s.generatedLine,column:s.generatedColumn}};s.source!=null&&(o.source=s.source,t!=null&&(o.source=te.relative(t,o.source)),o.original={line:s.originalLine,column:s.originalColumn},s.name!=null&&(o.name=s.name)),n.addMapping(o)}),e.sources.forEach(function(s){var o=s;t!==null&&(o=te.relative(t,s)),n._sources.has(o)||n._sources.add(o);var i=e.sourceContentFor(s);i!=null&&n.setSourceContent(s,i)}),n};He.prototype.addMapping=function(e){var t=te.getArg(e,"generated"),n=te.getArg(e,"original",null),s=te.getArg(e,"source",null),o=te.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,n,s,o),s!=null&&(s=String(s),this._sources.has(s)||this._sources.add(s)),o!=null&&(o=String(o),this._names.has(o)||this._names.add(o)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:n!=null&&n.line,originalColumn:n!=null&&n.column,source:s,name:o})};He.prototype.setSourceContent=function(e,t){var n=e;this._sourceRoot!=null&&(n=te.relative(this._sourceRoot,n)),t!=null?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[te.toSetString(n)]=t):this._sourcesContents&&(delete this._sourcesContents[te.toSetString(n)],Object.keys(this._sourcesContents).length===0&&(this._sourcesContents=null))};He.prototype.applySourceMap=function(e,t,n){var s=t;if(t==null){if(e.file==null)throw new Error(`SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`);s=e.file}var o=this._sourceRoot;o!=null&&(s=te.relative(o,s));var i=new Al,a=new Al;this._mappings.unsortedForEach(function(c){if(c.source===s&&c.originalLine!=null){var l=e.originalPositionFor({line:c.originalLine,column:c.originalColumn});l.source!=null&&(c.source=l.source,n!=null&&(c.source=te.join(n,c.source)),o!=null&&(c.source=te.relative(o,c.source)),c.originalLine=l.line,c.originalColumn=l.column,l.name!=null&&(c.name=l.name))}var f=c.source;f!=null&&!i.has(f)&&i.add(f);var u=c.name;u!=null&&!a.has(u)&&a.add(u)},this),this._sources=i,this._names=a,e.sources.forEach(function(c){var l=e.sourceContentFor(c);l!=null&&(n!=null&&(c=te.join(n,c)),o!=null&&(c=te.relative(o,c)),this.setSourceContent(c,l))},this)};He.prototype._validateMapping=function(e,t,n,s){if(t&&typeof t.line!="number"&&typeof t.column!="number")throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if(!(e&&"line"in e&&"column"in e&&e.line>0&&e.column>=0&&!t&&!n&&!s)){if(e&&"line"in e&&"column"in e&&t&&"line"in t&&"column"in t&&e.line>0&&e.column>=0&&t.line>0&&t.column>=0&&n)return;throw new Error("Invalid mapping: "+JSON.stringify({generated:e,source:n,original:t,name:s}))}};He.prototype._serializeMappings=function(){for(var e=0,t=1,n=0,s=0,o=0,i=0,a="",c,l,f,u,d=this._mappings.toArray(),h=0,p=d.length;h<p;h++){if(l=d[h],c="",l.generatedLine!==t)for(e=0;l.generatedLine!==t;)c+=";",t++;else if(h>0){if(!te.compareByGeneratedPositionsInflated(l,d[h-1]))continue;c+=","}c+=ai.encode(l.generatedColumn-e),e=l.generatedColumn,l.source!=null&&(u=this._sources.indexOf(l.source),c+=ai.encode(u-i),i=u,c+=ai.encode(l.originalLine-1-s),s=l.originalLine-1,c+=ai.encode(l.originalColumn-n),n=l.originalColumn,l.name!=null&&(f=this._names.indexOf(l.name),c+=ai.encode(f-o),o=f)),a+=c}return a};He.prototype._generateSourcesContent=function(e,t){return e.map(function(n){if(!this._sourcesContents)return null;t!=null&&(n=te.relative(t,n));var s=te.toSetString(n);return Object.prototype.hasOwnProperty.call(this._sourcesContents,s)?this._sourcesContents[s]:null},this)};He.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return this._file!=null&&(e.file=this._file),this._sourceRoot!=null&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e};He.prototype.toString=function(){return JSON.stringify(this.toJSON())};sy.SourceMapGenerator=He});var oy=j(on=>{on.GREATEST_LOWER_BOUND=1;on.LEAST_UPPER_BOUND=2;function Uf(r,e,t,n,s,o){var i=Math.floor((e-r)/2)+r,a=s(t,n[i],!0);return a===0?i:a>0?e-i>1?Uf(i,e,t,n,s,o):o==on.LEAST_UPPER_BOUND?e<n.length?e:-1:i:i-r>1?Uf(r,i,t,n,s,o):o==on.LEAST_UPPER_BOUND?i:r<0?-1:r}on.search=function(e,t,n,s){if(t.length===0)return-1;var o=Uf(-1,t.length,e,t,n,s||on.GREATEST_LOWER_BOUND);if(o<0)return-1;for(;o-1>=0&&n(t[o],t[o-1],!0)===0;)--o;return o}});var ay=j(iy=>{function qf(r,e,t){var n=r[e];r[e]=r[t],r[t]=n}function A0(r,e){return Math.round(r+Math.random()*(e-r))}function $f(r,e,t,n){if(t<n){var s=A0(t,n),o=t-1;qf(r,s,n);for(var i=r[n],a=t;a<n;a++)e(r[a],i)<=0&&(o+=1,qf(r,o,a));qf(r,o+1,a);var c=o+1;$f(r,e,t,c-1),$f(r,e,c+1,n)}}iy.quickSort=function(r,e){$f(r,e,0,r.length-1)}});var cy=j(Cl=>{var O=Ms(),Hf=oy(),Ls=Bf().ArraySet,C0=Mf(),li=ay().quickSort;function G(r,e){var t=r;return typeof r=="string"&&(t=O.parseSourceMapInput(r)),t.sections!=null?new Ye(t,e):new me(t,e)}G.fromSourceMap=function(r,e){return me.fromSourceMap(r,e)};G.prototype._version=3;G.prototype.__generatedMappings=null;Object.defineProperty(G.prototype,"_generatedMappings",{configurable:!0,enumerable:!0,get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}});G.prototype.__originalMappings=null;Object.defineProperty(G.prototype,"_originalMappings",{configurable:!0,enumerable:!0,get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}});G.prototype._charIsMappingSeparator=function(e,t){var n=e.charAt(t);return n===";"||n===","};G.prototype._parseMappings=function(e,t){throw new Error("Subclasses must implement _parseMappings")};G.GENERATED_ORDER=1;G.ORIGINAL_ORDER=2;G.GREATEST_LOWER_BOUND=1;G.LEAST_UPPER_BOUND=2;G.prototype.eachMapping=function(e,t,n){var s=t||null,o=n||G.GENERATED_ORDER,i;switch(o){case G.GENERATED_ORDER:i=this._generatedMappings;break;case G.ORIGINAL_ORDER:i=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var a=this.sourceRoot;i.map(function(c){var l=c.source===null?null:this._sources.at(c.source);return l=O.computeSourceURL(a,l,this._sourceMapURL),{source:l,generatedLine:c.generatedLine,generatedColumn:c.generatedColumn,originalLine:c.originalLine,originalColumn:c.originalColumn,name:c.name===null?null:this._names.at(c.name)}},this).forEach(e,s)};G.prototype.allGeneratedPositionsFor=function(e){var t=O.getArg(e,"line"),n={source:O.getArg(e,"source"),originalLine:t,originalColumn:O.getArg(e,"column",0)};if(n.source=this._findSourceIndex(n.source),n.source<0)return[];var s=[],o=this._findMapping(n,this._originalMappings,"originalLine","originalColumn",O.compareByOriginalPositions,Hf.LEAST_UPPER_BOUND);if(o>=0){var i=this._originalMappings[o];if(e.column===void 0)for(var a=i.originalLine;i&&i.originalLine===a;)s.push({line:O.getArg(i,"generatedLine",null),column:O.getArg(i,"generatedColumn",null),lastColumn:O.getArg(i,"lastGeneratedColumn",null)}),i=this._originalMappings[++o];else for(var c=i.originalColumn;i&&i.originalLine===t&&i.originalColumn==c;)s.push({line:O.getArg(i,"generatedLine",null),column:O.getArg(i,"generatedColumn",null),lastColumn:O.getArg(i,"lastGeneratedColumn",null)}),i=this._originalMappings[++o]}return s};Cl.SourceMapConsumer=G;function me(r,e){var t=r;typeof r=="string"&&(t=O.parseSourceMapInput(r));var n=O.getArg(t,"version"),s=O.getArg(t,"sources"),o=O.getArg(t,"names",[]),i=O.getArg(t,"sourceRoot",null),a=O.getArg(t,"sourcesContent",null),c=O.getArg(t,"mappings"),l=O.getArg(t,"file",null);if(n!=this._version)throw new Error("Unsupported version: "+n);i&&(i=O.normalize(i)),s=s.map(String).map(O.normalize).map(function(f){return i&&O.isAbsolute(i)&&O.isAbsolute(f)?O.relative(i,f):f}),this._names=Ls.fromArray(o.map(String),!0),this._sources=Ls.fromArray(s,!0),this._absoluteSources=this._sources.toArray().map(function(f){return O.computeSourceURL(i,f,e)}),this.sourceRoot=i,this.sourcesContent=a,this._mappings=c,this._sourceMapURL=e,this.file=l}me.prototype=Object.create(G.prototype);me.prototype.consumer=G;me.prototype._findSourceIndex=function(r){var e=r;if(this.sourceRoot!=null&&(e=O.relative(this.sourceRoot,e)),this._sources.has(e))return this._sources.indexOf(e);var t;for(t=0;t<this._absoluteSources.length;++t)if(this._absoluteSources[t]==r)return t;return-1};me.fromSourceMap=function(e,t){var n=Object.create(me.prototype),s=n._names=Ls.fromArray(e._names.toArray(),!0),o=n._sources=Ls.fromArray(e._sources.toArray(),!0);n.sourceRoot=e._sourceRoot,n.sourcesContent=e._generateSourcesContent(n._sources.toArray(),n.sourceRoot),n.file=e._file,n._sourceMapURL=t,n._absoluteSources=n._sources.toArray().map(function(h){return O.computeSourceURL(n.sourceRoot,h,t)});for(var i=e._mappings.toArray().slice(),a=n.__generatedMappings=[],c=n.__originalMappings=[],l=0,f=i.length;l<f;l++){var u=i[l],d=new ly;d.generatedLine=u.generatedLine,d.generatedColumn=u.generatedColumn,u.source&&(d.source=o.indexOf(u.source),d.originalLine=u.originalLine,d.originalColumn=u.originalColumn,u.name&&(d.name=s.indexOf(u.name)),c.push(d)),a.push(d)}return li(n.__originalMappings,O.compareByOriginalPositions),n};me.prototype._version=3;Object.defineProperty(me.prototype,"sources",{get:function(){return this._absoluteSources.slice()}});function ly(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}me.prototype._parseMappings=function(e,t){for(var n=1,s=0,o=0,i=0,a=0,c=0,l=e.length,f=0,u={},d={},h=[],p=[],y,g,S,_,A;f<l;)if(e.charAt(f)===";")n++,f++,s=0;else if(e.charAt(f)===",")f++;else{for(y=new ly,y.generatedLine=n,_=f;_<l&&!this._charIsMappingSeparator(e,_);_++);if(g=e.slice(f,_),S=u[g],S)f+=g.length;else{for(S=[];f<_;)C0.decode(e,f,d),A=d.value,f=d.rest,S.push(A);if(S.length===2)throw new Error("Found a source, but no line and column");if(S.length===3)throw new Error("Found a source and line, but no column");u[g]=S}y.generatedColumn=s+S[0],s=y.generatedColumn,S.length>1&&(y.source=a+S[1],a+=S[1],y.originalLine=o+S[2],o=y.originalLine,y.originalLine+=1,y.originalColumn=i+S[3],i=y.originalColumn,S.length>4&&(y.name=c+S[4],c+=S[4])),p.push(y),typeof y.originalLine=="number"&&h.push(y)}li(p,O.compareByGeneratedPositionsDeflated),this.__generatedMappings=p,li(h,O.compareByOriginalPositions),this.__originalMappings=h};me.prototype._findMapping=function(e,t,n,s,o,i){if(e[n]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+e[n]);if(e[s]<0)throw new TypeError("Column must be greater than or equal to 0, got "+e[s]);return Hf.search(e,t,o,i)};me.prototype.computeColumnSpans=function(){for(var e=0;e<this._generatedMappings.length;++e){var t=this._generatedMappings[e];if(e+1<this._generatedMappings.length){var n=this._generatedMappings[e+1];if(t.generatedLine===n.generatedLine){t.lastGeneratedColumn=n.generatedColumn-1;continue}}t.lastGeneratedColumn=1/0}};me.prototype.originalPositionFor=function(e){var t={generatedLine:O.getArg(e,"line"),generatedColumn:O.getArg(e,"column")},n=this._findMapping(t,this._generatedMappings,"generatedLine","generatedColumn",O.compareByGeneratedPositionsDeflated,O.getArg(e,"bias",G.GREATEST_LOWER_BOUND));if(n>=0){var s=this._generatedMappings[n];if(s.generatedLine===t.generatedLine){var o=O.getArg(s,"source",null);o!==null&&(o=this._sources.at(o),o=O.computeSourceURL(this.sourceRoot,o,this._sourceMapURL));var i=O.getArg(s,"name",null);return i!==null&&(i=this._names.at(i)),{source:o,line:O.getArg(s,"originalLine",null),column:O.getArg(s,"originalColumn",null),name:i}}}return{source:null,line:null,column:null,name:null}};me.prototype.hasContentsOfAllSources=function(){return this.sourcesContent?this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(e){return e==null}):!1};me.prototype.sourceContentFor=function(e,t){if(!this.sourcesContent)return null;var n=this._findSourceIndex(e);if(n>=0)return this.sourcesContent[n];var s=e;this.sourceRoot!=null&&(s=O.relative(this.sourceRoot,s));var o;if(this.sourceRoot!=null&&(o=O.urlParse(this.sourceRoot))){var i=s.replace(/^file:\/\//,"");if(o.scheme=="file"&&this._sources.has(i))return this.sourcesContent[this._sources.indexOf(i)];if((!o.path||o.path=="/")&&this._sources.has("/"+s))return this.sourcesContent[this._sources.indexOf("/"+s)]}if(t)return null;throw new Error('"'+s+'" is not in the SourceMap.')};me.prototype.generatedPositionFor=function(e){var t=O.getArg(e,"source");if(t=this._findSourceIndex(t),t<0)return{line:null,column:null,lastColumn:null};var n={source:t,originalLine:O.getArg(e,"line"),originalColumn:O.getArg(e,"column")},s=this._findMapping(n,this._originalMappings,"originalLine","originalColumn",O.compareByOriginalPositions,O.getArg(e,"bias",G.GREATEST_LOWER_BOUND));if(s>=0){var o=this._originalMappings[s];if(o.source===n.source)return{line:O.getArg(o,"generatedLine",null),column:O.getArg(o,"generatedColumn",null),lastColumn:O.getArg(o,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}};Cl.BasicSourceMapConsumer=me;function Ye(r,e){var t=r;typeof r=="string"&&(t=O.parseSourceMapInput(r));var n=O.getArg(t,"version"),s=O.getArg(t,"sections");if(n!=this._version)throw new Error("Unsupported version: "+n);this._sources=new Ls,this._names=new Ls;var o={line:-1,column:0};this._sections=s.map(function(i){if(i.url)throw new Error("Support for url field in sections not implemented.");var a=O.getArg(i,"offset"),c=O.getArg(a,"line"),l=O.getArg(a,"column");if(c<o.line||c===o.line&&l<o.column)throw new Error("Section offsets must be ordered and non-overlapping.");return o=a,{generatedOffset:{generatedLine:c+1,generatedColumn:l+1},consumer:new G(O.getArg(i,"map"),e)}})}Ye.prototype=Object.create(G.prototype);Ye.prototype.constructor=G;Ye.prototype._version=3;Object.defineProperty(Ye.prototype,"sources",{get:function(){for(var r=[],e=0;e<this._sections.length;e++)for(var t=0;t<this._sections[e].consumer.sources.length;t++)r.push(this._sections[e].consumer.sources[t]);return r}});Ye.prototype.originalPositionFor=function(e){var t={generatedLine:O.getArg(e,"line"),generatedColumn:O.getArg(e,"column")},n=Hf.search(t,this._sections,function(o,i){var a=o.generatedLine-i.generatedOffset.generatedLine;return a||o.generatedColumn-i.generatedOffset.generatedColumn}),s=this._sections[n];return s?s.consumer.originalPositionFor({line:t.generatedLine-(s.generatedOffset.generatedLine-1),column:t.generatedColumn-(s.generatedOffset.generatedLine===t.generatedLine?s.generatedOffset.generatedColumn-1:0),bias:e.bias}):{source:null,line:null,column:null,name:null}};Ye.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(e){return e.consumer.hasContentsOfAllSources()})};Ye.prototype.sourceContentFor=function(e,t){for(var n=0;n<this._sections.length;n++){var s=this._sections[n],o=s.consumer.sourceContentFor(e,!0);if(o)return o}if(t)return null;throw new Error('"'+e+'" is not in the SourceMap.')};Ye.prototype.generatedPositionFor=function(e){for(var t=0;t<this._sections.length;t++){var n=this._sections[t];if(n.consumer._findSourceIndex(O.getArg(e,"source"))!==-1){var s=n.consumer.generatedPositionFor(e);if(s){var o={line:s.line+(n.generatedOffset.generatedLine-1),column:s.column+(n.generatedOffset.generatedLine===s.line?n.generatedOffset.generatedColumn-1:0)};return o}}}return{line:null,column:null}};Ye.prototype._parseMappings=function(e,t){this.__generatedMappings=[],this.__originalMappings=[];for(var n=0;n<this._sections.length;n++)for(var s=this._sections[n],o=s.consumer._generatedMappings,i=0;i<o.length;i++){var a=o[i],c=s.consumer._sources.at(a.source);c=O.computeSourceURL(s.consumer.sourceRoot,c,this._sourceMapURL),this._sources.add(c),c=this._sources.indexOf(c);var l=null;a.name&&(l=s.consumer._names.at(a.name),this._names.add(l),l=this._names.indexOf(l));var f={source:c,generatedLine:a.generatedLine+(s.generatedOffset.generatedLine-1),generatedColumn:a.generatedColumn+(s.generatedOffset.generatedLine===a.generatedLine?s.generatedOffset.generatedColumn-1:0),originalLine:a.originalLine,originalColumn:a.originalColumn,name:l};this.__generatedMappings.push(f),typeof f.originalLine=="number"&&this.__originalMappings.push(f)}li(this.__generatedMappings,O.compareByGeneratedPositionsDeflated),li(this.__originalMappings,O.compareByOriginalPositions)};Cl.IndexedSourceMapConsumer=Ye});var fy=j(uy=>{var P0=Ff().SourceMapGenerator,Pl=Ms(),R0=/(\r?\n)/,T0=10,Ns="$$$isSourceNode$$$";function Me(r,e,t,n,s){this.children=[],this.sourceContents={},this.line=r==null?null:r,this.column=e==null?null:e,this.source=t==null?null:t,this.name=s==null?null:s,this[Ns]=!0,n!=null&&this.add(n)}Me.fromStringWithSourceMap=function(e,t,n){var s=new Me,o=e.split(R0),i=0,a=function(){var d=p(),h=p()||"";return d+h;function p(){return i<o.length?o[i++]:void 0}},c=1,l=0,f=null;return t.eachMapping(function(d){if(f!==null)if(c<d.generatedLine)u(f,a()),c++,l=0;else{var h=o[i]||"",p=h.substr(0,d.generatedColumn-l);o[i]=h.substr(d.generatedColumn-l),l=d.generatedColumn,u(f,p),f=d;return}for(;c<d.generatedLine;)s.add(a()),c++;if(l<d.generatedColumn){var h=o[i]||"";s.add(h.substr(0,d.generatedColumn)),o[i]=h.substr(d.generatedColumn),l=d.generatedColumn}f=d},this),i<o.length&&(f&&u(f,a()),s.add(o.splice(i).join(""))),t.sources.forEach(function(d){var h=t.sourceContentFor(d);h!=null&&(n!=null&&(d=Pl.join(n,d)),s.setSourceContent(d,h))}),s;function u(d,h){if(d===null||d.source===void 0)s.add(h);else{var p=n?Pl.join(n,d.source):d.source;s.add(new Me(d.originalLine,d.originalColumn,p,h,d.name))}}};Me.prototype.add=function(e){if(Array.isArray(e))e.forEach(function(t){this.add(t)},this);else if(e[Ns]||typeof e=="string")e&&this.children.push(e);else throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);return this};Me.prototype.prepend=function(e){if(Array.isArray(e))for(var t=e.length-1;t>=0;t--)this.prepend(e[t]);else if(e[Ns]||typeof e=="string")this.children.unshift(e);else throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);return this};Me.prototype.walk=function(e){for(var t,n=0,s=this.children.length;n<s;n++)t=this.children[n],t[Ns]?t.walk(e):t!==""&&e(t,{source:this.source,line:this.line,column:this.column,name:this.name})};Me.prototype.join=function(e){var t,n,s=this.children.length;if(s>0){for(t=[],n=0;n<s-1;n++)t.push(this.children[n]),t.push(e);t.push(this.children[n]),this.children=t}return this};Me.prototype.replaceRight=function(e,t){var n=this.children[this.children.length-1];return n[Ns]?n.replaceRight(e,t):typeof n=="string"?this.children[this.children.length-1]=n.replace(e,t):this.children.push("".replace(e,t)),this};Me.prototype.setSourceContent=function(e,t){this.sourceContents[Pl.toSetString(e)]=t};Me.prototype.walkSourceContents=function(e){for(var t=0,n=this.children.length;t<n;t++)this.children[t][Ns]&&this.children[t].walkSourceContents(e);for(var s=Object.keys(this.sourceContents),t=0,n=s.length;t<n;t++)e(Pl.fromSetString(s[t]),this.sourceContents[s[t]])};Me.prototype.toString=function(){var e="";return this.walk(function(t){e+=t}),e};Me.prototype.toStringWithSourceMap=function(e){var t={code:"",line:1,column:0},n=new P0(e),s=!1,o=null,i=null,a=null,c=null;return this.walk(function(l,f){t.code+=l,f.source!==null&&f.line!==null&&f.column!==null?((o!==f.source||i!==f.line||a!==f.column||c!==f.name)&&n.addMapping({source:f.source,original:{line:f.line,column:f.column},generated:{line:t.line,column:t.column},name:f.name}),o=f.source,i=f.line,a=f.column,c=f.name,s=!0):s&&(n.addMapping({generated:{line:t.line,column:t.column}}),o=null,s=!1);for(var u=0,d=l.length;u<d;u++)l.charCodeAt(u)===T0?(t.line++,t.column=0,u+1===d?(o=null,s=!1):s&&n.addMapping({source:f.source,original:{line:f.line,column:f.column},generated:{line:t.line,column:t.column},name:f.name})):t.column++}),this.walkSourceContents(function(l,f){n.setSourceContent(l,f)}),{code:t.code,map:n}};uy.SourceNode=Me});var dy=j(Rl=>{Rl.SourceMapGenerator=Ff().SourceMapGenerator;Rl.SourceMapConsumer=cy().SourceMapConsumer;Rl.SourceNode=fy().SourceNode});var gy=j((Tl,my)=>{"use strict";Tl.__esModule=!0;var Wf=Ee(),an=void 0;try{(typeof define!="function"||!define.amd)&&(hy=dy(),an=hy.SourceNode)}catch(r){}var hy;an||(an=function(r,e,t,n){this.src="",n&&this.add(n)},an.prototype={add:function(e){Wf.isArray(e)&&(e=e.join("")),this.src+=e},prepend:function(e){Wf.isArray(e)&&(e=e.join("")),this.src=e+this.src},toStringWithSourceMap:function(){return{code:this.toString()}},toString:function(){return this.src}});function jf(r,e,t){if(Wf.isArray(r)){for(var n=[],s=0,o=r.length;s<o;s++)n.push(e.wrap(r[s],t));return n}else if(typeof r=="boolean"||typeof r=="number")return r+"";return r}function py(r){this.srcFile=r,this.source=[]}py.prototype={isEmpty:function(){return!this.source.length},prepend:function(e,t){this.source.unshift(this.wrap(e,t))},push:function(e,t){this.source.push(this.wrap(e,t))},merge:function(){var e=this.empty();return this.each(function(t){e.add(["  ",t,`
`])}),e},each:function(e){for(var t=0,n=this.source.length;t<n;t++)e(this.source[t])},empty:function(){var e=this.currentLocation||{start:{}};return new an(e.start.line,e.start.column,this.srcFile)},wrap:function(e){var t=arguments.length<=1||arguments[1]===void 0?this.currentLocation||{start:{}}:arguments[1];return e instanceof an?e:(e=jf(e,this,t),new an(t.start.line,t.start.column,this.srcFile,e))},functionCall:function(e,t,n){return n=this.generateList(n),this.wrap([e,t?"."+t+"(":"(",n,")"])},quotedString:function(e){return'"'+(e+"").replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")+'"'},objectLiteral:function(e){var t=this,n=[];Object.keys(e).forEach(function(o){var i=jf(e[o],t);i!=="undefined"&&n.push([t.quotedString(o),":",i])});var s=this.generateList(n);return s.prepend("{"),s.add("}"),s},generateList:function(e){for(var t=this.empty(),n=0,s=e.length;n<s;n++)n&&t.add(","),t.add(jf(e[n],this));return t},generateArray:function(e){var t=this.generateList(e);return t.prepend("["),t.add("]"),t}};Tl.default=py;my.exports=Tl.default});var Sy=j((Il,_y)=>{"use strict";Il.__esModule=!0;function wy(r){return r&&r.__esModule?r:{default:r}}var yy=dl(),I0=qe(),Vf=wy(I0),k0=Ee(),O0=gy(),by=wy(O0);function Ds(r){this.value=r}function Bs(){}Bs.prototype={nameLookup:function(e,t){return this.internalNameLookup(e,t)},depthedLookup:function(e){return[this.aliasable("container.lookup"),"(depths, ",JSON.stringify(e),")"]},compilerInfo:function(){var e=yy.COMPILER_REVISION,t=yy.REVISION_CHANGES[e];return[e,t]},appendToBuffer:function(e,t,n){return k0.isArray(e)||(e=[e]),e=this.source.wrap(e,t),this.environment.isSimple?["return ",e,";"]:n?["buffer += ",e,";"]:(e.appendToBuffer=!0,e)},initializeBuffer:function(){return this.quotedString("")},internalNameLookup:function(e,t){return this.lookupPropertyFunctionIsUsed=!0,["lookupProperty(",e,",",JSON.stringify(t),")"]},lookupPropertyFunctionIsUsed:!1,compile:function(e,t,n,s){this.environment=e,this.options=t,this.stringParams=this.options.stringParams,this.trackIds=this.options.trackIds,this.precompile=!s,this.name=this.environment.name,this.isChild=!!n,this.context=n||{decorators:[],programs:[],environments:[]},this.preamble(),this.stackSlot=0,this.stackVars=[],this.aliases={},this.registers={list:[]},this.hashes=[],this.compileStack=[],this.inlineStack=[],this.blockParams=[],this.compileChildren(e,t),this.useDepths=this.useDepths||e.useDepths||e.useDecorators||this.options.compat,this.useBlockParams=this.useBlockParams||e.useBlockParams;var o=e.opcodes,i=void 0,a=void 0,c=void 0,l=void 0;for(c=0,l=o.length;c<l;c++)i=o[c],this.source.currentLocation=i.loc,a=a||i.loc,this[i.opcode].apply(this,i.args);if(this.source.currentLocation=a,this.pushSource(""),this.stackSlot||this.inlineStack.length||this.compileStack.length)throw new Vf.default("Compile completed with content left on stack");this.decorators.isEmpty()?this.decorators=void 0:(this.useDecorators=!0,this.decorators.prepend(["var decorators = container.decorators, ",this.lookupPropertyFunctionVarDeclaration(),`;
`]),this.decorators.push("return fn;"),s?this.decorators=Function.apply(this,["fn","props","container","depth0","data","blockParams","depths",this.decorators.merge()]):(this.decorators.prepend(`function(fn, props, container, depth0, data, blockParams, depths) {
`),this.decorators.push(`}
`),this.decorators=this.decorators.merge()));var f=this.createFunctionContext(s);if(this.isChild)return f;var u={compiler:this.compilerInfo(),main:f};this.decorators&&(u.main_d=this.decorators,u.useDecorators=!0);var d=this.context,h=d.programs,p=d.decorators;for(c=0,l=h.length;c<l;c++)h[c]&&(u[c]=h[c],p[c]&&(u[c+"_d"]=p[c],u.useDecorators=!0));return this.environment.usePartial&&(u.usePartial=!0),this.options.data&&(u.useData=!0),this.useDepths&&(u.useDepths=!0),this.useBlockParams&&(u.useBlockParams=!0),this.options.compat&&(u.compat=!0),s?u.compilerOptions=this.options:(u.compiler=JSON.stringify(u.compiler),this.source.currentLocation={start:{line:1,column:0}},u=this.objectLiteral(u),t.srcName?(u=u.toStringWithSourceMap({file:t.destName}),u.map=u.map&&u.map.toString()):u=u.toString()),u},preamble:function(){this.lastContext=0,this.source=new by.default(this.options.srcName),this.decorators=new by.default(this.options.srcName)},createFunctionContext:function(e){var t=this,n="",s=this.stackVars.concat(this.registers.list);s.length>0&&(n+=", "+s.join(", "));var o=0;Object.keys(this.aliases).forEach(function(c){var l=t.aliases[c];l.children&&l.referenceCount>1&&(n+=", alias"+ ++o+"="+c,l.children[0]="alias"+o)}),this.lookupPropertyFunctionIsUsed&&(n+=", "+this.lookupPropertyFunctionVarDeclaration());var i=["container","depth0","helpers","partials","data"];(this.useBlockParams||this.useDepths)&&i.push("blockParams"),this.useDepths&&i.push("depths");var a=this.mergeSource(n);return e?(i.push(a),Function.apply(this,i)):this.source.wrap(["function(",i.join(","),`) {
  `,a,"}"])},mergeSource:function(e){var t=this.environment.isSimple,n=!this.forceBuffer,s=void 0,o=void 0,i=void 0,a=void 0;return this.source.each(function(c){c.appendToBuffer?(i?c.prepend("  + "):i=c,a=c):(i&&(o?i.prepend("buffer += "):s=!0,a.add(";"),i=a=void 0),o=!0,t||(n=!1))}),n?i?(i.prepend("return "),a.add(";")):o||this.source.push('return "";'):(e+=", buffer = "+(s?"":this.initializeBuffer()),i?(i.prepend("return buffer + "),a.add(";")):this.source.push("return buffer;")),e&&this.source.prepend("var "+e.substring(2)+(s?"":`;
`)),this.source.merge()},lookupPropertyFunctionVarDeclaration:function(){return`
      lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    }
    `.trim()},blockValue:function(e){var t=this.aliasable("container.hooks.blockHelperMissing"),n=[this.contextName(0)];this.setupHelperArgs(e,0,n);var s=this.popStack();n.splice(1,0,s),this.push(this.source.functionCall(t,"call",n))},ambiguousBlockValue:function(){var e=this.aliasable("container.hooks.blockHelperMissing"),t=[this.contextName(0)];this.setupHelperArgs("",0,t,!0),this.flushInline();var n=this.topStack();t.splice(1,0,n),this.pushSource(["if (!",this.lastHelper,") { ",n," = ",this.source.functionCall(e,"call",t),"}"])},appendContent:function(e){this.pendingContent?e=this.pendingContent+e:this.pendingLocation=this.source.currentLocation,this.pendingContent=e},append:function(){if(this.isInline())this.replaceStack(function(t){return[" != null ? ",t,' : ""']}),this.pushSource(this.appendToBuffer(this.popStack()));else{var e=this.popStack();this.pushSource(["if (",e," != null) { ",this.appendToBuffer(e,void 0,!0)," }"]),this.environment.isSimple&&this.pushSource(["else { ",this.appendToBuffer("''",void 0,!0)," }"])}},appendEscaped:function(){this.pushSource(this.appendToBuffer([this.aliasable("container.escapeExpression"),"(",this.popStack(),")"]))},getContext:function(e){this.lastContext=e},pushContext:function(){this.pushStackLiteral(this.contextName(this.lastContext))},lookupOnContext:function(e,t,n,s){var o=0;!s&&this.options.compat&&!this.lastContext?this.push(this.depthedLookup(e[o++])):this.pushContext(),this.resolvePath("context",e,o,t,n)},lookupBlockParam:function(e,t){this.useBlockParams=!0,this.push(["blockParams[",e[0],"][",e[1],"]"]),this.resolvePath("context",t,1)},lookupData:function(e,t,n){e?this.pushStackLiteral("container.data(data, "+e+")"):this.pushStackLiteral("data"),this.resolvePath("data",t,0,!0,n)},resolvePath:function(e,t,n,s,o){var i=this;if(this.options.strict||this.options.assumeObjects){this.push(M0(this.options.strict&&o,this,t,n,e));return}for(var a=t.length;n<a;n++)this.replaceStack(function(c){var l=i.nameLookup(c,t[n],e);return s?[" && ",l]:[" != null ? ",l," : ",c]})},resolvePossibleLambda:function(){this.push([this.aliasable("container.lambda"),"(",this.popStack(),", ",this.contextName(0),")"])},pushStringParam:function(e,t){this.pushContext(),this.pushString(t),t!=="SubExpression"&&(typeof e=="string"?this.pushString(e):this.pushStackLiteral(e))},emptyHash:function(e){this.trackIds&&this.push("{}"),this.stringParams&&(this.push("{}"),this.push("{}")),this.pushStackLiteral(e?"undefined":"{}")},pushHash:function(){this.hash&&this.hashes.push(this.hash),this.hash={values:{},types:[],contexts:[],ids:[]}},popHash:function(){var e=this.hash;this.hash=this.hashes.pop(),this.trackIds&&this.push(this.objectLiteral(e.ids)),this.stringParams&&(this.push(this.objectLiteral(e.contexts)),this.push(this.objectLiteral(e.types))),this.push(this.objectLiteral(e.values))},pushString:function(e){this.pushStackLiteral(this.quotedString(e))},pushLiteral:function(e){this.pushStackLiteral(e)},pushProgram:function(e){e!=null?this.pushStackLiteral(this.programExpression(e)):this.pushStackLiteral(null)},registerDecorator:function(e,t){var n=this.nameLookup("decorators",t,"decorator"),s=this.setupHelperArgs(t,e);this.decorators.push(["fn = ",this.decorators.functionCall(n,"",["fn","props","container",s])," || fn;"])},invokeHelper:function(e,t,n){var s=this.popStack(),o=this.setupHelper(e,t),i=[];n&&i.push(o.name),i.push(s),this.options.strict||i.push(this.aliasable("container.hooks.helperMissing"));var a=["(",this.itemsSeparatedBy(i,"||"),")"],c=this.source.functionCall(a,"call",o.callParams);this.push(c)},itemsSeparatedBy:function(e,t){var n=[];n.push(e[0]);for(var s=1;s<e.length;s++)n.push(t,e[s]);return n},invokeKnownHelper:function(e,t){var n=this.setupHelper(e,t);this.push(this.source.functionCall(n.name,"call",n.callParams))},invokeAmbiguous:function(e,t){this.useRegister("helper");var n=this.popStack();this.emptyHash();var s=this.setupHelper(0,e,t),o=this.lastHelper=this.nameLookup("helpers",e,"helper"),i=["(","(helper = ",o," || ",n,")"];this.options.strict||(i[0]="(helper = ",i.push(" != null ? helper : ",this.aliasable("container.hooks.helperMissing"))),this.push(["(",i,s.paramsInit?["),(",s.paramsInit]:[],"),","(typeof helper === ",this.aliasable('"function"')," ? ",this.source.functionCall("helper","call",s.callParams)," : helper))"])},invokePartial:function(e,t,n){var s=[],o=this.setupParams(t,1,s);e&&(t=this.popStack(),delete o.name),n&&(o.indent=JSON.stringify(n)),o.helpers="helpers",o.partials="partials",o.decorators="container.decorators",e?s.unshift(t):s.unshift(this.nameLookup("partials",t,"partial")),this.options.compat&&(o.depths="depths"),o=this.objectLiteral(o),s.push(o),this.push(this.source.functionCall("container.invokePartial","",s))},assignToHash:function(e){var t=this.popStack(),n=void 0,s=void 0,o=void 0;this.trackIds&&(o=this.popStack()),this.stringParams&&(s=this.popStack(),n=this.popStack());var i=this.hash;n&&(i.contexts[e]=n),s&&(i.types[e]=s),o&&(i.ids[e]=o),i.values[e]=t},pushId:function(e,t,n){e==="BlockParam"?this.pushStackLiteral("blockParams["+t[0]+"].path["+t[1]+"]"+(n?" + "+JSON.stringify("."+n):"")):e==="PathExpression"?this.pushString(t):e==="SubExpression"?this.pushStackLiteral("true"):this.pushStackLiteral("null")},compiler:Bs,compileChildren:function(e,t){for(var n=e.children,s=void 0,o=void 0,i=0,a=n.length;i<a;i++){s=n[i],o=new this.compiler;var c=this.matchExistingProgram(s);if(c==null){this.context.programs.push("");var l=this.context.programs.length;s.index=l,s.name="program"+l,this.context.programs[l]=o.compile(s,t,this.context,!this.precompile),this.context.decorators[l]=o.decorators,this.context.environments[l]=s,this.useDepths=this.useDepths||o.useDepths,this.useBlockParams=this.useBlockParams||o.useBlockParams,s.useDepths=this.useDepths,s.useBlockParams=this.useBlockParams}else s.index=c.index,s.name="program"+c.index,this.useDepths=this.useDepths||c.useDepths,this.useBlockParams=this.useBlockParams||c.useBlockParams}},matchExistingProgram:function(e){for(var t=0,n=this.context.environments.length;t<n;t++){var s=this.context.environments[t];if(s&&s.equals(e))return s}},programExpression:function(e){var t=this.environment.children[e],n=[t.index,"data",t.blockParams];return(this.useBlockParams||this.useDepths)&&n.push("blockParams"),this.useDepths&&n.push("depths"),"container.program("+n.join(", ")+")"},useRegister:function(e){this.registers[e]||(this.registers[e]=!0,this.registers.list.push(e))},push:function(e){return e instanceof Ds||(e=this.source.wrap(e)),this.inlineStack.push(e),e},pushStackLiteral:function(e){this.push(new Ds(e))},pushSource:function(e){this.pendingContent&&(this.source.push(this.appendToBuffer(this.source.quotedString(this.pendingContent),this.pendingLocation)),this.pendingContent=void 0),e&&this.source.push(e)},replaceStack:function(e){var t=["("],n=void 0,s=void 0,o=void 0;if(!this.isInline())throw new Vf.default("replaceStack on non-inline");var i=this.popStack(!0);if(i instanceof Ds)n=[i.value],t=["(",n],o=!0;else{s=!0;var a=this.incrStack();t=["((",this.push(a)," = ",i,")"],n=this.topStack()}var c=e.call(this,n);o||this.popStack(),s&&this.stackSlot--,this.push(t.concat(c,")"))},incrStack:function(){return this.stackSlot++,this.stackSlot>this.stackVars.length&&this.stackVars.push("stack"+this.stackSlot),this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var e=this.inlineStack;this.inlineStack=[];for(var t=0,n=e.length;t<n;t++){var s=e[t];if(s instanceof Ds)this.compileStack.push(s);else{var o=this.incrStack();this.pushSource([o," = ",s,";"]),this.compileStack.push(o)}}},isInline:function(){return this.inlineStack.length},popStack:function(e){var t=this.isInline(),n=(t?this.inlineStack:this.compileStack).pop();if(!e&&n instanceof Ds)return n.value;if(!t){if(!this.stackSlot)throw new Vf.default("Invalid stack pop");this.stackSlot--}return n},topStack:function(){var e=this.isInline()?this.inlineStack:this.compileStack,t=e[e.length-1];return t instanceof Ds?t.value:t},contextName:function(e){return this.useDepths&&e?"depths["+e+"]":"depth"+e},quotedString:function(e){return this.source.quotedString(e)},objectLiteral:function(e){return this.source.objectLiteral(e)},aliasable:function(e){var t=this.aliases[e];return t?(t.referenceCount++,t):(t=this.aliases[e]=this.source.wrap(e),t.aliasable=!0,t.referenceCount=1,t)},setupHelper:function(e,t,n){var s=[],o=this.setupHelperArgs(t,e,s,n),i=this.nameLookup("helpers",t,"helper"),a=this.aliasable(this.contextName(0)+" != null ? "+this.contextName(0)+" : (container.nullContext || {})");return{params:s,paramsInit:o,name:i,callParams:[a].concat(s)}},setupParams:function(e,t,n){var s={},o=[],i=[],a=[],c=!n,l=void 0;c&&(n=[]),s.name=this.quotedString(e),s.hash=this.popStack(),this.trackIds&&(s.hashIds=this.popStack()),this.stringParams&&(s.hashTypes=this.popStack(),s.hashContexts=this.popStack());var f=this.popStack(),u=this.popStack();(u||f)&&(s.fn=u||"container.noop",s.inverse=f||"container.noop");for(var d=t;d--;)l=this.popStack(),n[d]=l,this.trackIds&&(a[d]=this.popStack()),this.stringParams&&(i[d]=this.popStack(),o[d]=this.popStack());return c&&(s.args=this.source.generateArray(n)),this.trackIds&&(s.ids=this.source.generateArray(a)),this.stringParams&&(s.types=this.source.generateArray(i),s.contexts=this.source.generateArray(o)),this.options.data&&(s.data="data"),this.useBlockParams&&(s.blockParams="blockParams"),s},setupHelperArgs:function(e,t,n,s){var o=this.setupParams(e,t,n);return o.loc=JSON.stringify(this.source.currentLocation),o=this.objectLiteral(o),s?(this.useRegister("options"),n.push("options"),["options=",o]):n?(n.push(o),""):o}};(function(){for(var r="break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield await null true false".split(" "),e=Bs.RESERVED_WORDS={},t=0,n=r.length;t<n;t++)e[r[t]]=!0})();Bs.isValidJavaScriptVariableName=function(r){return!Bs.RESERVED_WORDS[r]&&/^[a-zA-Z_$][0-9a-zA-Z_$]*$/.test(r)};function M0(r,e,t,n,s){var o=e.popStack(),i=t.length;for(r&&i--;n<i;n++)o=e.nameLookup(o,t[n],s);return r?[e.aliasable("container.strict"),"(",o,", ",e.quotedString(t[n]),", ",JSON.stringify(e.source.currentLocation)," )"]:o}Il.default=Bs;_y.exports=Il.default});var vy=j((kl,Ey)=>{"use strict";kl.__esModule=!0;function ci(r){return r&&r.__esModule?r:{default:r}}var L0=Ag(),N0=ci(L0),D0=Sf(),B0=ci(D0),Kf=Fg(),Gf=Hg(),F0=Sy(),U0=ci(F0),q0=Ef(),$0=ci(q0),H0=yf(),j0=ci(H0),W0=N0.default.create;function xy(){var r=W0();return r.compile=function(e,t){return Gf.compile(e,t,r)},r.precompile=function(e,t){return Gf.precompile(e,t,r)},r.AST=B0.default,r.Compiler=Gf.Compiler,r.JavaScriptCompiler=U0.default,r.Parser=Kf.parser,r.parse=Kf.parse,r.parseWithoutProcessing=Kf.parseWithoutProcessing,r}var Fs=xy();Fs.create=xy;j0.default(Fs);Fs.Visitor=$0.default;Fs.default=Fs;kl.default=Fs;Ey.exports=kl.default});var X0={};td(X0,{default:()=>Dl});module.exports=Fy(X0);var qt=require("obsidian");var Xe=require("obsidian");var Nt=require("obsidian");var Bl={Error:"Error",user:"user",system:"system",assistant:"assistant",newChat:"newChat","Conversion failed. Selected sections is a":"Conversion failed. Selected sections is a",message:"message","Check the developer console for error details. ":"Check the developer console for error details. ","Cancel generation":"Cancel generation","Regenerate?":"Regenerate?","This will delete the current response content. You can configure this in settings to not require confirmation.":"This will delete the current response content. You can configure this in settings to not require confirmation.",Yes:"Yes","Export conversations to JSONL":"Export conversations to JSONL","No conversation found":"No conversation found","Exported to the same directory, Obsidian does not display the JSONL format. Please open with another software.":"Exported to the same directory, Obsidian does not display the JSONL format. Please open with another software.","Replace speaker with tag":"Replace speaker with tag","No speaker found":"No speaker found","Replace the names of the two most frequently occurring speakers with tag format.":"Replace the names of the two most frequently occurring speakers with tag format.",Replace:"Replace","Select message at cursor":"Select message at cursor","No message found at cursor":"No message found at cursor","API key is required":"API key is required","API secret is required":"API secret is required","Model is required":"Model is required","API URL is required":"API URL is required","Text Generation":"Text Generation","Image Vision":"Image Vision","PDF Vision":"PDF Vision","Image Generation":"Image Generation","Image Editing":"Image Editing","Web Search":"Web Search",Reasoning:"Reasoning","Only PNG, JPEG, GIF, and WebP images are supported.":"Only PNG, JPEG, GIF, and WebP images are supported.","Only PNG, JPEG, GIF, WebP, and PDF files are supported.":"Only PNG, JPEG, GIF, WebP, and PDF files are supported.","Only the last user message is used for image generation. Other messages are ignored.":"Only the last user message is used for image generation. Other messages are ignored.","Multiple embeds found, only the first one will be used":"Multiple embeds found, only the first one will be used","Only PNG, JPEG, and WebP images are supported for editing.":"Only PNG, JPEG, and WebP images are supported for editing.","Embed data is empty or invalid":"Embed data is empty or invalid","Failed to generate image. no data received from API":"Failed to generate image. no data received from API","Load template file: ":"Load template file: ","Templates have been updated: ":"Templates have been updated: ","Syntax Error Report":"Syntax Error Report","Create prompt template file":"Create prompt template file","Expected at least 2 sections, heading and content":"Expected at least 2 sections, heading and content","Expected heading":"Expected heading","Duplicate title:":"Duplicate title:","Please add a user message first, or wait for the user message to be parsed.":"Please add a user message first, or wait for the user message to be parsed.","Waiting for metadata to be ready. Please try again.":"Waiting for metadata to be ready. Please try again.","No text generated":"No text generated",characters:"characters","Removed commands":"Removed commands","Added commands":"Added commands","No active generation to cancel":"No active generation to cancel","Generation already cancelled":"Generation already cancelled","Generation cancelled":"Generation cancelled","Restore default":"Restore default","AI assistants":"AI assistants","New AI assistant":"New AI assistant","For those compatible with the OpenAI protocol, you can select OpenAI.":"For those compatible with the OpenAI protocol, you can select OpenAI.","Add AI Provider":"Add AI Provider","Please add at least one AI assistant to start using the plugin.":"Please add at least one AI assistant to start using the plugin.","Message tags":"Message tags","Keywords for tags in the text box are separated by spaces":"Keywords for tags in the text box are separated by spaces","New chat tags":"New chat tags","User message tags":"User message tags","System message tags":"System message tags","At least one tag is required":"At least one tag is required","Assistant message tag":"Assistant message tag","Tag used to trigger AI text generation":"Tag used to trigger AI text generation","Obtain key from ":"Obtain key from ","Web search":"Web search","Enable web search for AI":"Enable web search for AI","Enter your key":"Enter your key","Default:":"Default:","Refer to the technical documentation":"Refer to the technical documentation","Keyword for tag must not contain #":"Keyword for tag must not contain #","Keyword for tag must not contain space":"Keyword for tag must not contain space","Keyword for tag must be unique":"Keyword for tag must be unique",Model:"Model","Supported features":"Supported features","Select the model to use":"Select the model to use","Please input API key first":"Please input API key first","Please enter a number":"Please enter a number","Minimum value is 256":"Minimum value is 256","Invalid URL":"Invalid URL","Override input parameters":"Override input parameters",'Developer feature, in JSON format. For example, if the model list doesn\'t have the model you want, enter {"model": "your desired model"}':`Developer feature, in JSON format. For example, if the model list doesn't have the model you want, enter {"model": "your desired model"}`,"Remove AI assistant":"Remove AI assistant",Remove:"Remove",Endpoint:"Endpoint","API version":"API version","Select assistant":"Select assistant","Confirm before regeneration":"Confirm before regeneration","Confirm before replacing existing assistant responses when using assistant commands":"Confirm before replacing existing assistant responses when using assistant commands","Internal links":"Internal links","Internal links in user and system messages will be replaced with their referenced content. When disabled, only the original text of the links will be used.":"Internal links in user and system messages will be replaced with their referenced content. When disabled, only the original text of the links will be used.","Internal links for assistant messages":"Internal links for assistant messages","Replace internal links in assistant messages with their referenced content. Note: This feature is generally not recommended as assistant-generated content may contain non-existent links.":"Replace internal links in assistant messages with their referenced content. Note: This feature is generally not recommended as assistant-generated content may contain non-existent links.","System message":"System message","Enable default system message":"Enable default system message","Automatically add a system message when none exists in the conversation":"Automatically add a system message when none exists in the conversation","Default system message":"Default system message",Advanced:"Advanced","Delay before answer (Seconds)":"Delay before answer (Seconds)","If you encounter errors with missing user messages when executing assistant commands on selected text, it may be due to the need for more time to parse the messages. Please slightly increase the delay time.":"If you encounter errors with missing user messages when executing assistant commands on selected text, it may be due to the need for more time to parse the messages. Please slightly increase the delay time.","Replace tag Command":"Replace tag Command","Export to JSONL Command":"Export to JSONL Command","Tag suggest":"Tag suggest","If you only use commands without needing tag suggestions, you can disable this feature. Changes will take effect after restarting the plugin.":"If you only use commands without needing tag suggestions, you can disable this feature. Changes will take effect after restarting the plugin.","Image Display Width":"Image Display Width","Example: 400px width would output as ![[image.jpg|400]]":"Example: 400px width would output as ![[image.jpg|400]]","Number of images":"Number of images","Number of images to generate (1-5)":"Number of images to generate (1-5)","Image size":"Image size",landscape:"landscape",portrait:"portrait","Output format":"Output format",Quality:"Quality","Quality level for generated images. default: Auto":"Quality level for generated images. default: Auto",Auto:"Auto",High:"High",Medium:"Medium",Low:"Low",Background:"Background","Background of the generated image. default: Auto":"Background of the generated image. default: Auto",Transparent:"Transparent",Opaque:"Opaque","Output compression":"Output compression","Compression level of the output image, 10% - 100%. Only for webp or jpeg output format":"Compression level of the output image, 10% - 100%. Only for webp or jpeg output format","AI generate":"AI generate","Text generated successfully":"Text generated successfully","This is a non-streaming request, please wait...":"This is a non-streaming request, please wait...",promptFileName:"prompt.en",PRESET_PROMPT_TEMPLATES:`# Instructions

- Collect your commonly used prompts here for use with the Tars plugin commands.
- This file follows Obsidian's slide format, using \`---\` to separate each page.
- The first page contains instructions, and each subsequent page is a prompt template.
- Each template starts with a title in Markdown heading format, titles cannot be repeated. This is followed by the template content. Both title and content are required.
- If the content contains \`{{s}}\`, it will be replaced with your selected text.
- If there's no \`{{s}}\`, the selected text will be appended.
- If no text is selected, the template content will be used as is.
- If a page contains syntax errors, it won't appear in the command list.
- If you've edited this file, to load the updated templates into commands, please ==run the 'Load template file' command==, which will also check for syntax errors and display them in a popup.

---

# Prompt example

Tell me a joke

---

# Translation

Translate the following content into English\uFF1A{{s}}

---

# One-sentence summary

{{s}} Summarize the above content in one sentence

`,Thinking:"Thinking","When enabled, Claude will show its reasoning process before giving the final answer.":"When enabled, Claude will show its reasoning process before giving the final answer.","Budget tokens for thinking":"Budget tokens for thinking","Must be \u22651024 and less than max_tokens":"Must be \u22651024 and less than max_tokens","Minimum value is 1024":"Minimum value is 1024","AI Generation Details":"AI Generation Details",Round:"Round",Duration:"Duration","Start Time":"Start Time","End Time":"End Time","Error Details":"Error Details","Error Type":"Error Type","Error Message":"Error Message","Occurrence Time":"Occurrence Time","Stack Trace":"Stack Trace","Copy Error Info":"Copy Error Info","Error info copied to clipboard":"Error info copied to clipboard","Unknown Error":"Unknown Error","Tars AI assistant is ready":"Tars AI assistant is ready","Generating round":"Generating round","answer...":"answer...","Generating...":"Generating...","Click status bar for error details. ":"Click status bar for error details. ",Vendor:"Vendor",Characters:"Characters"};var nd={Error:"\u5F02\u5E38",user:"\u7528\u6237",system:"\u7CFB\u7EDF",assistant:"\u52A9\u624B",newChat:"\u65B0\u5BF9\u8BDD","Conversion failed. Selected sections is a":"\u8F6C\u6362\u5931\u8D25\u3002\u9009\u4E2D\u7684\u6BB5\u843D\u662F",message:"\u6D88\u606F","Check the developer console for error details. ":"\u67E5\u770B\u5F00\u53D1\u8005\u63A7\u5236\u53F0\u4E86\u89E3\u9519\u8BEF\u8BE6\u60C5\u3002","Cancel generation":"\u53D6\u6D88\u751F\u6210","Regenerate?":"\u91CD\u65B0\u751F\u6210?","This will delete the current response content. You can configure this in settings to not require confirmation.":"\u8FD9\u4F1A\u5220\u6389\u5F53\u524D\u7684\u56DE\u7B54\u5185\u5BB9\u3002\u4F60\u53EF\u4EE5\u5728\u8BBE\u7F6E\u91CC\u914D\u7F6E\u4E0D\u9700\u8981\u5F39\u7A97\u786E\u8BA4\u3002",Yes:"\u662F","Export conversations to JSONL":"\u5BFC\u51FA\u5BF9\u8BDD\u5230 JSONL","No conversation found":"\u6CA1\u6709\u627E\u5230\u5BF9\u8BDD","Exported to the same directory, Obsidian does not display the JSONL format. Please open with another software.":"\u5DF2\u7ECF\u5BFC\u51FA\u5230\u540C\u4E00\u76EE\u5F55\uFF0Cobsidian \u4E0D\u663E\u793A jsonl \u683C\u5F0F\uFF0C\u8BF7\u7528\u5176\u4ED6\u8F6F\u4EF6\u6253\u5F00","Replace speaker with tag":"\u628A\u8BF4\u8BDD\u8005\u66FF\u6362\u4E3A\u6807\u7B7E","No speaker found":"\u6CA1\u6709\u627E\u5230\u8BF4\u8BDD\u8005","Replace the names of the two most frequently occurring speakers with tag format.":"\u7528\u6807\u7B7E\u683C\u5F0F\u66FF\u6362\u4E24\u4E2A\u6700\u5E38\u51FA\u73B0\u7684\u8BF4\u8BDD\u8005\u7684\u540D\u5B57",Replace:"\u66FF\u6362","Select message at cursor":"\u9009\u62E9\u5149\u6807\u5904\u7684\u6D88\u606F","No message found at cursor":"\u5149\u6807\u5904\u6CA1\u6709\u627E\u5230\u6D88\u606F","API key is required":"\u8BF7\u914D\u7F6E\u5BF9\u5E94\u7684 API key","API secret is required":"\u8BF7\u914D\u7F6E\u5BF9\u5E94\u7684 API secret","Model is required":"\u8BF7\u914D\u7F6E\u5BF9\u5E94\u7684\u6A21\u578B","API URL is required":"\u8BF7\u914D\u7F6E\u5BF9\u5E94\u7684 API URL","Text Generation":"\u6587\u672C\u751F\u6210","Image Vision":"\u56FE\u50CF\u89C6\u89C9","PDF Vision":"PDF\u89C6\u89C9","Image Generation":"\u56FE\u50CF\u751F\u6210","Image Editing":"\u56FE\u50CF\u7F16\u8F91","Web Search":"\u7F51\u7EDC\u641C\u7D22",Reasoning:"\u63A8\u7406","Only PNG, JPEG, GIF, and WebP images are supported.":"\u4EC5\u652F\u6301 PNG\u3001JPEG\u3001GIF \u548C WebP \u683C\u5F0F\u7684\u56FE\u7247\u3002","Only PNG, JPEG, GIF, WebP, and PDF files are supported.":"\u4EC5\u652F\u6301 PNG\u3001JPEG\u3001GIF\u3001WebP \u548C PDF \u6587\u4EF6\u3002","Only the last user message is used for image generation. Other messages are ignored.":"\u4EC5\u4F7F\u7528\u6700\u540E\u4E00\u6761\u7528\u6237\u6D88\u606F\u8FDB\u884C\u56FE\u7247\u751F\u6210\uFF0C\u5176\u4ED6\u6D88\u606F\u5C06\u88AB\u5FFD\u7565\u3002","Multiple embeds found, only the first one will be used":"\u53D1\u73B0\u591A\u4E2A\u5D4C\u5165\u5185\u5BB9\uFF0C\u4EC5\u4F7F\u7528\u7B2C\u4E00\u4E2A","Only PNG, JPEG, and WebP images are supported for editing.":"\u4EC5\u652F\u6301 PNG\u3001JPEG \u548C WebP \u683C\u5F0F\u7684\u56FE\u7247\u8FDB\u884C\u7F16\u8F91","Embed data is empty or invalid":"\u5D4C\u5165\u6570\u636E\u4E3A\u7A7A\u6216\u65E0\u6548","Failed to generate image. no data received from API":"\u751F\u6210\u56FE\u7247\u5931\u8D25\u3002\u6CA1\u6709\u4ECE API \u63A5\u6536\u5230\u6570\u636E","Load template file: ":"\u52A0\u8F7D\u6A21\u677F\u6587\u4EF6: ","Templates have been updated: ":"\u6A21\u677F\u5DF2\u66F4\u65B0: ","Syntax Error Report":"\u8BED\u6CD5\u9519\u8BEF\u62A5\u544A","Create prompt template file":"\u521B\u5EFA\u63D0\u793A\u8BCD\u6A21\u677F\u6587\u4EF6","Expected at least 2 sections, heading and content":"\u81F3\u5C11\u9700\u8981 2 \u4E2A\u90E8\u5206\uFF0C\u6807\u9898\u548C\u5185\u5BB9","Expected heading":"\u9700\u8981\u6807\u9898","Duplicate title:":"\u91CD\u590D\u7684\u6807\u9898:","Please add a user message first, or wait for the user message to be parsed.":"\u8BF7\u5148\u6DFB\u52A0\u7528\u6237\u6D88\u606F\uFF0C\u6216\u8005\u7A0D\u7B49\u7528\u6237\u6D88\u606F\u89E3\u6790\u5B8C\u6210","Waiting for metadata to be ready. Please try again.":"\u6B63\u5728\u7B49\u5F85\u5143\u6570\u636E\u51C6\u5907\u5C31\u7EEA\u3002\u8BF7\u91CD\u8BD5\u3002","No text generated":"\u6CA1\u6709\u751F\u6210\u6587\u672C",characters:"\u4E2A\u5B57\u7B26","Removed commands":"\u5DF2\u79FB\u9664\u547D\u4EE4","Added commands":"\u5DF2\u6DFB\u52A0\u547D\u4EE4","No active generation to cancel":"\u6CA1\u6709\u6B63\u5728\u8FDB\u884C\u7684\u751F\u6210\u53EF\u53D6\u6D88","Generation already cancelled":"\u751F\u6210\u5DF2\u7ECF\u53D6\u6D88","Generation cancelled":"\u5DF2\u53D6\u6D88\u751F\u6210","Restore default":"\u6062\u590D\u9ED8\u8BA4","AI assistants":"AI \u52A9\u624B","New AI assistant":"\u65B0\u7684 AI \u52A9\u624B","For those compatible with the OpenAI protocol, you can select OpenAI.":"\u5BF9\u4E8E\u517C\u5BB9 OpenAI \u534F\u8BAE\u7684\uFF0C\u53EF\u4EE5\u9009\u62E9 OpenAI.","Add AI Provider":"\u6DFB\u52A0 AI \u670D\u52A1\u5546","Please add at least one AI assistant to start using the plugin.":"\u8BF7\u81F3\u5C11\u6DFB\u52A0\u4E00\u4E2A AI \u52A9\u624B\uFF0C\u4EE5\u4FBF\u5F00\u59CB\u4F7F\u7528\u63D2\u4EF6","Message tags":"\u6D88\u606F\u6807\u7B7E","Keywords for tags in the text box are separated by spaces":"\u5728\u6587\u672C\u6846\u4E2D\u7684\u6807\u7B7E\u5173\u952E\u5B57\u7528\u7A7A\u683C\u5206\u9694","New chat tags":"\u65B0\u5BF9\u8BDD\u7684\u6807\u7B7E","User message tags":"\u7528\u6237\u6D88\u606F\u7684\u6807\u7B7E","System message tags":"\u7CFB\u7EDF\u6D88\u606F\u7684\u6807\u7B7E","At least one tag is required":"\u81F3\u5C11\u9700\u8981\u4E00\u4E2A\u6807\u7B7E","Assistant message tag":"\u52A9\u624B\u6D88\u606F\u7684\u6807\u7B7E","Tag used to trigger AI text generation":"\u7528\u4E8E\u89E6\u53D1 AI \u6587\u672C\u751F\u6210\u7684\u6807\u7B7E","Obtain key from ":"\u83B7\u53D6 key \u7F51\u7AD9 ","Web search":"\u7F51\u7EDC\u641C\u7D22","Enable web search for AI":"\u4E3A\u5F53\u524D AI \u542F\u7528\u7F51\u7EDC\u641C\u7D22","Enter your key":"\u8F93\u5165\u4F60\u7684 key","Default:":"\u9ED8\u8BA4:","Refer to the technical documentation":"\u53C2\u8003\u6280\u672F\u6587\u6863","Keyword for tag must not contain #":"\u6807\u7B7E\u5173\u952E\u5B57\u4E0D\u80FD\u5305\u542B #","Keyword for tag must not contain space":"\u6807\u7B7E\u5173\u952E\u5B57\u4E0D\u80FD\u5305\u542B\u7A7A\u683C","Keyword for tag must be unique":"\u6807\u7B7E\u5173\u952E\u5B57\u5FC5\u987B\u552F\u4E00",Model:"\u6A21\u578B","Supported features":"\u652F\u6301\u529F\u80FD","Select the model to use":"\u9009\u62E9\u8981\u4F7F\u7528\u7684\u6A21\u578B","Please input API key first":"\u8BF7\u5148\u8F93\u5165 API key","Please enter a number":"\u8BF7\u8F93\u5165\u4E00\u4E2A\u6570\u5B57","Minimum value is 256":"\u6700\u5C0F\u503C\u662F256","Invalid URL":"\u65E0\u6548\u7684 URL","Override input parameters":"\u8986\u76D6\u8F93\u5165\u53C2\u6570",'Developer feature, in JSON format. For example, if the model list doesn\'t have the model you want, enter {"model": "your desired model"}':'\u5F00\u53D1\u8005\u529F\u80FD\uFF0C\u4EE5 JSON \u683C\u5F0F\u3002\u4F8B\u5982: \u6A21\u578B\u5217\u8868\u4E2D\u6CA1\u6709\u4F60\u9700\u8981\u7684\u6A21\u578B\u65F6\uFF0C\u53EF\u4EE5\u586B\u5165 {"model": "\u4F60\u60F3\u8981\u7684model"}',"Remove AI assistant":"\u79FB\u9664 AI \u52A9\u624B",Remove:"\u79FB\u9664",Endpoint:"\u7EC8\u7ED3\u70B9","API version":"API \u7248\u672C","Select assistant":"\u9009\u62E9\u52A9\u624B","Confirm before regeneration":"\u91CD\u65B0\u751F\u6210\u524D\u662F\u5426\u9700\u8981\u786E\u8BA4","Confirm before replacing existing assistant responses when using assistant commands":"\u5728\u4F7F\u7528\u52A9\u624B\u6807\u7B7E\u547D\u4EE4\u65F6\uFF0C\u66FF\u6362\u65E7\u7684\u52A9\u624B\u6D88\u606F\uFF0C\u662F\u5426\u9700\u8981\u5F39\u7A97\u786E\u8BA4","Internal links":"\u5185\u90E8\u94FE\u63A5","Internal links in user and system messages will be replaced with their referenced content. When disabled, only the original text of the links will be used.":"\u7528\u6237\u548C\u7CFB\u7EDF\u6D88\u606F\u4E2D\u7684\u5185\u90E8\u94FE\u63A5\u5C06\u88AB\u66FF\u6362\u4E3A\u5176\u5F15\u7528\u7684\u5185\u5BB9\u3002\u7981\u7528\u65F6\uFF0C\u4EC5\u4F7F\u7528\u94FE\u63A5\u7684\u539F\u59CB\u6587\u672C\u3002","Internal links for assistant messages":"\u52A9\u624B\u6D88\u606F\u7684\u5185\u90E8\u94FE\u63A5","Replace internal links in assistant messages with their referenced content. Note: This feature is generally not recommended as assistant-generated content may contain non-existent links.":"\u52A9\u624B\u6D88\u606F\u4E2D\u7684\u5185\u90E8\u94FE\u63A5\u66FF\u6362\u4E3A\u5176\u5F15\u7528\u7684\u5185\u5BB9\u3002\u6CE8\u610F\uFF1A\u901A\u5E38\u60C5\u51B5\u4E0B\u4E0D\u5EFA\u8BAE\u542F\u7528\u6B64\u529F\u80FD\uFF0C\u56E0\u4E3A\u52A9\u624B\u751F\u6210\u7684\u5185\u5BB9\u53EF\u80FD\u5305\u542B\u4E0D\u5B58\u5728\u7684\u94FE\u63A5\u3002","System message":"\u7CFB\u7EDF\u6D88\u606F","Enable default system message":"\u542F\u7528\u9ED8\u8BA4\u7CFB\u7EDF\u6D88\u606F","Automatically add a system message when none exists in the conversation":"\u5F53\u5BF9\u8BDD\u4E2D\u6CA1\u6709\u7CFB\u7EDF\u6D88\u606F\u65F6\uFF0C\u81EA\u52A8\u6DFB\u52A0\u81EA\u5B9A\u4E49\u7684\u9ED8\u8BA4\u7684\u7CFB\u7EDF\u6D88\u606F","Default system message":"\u9ED8\u8BA4\u7CFB\u7EDF\u6D88\u606F",Advanced:"\u9AD8\u7EA7","Delay before answer (Seconds)":"\u56DE\u7B54\u524D\u7684\u5EF6\u8FDF\uFF08\u79D2\uFF09","If you encounter errors with missing user messages when executing assistant commands on selected text, it may be due to the need for more time to parse the messages. Please slightly increase the delay time.":"\u5728\u9009\u4E2D\u6587\u672C\u6267\u884C\u52A9\u624B\u547D\u4EE4\u7684\u65F6\u5019\uFF0C\u5982\u679C\u9047\u5230\u7F3A\u5C11\u7528\u6237\u6D88\u606F\u7684\u9519\u8BEF\uFF0C\u53EF\u80FD\u662F\u9700\u8981\u66F4\u591A\u65F6\u95F4\u6765\u89E3\u6790\u6D88\u606F\uFF0C\u8BF7\u7A0D\u5FAE\u589E\u52A0\u5EF6\u8FDF","Replace tag Command":"\u66FF\u6362\u6807\u7B7E\u547D\u4EE4","Export to JSONL Command":"\u5BFC\u51FA\u5230 JSONL \u547D\u4EE4","Tag suggest":"\u6807\u7B7E\u5EFA\u8BAE","If you only use commands without needing tag suggestions, you can disable this feature. Changes will take effect after restarting the plugin.":"\u5982\u679C\u4F60\u53EA\u4F7F\u7528\u547D\u4EE4\u800C\u4E0D\u9700\u8981\u6807\u7B7E\u5EFA\u8BAE\uFF0C\u53EF\u4EE5\u7981\u7528\u6B64\u529F\u80FD\u3002\u66F4\u6539\u5C06\u5728\u91CD\u65B0\u542F\u52A8\u63D2\u4EF6\u540E\u751F\u6548\u3002","Image Display Width":"\u56FE\u7247\u663E\u793A\u5BBD\u5EA6","Example: 400px width would output as ![[image.jpg|400]]":"\u4F8B\u5982: 400px \u5BBD\u5EA6\u4F1A\u8F93\u51FA\u4E3A ![[image.jpg|400]]","Number of images":"\u56FE\u7247\u6570\u91CF","Number of images to generate (1-5)":"\u751F\u6210\u7684\u56FE\u7247\u6570\u91CF (1-5)","Image size":"\u56FE\u7247\u5C3A\u5BF8",landscape:"\u6A2A\u5411",portrait:"\u7EB5\u5411","Output format":"\u8F93\u51FA\u683C\u5F0F",Quality:"\u8D28\u91CF","Quality level for generated images. default: Auto":"\u751F\u6210\u56FE\u7247\u7684\u8D28\u91CF\u7B49\u7EA7\u3002\u9ED8\u8BA4: \u81EA\u52A8",Auto:"\u81EA\u52A8",High:"\u9AD8",Medium:"\u4E2D",Low:"\u4F4E",Background:"\u80CC\u666F","Background of the generated image. default: Auto":"\u751F\u6210\u56FE\u7247\u7684\u80CC\u666F\u3002\u9ED8\u8BA4: \u81EA\u52A8",Transparent:"\u900F\u660E",Opaque:"\u4E0D\u900F\u660E","Output compression":"\u8F93\u51FA\u538B\u7F29","Compression level of the output image, 10% - 100%. Only for webp or jpeg output format":"\u8F93\u51FA\u56FE\u7247\u7684\u538B\u7F29\u7EA7\u522B\uFF0C10% - 100%\u3002\u4EC5\u9002\u7528\u4E8E webp \u6216 jpeg \u8F93\u51FA\u683C\u5F0F","AI generate":"AI \u751F\u6210","Text generated successfully":"\u6587\u672C\u751F\u6210\u6210\u529F","This is a non-streaming request, please wait...":"\u8FD9\u662F\u4E00\u4E2A\u975E\u6D41\u5F0F\u8BF7\u6C42\uFF0C\u8BF7\u7A0D\u5019...",promptFileName:"prompt.zh",PRESET_PROMPT_TEMPLATES:`# \u4F7F\u7528\u8BF4\u660E

- \u628A\u4F60\u7684\u5E38\u7528\u63D0\u793A\u8BCD\u6536\u96C6\u5230\u8FD9\u91CC\uFF0C\u4EE5\u4FBF\u5728Tars\u63D2\u4EF6\u7684\u547D\u4EE4\u4E2D\u4F7F\u7528\u3002
- \u672C\u6587\u4EF6\u6309\u7167 obsidian \u7684\u5E7B\u706F\u7247\u683C\u5F0F\uFF0C\u7528\`---\`\u6765\u5206\u9694\u6BCF\u4E00\u9875
- \u7B2C\u4E00\u9875\u662F\u8BF4\u660E\uFF0C\u540E\u9762\u7684\u6BCF\u4E00\u9875\u90FD\u662F\u4E00\u4E2A\u63D0\u793A\u8BCD\u6A21\u677F
- \u9996\u5148\u662F\u6A21\u677F\u7684\u6807\u9898, \u4EE5markdown\u7684\u6807\u9898\u683C\u5F0F, \u6807\u9898\u4E0D\u80FD\u91CD\u590D\u3002\u63A5\u4E0B\u6765\u90FD\u662F\u6A21\u677F\u7684\u5185\u5BB9\u3002\u6807\u9898\u548C\u5185\u5BB9\u90FD\u4E0D\u53EF\u7F3A\u5C11\u3002
- \u5982\u679C\u5185\u5BB9\u6709 \`{{s}}\`\uFF0C\u4F1A\u628A \`{{s}}\`\u66FF\u6362\u4E3A\u9009\u4E2D\u7684\u6587\u672C
- \u5982\u679C\u6CA1\u6709 \`{{s}}\`\uFF0C\u5219\u8FFD\u52A0
- \u5982\u679C\u6CA1\u6709\u9009\u4E2D\u7684\u6587\u672C\uFF0C\u5219\u76F4\u63A5\u4F7F\u7528\u6A21\u677F\u7684\u5185\u5BB9
- \u5982\u679C\u67D0\u4E00\u9875\u6709\u8BED\u6CD5\u9519\u8BEF\uFF0C\u5219\u4E0D\u4F1A\u51FA\u73B0\u5728\u547D\u4EE4\u4E2D
- \u5982\u679C\u4F60\u7F16\u8F91\u4E86\u8BE5\u6587\u4EF6\uFF0C\u8981\u628A\u66F4\u65B0\u540E\u7684\u6A21\u677F\u52A0\u8F7D\u5230\u547D\u4EE4\u4E2D\uFF0C==\u6267\u884C\u547D\u4EE4\u201C\u52A0\u8F7D\u6A21\u677F\u6587\u4EF6\u201D==\uFF0C\u8BE5\u547D\u4EE4\u540C\u65F6\u4F1A\u68C0\u67E5\u8BED\u6CD5\u9519\u8BEF\u5E76\u5F39\u7A97\u663E\u793A\u3002

---

# \u63D0\u793A\u8BCD\u4F8B\u5B50

\u7ED9\u6211\u8BB2\u4E2A\u7B11\u8BDD

---

# \u7FFB\u8BD1

\u628A\u4EE5\u4E0B\u5185\u5BB9\u7FFB\u8BD1\u4E3A\u4E2D\u6587\uFF1A{{s}}

---

# \u4E00\u53E5\u8BDD\u603B\u7ED3

{{s}} \u7528\u4E00\u53E5\u8BDD\u603B\u7ED3\u4EE5\u4E0A\u5185\u5BB9

`,Thinking:"\u601D\u8003","When enabled, Claude will show its reasoning process before giving the final answer.":"\u542F\u7528\u540E\uFF0CClaude \u5C06\u5728\u7ED9\u51FA\u6700\u7EC8\u7B54\u6848\u524D\u5C55\u793A\u5176\u63A8\u7406\u8FC7\u7A0B","Budget tokens for thinking":"\u601D\u8003\u4EE4\u724C\u9884\u7B97","Must be \u22651024 and less than max_tokens":"\u5FC5\u987B \u22651024 \u4E14\u5C0F\u4E8E max_tokens","Minimum value is 1024":"\u6700\u5C0F\u503C\u4E3A 1024","AI Generation Details":"AI \u751F\u6210\u8BE6\u60C5",Round:"\u56DE\u5408",Duration:"\u7528\u65F6","Start Time":"\u5F00\u59CB\u65F6\u95F4","End Time":"\u7ED3\u675F\u65F6\u95F4","Error Details":"\u9519\u8BEF\u8BE6\u60C5","Error Type":"\u9519\u8BEF\u7C7B\u578B","Error Message":"\u9519\u8BEF\u4FE1\u606F","Occurrence Time":"\u53D1\u751F\u65F6\u95F4","Stack Trace":"\u5806\u6808\u8DDF\u8E2A","Copy Error Info":"\u590D\u5236\u9519\u8BEF\u4FE1\u606F","Error info copied to clipboard":"\u9519\u8BEF\u4FE1\u606F\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F","Unknown Error":"\u672A\u77E5\u9519\u8BEF","Tars AI assistant is ready":"Tars AI \u52A9\u624B\u5DF2\u5C31\u7EEA","Generating round":"\u6B63\u5728\u751F\u6210\u7B2C","answer...":"\u8F6E\u56DE\u7B54...","Generating...":"\u6B63\u5728\u751F\u6210...","Click status bar for error details. ":"\u70B9\u51FB\u72B6\u6001\u680F\u67E5\u770B\u9519\u8BEF\u8BE6\u60C5\u3002",Vendor:"\u670D\u52A1\u5546",Characters:"\u5B57\u7B26\u6570"};var sd={};var Uy={en:Bl,"zh-TW":sd,zh:nd},od=window.localStorage.getItem("language"),Fl=Uy[od||"en"];function m(r){return Fl||console.error("Error: locale not found",od),Fl&&Fl[r]||Bl[r]}var Pm=require("obsidian");function N(r,e,t,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(r,t):s?s.value=t:e.set(r,t),t}function b(r,e,t,n){if(t==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?n:t==="a"?n.call(r):n?n.value:e.get(r)}var Ul=function(){let{crypto:r}=globalThis;if(r!=null&&r.randomUUID)return Ul=r.randomUUID.bind(r),r.randomUUID();let e=new Uint8Array(1),t=r?()=>r.getRandomValues(e)[0]:()=>Math.random()*255&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,n=>(+n^t()&15>>+n/4).toString(16))};function Us(r){return typeof r=="object"&&r!==null&&("name"in r&&r.name==="AbortError"||"message"in r&&String(r.message).includes("FetchRequestCanceledException"))}var qs=r=>{if(r instanceof Error)return r;if(typeof r=="object"&&r!==null){try{if(Object.prototype.toString.call(r)==="[object Error]"){let e=new Error(r.message,r.cause?{cause:r.cause}:{});return r.stack&&(e.stack=r.stack),r.cause&&!e.cause&&(e.cause=r.cause),r.name&&(e.name=r.name),e}}catch(e){}try{return new Error(JSON.stringify(r))}catch(e){}}return new Error(r)};var T=class extends Error{},X=class extends T{constructor(e,t,n,s){super(`${X.makeMessage(e,t,n)}`),this.status=e,this.headers=s,this.requestID=s==null?void 0:s.get("x-request-id"),this.error=t;let o=t;this.code=o==null?void 0:o.code,this.param=o==null?void 0:o.param,this.type=o==null?void 0:o.type}static makeMessage(e,t,n){let s=t!=null&&t.message?typeof t.message=="string"?t.message:JSON.stringify(t.message):t?JSON.stringify(t):n;return e&&s?`${e} ${s}`:e?`${e} status code (no body)`:s||"(no status code or body)"}static generate(e,t,n,s){if(!e||!s)return new $t({message:n,cause:qs(t)});let o=t==null?void 0:t.error;return e===400?new cn(e,o,n,s):e===401?new un(e,o,n,s):e===403?new fn(e,o,n,s):e===404?new dn(e,o,n,s):e===409?new hn(e,o,n,s):e===422?new pn(e,o,n,s):e===429?new mn(e,o,n,s):e>=500?new gn(e,o,n,s):new X(e,o,n,s)}},Q=class extends X{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}},$t=class extends X{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}},Ht=class extends $t{constructor({message:e}={}){super({message:e!=null?e:"Request timed out."})}},cn=class extends X{},un=class extends X{},fn=class extends X{},dn=class extends X{},hn=class extends X{},pn=class extends X{},mn=class extends X{},gn=class extends X{},yn=class extends T{constructor(){super("Could not parse response content as the length limit was reached")}},bn=class extends T{constructor(){super("Could not parse response content as the request was rejected by the content filter")}};var $y=/^[a-z][a-z0-9+.-]*:/i,id=r=>$y.test(r);function ad(r){return typeof r!="object"?{}:r!=null?r:{}}function ld(r){if(!r)return!0;for(let e in r)return!1;return!0}function cd(r,e){return Object.prototype.hasOwnProperty.call(r,e)}function wn(r){return r!=null&&typeof r=="object"&&!Array.isArray(r)}var ud=(r,e)=>{if(typeof e!="number"||!Number.isInteger(e))throw new T(`${r} must be an integer`);if(e<0)throw new T(`${r} must be a positive integer`);return e};var fd=r=>{try{return JSON.parse(r)}catch(e){return}};var Ze=r=>new Promise(e=>setTimeout(e,r));var pi={off:0,error:200,warn:300,info:400,debug:500},ql=(r,e,t)=>{if(r){if(cd(pi,r))return r;ne(t).warn(`${e} was set to ${JSON.stringify(r)}, expected one of ${JSON.stringify(Object.keys(pi))}`)}};function $s(){}function hi(r,e,t){return!e||pi[r]>pi[t]?$s:e[r].bind(e)}var Hy={error:$s,warn:$s,info:$s,debug:$s},dd=new WeakMap;function ne(r){var o;let e=r.logger,t=(o=r.logLevel)!=null?o:"off";if(!e)return Hy;let n=dd.get(e);if(n&&n[0]===t)return n[1];let s={error:hi("error",e,t),warn:hi("warn",e,t),info:hi("info",e,t),debug:hi("debug",e,t)};return dd.set(e,[t,s]),s}var wt=r=>(r.options&&(r.options={...r.options},delete r.options.headers),r.headers&&(r.headers=Object.fromEntries((r.headers instanceof Headers?[...r.headers]:Object.entries(r.headers)).map(([e,t])=>[e,e.toLowerCase()==="authorization"||e.toLowerCase()==="cookie"||e.toLowerCase()==="set-cookie"?"***":t]))),"retryOfRequestLogID"in r&&(r.retryOfRequestLogID&&(r.retryOf=r.retryOfRequestLogID),delete r.retryOfRequestLogID),r);var jt="5.1.1";var md=()=>typeof window!="undefined"&&typeof window.document!="undefined"&&typeof navigator!="undefined";function jy(){return typeof Deno!="undefined"&&Deno.build!=null?"deno":typeof EdgeRuntime!="undefined"?"edge":Object.prototype.toString.call(typeof globalThis.process!="undefined"?globalThis.process:0)==="[object process]"?"node":"unknown"}var Wy=()=>{var t,n,s,o,i;let r=jy();if(r==="deno")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":jt,"X-Stainless-OS":pd(Deno.build.os),"X-Stainless-Arch":hd(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:(n=(t=Deno.version)==null?void 0:t.deno)!=null?n:"unknown"};if(typeof EdgeRuntime!="undefined")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":jt,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if(r==="node")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":jt,"X-Stainless-OS":pd((s=globalThis.process.platform)!=null?s:"unknown"),"X-Stainless-Arch":hd((o=globalThis.process.arch)!=null?o:"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":(i=globalThis.process.version)!=null?i:"unknown"};let e=Vy();return e?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":jt,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${e.browser}`,"X-Stainless-Runtime-Version":e.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":jt,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function Vy(){if(typeof navigator=="undefined"||!navigator)return null;let r=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(let{key:e,pattern:t}of r){let n=t.exec(navigator.userAgent);if(n){let s=n[1]||0,o=n[2]||0,i=n[3]||0;return{browser:e,version:`${s}.${o}.${i}`}}}return null}var hd=r=>r==="x32"?"x32":r==="x86_64"||r==="x64"?"x64":r==="arm"?"arm":r==="aarch64"||r==="arm64"?"arm64":r?`other:${r}`:"unknown",pd=r=>(r=r.toLowerCase(),r.includes("ios")?"iOS":r==="android"?"Android":r==="darwin"?"MacOS":r==="win32"?"Windows":r==="freebsd"?"FreeBSD":r==="openbsd"?"OpenBSD":r==="linux"?"Linux":r?`Other:${r}`:"Unknown"),mi,gd=()=>mi!=null?mi:mi=Wy();function yd(){if(typeof fetch!="undefined")return fetch;throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}function $l(...r){let e=globalThis.ReadableStream;if(typeof e=="undefined")throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new e(...r)}function gi(r){let e=Symbol.asyncIterator in r?r[Symbol.asyncIterator]():r[Symbol.iterator]();return $l({start(){},async pull(t){let{done:n,value:s}=await e.next();n?t.close():t.enqueue(s)},async cancel(){var t;await((t=e.return)==null?void 0:t.call(e))}})}function Hl(r){if(r[Symbol.asyncIterator])return r;let e=r.getReader();return{async next(){try{let t=await e.read();return t!=null&&t.done&&e.releaseLock(),t}catch(t){throw e.releaseLock(),t}},async return(){let t=e.cancel();return e.releaseLock(),await t,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function bd(r){var n,s;if(r===null||typeof r!="object")return;if(r[Symbol.asyncIterator]){await((s=(n=r[Symbol.asyncIterator]()).return)==null?void 0:s.call(n));return}let e=r.getReader(),t=e.cancel();e.releaseLock(),await t}var wd=({headers:r,body:e})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(e)});var Hs="RFC3986",js={RFC1738:r=>String(r).replace(/%20/g,"+"),RFC3986:r=>String(r)},jl="RFC1738";var Jy=Array.isArray,et=(()=>{let r=[];for(let e=0;e<256;++e)r.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return r})();var Wl=1024,_d=(r,e,t,n,s)=>{if(r.length===0)return r;let o=r;if(typeof r=="symbol"?o=Symbol.prototype.toString.call(r):typeof r!="string"&&(o=String(r)),t==="iso-8859-1")return escape(o).replace(/%u[0-9a-f]{4}/gi,function(a){return"%26%23"+parseInt(a.slice(2),16)+"%3B"});let i="";for(let a=0;a<o.length;a+=Wl){let c=o.length>=Wl?o.slice(a,a+Wl):o,l=[];for(let f=0;f<c.length;++f){let u=c.charCodeAt(f);if(u===45||u===46||u===95||u===126||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||s===jl&&(u===40||u===41)){l[l.length]=c.charAt(f);continue}if(u<128){l[l.length]=et[u];continue}if(u<2048){l[l.length]=et[192|u>>6]+et[128|u&63];continue}if(u<55296||u>=57344){l[l.length]=et[224|u>>12]+et[128|u>>6&63]+et[128|u&63];continue}f+=1,u=65536+((u&1023)<<10|c.charCodeAt(f)&1023),l[l.length]=et[240|u>>18]+et[128|u>>12&63]+et[128|u>>6&63]+et[128|u&63]}i+=l.join("")}return i};function Sd(r){return!r||typeof r!="object"?!1:!!(r.constructor&&r.constructor.isBuffer&&r.constructor.isBuffer(r))}function Vl(r,e){if(Jy(r)){let t=[];for(let n=0;n<r.length;n+=1)t.push(e(r[n]));return t}return e(r)}var zy=Object.prototype.hasOwnProperty,xd={brackets(r){return String(r)+"[]"},comma:"comma",indices(r,e){return String(r)+"["+e+"]"},repeat(r){return String(r)}},tt=Array.isArray,Xy=Array.prototype.push,Ed=function(r,e){Xy.apply(r,tt(e)?e:[e])},Yy=Date.prototype.toISOString,se={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:_d,encodeValuesOnly:!1,format:Hs,formatter:js[Hs],indices:!1,serializeDate(r){return Yy.call(r)},skipNulls:!1,strictNullHandling:!1};function Qy(r){return typeof r=="string"||typeof r=="number"||typeof r=="boolean"||typeof r=="symbol"||typeof r=="bigint"}var Kl={};function vd(r,e,t,n,s,o,i,a,c,l,f,u,d,h,p,y,g,S){let _=r,A=S,P=0,x=!1;for(;(A=A.get(Kl))!==void 0&&!x;){let F=A.get(r);if(P+=1,typeof F!="undefined"){if(F===P)throw new RangeError("Cyclic object value");x=!0}typeof A.get(Kl)=="undefined"&&(P=0)}if(typeof l=="function"?_=l(e,_):_ instanceof Date?_=d==null?void 0:d(_):t==="comma"&&tt(_)&&(_=Vl(_,function(F){return F instanceof Date?d==null?void 0:d(F):F})),_===null){if(o)return c&&!y?c(e,se.encoder,g,"key",h):e;_=""}if(Qy(_)||Sd(_)){if(c){let F=y?e:c(e,se.encoder,g,"key",h);return[(p==null?void 0:p(F))+"="+(p==null?void 0:p(c(_,se.encoder,g,"value",h)))]}return[(p==null?void 0:p(e))+"="+(p==null?void 0:p(String(_)))]}let R=[];if(typeof _=="undefined")return R;let k;if(t==="comma"&&tt(_))y&&c&&(_=Vl(_,c)),k=[{value:_.length>0?_.join(",")||null:void 0}];else if(tt(l))k=l;else{let F=Object.keys(_);k=f?F.sort(f):F}let M=a?String(e).replace(/\./g,"%2E"):String(e),q=n&&tt(_)&&_.length===1?M+"[]":M;if(s&&tt(_)&&_.length===0)return q+"[]";for(let F=0;F<k.length;++F){let V=k[F],H=typeof V=="object"&&typeof V.value!="undefined"?V.value:_[V];if(i&&H===null)continue;let re=u&&a?V.replace(/\./g,"%2E"):V,ve=tt(_)?typeof t=="function"?t(q,re):q:q+(u?"."+re:"["+re+"]");S.set(r,P);let ue=new WeakMap;ue.set(Kl,S),Ed(R,vd(H,ve,t,n,s,o,i,a,t==="comma"&&y&&tt(_)?null:c,l,f,u,d,h,p,y,g,ue))}return R}function Zy(r=se){if(typeof r.allowEmptyArrays!="undefined"&&typeof r.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof r.encodeDotInKeys!="undefined"&&typeof r.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(r.encoder!==null&&typeof r.encoder!="undefined"&&typeof r.encoder!="function")throw new TypeError("Encoder has to be a function.");let e=r.charset||se.charset;if(typeof r.charset!="undefined"&&r.charset!=="utf-8"&&r.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let t=Hs;if(typeof r.format!="undefined"){if(!zy.call(js,r.format))throw new TypeError("Unknown format option provided.");t=r.format}let n=js[t],s=se.filter;(typeof r.filter=="function"||tt(r.filter))&&(s=r.filter);let o;if(r.arrayFormat&&r.arrayFormat in xd?o=r.arrayFormat:"indices"in r?o=r.indices?"indices":"repeat":o=se.arrayFormat,"commaRoundTrip"in r&&typeof r.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");let i=typeof r.allowDots=="undefined"?r.encodeDotInKeys?!0:se.allowDots:!!r.allowDots;return{addQueryPrefix:typeof r.addQueryPrefix=="boolean"?r.addQueryPrefix:se.addQueryPrefix,allowDots:i,allowEmptyArrays:typeof r.allowEmptyArrays=="boolean"?!!r.allowEmptyArrays:se.allowEmptyArrays,arrayFormat:o,charset:e,charsetSentinel:typeof r.charsetSentinel=="boolean"?r.charsetSentinel:se.charsetSentinel,commaRoundTrip:!!r.commaRoundTrip,delimiter:typeof r.delimiter=="undefined"?se.delimiter:r.delimiter,encode:typeof r.encode=="boolean"?r.encode:se.encode,encodeDotInKeys:typeof r.encodeDotInKeys=="boolean"?r.encodeDotInKeys:se.encodeDotInKeys,encoder:typeof r.encoder=="function"?r.encoder:se.encoder,encodeValuesOnly:typeof r.encodeValuesOnly=="boolean"?r.encodeValuesOnly:se.encodeValuesOnly,filter:s,format:t,formatter:n,serializeDate:typeof r.serializeDate=="function"?r.serializeDate:se.serializeDate,skipNulls:typeof r.skipNulls=="boolean"?r.skipNulls:se.skipNulls,sort:typeof r.sort=="function"?r.sort:null,strictNullHandling:typeof r.strictNullHandling=="boolean"?r.strictNullHandling:se.strictNullHandling}}function Gl(r,e={}){let t=r,n=Zy(e),s,o;typeof n.filter=="function"?(o=n.filter,t=o("",t)):tt(n.filter)&&(o=n.filter,s=o);let i=[];if(typeof t!="object"||t===null)return"";let a=xd[n.arrayFormat],c=a==="comma"&&n.commaRoundTrip;s||(s=Object.keys(t)),n.sort&&s.sort(n.sort);let l=new WeakMap;for(let d=0;d<s.length;++d){let h=s[d];n.skipNulls&&t[h]===null||Ed(i,vd(t[h],h,a,c,n.allowEmptyArrays,n.strictNullHandling,n.skipNulls,n.encodeDotInKeys,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,l))}let f=i.join(n.delimiter),u=n.addQueryPrefix===!0?"?":"";return n.charsetSentinel&&(n.charset==="iso-8859-1"?u+="utf8=%26%2310003%3B&":u+="utf8=%E2%9C%93&"),f.length>0?u+f:""}function Ad(r){let e=0;for(let s of r)e+=s.length;let t=new Uint8Array(e),n=0;for(let s of r)t.set(s,n),n+=s.length;return t}var yi;function _n(r){let e;return(yi!=null?yi:(e=new globalThis.TextEncoder,yi=e.encode.bind(e)))(r)}var bi;function Jl(r){let e;return(bi!=null?bi:(e=new globalThis.TextDecoder,bi=e.decode.bind(e)))(r)}var Ae,Ce,gr=class{constructor(){Ae.set(this,void 0),Ce.set(this,void 0),N(this,Ae,new Uint8Array,"f"),N(this,Ce,null,"f")}decode(e){if(e==null)return[];let t=e instanceof ArrayBuffer?new Uint8Array(e):typeof e=="string"?_n(e):e;N(this,Ae,Ad([b(this,Ae,"f"),t]),"f");let n=[],s;for(;(s=tb(b(this,Ae,"f"),b(this,Ce,"f")))!=null;){if(s.carriage&&b(this,Ce,"f")==null){N(this,Ce,s.index,"f");continue}if(b(this,Ce,"f")!=null&&(s.index!==b(this,Ce,"f")+1||s.carriage)){n.push(Jl(b(this,Ae,"f").subarray(0,b(this,Ce,"f")-1))),N(this,Ae,b(this,Ae,"f").subarray(b(this,Ce,"f")),"f"),N(this,Ce,null,"f");continue}let o=b(this,Ce,"f")!==null?s.preceding-1:s.preceding,i=Jl(b(this,Ae,"f").subarray(0,o));n.push(i),N(this,Ae,b(this,Ae,"f").subarray(s.index),"f"),N(this,Ce,null,"f")}return n}flush(){return b(this,Ae,"f").length?this.decode(`
`):[]}};Ae=new WeakMap,Ce=new WeakMap;gr.NEWLINE_CHARS=new Set([`
`,"\r"]);gr.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function tb(r,e){for(let s=e!=null?e:0;s<r.length;s++){if(r[s]===10)return{preceding:s,index:s+1,carriage:!1};if(r[s]===13)return{preceding:s,index:s+1,carriage:!0}}return null}function Cd(r){for(let n=0;n<r.length-1;n++){if(r[n]===10&&r[n+1]===10||r[n]===13&&r[n+1]===13)return n+2;if(r[n]===13&&r[n+1]===10&&n+3<r.length&&r[n+2]===13&&r[n+3]===10)return n+4}return-1}var we=class{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let n=!1;async function*s(){if(n)throw new T("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let o=!1;try{for await(let i of rb(e,t))if(!o){if(i.data.startsWith("[DONE]")){o=!0;continue}if(i.event===null||i.event.startsWith("response.")||i.event.startsWith("transcript.")){let a;try{a=JSON.parse(i.data)}catch(c){throw console.error("Could not parse message into JSON:",i.data),console.error("From chunk:",i.raw),c}if(a&&a.error)throw new X(void 0,a.error,void 0,e.headers);yield a}else{let a;try{a=JSON.parse(i.data)}catch(c){throw console.error("Could not parse message into JSON:",i.data),console.error("From chunk:",i.raw),c}if(i.event=="error")throw new X(void 0,a.error,a.message,void 0);yield{event:i.event,data:a}}}o=!0}catch(i){if(Us(i))return;throw i}finally{o||t.abort()}}return new we(s,t)}static fromReadableStream(e,t){let n=!1;async function*s(){let i=new gr,a=Hl(e);for await(let c of a)for(let l of i.decode(c))yield l;for(let c of i.flush())yield c}async function*o(){if(n)throw new T("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let i=!1;try{for await(let a of s())i||a&&(yield JSON.parse(a));i=!0}catch(a){if(Us(a))return;throw a}finally{i||t.abort()}}return new we(o,t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],n=this.iterator(),s=o=>({next:()=>{if(o.length===0){let i=n.next();e.push(i),t.push(i)}return o.shift()}});return[new we(()=>s(e),this.controller),new we(()=>s(t),this.controller)]}toReadableStream(){let e=this,t;return $l({async start(){t=e[Symbol.asyncIterator]()},async pull(n){try{let{value:s,done:o}=await t.next();if(o)return n.close();let i=_n(JSON.stringify(s)+`
`);n.enqueue(i)}catch(s){n.error(s)}},async cancel(){var n;await((n=t.return)==null?void 0:n.call(t))}})}};async function*rb(r,e){if(!r.body)throw e.abort(),typeof globalThis.navigator!="undefined"&&globalThis.navigator.product==="ReactNative"?new T("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new T("Attempted to iterate over a response with no body");let t=new zl,n=new gr,s=Hl(r.body);for await(let o of nb(s))for(let i of n.decode(o)){let a=t.decode(i);a&&(yield a)}for(let o of n.flush()){let i=t.decode(o);i&&(yield i)}}async function*nb(r){let e=new Uint8Array;for await(let t of r){if(t==null)continue;let n=t instanceof ArrayBuffer?new Uint8Array(t):typeof t=="string"?_n(t):t,s=new Uint8Array(e.length+n.length);s.set(e),s.set(n,e.length),e=s;let o;for(;(o=Cd(e))!==-1;)yield e.slice(0,o),e=e.slice(o)}e.length>0&&(yield e)}var zl=class{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let o={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],o}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,n,s]=sb(e,":");return s.startsWith(" ")&&(s=s.substring(1)),t==="event"?this.event=s:t==="data"&&this.data.push(s),null}};function sb(r,e){let t=r.indexOf(e);return t!==-1?[r.substring(0,t),e,r.substring(t+e.length)]:[r,"",""]}async function wi(r,e){let{response:t,requestLogID:n,retryOfRequestLogID:s,startTime:o}=e,i=await(async()=>{var u;if(e.options.stream)return ne(r).debug("response",t.status,t.url,t.headers,t.body),e.options.__streamClass?e.options.__streamClass.fromSSEResponse(t,e.controller):we.fromSSEResponse(t,e.controller);if(t.status===204)return null;if(e.options.__binaryResponse)return t;let a=t.headers.get("content-type"),c=(u=a==null?void 0:a.split(";")[0])==null?void 0:u.trim();if((c==null?void 0:c.includes("application/json"))||(c==null?void 0:c.endsWith("+json"))){let d=await t.json();return Xl(d,t)}return await t.text()})();return ne(r).debug(`[${n}] response parsed`,wt({retryOfRequestLogID:s,url:t.url,status:t.status,body:i,durationMs:Date.now()-o})),i}function Xl(r,e){return!r||typeof r!="object"||Array.isArray(r)?r:Object.defineProperty(r,"_request_id",{value:e.headers.get("x-request-id"),enumerable:!1})}var Ws,_t=class extends Promise{constructor(e,t,n=wi){super(s=>{s(null)}),this.responsePromise=t,this.parseResponse=n,Ws.set(this,void 0),N(this,Ws,e,"f")}_thenUnwrap(e){return new _t(b(this,Ws,"f"),this.responsePromise,async(t,n)=>Xl(e(await this.parseResponse(t,n),n),n.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(b(this,Ws,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}};Ws=new WeakMap;var _i,Si=class{constructor(e,t,n,s){_i.set(this,void 0),N(this,_i,e,"f"),this.options=s,this.response=t,this.body=n}hasNextPage(){return this.getPaginatedItems().length?this.nextPageRequestOptions()!=null:!1}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new T("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await b(this,_i,"f").requestAPIList(this.constructor,e)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(_i=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}},Vs=class extends _t{constructor(e,t,n){super(e,t,async(s,o)=>new n(s,o.response,await wi(s,o),o.options))}async*[Symbol.asyncIterator](){let e=await this;for await(let t of e)yield t}},rt=class extends Si{constructor(e,t,n,s){super(e,t,n,s),this.data=n.data||[],this.object=n.object}getPaginatedItems(){var e;return(e=this.data)!=null?e:[]}nextPageRequestOptions(){return null}},W=class extends Si{constructor(e,t,n,s){super(e,t,n,s),this.data=n.data||[],this.has_more=n.has_more||!1}getPaginatedItems(){var e;return(e=this.data)!=null?e:[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageRequestOptions(){var n;let e=this.getPaginatedItems(),t=(n=e[e.length-1])==null?void 0:n.id;return t?{...this.options,query:{...ad(this.options.query),after:t}}:null}};var Ql=()=>{var r;if(typeof File=="undefined"){let{process:e}=globalThis,t=typeof((r=e==null?void 0:e.versions)==null?void 0:r.node)=="string"&&parseInt(e.versions.node.split("."))<20;throw new Error("`File` is not defined as a global, which is required for file uploads."+(t?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function Sn(r,e,t){return Ql(),new File(r,e!=null?e:"unknown_file",t)}function Ks(r){return(typeof r=="object"&&r!==null&&("name"in r&&r.name&&String(r.name)||"url"in r&&r.url&&String(r.url)||"filename"in r&&r.filename&&String(r.filename)||"path"in r&&r.path&&String(r.path))||"").split(/[\\/]/).pop()||void 0}var Zl=r=>r!=null&&typeof r=="object"&&typeof r[Symbol.asyncIterator]=="function";var Pe=async(r,e)=>({...r,body:await ab(r.body,e)}),Pd=new WeakMap;function ib(r){let e=typeof r=="function"?r:r.fetch,t=Pd.get(e);if(t)return t;let n=(async()=>{try{let s="Response"in e?e.Response:(await e("data:,")).constructor,o=new FormData;return o.toString()!==await new s(o).text()}catch(s){return!0}})();return Pd.set(e,n),n}var ab=async(r,e)=>{if(!await ib(e))throw new TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let t=new FormData;return await Promise.all(Object.entries(r||{}).map(([n,s])=>Yl(t,n,s))),t},lb=r=>r instanceof Blob&&"name"in r;var Yl=async(r,e,t)=>{if(t!==void 0){if(t==null)throw new TypeError(`Received null for "${e}"; to pass null in FormData, you must use the string 'null'`);if(typeof t=="string"||typeof t=="number"||typeof t=="boolean")r.append(e,String(t));else if(t instanceof Response)r.append(e,Sn([await t.blob()],Ks(t)));else if(Zl(t))r.append(e,Sn([await new Response(gi(t)).blob()],Ks(t)));else if(lb(t))r.append(e,t,Ks(t));else if(Array.isArray(t))await Promise.all(t.map(n=>Yl(r,e+"[]",n)));else if(typeof t=="object")await Promise.all(Object.entries(t).map(([n,s])=>Yl(r,`${e}[${n}]`,s)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${t} instead`)}};var Rd=r=>r!=null&&typeof r=="object"&&typeof r.size=="number"&&typeof r.type=="string"&&typeof r.text=="function"&&typeof r.slice=="function"&&typeof r.arrayBuffer=="function",cb=r=>r!=null&&typeof r=="object"&&typeof r.name=="string"&&typeof r.lastModified=="number"&&Rd(r),ub=r=>r!=null&&typeof r=="object"&&typeof r.url=="string"&&typeof r.blob=="function";async function xi(r,e,t){if(Ql(),r=await r,cb(r))return r instanceof File?r:Sn([await r.arrayBuffer()],r.name);if(ub(r)){let s=await r.blob();return e||(e=new URL(r.url).pathname.split(/[\\/]/).pop()),Sn(await ec(s),e,t)}let n=await ec(r);if(e||(e=Ks(r)),!(t!=null&&t.type)){let s=n.find(o=>typeof o=="object"&&"type"in o&&o.type);typeof s=="string"&&(t={...t,type:s})}return Sn(n,e,t)}async function ec(r){var t;let e=[];if(typeof r=="string"||ArrayBuffer.isView(r)||r instanceof ArrayBuffer)e.push(r);else if(Rd(r))e.push(r instanceof Blob?r:await r.arrayBuffer());else if(Zl(r))for await(let n of r)e.push(...await ec(n));else{let n=(t=r==null?void 0:r.constructor)==null?void 0:t.name;throw new Error(`Unexpected data type: ${typeof r}${n?`; constructor: ${n}`:""}${fb(r)}`)}return e}function fb(r){return typeof r!="object"||r===null?"":`; props: [${Object.getOwnPropertyNames(r).map(t=>`"${t}"`).join(", ")}]`}var C=class{constructor(e){this._client=e}};function Td(r){return r.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}var hb=(r=Td)=>function(t,...n){if(t.length===1)return t[0];let s=!1,o=t.reduce((f,u,d)=>(/[?#]/.test(u)&&(s=!0),f+u+(d===n.length?"":(s?encodeURIComponent:r)(String(n[d])))),""),i=o.split(/[?#]/,1)[0],a=[],c=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi,l;for(;(l=c.exec(i))!==null;)a.push({start:l.index,length:l[0].length});if(a.length>0){let f=0,u=a.reduce((d,h)=>{let p=" ".repeat(h.start-f),y="^".repeat(h.length);return f=h.start+h.length,d+p+y},"");throw new T(`Path parameters result in path with invalid segments:
${o}
${u}`)}return o},v=hb(Td);var yr=class extends C{list(e,t={},n){return this._client.getAPIList(v`/chat/completions/${e}/messages`,W,{query:t,...n})}};function Id(r){return typeof r.parse=="function"}var xn=r=>(r==null?void 0:r.role)==="assistant",tc=r=>(r==null?void 0:r.role)==="tool";var rc,Ei,vi,Gs,Js,Ai,zs,St,Xs,Ci,Pi,En,kd,Wt=class{constructor(){rc.add(this),this.controller=new AbortController,Ei.set(this,void 0),vi.set(this,()=>{}),Gs.set(this,()=>{}),Js.set(this,void 0),Ai.set(this,()=>{}),zs.set(this,()=>{}),St.set(this,{}),Xs.set(this,!1),Ci.set(this,!1),Pi.set(this,!1),En.set(this,!1),N(this,Ei,new Promise((e,t)=>{N(this,vi,e,"f"),N(this,Gs,t,"f")}),"f"),N(this,Js,new Promise((e,t)=>{N(this,Ai,e,"f"),N(this,zs,t,"f")}),"f"),b(this,Ei,"f").catch(()=>{}),b(this,Js,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},b(this,rc,"m",kd).bind(this))},0)}_connected(){this.ended||(b(this,vi,"f").call(this),this._emit("connect"))}get ended(){return b(this,Xs,"f")}get errored(){return b(this,Ci,"f")}get aborted(){return b(this,Pi,"f")}abort(){this.controller.abort()}on(e,t){return(b(this,St,"f")[e]||(b(this,St,"f")[e]=[])).push({listener:t}),this}off(e,t){let n=b(this,St,"f")[e];if(!n)return this;let s=n.findIndex(o=>o.listener===t);return s>=0&&n.splice(s,1),this}once(e,t){return(b(this,St,"f")[e]||(b(this,St,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,n)=>{N(this,En,!0,"f"),e!=="error"&&this.once("error",n),this.once(e,t)})}async done(){N(this,En,!0,"f"),await b(this,Js,"f")}_emit(e,...t){if(b(this,Xs,"f"))return;e==="end"&&(N(this,Xs,!0,"f"),b(this,Ai,"f").call(this));let n=b(this,St,"f")[e];if(n&&(b(this,St,"f")[e]=n.filter(s=>!s.once),n.forEach(({listener:s})=>s(...t))),e==="abort"){let s=t[0];!b(this,En,"f")&&!(n!=null&&n.length)&&Promise.reject(s),b(this,Gs,"f").call(this,s),b(this,zs,"f").call(this,s),this._emit("end");return}if(e==="error"){let s=t[0];!b(this,En,"f")&&!(n!=null&&n.length)&&Promise.reject(s),b(this,Gs,"f").call(this,s),b(this,zs,"f").call(this,s),this._emit("end")}}_emitFinal(){}};Ei=new WeakMap,vi=new WeakMap,Gs=new WeakMap,Js=new WeakMap,Ai=new WeakMap,zs=new WeakMap,St=new WeakMap,Xs=new WeakMap,Ci=new WeakMap,Pi=new WeakMap,En=new WeakMap,rc=new WeakSet,kd=function(e){if(N(this,Ci,!0,"f"),e instanceof Error&&e.name==="AbortError"&&(e=new Q),e instanceof Q)return N(this,Pi,!0,"f"),this._emit("abort",e);if(e instanceof T)return this._emit("error",e);if(e instanceof Error){let t=new T(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new T(String(e)))};function Ys(r){return(r==null?void 0:r.$brand)==="auto-parseable-response-format"}function br(r){return(r==null?void 0:r.$brand)==="auto-parseable-tool"}function Od(r,e){return!e||!nc(e)?{...r,choices:r.choices.map(t=>({...t,message:{...t.message,parsed:null,...t.message.tool_calls?{tool_calls:t.message.tool_calls}:void 0}}))}:Qs(r,e)}function Qs(r,e){let t=r.choices.map(n=>{var s,o;if(n.finish_reason==="length")throw new yn;if(n.finish_reason==="content_filter")throw new bn;return{...n,message:{...n.message,...n.message.tool_calls?{tool_calls:(o=(s=n.message.tool_calls)==null?void 0:s.map(i=>yb(e,i)))!=null?o:void 0}:void 0,parsed:n.message.content&&!n.message.refusal?gb(e,n.message.content):null}}});return{...r,choices:t}}function gb(r,e){var t,n;return((t=r.response_format)==null?void 0:t.type)!=="json_schema"?null:((n=r.response_format)==null?void 0:n.type)==="json_schema"?"$parseRaw"in r.response_format?r.response_format.$parseRaw(e):JSON.parse(e):null}function yb(r,e){var n;let t=(n=r.tools)==null?void 0:n.find(s=>{var o;return((o=s.function)==null?void 0:o.name)===e.function.name});return{...e,function:{...e.function,parsed_arguments:br(t)?t.$parseRaw(e.function.arguments):t!=null&&t.function.strict?JSON.parse(e.function.arguments):null}}}function Md(r,e){var n;if(!r)return!1;let t=(n=r.tools)==null?void 0:n.find(s=>{var o;return((o=s.function)==null?void 0:o.name)===e.function.name});return br(t)||(t==null?void 0:t.function.strict)||!1}function nc(r){var e,t;return Ys(r.response_format)?!0:(t=(e=r.tools)==null?void 0:e.some(n=>br(n)||n.type==="function"&&n.function.strict===!0))!=null?t:!1}function Ld(r){for(let e of r!=null?r:[]){if(e.type!=="function")throw new T(`Currently only \`function\` tool types support auto-parsing; Received \`${e.type}\``);if(e.function.strict!==!0)throw new T(`The \`${e.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}var _e,sc,Ri,oc,ic,ac,Nd,Dd,bb=10,vn=class extends Wt{constructor(){super(...arguments),_e.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){var n;this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=(n=e.choices[0])==null?void 0:n.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),tc(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(xn(e)&&e.tool_calls)for(let n of e.tool_calls)n.type==="function"&&this._emit("functionToolCall",n.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new T("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),b(this,_e,"m",sc).call(this)}async finalMessage(){return await this.done(),b(this,_e,"m",Ri).call(this)}async finalFunctionToolCall(){return await this.done(),b(this,_e,"m",oc).call(this)}async finalFunctionToolCallResult(){return await this.done(),b(this,_e,"m",ic).call(this)}async totalUsage(){return await this.done(),b(this,_e,"m",ac).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=b(this,_e,"m",Ri).call(this);t&&this._emit("finalMessage",t);let n=b(this,_e,"m",sc).call(this);n&&this._emit("finalContent",n);let s=b(this,_e,"m",oc).call(this);s&&this._emit("finalFunctionToolCall",s);let o=b(this,_e,"m",ic).call(this);o!=null&&this._emit("finalFunctionToolCallResult",o),this._chatCompletions.some(i=>i.usage)&&this._emit("totalUsage",b(this,_e,"m",ac).call(this))}async _createChatCompletion(e,t,n){let s=n==null?void 0:n.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),b(this,_e,"m",Nd).call(this,t);let o=await e.chat.completions.create({...t,stream:!1},{...n,signal:this.controller.signal});return this._connected(),this._addChatCompletion(Qs(o,t))}async _runChatCompletion(e,t,n){for(let s of t.messages)this._addMessage(s,!1);return await this._createChatCompletion(e,t,n)}async _runTools(e,t,n){var h,p,y;let s="tool",{tool_choice:o="auto",stream:i,...a}=t,c=typeof o!="string"&&((h=o==null?void 0:o.function)==null?void 0:h.name),{maxChatCompletions:l=bb}=n||{},f=t.tools.map(g=>{if(br(g)){if(!g.$callback)throw new T("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:g.$callback,name:g.function.name,description:g.function.description||"",parameters:g.function.parameters,parse:g.$parseRaw,strict:!0}}}return g}),u={};for(let g of f)g.type==="function"&&(u[g.function.name||g.function.function.name]=g.function);let d="tools"in t?f.map(g=>g.type==="function"?{type:"function",function:{name:g.function.name||g.function.function.name,parameters:g.function.parameters,description:g.function.description,strict:g.function.strict}}:g):void 0;for(let g of t.messages)this._addMessage(g,!1);for(let g=0;g<l;++g){let _=(p=(await this._createChatCompletion(e,{...a,tool_choice:o,tools:d,messages:[...this.messages]},n)).choices[0])==null?void 0:p.message;if(!_)throw new T("missing message in ChatCompletion response");if(!((y=_.tool_calls)!=null&&y.length))return;for(let A of _.tool_calls){if(A.type!=="function")continue;let P=A.id,{name:x,arguments:R}=A.function,k=u[x];if(k){if(c&&c!==x){let V=`Invalid tool_call: ${JSON.stringify(x)}. ${JSON.stringify(c)} requested. Please try again`;this._addMessage({role:s,tool_call_id:P,content:V});continue}}else{let V=`Invalid tool_call: ${JSON.stringify(x)}. Available options are: ${Object.keys(u).map(H=>JSON.stringify(H)).join(", ")}. Please try again`;this._addMessage({role:s,tool_call_id:P,content:V});continue}let M;try{M=Id(k)?await k.parse(R):R}catch(V){let H=V instanceof Error?V.message:String(V);this._addMessage({role:s,tool_call_id:P,content:H});continue}let q=await k.function(M,this),F=b(this,_e,"m",Dd).call(this,q);if(this._addMessage({role:s,tool_call_id:P,content:F}),c)return}}}};_e=new WeakSet,sc=function(){var e;return(e=b(this,_e,"m",Ri).call(this).content)!=null?e:null},Ri=function(){var t,n;let e=this.messages.length;for(;e-- >0;){let s=this.messages[e];if(xn(s))return{...s,content:(t=s.content)!=null?t:null,refusal:(n=s.refusal)!=null?n:null}}throw new T("stream ended without producing a ChatCompletionMessage with role=assistant")},oc=function(){var e,t;for(let n=this.messages.length-1;n>=0;n--){let s=this.messages[n];if(xn(s)&&((e=s==null?void 0:s.tool_calls)!=null&&e.length))return(t=s.tool_calls.at(-1))==null?void 0:t.function}},ic=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(tc(t)&&t.content!=null&&typeof t.content=="string"&&this.messages.some(n=>{var s;return n.role==="assistant"&&((s=n.tool_calls)==null?void 0:s.some(o=>o.type==="function"&&o.id===t.tool_call_id))}))return t.content}},ac=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},Nd=function(e){if(e.n!=null&&e.n>1)throw new T("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},Dd=function(e){return typeof e=="string"?e:e===void 0?"undefined":JSON.stringify(e)};var wr=class extends vn{static runTools(e,t,n){let s=new wr,o={...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,t,o)),s}_addMessage(e,t=!0){super._addMessage(e,t),xn(e)&&e.content&&this._emit("content",e.content)}};var le={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,INF:384,SPECIAL:496,ATOM:499,COLLECTION:12,ALL:511},lc=class extends Error{},cc=class extends Error{};function wb(r,e=le.ALL){if(typeof r!="string")throw new TypeError(`expecting str, got ${typeof r}`);if(!r.trim())throw new Error(`${r} is empty`);return _b(r.trim(),e)}var _b=(r,e)=>{let t=r.length,n=0,s=d=>{throw new lc(`${d} at position ${n}`)},o=d=>{throw new cc(`${d} at position ${n}`)},i=()=>(u(),n>=t&&s("Unexpected end of input"),r[n]==='"'?a():r[n]==="{"?c():r[n]==="["?l():r.substring(n,n+4)==="null"||le.NULL&e&&t-n<4&&"null".startsWith(r.substring(n))?(n+=4,null):r.substring(n,n+4)==="true"||le.BOOL&e&&t-n<4&&"true".startsWith(r.substring(n))?(n+=4,!0):r.substring(n,n+5)==="false"||le.BOOL&e&&t-n<5&&"false".startsWith(r.substring(n))?(n+=5,!1):r.substring(n,n+8)==="Infinity"||le.INFINITY&e&&t-n<8&&"Infinity".startsWith(r.substring(n))?(n+=8,1/0):r.substring(n,n+9)==="-Infinity"||le.MINUS_INFINITY&e&&1<t-n&&t-n<9&&"-Infinity".startsWith(r.substring(n))?(n+=9,-1/0):r.substring(n,n+3)==="NaN"||le.NAN&e&&t-n<3&&"NaN".startsWith(r.substring(n))?(n+=3,NaN):f()),a=()=>{let d=n,h=!1;for(n++;n<t&&(r[n]!=='"'||h&&r[n-1]==="\\");)h=r[n]==="\\"?!h:!1,n++;if(r.charAt(n)=='"')try{return JSON.parse(r.substring(d,++n-Number(h)))}catch(p){o(String(p))}else if(le.STR&e)try{return JSON.parse(r.substring(d,n-Number(h))+'"')}catch(p){return JSON.parse(r.substring(d,r.lastIndexOf("\\"))+'"')}s("Unterminated string literal")},c=()=>{n++,u();let d={};try{for(;r[n]!=="}";){if(u(),n>=t&&le.OBJ&e)return d;let h=a();u(),n++;try{let p=i();Object.defineProperty(d,h,{value:p,writable:!0,enumerable:!0,configurable:!0})}catch(p){if(le.OBJ&e)return d;throw p}u(),r[n]===","&&n++}}catch(h){if(le.OBJ&e)return d;s("Expected '}' at end of object")}return n++,d},l=()=>{n++;let d=[];try{for(;r[n]!=="]";)d.push(i()),u(),r[n]===","&&n++}catch(h){if(le.ARR&e)return d;s("Expected ']' at end of array")}return n++,d},f=()=>{if(n===0){r==="-"&&le.NUM&e&&s("Not sure what '-' is");try{return JSON.parse(r)}catch(h){if(le.NUM&e)try{return r[r.length-1]==="."?JSON.parse(r.substring(0,r.lastIndexOf("."))):JSON.parse(r.substring(0,r.lastIndexOf("e")))}catch(p){}o(String(h))}}let d=n;for(r[n]==="-"&&n++;r[n]&&!",]}".includes(r[n]);)n++;n==t&&!(le.NUM&e)&&s("Unterminated number literal");try{return JSON.parse(r.substring(d,n))}catch(h){r.substring(d,n)==="-"&&le.NUM&e&&s("Not sure what '-' is");try{return JSON.parse(r.substring(d,r.lastIndexOf("e")))}catch(p){o(String(p))}}},u=()=>{for(;n<t&&` 
\r	`.includes(r[n]);)n++};return i()},uc=r=>wb(r,le.ALL^le.NUM);var oe,xt,An,Vt,fc,Ti,dc,hc,pc,Ii,mc,Bd,nt=class extends vn{constructor(e){super(),oe.add(this),xt.set(this,void 0),An.set(this,void 0),Vt.set(this,void 0),N(this,xt,e,"f"),N(this,An,[],"f")}get currentChatCompletionSnapshot(){return b(this,Vt,"f")}static fromReadableStream(e){let t=new nt(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,n){let s=new nt(t);return s._run(()=>s._runChatCompletion(e,{...t,stream:!0},{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createChatCompletion(e,t,n){var i;super._createChatCompletion;let s=n==null?void 0:n.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),b(this,oe,"m",fc).call(this);let o=await e.chat.completions.create({...t,stream:!0},{...n,signal:this.controller.signal});this._connected();for await(let a of o)b(this,oe,"m",dc).call(this,a);if((i=o.controller.signal)!=null&&i.aborted)throw new Q;return this._addChatCompletion(b(this,oe,"m",Ii).call(this))}async _fromReadableStream(e,t){var i;let n=t==null?void 0:t.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),b(this,oe,"m",fc).call(this),this._connected();let s=we.fromReadableStream(e,this.controller),o;for await(let a of s)o&&o!==a.id&&this._addChatCompletion(b(this,oe,"m",Ii).call(this)),b(this,oe,"m",dc).call(this,a),o=a.id;if((i=s.controller.signal)!=null&&i.aborted)throw new Q;return this._addChatCompletion(b(this,oe,"m",Ii).call(this))}[(xt=new WeakMap,An=new WeakMap,Vt=new WeakMap,oe=new WeakSet,fc=function(){this.ended||N(this,Vt,void 0,"f")},Ti=function(t){let n=b(this,An,"f")[t.index];return n||(n={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},b(this,An,"f")[t.index]=n,n)},dc=function(t){var s,o,i,a,c,l,f,u,d,h,p,y,g,S,_,A,P,x,R,k;if(this.ended)return;let n=b(this,oe,"m",Bd).call(this,t);this._emit("chunk",t,n);for(let M of t.choices){let q=n.choices[M.index];M.delta.content!=null&&((s=q.message)==null?void 0:s.role)==="assistant"&&((o=q.message)!=null&&o.content)&&(this._emit("content",M.delta.content,q.message.content),this._emit("content.delta",{delta:M.delta.content,snapshot:q.message.content,parsed:q.message.parsed})),M.delta.refusal!=null&&((i=q.message)==null?void 0:i.role)==="assistant"&&((a=q.message)!=null&&a.refusal)&&this._emit("refusal.delta",{delta:M.delta.refusal,snapshot:q.message.refusal}),((c=M.logprobs)==null?void 0:c.content)!=null&&((l=q.message)==null?void 0:l.role)==="assistant"&&this._emit("logprobs.content.delta",{content:(f=M.logprobs)==null?void 0:f.content,snapshot:(d=(u=q.logprobs)==null?void 0:u.content)!=null?d:[]}),((h=M.logprobs)==null?void 0:h.refusal)!=null&&((p=q.message)==null?void 0:p.role)==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:(y=M.logprobs)==null?void 0:y.refusal,snapshot:(S=(g=q.logprobs)==null?void 0:g.refusal)!=null?S:[]});let F=b(this,oe,"m",Ti).call(this,q);q.finish_reason&&(b(this,oe,"m",pc).call(this,q),F.current_tool_call_index!=null&&b(this,oe,"m",hc).call(this,q,F.current_tool_call_index));for(let V of(_=M.delta.tool_calls)!=null?_:[])F.current_tool_call_index!==V.index&&(b(this,oe,"m",pc).call(this,q),F.current_tool_call_index!=null&&b(this,oe,"m",hc).call(this,q,F.current_tool_call_index)),F.current_tool_call_index=V.index;for(let V of(A=M.delta.tool_calls)!=null?A:[]){let H=(P=q.message.tool_calls)==null?void 0:P[V.index];H!=null&&H.type&&((H==null?void 0:H.type)==="function"?this._emit("tool_calls.function.arguments.delta",{name:(x=H.function)==null?void 0:x.name,index:V.index,arguments:H.function.arguments,parsed_arguments:H.function.parsed_arguments,arguments_delta:(k=(R=V.function)==null?void 0:R.arguments)!=null?k:""}):(H==null||H.type,void 0))}}},hc=function(t,n){var i,a,c;if(b(this,oe,"m",Ti).call(this,t).done_tool_calls.has(n))return;let o=(i=t.message.tool_calls)==null?void 0:i[n];if(!o)throw new Error("no tool call snapshot");if(!o.type)throw new Error("tool call snapshot missing `type`");if(o.type==="function"){let l=(c=(a=b(this,xt,"f"))==null?void 0:a.tools)==null?void 0:c.find(f=>f.type==="function"&&f.function.name===o.function.name);this._emit("tool_calls.function.arguments.done",{name:o.function.name,index:n,arguments:o.function.arguments,parsed_arguments:br(l)?l.$parseRaw(o.function.arguments):l!=null&&l.function.strict?JSON.parse(o.function.arguments):null})}else o.type},pc=function(t){var s,o;let n=b(this,oe,"m",Ti).call(this,t);if(t.message.content&&!n.content_done){n.content_done=!0;let i=b(this,oe,"m",mc).call(this);this._emit("content.done",{content:t.message.content,parsed:i?i.$parseRaw(t.message.content):null})}t.message.refusal&&!n.refusal_done&&(n.refusal_done=!0,this._emit("refusal.done",{refusal:t.message.refusal})),(s=t.logprobs)!=null&&s.content&&!n.logprobs_content_done&&(n.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:t.logprobs.content})),(o=t.logprobs)!=null&&o.refusal&&!n.logprobs_refusal_done&&(n.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:t.logprobs.refusal}))},Ii=function(){if(this.ended)throw new T("stream has ended, this shouldn't happen");let t=b(this,Vt,"f");if(!t)throw new T("request ended without sending any chunks");return N(this,Vt,void 0,"f"),N(this,An,[],"f"),Sb(t,b(this,xt,"f"))},mc=function(){var n;let t=(n=b(this,xt,"f"))==null?void 0:n.response_format;return Ys(t)?t:null},Bd=function(t){var f,u,d,h,p,y;var n,s,o,i;let a=b(this,Vt,"f"),{choices:c,...l}=t;a?Object.assign(a,l):a=N(this,Vt,{...l,choices:[]},"f");for(let{delta:g,finish_reason:S,index:_,logprobs:A=null,...P}of t.choices){let x=a.choices[_];if(x||(x=a.choices[_]={finish_reason:S,index:_,message:{},logprobs:A,...P}),A)if(!x.logprobs)x.logprobs=Object.assign({},A);else{let{content:H,refusal:re,...ve}=A;Object.assign(x.logprobs,ve),H&&((f=(n=x.logprobs).content)!=null||(n.content=[]),x.logprobs.content.push(...H)),re&&((u=(s=x.logprobs).refusal)!=null||(s.refusal=[]),x.logprobs.refusal.push(...re))}if(S&&(x.finish_reason=S,b(this,xt,"f")&&nc(b(this,xt,"f")))){if(S==="length")throw new yn;if(S==="content_filter")throw new bn}if(Object.assign(x,P),!g)continue;let{content:R,refusal:k,function_call:M,role:q,tool_calls:F,...V}=g;if(Object.assign(x.message,V),k&&(x.message.refusal=(x.message.refusal||"")+k),q&&(x.message.role=q),M&&(x.message.function_call?(M.name&&(x.message.function_call.name=M.name),M.arguments&&((d=(o=x.message.function_call).arguments)!=null||(o.arguments=""),x.message.function_call.arguments+=M.arguments)):x.message.function_call=M),R&&(x.message.content=(x.message.content||"")+R,!x.message.refusal&&b(this,oe,"m",mc).call(this)&&(x.message.parsed=uc(x.message.content))),F){x.message.tool_calls||(x.message.tool_calls=[]);for(let{index:H,id:re,type:ve,function:ue,...Le}of F){let Qe=(h=(i=x.message.tool_calls)[H])!=null?h:i[H]={};Object.assign(Qe,Le),re&&(Qe.id=re),ve&&(Qe.type=ve),ue&&((y=Qe.function)!=null||(Qe.function={name:(p=ue.name)!=null?p:"",arguments:""})),ue!=null&&ue.name&&(Qe.function.name=ue.name),ue!=null&&ue.arguments&&(Qe.function.arguments+=ue.arguments,Md(b(this,xt,"f"),Qe)&&(Qe.function.parsed_arguments=uc(Qe.function.arguments)))}}}return a},Symbol.asyncIterator)](){let e=[],t=[],n=!1;return this.on("chunk",s=>{let o=t.shift();o?o.resolve(s):e.push(s)}),this.on("end",()=>{n=!0;for(let s of t)s.resolve(void 0);t.length=0}),this.on("abort",s=>{n=!0;for(let o of t)o.reject(s);t.length=0}),this.on("error",s=>{n=!0;for(let o of t)o.reject(s);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:n?{value:void 0,done:!0}:new Promise((o,i)=>t.push({resolve:o,reject:i})).then(o=>o?{value:o,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new we(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}};function Sb(r,e){let{id:t,choices:n,created:s,model:o,system_fingerprint:i,...a}=r,c={...a,id:t,choices:n.map(({message:l,finish_reason:f,index:u,logprobs:d,...h})=>{var A,P,x;if(!f)throw new T(`missing finish_reason for choice ${u}`);let{content:p=null,function_call:y,tool_calls:g,...S}=l,_=l.role;if(!_)throw new T(`missing role for choice ${u}`);if(y){let{arguments:R,name:k}=y;if(R==null)throw new T(`missing function_call.arguments for choice ${u}`);if(!k)throw new T(`missing function_call.name for choice ${u}`);return{...h,message:{content:p,function_call:{arguments:R,name:k},role:_,refusal:(A=l.refusal)!=null?A:null},finish_reason:f,index:u,logprobs:d}}return g?{...h,index:u,finish_reason:f,logprobs:d,message:{...S,role:_,content:p,refusal:(P=l.refusal)!=null?P:null,tool_calls:g.map((R,k)=>{let{function:M,type:q,id:F,...V}=R,{arguments:H,name:re,...ve}=M||{};if(F==null)throw new T(`missing choices[${u}].tool_calls[${k}].id
${ki(r)}`);if(q==null)throw new T(`missing choices[${u}].tool_calls[${k}].type
${ki(r)}`);if(re==null)throw new T(`missing choices[${u}].tool_calls[${k}].function.name
${ki(r)}`);if(H==null)throw new T(`missing choices[${u}].tool_calls[${k}].function.arguments
${ki(r)}`);return{...V,id:F,type:q,function:{...ve,name:re,arguments:H}}})}}:{...h,message:{...S,content:p,role:_,refusal:(x=l.refusal)!=null?x:null},finish_reason:f,index:u,logprobs:d}}),created:s,model:o,object:"chat.completion",...i?{system_fingerprint:i}:{}};return Od(c,e)}function ki(r){return JSON.stringify(r)}var Kt=class extends nt{static fromReadableStream(e){let t=new Kt(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,n){let s=new Kt(t),o={...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,t,o)),s}};var Et=class extends C{constructor(){super(...arguments),this.messages=new yr(this._client)}create(e,t){var n;return this._client.post("/chat/completions",{body:e,...t,stream:(n=e.stream)!=null?n:!1})}retrieve(e,t){return this._client.get(v`/chat/completions/${e}`,t)}update(e,t,n){return this._client.post(v`/chat/completions/${e}`,{body:t,...n})}list(e={},t){return this._client.getAPIList("/chat/completions",W,{query:e,...t})}delete(e,t){return this._client.delete(v`/chat/completions/${e}`,t)}parse(e,t){return Ld(e.tools),this._client.chat.completions.create(e,{...t,headers:{...t==null?void 0:t.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(n=>Qs(n,e))}runTools(e,t){return e.stream?Kt.runTools(this._client,e,t):wr.runTools(this._client,e,t)}stream(e,t){return nt.createChatCompletion(this._client,e,t)}};Et.Messages=yr;var Gt=class extends C{constructor(){super(...arguments),this.completions=new Et(this._client)}};Gt.Completions=Et;var Ud=Symbol("brand.privateNullableHeaders"),Fd=Array.isArray;function*Eb(r){if(!r)return;if(Ud in r){let{values:n,nulls:s}=r;yield*n.entries();for(let o of s)yield[o,null];return}let e=!1,t;r instanceof Headers?t=r.entries():Fd(r)?t=r:(e=!0,t=Object.entries(r!=null?r:{}));for(let n of t){let s=n[0];if(typeof s!="string")throw new TypeError("expected header name to be a string");let o=Fd(n[1])?n[1]:[n[1]],i=!1;for(let a of o)a!==void 0&&(e&&!i&&(i=!0,yield[s,null]),yield[s,a])}}var I=r=>{let e=new Headers,t=new Set;for(let n of r){let s=new Set;for(let[o,i]of Eb(n)){let a=o.toLowerCase();s.has(a)||(e.delete(o),s.add(a)),i===null?(e.delete(o),t.add(a)):(e.append(o,i),t.delete(a))}}return{[Ud]:!0,values:e,nulls:t}};var Cn=class extends C{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:I([{Accept:"application/octet-stream"},t==null?void 0:t.headers]),__binaryResponse:!0})}};var Pn=class extends C{create(e,t){var n;return this._client.post("/audio/transcriptions",Pe({body:e,...t,stream:(n=e.stream)!=null?n:!1,__metadata:{model:e.model}},this._client))}};var Rn=class extends C{create(e,t){return this._client.post("/audio/translations",Pe({body:e,...t,__metadata:{model:e.model}},this._client))}};var st=class extends C{constructor(){super(...arguments),this.transcriptions=new Pn(this._client),this.translations=new Rn(this._client),this.speech=new Cn(this._client)}};st.Transcriptions=Pn;st.Translations=Rn;st.Speech=Cn;var _r=class extends C{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(v`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",W,{query:e,...t})}cancel(e,t){return this._client.post(v`/batches/${e}/cancel`,t)}};var Tn=class extends C{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}retrieve(e,t){return this._client.get(v`/assistants/${e}`,{...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}update(e,t,n){return this._client.post(v`/assistants/${e}`,{body:t,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}list(e={},t){return this._client.getAPIList("/assistants",W,{query:e,...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}delete(e,t){return this._client.delete(v`/assistants/${e}`,{...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}};var In=class extends C{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}};var kn=class extends C{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}};var Jt=class extends C{constructor(){super(...arguments),this.sessions=new In(this._client),this.transcriptionSessions=new kn(this._client)}};Jt.Sessions=In;Jt.TranscriptionSessions=kn;var On=class extends C{create(e,t,n){return this._client.post(v`/threads/${e}/messages`,{body:t,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}retrieve(e,t,n){let{thread_id:s}=t;return this._client.get(v`/threads/${s}/messages/${e}`,{...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}update(e,t,n){let{thread_id:s,...o}=t;return this._client.post(v`/threads/${s}/messages/${e}`,{body:o,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}list(e,t={},n){return this._client.getAPIList(v`/threads/${e}/messages`,W,{query:t,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}delete(e,t,n){let{thread_id:s}=t;return this._client.delete(v`/threads/${s}/messages/${e}`,{...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}};var Mn=class extends C{retrieve(e,t,n){let{thread_id:s,run_id:o,...i}=t;return this._client.get(v`/threads/${s}/runs/${o}/steps/${e}`,{query:i,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}list(e,t,n){let{thread_id:s,...o}=t;return this._client.getAPIList(v`/threads/${s}/runs/${e}/steps`,W,{query:o,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}};var qd=r=>{if(typeof Buffer!="undefined"){let e=Buffer.from(r,"base64");return Array.from(new Float32Array(e.buffer,e.byteOffset,e.length/Float32Array.BYTES_PER_ELEMENT))}else{let e=atob(r),t=e.length,n=new Uint8Array(t);for(let s=0;s<t;s++)n[s]=e.charCodeAt(s);return Array.from(new Float32Array(n.buffer))}};var ot=r=>{var e,t,n,s,o,i;if(typeof globalThis.process!="undefined")return(n=(t=(e=globalThis.process.env)==null?void 0:e[r])==null?void 0:t.trim())!=null?n:void 0;if(typeof globalThis.Deno!="undefined")return(i=(o=(s=globalThis.Deno.env)==null?void 0:s.get)==null?void 0:o.call(s,r))==null?void 0:i.trim()};var fe,xr,gc,it,Oi,We,Er,Ln,Sr,Ni,Re,Mi,Li,to,Zs,eo,$d,Hd,jd,Wd,Vd,Kd,Gd,vt=class extends Wt{constructor(){super(...arguments),fe.add(this),gc.set(this,[]),it.set(this,{}),Oi.set(this,{}),We.set(this,void 0),Er.set(this,void 0),Ln.set(this,void 0),Sr.set(this,void 0),Ni.set(this,void 0),Re.set(this,void 0),Mi.set(this,void 0),Li.set(this,void 0),to.set(this,void 0)}[(gc=new WeakMap,it=new WeakMap,Oi=new WeakMap,We=new WeakMap,Er=new WeakMap,Ln=new WeakMap,Sr=new WeakMap,Ni=new WeakMap,Re=new WeakMap,Mi=new WeakMap,Li=new WeakMap,to=new WeakMap,fe=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],n=!1;return this.on("event",s=>{let o=t.shift();o?o.resolve(s):e.push(s)}),this.on("end",()=>{n=!0;for(let s of t)s.resolve(void 0);t.length=0}),this.on("abort",s=>{n=!0;for(let o of t)o.reject(s);t.length=0}),this.on("error",s=>{n=!0;for(let o of t)o.reject(s);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:n?{value:void 0,done:!0}:new Promise((o,i)=>t.push({resolve:o,reject:i})).then(o=>o?{value:o,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new xr;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){var o;let n=t==null?void 0:t.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),this._connected();let s=we.fromReadableStream(e,this.controller);for await(let i of s)b(this,fe,"m",Zs).call(this,i);if((o=s.controller.signal)!=null&&o.aborted)throw new Q;return this._addRun(b(this,fe,"m",eo).call(this))}toReadableStream(){return new we(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,n,s){let o=new xr;return o._run(()=>o._runToolAssistantStream(e,t,n,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),o}async _createToolAssistantStream(e,t,n,s){var c;let o=s==null?void 0:s.signal;o&&(o.aborted&&this.controller.abort(),o.addEventListener("abort",()=>this.controller.abort()));let i={...n,stream:!0},a=await e.submitToolOutputs(t,i,{...s,signal:this.controller.signal});this._connected();for await(let l of a)b(this,fe,"m",Zs).call(this,l);if((c=a.controller.signal)!=null&&c.aborted)throw new Q;return this._addRun(b(this,fe,"m",eo).call(this))}static createThreadAssistantStream(e,t,n){let s=new xr;return s._run(()=>s._threadAssistantStream(e,t,{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}})),s}static createAssistantStream(e,t,n,s){let o=new xr;return o._run(()=>o._runAssistantStream(e,t,n,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),o}currentEvent(){return b(this,Mi,"f")}currentRun(){return b(this,Li,"f")}currentMessageSnapshot(){return b(this,We,"f")}currentRunStepSnapshot(){return b(this,to,"f")}async finalRunSteps(){return await this.done(),Object.values(b(this,it,"f"))}async finalMessages(){return await this.done(),Object.values(b(this,Oi,"f"))}async finalRun(){if(await this.done(),!b(this,Er,"f"))throw Error("Final run was not received.");return b(this,Er,"f")}async _createThreadAssistantStream(e,t,n){var a;let s=n==null?void 0:n.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort()));let o={...t,stream:!0},i=await e.createAndRun(o,{...n,signal:this.controller.signal});this._connected();for await(let c of i)b(this,fe,"m",Zs).call(this,c);if((a=i.controller.signal)!=null&&a.aborted)throw new Q;return this._addRun(b(this,fe,"m",eo).call(this))}async _createAssistantStream(e,t,n,s){var c;let o=s==null?void 0:s.signal;o&&(o.aborted&&this.controller.abort(),o.addEventListener("abort",()=>this.controller.abort()));let i={...n,stream:!0},a=await e.create(t,i,{...s,signal:this.controller.signal});this._connected();for await(let l of a)b(this,fe,"m",Zs).call(this,l);if((c=a.controller.signal)!=null&&c.aborted)throw new Q;return this._addRun(b(this,fe,"m",eo).call(this))}static accumulateDelta(e,t){for(let[n,s]of Object.entries(t)){if(!e.hasOwnProperty(n)){e[n]=s;continue}let o=e[n];if(o==null){e[n]=s;continue}if(n==="index"||n==="type"){e[n]=s;continue}if(typeof o=="string"&&typeof s=="string")o+=s;else if(typeof o=="number"&&typeof s=="number")o+=s;else if(wn(o)&&wn(s))o=this.accumulateDelta(o,s);else if(Array.isArray(o)&&Array.isArray(s)){if(o.every(i=>typeof i=="string"||typeof i=="number")){o.push(...s);continue}for(let i of s){if(!wn(i))throw new Error(`Expected array delta entry to be an object but got: ${i}`);let a=i.index;if(a==null)throw console.error(i),new Error("Expected array delta entry to have an `index` property");if(typeof a!="number")throw new Error(`Expected array delta entry \`index\` property to be a number but got ${a}`);let c=o[a];c==null?o.push(i):o[a]=this.accumulateDelta(c,i)}continue}else throw Error(`Unhandled record type: ${n}, deltaValue: ${s}, accValue: ${o}`);e[n]=o}return e}_addRun(e){return e}async _threadAssistantStream(e,t,n){return await this._createThreadAssistantStream(t,e,n)}async _runAssistantStream(e,t,n,s){return await this._createAssistantStream(t,e,n,s)}async _runToolAssistantStream(e,t,n,s){return await this._createToolAssistantStream(t,e,n,s)}};xr=vt,Zs=function(e){if(!this.ended)switch(N(this,Mi,e,"f"),b(this,fe,"m",jd).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":b(this,fe,"m",Gd).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":b(this,fe,"m",Hd).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":b(this,fe,"m",$d).call(this,e);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier");default:}},eo=function(){if(this.ended)throw new T("stream has ended, this shouldn't happen");if(!b(this,Er,"f"))throw Error("Final run has not been received");return b(this,Er,"f")},$d=function(e){let[t,n]=b(this,fe,"m",Vd).call(this,e,b(this,We,"f"));N(this,We,t,"f"),b(this,Oi,"f")[t.id]=t;for(let s of n){let o=t.content[s.index];(o==null?void 0:o.type)=="text"&&this._emit("textCreated",o.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let s of e.data.delta.content){if(s.type=="text"&&s.text){let o=s.text,i=t.content[s.index];if(i&&i.type=="text")this._emit("textDelta",o,i.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=b(this,Ln,"f")){if(b(this,Sr,"f"))switch(b(this,Sr,"f").type){case"text":this._emit("textDone",b(this,Sr,"f").text,b(this,We,"f"));break;case"image_file":this._emit("imageFileDone",b(this,Sr,"f").image_file,b(this,We,"f"));break}N(this,Ln,s.index,"f")}N(this,Sr,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(b(this,Ln,"f")!==void 0){let s=e.data.content[b(this,Ln,"f")];if(s)switch(s.type){case"image_file":this._emit("imageFileDone",s.image_file,b(this,We,"f"));break;case"text":this._emit("textDone",s.text,b(this,We,"f"));break}}b(this,We,"f")&&this._emit("messageDone",e.data),N(this,We,void 0,"f")}},Hd=function(e){let t=b(this,fe,"m",Wd).call(this,e);switch(N(this,to,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let n=e.data.delta;if(n.step_details&&n.step_details.type=="tool_calls"&&n.step_details.tool_calls&&t.step_details.type=="tool_calls")for(let o of n.step_details.tool_calls)o.index==b(this,Ni,"f")?this._emit("toolCallDelta",o,t.step_details.tool_calls[o.index]):(b(this,Re,"f")&&this._emit("toolCallDone",b(this,Re,"f")),N(this,Ni,o.index,"f"),N(this,Re,t.step_details.tool_calls[o.index],"f"),b(this,Re,"f")&&this._emit("toolCallCreated",b(this,Re,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":N(this,to,void 0,"f"),e.data.step_details.type=="tool_calls"&&b(this,Re,"f")&&(this._emit("toolCallDone",b(this,Re,"f")),N(this,Re,void 0,"f")),this._emit("runStepDone",e.data,t);break;case"thread.run.step.in_progress":break}},jd=function(e){b(this,gc,"f").push(e),this._emit("event",e)},Wd=function(e){switch(e.event){case"thread.run.step.created":return b(this,it,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=b(this,it,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let n=e.data;if(n.delta){let s=xr.accumulateDelta(t,n.delta);b(this,it,"f")[e.data.id]=s}return b(this,it,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":b(this,it,"f")[e.data.id]=e.data;break}if(b(this,it,"f")[e.data.id])return b(this,it,"f")[e.data.id];throw new Error("No snapshot available")},Vd=function(e,t){let n=[];switch(e.event){case"thread.message.created":return[e.data,n];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let s=e.data;if(s.delta.content)for(let o of s.delta.content)if(o.index in t.content){let i=t.content[o.index];t.content[o.index]=b(this,fe,"m",Kd).call(this,o,i)}else t.content[o.index]=o,n.push(o);return[t,n];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,n];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},Kd=function(e,t){return xr.accumulateDelta(t,e)},Gd=function(e){switch(N(this,Li,e.data,"f"),e.event){case"thread.run.created":break;case"thread.run.queued":break;case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":N(this,Er,e.data,"f"),b(this,Re,"f")&&(this._emit("toolCallDone",b(this,Re,"f")),N(this,Re,void 0,"f"));break;case"thread.run.cancelling":break}};var vr=class extends C{constructor(){super(...arguments),this.steps=new Mn(this._client)}create(e,t,n){var i;let{include:s,...o}=t;return this._client.post(v`/threads/${e}/runs`,{query:{include:s},body:o,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers]),stream:(i=t.stream)!=null?i:!1})}retrieve(e,t,n){let{thread_id:s}=t;return this._client.get(v`/threads/${s}/runs/${e}`,{...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}update(e,t,n){let{thread_id:s,...o}=t;return this._client.post(v`/threads/${s}/runs/${e}`,{body:o,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}list(e,t={},n){return this._client.getAPIList(v`/threads/${e}/runs`,W,{query:t,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}cancel(e,t,n){let{thread_id:s}=t;return this._client.post(v`/threads/${s}/runs/${e}/cancel`,{...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}async createAndPoll(e,t,n){let s=await this.create(e,t,n);return await this.poll(s.id,{thread_id:e},n)}createAndStream(e,t,n){return vt.createAssistantStream(e,this._client.beta.threads.runs,t,n)}async poll(e,t,n){var o,i;let s=I([n==null?void 0:n.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":(i=(o=n==null?void 0:n.pollIntervalMs)==null?void 0:o.toString())!=null?i:void 0}]);for(;;){let{data:a,response:c}=await this.retrieve(e,t,{...n,headers:{...n==null?void 0:n.headers,...s}}).withResponse();switch(a.status){case"queued":case"in_progress":case"cancelling":let l=5e3;if(n!=null&&n.pollIntervalMs)l=n.pollIntervalMs;else{let f=c.headers.get("openai-poll-after-ms");if(f){let u=parseInt(f);isNaN(u)||(l=u)}}await Ze(l);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return a}}}stream(e,t,n){return vt.createAssistantStream(e,this._client.beta.threads.runs,t,n)}submitToolOutputs(e,t,n){var i;let{thread_id:s,...o}=t;return this._client.post(v`/threads/${s}/runs/${e}/submit_tool_outputs`,{body:o,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers]),stream:(i=t.stream)!=null?i:!1})}async submitToolOutputsAndPoll(e,t,n){let s=await this.submitToolOutputs(e,t,n);return await this.poll(s.id,t,n)}submitToolOutputsStream(e,t,n){return vt.createToolAssistantStream(e,this._client.beta.threads.runs,t,n)}};vr.Steps=Mn;var zt=class extends C{constructor(){super(...arguments),this.runs=new vr(this._client),this.messages=new On(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}retrieve(e,t){return this._client.get(v`/threads/${e}`,{...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}update(e,t,n){return this._client.post(v`/threads/${e}`,{body:t,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}delete(e,t){return this._client.delete(v`/threads/${e}`,{...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}createAndRun(e,t){var n;return this._client.post("/threads/runs",{body:e,...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers]),stream:(n=e.stream)!=null?n:!1})}async createAndRunPoll(e,t){let n=await this.createAndRun(e,t);return await this.runs.poll(n.id,{thread_id:n.thread_id},t)}createAndRunStream(e,t){return vt.createThreadAssistantStream(e,this._client.beta.threads,t)}};zt.Runs=vr;zt.Messages=On;var at=class extends C{constructor(){super(...arguments),this.realtime=new Jt(this._client),this.assistants=new Tn(this._client),this.threads=new zt(this._client)}};at.Realtime=Jt;at.Assistants=Tn;at.Threads=zt;var Ar=class extends C{create(e,t){var n;return this._client.post("/completions",{body:e,...t,stream:(n=e.stream)!=null?n:!1})}};var Nn=class extends C{retrieve(e,t,n){let{container_id:s}=t;return this._client.get(v`/containers/${s}/files/${e}/content`,{...n,headers:I([{Accept:"application/binary"},n==null?void 0:n.headers]),__binaryResponse:!0})}};var Cr=class extends C{constructor(){super(...arguments),this.content=new Nn(this._client)}create(e,t,n){return this._client.post(v`/containers/${e}/files`,Pe({body:t,...n},this._client))}retrieve(e,t,n){let{container_id:s}=t;return this._client.get(v`/containers/${s}/files/${e}`,n)}list(e,t={},n){return this._client.getAPIList(v`/containers/${e}/files`,W,{query:t,...n})}delete(e,t,n){let{container_id:s}=t;return this._client.delete(v`/containers/${s}/files/${e}`,{...n,headers:I([{Accept:"*/*"},n==null?void 0:n.headers])})}};Cr.Content=Nn;var Xt=class extends C{constructor(){super(...arguments),this.files=new Cr(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(v`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",W,{query:e,...t})}delete(e,t){return this._client.delete(v`/containers/${e}`,{...t,headers:I([{Accept:"*/*"},t==null?void 0:t.headers])})}};Xt.Files=Cr;var Pr=class extends C{create(e,t){let n=!!e.encoding_format,s=n?e.encoding_format:"base64";n&&ne(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let o=this._client.post("/embeddings",{body:{...e,encoding_format:s},...t});return n?o:(ne(this._client).debug("embeddings/decoding base64 embeddings from base64"),o._thenUnwrap(i=>(i&&i.data&&i.data.forEach(a=>{let c=a.embedding;a.embedding=qd(c)}),i)))}};var Dn=class extends C{retrieve(e,t,n){let{eval_id:s,run_id:o}=t;return this._client.get(v`/evals/${s}/runs/${o}/output_items/${e}`,n)}list(e,t,n){let{eval_id:s,...o}=t;return this._client.getAPIList(v`/evals/${s}/runs/${e}/output_items`,W,{query:o,...n})}};var Rr=class extends C{constructor(){super(...arguments),this.outputItems=new Dn(this._client)}create(e,t,n){return this._client.post(v`/evals/${e}/runs`,{body:t,...n})}retrieve(e,t,n){let{eval_id:s}=t;return this._client.get(v`/evals/${s}/runs/${e}`,n)}list(e,t={},n){return this._client.getAPIList(v`/evals/${e}/runs`,W,{query:t,...n})}delete(e,t,n){let{eval_id:s}=t;return this._client.delete(v`/evals/${s}/runs/${e}`,n)}cancel(e,t,n){let{eval_id:s}=t;return this._client.post(v`/evals/${s}/runs/${e}`,n)}};Rr.OutputItems=Dn;var Yt=class extends C{constructor(){super(...arguments),this.runs=new Rr(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(v`/evals/${e}`,t)}update(e,t,n){return this._client.post(v`/evals/${e}`,{body:t,...n})}list(e={},t){return this._client.getAPIList("/evals",W,{query:e,...t})}delete(e,t){return this._client.delete(v`/evals/${e}`,t)}};Yt.Runs=Rr;var Tr=class extends C{create(e,t){return this._client.post("/files",Pe({body:e,...t},this._client))}retrieve(e,t){return this._client.get(v`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",W,{query:e,...t})}delete(e,t){return this._client.delete(v`/files/${e}`,t)}content(e,t){return this._client.get(v`/files/${e}/content`,{...t,headers:I([{Accept:"application/binary"},t==null?void 0:t.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:n=30*60*1e3}={}){let s=new Set(["processed","error","deleted"]),o=Date.now(),i=await this.retrieve(e);for(;!i.status||!s.has(i.status);)if(await Ze(t),i=await this.retrieve(e),Date.now()-o>n)throw new Ht({message:`Giving up on waiting for file ${e} to finish processing after ${n} milliseconds.`});return i}};var Bn=class extends C{};var Fn=class extends C{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}};var Ir=class extends C{constructor(){super(...arguments),this.graders=new Fn(this._client)}};Ir.Graders=Fn;var Un=class extends C{create(e,t,n){return this._client.getAPIList(v`/fine_tuning/checkpoints/${e}/permissions`,rt,{body:t,method:"post",...n})}retrieve(e,t={},n){return this._client.get(v`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...n})}delete(e,t,n){let{fine_tuned_model_checkpoint:s}=t;return this._client.delete(v`/fine_tuning/checkpoints/${s}/permissions/${e}`,n)}};var kr=class extends C{constructor(){super(...arguments),this.permissions=new Un(this._client)}};kr.Permissions=Un;var qn=class extends C{list(e,t={},n){return this._client.getAPIList(v`/fine_tuning/jobs/${e}/checkpoints`,W,{query:t,...n})}};var Or=class extends C{constructor(){super(...arguments),this.checkpoints=new qn(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(v`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",W,{query:e,...t})}cancel(e,t){return this._client.post(v`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},n){return this._client.getAPIList(v`/fine_tuning/jobs/${e}/events`,W,{query:t,...n})}pause(e,t){return this._client.post(v`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(v`/fine_tuning/jobs/${e}/resume`,t)}};Or.Checkpoints=qn;var Ve=class extends C{constructor(){super(...arguments),this.methods=new Bn(this._client),this.jobs=new Or(this._client),this.checkpoints=new kr(this._client),this.alpha=new Ir(this._client)}};Ve.Methods=Bn;Ve.Jobs=Or;Ve.Checkpoints=kr;Ve.Alpha=Ir;var $n=class extends C{};var Qt=class extends C{constructor(){super(...arguments),this.graderModels=new $n(this._client)}};Qt.GraderModels=$n;var Mr=class extends C{createVariation(e,t){return this._client.post("/images/variations",Pe({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",Pe({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}};var Lr=class extends C{retrieve(e,t){return this._client.get(v`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",rt,e)}delete(e,t){return this._client.delete(v`/models/${e}`,t)}};var Nr=class extends C{create(e,t){return this._client.post("/moderations",{body:e,...t})}};function Jd(r,e){return!e||!Jb(e)?{...r,output_parsed:null,output:r.output.map(t=>t.type==="function_call"?{...t,parsed_arguments:null}:t.type==="message"?{...t,content:t.content.map(n=>({...n,parsed:null}))}:t)}:yc(r,e)}function yc(r,e){let t=r.output.map(s=>{if(s.type==="function_call")return{...s,parsed_arguments:Yb(e,s)};if(s.type==="message"){let o=s.content.map(i=>i.type==="output_text"?{...i,parsed:Gb(e,i.text)}:i);return{...s,content:o}}return s}),n=Object.assign({},r,{output:t});return Object.getOwnPropertyDescriptor(r,"output_text")||bc(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(let s of n.output)if(s.type==="message"){for(let o of s.content)if(o.type==="output_text"&&o.parsed!==null)return o.parsed}return null}}),n}function Gb(r,e){var t,n,s,o;return((n=(t=r.text)==null?void 0:t.format)==null?void 0:n.type)!=="json_schema"?null:"$parseRaw"in((s=r.text)==null?void 0:s.format)?((o=r.text)==null?void 0:o.format).$parseRaw(e):JSON.parse(e)}function Jb(r){var e;return!!Ys((e=r.text)==null?void 0:e.format)}function zb(r){return(r==null?void 0:r.$brand)==="auto-parseable-tool"}function Xb(r,e){return r.find(t=>t.type==="function"&&t.name===e)}function Yb(r,e){var n;let t=Xb((n=r.tools)!=null?n:[],e.name);return{...e,...e,parsed_arguments:zb(t)?t.$parseRaw(e.arguments):t!=null&&t.strict?JSON.parse(e.arguments):null}}function bc(r){let e=[];for(let t of r.output)if(t.type==="message")for(let n of t.content)n.type==="output_text"&&e.push(n.text);r.output_text=e.join("")}var Hn,Di,Zt,Bi,zd,Xd,Yd,Qd,jn=class extends Wt{constructor(e){super(),Hn.add(this),Di.set(this,void 0),Zt.set(this,void 0),Bi.set(this,void 0),N(this,Di,e,"f")}static createResponse(e,t,n){let s=new jn(t);return s._run(()=>s._createOrRetrieveResponse(e,t,{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createOrRetrieveResponse(e,t,n){var a,c;let s=n==null?void 0:n.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),b(this,Hn,"m",zd).call(this);let o,i=null;"response_id"in t?(o=await e.responses.retrieve(t.response_id,{stream:!0},{...n,signal:this.controller.signal,stream:!0}),i=(a=t.starting_after)!=null?a:null):o=await e.responses.create({...t,stream:!0},{...n,signal:this.controller.signal}),this._connected();for await(let l of o)b(this,Hn,"m",Xd).call(this,l,i);if((c=o.controller.signal)!=null&&c.aborted)throw new Q;return b(this,Hn,"m",Yd).call(this)}[(Di=new WeakMap,Zt=new WeakMap,Bi=new WeakMap,Hn=new WeakSet,zd=function(){this.ended||N(this,Zt,void 0,"f")},Xd=function(t,n){if(this.ended)return;let s=(i,a)=>{(n==null||a.sequence_number>n)&&this._emit(i,a)},o=b(this,Hn,"m",Qd).call(this,t);switch(s("event",t),t.type){case"response.output_text.delta":{let i=o.output[t.output_index];if(!i)throw new T(`missing output at index ${t.output_index}`);if(i.type==="message"){let a=i.content[t.content_index];if(!a)throw new T(`missing content at index ${t.content_index}`);if(a.type!=="output_text")throw new T(`expected content to be 'output_text', got ${a.type}`);s("response.output_text.delta",{...t,snapshot:a.text})}break}case"response.function_call_arguments.delta":{let i=o.output[t.output_index];if(!i)throw new T(`missing output at index ${t.output_index}`);i.type==="function_call"&&s("response.function_call_arguments.delta",{...t,snapshot:i.arguments});break}default:s(t.type,t);break}},Yd=function(){if(this.ended)throw new T("stream has ended, this shouldn't happen");let t=b(this,Zt,"f");if(!t)throw new T("request ended without sending any events");N(this,Zt,void 0,"f");let n=Qb(t,b(this,Di,"f"));return N(this,Bi,n,"f"),n},Qd=function(t){let n=b(this,Zt,"f");if(!n){if(t.type!=="response.created")throw new T(`When snapshot hasn't been set yet, expected 'response.created' event, got ${t.type}`);return n=N(this,Zt,t.response,"f"),n}switch(t.type){case"response.output_item.added":{n.output.push(t.item);break}case"response.content_part.added":{let s=n.output[t.output_index];if(!s)throw new T(`missing output at index ${t.output_index}`);s.type==="message"&&s.content.push(t.part);break}case"response.output_text.delta":{let s=n.output[t.output_index];if(!s)throw new T(`missing output at index ${t.output_index}`);if(s.type==="message"){let o=s.content[t.content_index];if(!o)throw new T(`missing content at index ${t.content_index}`);if(o.type!=="output_text")throw new T(`expected content to be 'output_text', got ${o.type}`);o.text+=t.delta}break}case"response.function_call_arguments.delta":{let s=n.output[t.output_index];if(!s)throw new T(`missing output at index ${t.output_index}`);s.type==="function_call"&&(s.arguments+=t.delta);break}case"response.completed":{N(this,Zt,t.response,"f");break}}return n},Symbol.asyncIterator)](){let e=[],t=[],n=!1;return this.on("event",s=>{let o=t.shift();o?o.resolve(s):e.push(s)}),this.on("end",()=>{n=!0;for(let s of t)s.resolve(void 0);t.length=0}),this.on("abort",s=>{n=!0;for(let o of t)o.reject(s);t.length=0}),this.on("error",s=>{n=!0;for(let o of t)o.reject(s);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:n?{value:void 0,done:!0}:new Promise((o,i)=>t.push({resolve:o,reject:i})).then(o=>o?{value:o,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=b(this,Bi,"f");if(!e)throw new T("stream ended without producing a ChatCompletion");return e}};function Qb(r,e){return Jd(r,e)}var Wn=class extends C{list(e,t={},n){return this._client.getAPIList(v`/responses/${e}/input_items`,W,{query:t,...n})}};var er=class extends C{constructor(){super(...arguments),this.inputItems=new Wn(this._client)}create(e,t){var n;return this._client.post("/responses",{body:e,...t,stream:(n=e.stream)!=null?n:!1})._thenUnwrap(s=>("object"in s&&s.object==="response"&&bc(s),s))}retrieve(e,t={},n){var s;return this._client.get(v`/responses/${e}`,{query:t,...n,stream:(s=t==null?void 0:t.stream)!=null?s:!1})}delete(e,t){return this._client.delete(v`/responses/${e}`,{...t,headers:I([{Accept:"*/*"},t==null?void 0:t.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(n=>yc(n,e))}stream(e,t){return jn.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(v`/responses/${e}/cancel`,t)}};er.InputItems=Wn;var Vn=class extends C{create(e,t,n){return this._client.post(v`/uploads/${e}/parts`,Pe({body:t,...n},this._client))}};var tr=class extends C{constructor(){super(...arguments),this.parts=new Vn(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(v`/uploads/${e}/cancel`,t)}complete(e,t,n){return this._client.post(v`/uploads/${e}/complete`,{body:t,...n})}};tr.Parts=Vn;var Zd=async r=>{let e=await Promise.allSettled(r),t=e.filter(s=>s.status==="rejected");if(t.length){for(let s of t)console.error(s.reason);throw new Error(`${t.length} promise(s) failed - see the above errors`)}let n=[];for(let s of e)s.status==="fulfilled"&&n.push(s.value);return n};var Kn=class extends C{create(e,t,n){return this._client.post(v`/vector_stores/${e}/file_batches`,{body:t,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}retrieve(e,t,n){let{vector_store_id:s}=t;return this._client.get(v`/vector_stores/${s}/file_batches/${e}`,{...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}cancel(e,t,n){let{vector_store_id:s}=t;return this._client.post(v`/vector_stores/${s}/file_batches/${e}/cancel`,{...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}async createAndPoll(e,t,n){let s=await this.create(e,t);return await this.poll(e,s.id,n)}listFiles(e,t,n){let{vector_store_id:s,...o}=t;return this._client.getAPIList(v`/vector_stores/${s}/file_batches/${e}/files`,W,{query:o,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}async poll(e,t,n){var o,i;let s=I([n==null?void 0:n.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":(i=(o=n==null?void 0:n.pollIntervalMs)==null?void 0:o.toString())!=null?i:void 0}]);for(;;){let{data:a,response:c}=await this.retrieve(t,{vector_store_id:e},{...n,headers:s}).withResponse();switch(a.status){case"in_progress":let l=5e3;if(n!=null&&n.pollIntervalMs)l=n.pollIntervalMs;else{let f=c.headers.get("openai-poll-after-ms");if(f){let u=parseInt(f);isNaN(u)||(l=u)}}await Ze(l);break;case"failed":case"cancelled":case"completed":return a}}}async uploadAndPoll(e,{files:t,fileIds:n=[]},s){var d;if(t==null||t.length==0)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let o=(d=s==null?void 0:s.maxConcurrency)!=null?d:5,i=Math.min(o,t.length),a=this._client,c=t.values(),l=[...n];async function f(h){for(let p of h){let y=await a.files.create({file:p,purpose:"assistants"},s);l.push(y.id)}}let u=Array(i).fill(c).map(f);return await Zd(u),await this.createAndPoll(e,{file_ids:l})}};var Gn=class extends C{create(e,t,n){return this._client.post(v`/vector_stores/${e}/files`,{body:t,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}retrieve(e,t,n){let{vector_store_id:s}=t;return this._client.get(v`/vector_stores/${s}/files/${e}`,{...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}update(e,t,n){let{vector_store_id:s,...o}=t;return this._client.post(v`/vector_stores/${s}/files/${e}`,{body:o,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}list(e,t={},n){return this._client.getAPIList(v`/vector_stores/${e}/files`,W,{query:t,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}delete(e,t,n){let{vector_store_id:s}=t;return this._client.delete(v`/vector_stores/${s}/files/${e}`,{...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}async createAndPoll(e,t,n){let s=await this.create(e,t,n);return await this.poll(e,s.id,n)}async poll(e,t,n){var o,i;let s=I([n==null?void 0:n.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":(i=(o=n==null?void 0:n.pollIntervalMs)==null?void 0:o.toString())!=null?i:void 0}]);for(;;){let a=await this.retrieve(t,{vector_store_id:e},{...n,headers:s}).withResponse(),c=a.data;switch(c.status){case"in_progress":let l=5e3;if(n!=null&&n.pollIntervalMs)l=n.pollIntervalMs;else{let f=a.response.headers.get("openai-poll-after-ms");if(f){let u=parseInt(f);isNaN(u)||(l=u)}}await Ze(l);break;case"failed":case"completed":return c}}}async upload(e,t,n){let s=await this._client.files.create({file:t,purpose:"assistants"},n);return this.create(e,{file_id:s.id},n)}async uploadAndPoll(e,t,n){let s=await this.upload(e,t,n);return await this.poll(e,s.id,n)}content(e,t,n){let{vector_store_id:s}=t;return this._client.getAPIList(v`/vector_stores/${s}/files/${e}/content`,rt,{...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}};var At=class extends C{constructor(){super(...arguments),this.files=new Gn(this._client),this.fileBatches=new Kn(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}retrieve(e,t){return this._client.get(v`/vector_stores/${e}`,{...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}update(e,t,n){return this._client.post(v`/vector_stores/${e}`,{body:t,...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",W,{query:e,...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}delete(e,t){return this._client.delete(v`/vector_stores/${e}`,{...t,headers:I([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}search(e,t,n){return this._client.getAPIList(v`/vector_stores/${e}/search`,rt,{body:t,method:"post",...n,headers:I([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}};At.Files=Gn;At.FileBatches=Kn;var wc,Fi,D=class{constructor({baseURL:e=ot("OPENAI_BASE_URL"),apiKey:t=ot("OPENAI_API_KEY"),organization:n=(i=>(i=ot("OPENAI_ORG_ID"))!=null?i:null)(),project:s=(a=>(a=ot("OPENAI_PROJECT_ID"))!=null?a:null)(),...o}={}){var f,u,d,h,p,y;if(Fi.set(this,void 0),this.completions=new Ar(this),this.chat=new Gt(this),this.embeddings=new Pr(this),this.files=new Tr(this),this.images=new Mr(this),this.audio=new st(this),this.moderations=new Nr(this),this.models=new Lr(this),this.fineTuning=new Ve(this),this.graders=new Qt(this),this.vectorStores=new At(this),this.beta=new at(this),this.batches=new _r(this),this.uploads=new tr(this),this.responses=new er(this),this.evals=new Yt(this),this.containers=new Xt(this),t===void 0)throw new T("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let c={apiKey:t,organization:n,project:s,...o,baseURL:e||"https://api.openai.com/v1"};if(!c.dangerouslyAllowBrowser&&md())throw new T(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new OpenAI({ apiKey, dangerouslyAllowBrowser: true });

https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety
`);this.baseURL=c.baseURL,this.timeout=(f=c.timeout)!=null?f:wc.DEFAULT_TIMEOUT,this.logger=(u=c.logger)!=null?u:console;let l="warn";this.logLevel=l,this.logLevel=(h=(d=ql(c.logLevel,"ClientOptions.logLevel",this))!=null?d:ql(ot("OPENAI_LOG"),"process.env['OPENAI_LOG']",this))!=null?h:l,this.fetchOptions=c.fetchOptions,this.maxRetries=(p=c.maxRetries)!=null?p:2,this.fetch=(y=c.fetch)!=null?y:yd(),N(this,Fi,wd,"f"),this._options=c,this.apiKey=t,this.organization=n,this.project=s}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return I([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return Gl(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${jt}`}defaultIdempotencyKey(){return`stainless-node-retry-${Ul()}`}makeStatusError(e,t,n,s){return X.generate(e,t,n,s)}buildURL(e,t){let n=id(e)?new URL(e):new URL(this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),s=this.defaultQuery();return ld(s)||(t={...s,...t}),typeof t=="object"&&t&&!Array.isArray(t)&&(n.search=this.stringifyQuery(t)),n.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:n}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,n){return this.request(Promise.resolve(n).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new _t(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,n){var S,_,A;let s=await e,o=(S=s.maxRetries)!=null?S:this.maxRetries;t==null&&(t=o),await this.prepareOptions(s);let{req:i,url:a,timeout:c}=this.buildRequest(s,{retryCount:o-t});await this.prepareRequest(i,{url:a,options:s});let l="log_"+(Math.random()*(1<<24)|0).toString(16).padStart(6,"0"),f=n===void 0?"":`, retryOf: ${n}`,u=Date.now();if(ne(this).debug(`[${l}] sending request`,wt({retryOfRequestLogID:n,method:s.method,url:a,options:s,headers:i.headers})),(_=s.signal)!=null&&_.aborted)throw new Q;let d=new AbortController,h=await this.fetchWithTimeout(a,i,c,d).catch(qs),p=Date.now();if(h instanceof Error){let P=`retrying, ${t} attempts remaining`;if((A=s.signal)!=null&&A.aborted)throw new Q;let x=Us(h)||/timed? ?out/i.test(String(h)+("cause"in h?String(h.cause):""));if(t)return ne(this).info(`[${l}] connection ${x?"timed out":"failed"} - ${P}`),ne(this).debug(`[${l}] connection ${x?"timed out":"failed"} (${P})`,wt({retryOfRequestLogID:n,url:a,durationMs:p-u,message:h.message})),this.retryRequest(s,t,n!=null?n:l);throw ne(this).info(`[${l}] connection ${x?"timed out":"failed"} - error; no more retries left`),ne(this).debug(`[${l}] connection ${x?"timed out":"failed"} (error; no more retries left)`,wt({retryOfRequestLogID:n,url:a,durationMs:p-u,message:h.message})),x?new Ht:new $t({cause:h})}let y=[...h.headers.entries()].filter(([P])=>P==="x-request-id").map(([P,x])=>", "+P+": "+JSON.stringify(x)).join(""),g=`[${l}${f}${y}] ${i.method} ${a} ${h.ok?"succeeded":"failed"} with status ${h.status} in ${p-u}ms`;if(!h.ok){let P=this.shouldRetry(h);if(t&&P){let F=`retrying, ${t} attempts remaining`;return await bd(h.body),ne(this).info(`${g} - ${F}`),ne(this).debug(`[${l}] response error (${F})`,wt({retryOfRequestLogID:n,url:h.url,status:h.status,headers:h.headers,durationMs:p-u})),this.retryRequest(s,t,n!=null?n:l,h.headers)}let x=P?"error; no more retries left":"error; not retryable";ne(this).info(`${g} - ${x}`);let R=await h.text().catch(F=>qs(F).message),k=fd(R),M=k?void 0:R;throw ne(this).debug(`[${l}] response error (${x})`,wt({retryOfRequestLogID:n,url:h.url,status:h.status,headers:h.headers,message:M,durationMs:Date.now()-u})),this.makeStatusError(h.status,k,M,h.headers)}return ne(this).info(g),ne(this).debug(`[${l}] response start`,wt({retryOfRequestLogID:n,url:h.url,status:h.status,headers:h.headers,durationMs:p-u})),{response:h,options:s,controller:d,requestLogID:l,retryOfRequestLogID:n,startTime:u}}getAPIList(e,t,n){return this.requestAPIList(t,{method:"get",path:e,...n})}requestAPIList(e,t){let n=this.makeRequest(t,null,void 0);return new Vs(this,n,e)}async fetchWithTimeout(e,t,n,s){let{signal:o,method:i,...a}=t||{};o&&o.addEventListener("abort",()=>s.abort());let c=setTimeout(()=>s.abort(),n),l=globalThis.ReadableStream&&a.body instanceof globalThis.ReadableStream||typeof a.body=="object"&&a.body!==null&&Symbol.asyncIterator in a.body,f={signal:s.signal,...l?{duplex:"half"}:{},method:"GET",...a};i&&(f.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,f)}finally{clearTimeout(c)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return t==="true"?!0:t==="false"?!1:e.status===408||e.status===409||e.status===429||e.status>=500}async retryRequest(e,t,n,s){var c;let o,i=s==null?void 0:s.get("retry-after-ms");if(i){let l=parseFloat(i);Number.isNaN(l)||(o=l)}let a=s==null?void 0:s.get("retry-after");if(a&&!o){let l=parseFloat(a);Number.isNaN(l)?o=Date.parse(a)-Date.now():o=l*1e3}if(!(o&&0<=o&&o<60*1e3)){let l=(c=e.maxRetries)!=null?c:this.maxRetries;o=this.calculateDefaultRetryTimeoutMillis(t,l)}return await Ze(o),this.makeRequest(e,t-1,n)}calculateDefaultRetryTimeoutMillis(e,t){let o=t-e,i=Math.min(.5*Math.pow(2,o),8),a=1-Math.random()*.25;return i*a*1e3}buildRequest(e,{retryCount:t=0}={}){var d,h,p;let n={...e},{method:s,path:o,query:i}=n,a=this.buildURL(o,i);"timeout"in n&&ud("timeout",n.timeout),n.timeout=(d=n.timeout)!=null?d:this.timeout;let{bodyHeaders:c,body:l}=this.buildBody({options:n}),f=this.buildHeaders({options:e,method:s,bodyHeaders:c,retryCount:t});return{req:{method:s,headers:f,...n.signal&&{signal:n.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...(h=this.fetchOptions)!=null?h:{},...(p=n.fetchOptions)!=null?p:{}},url:a,timeout:n.timeout}}buildHeaders({options:e,method:t,bodyHeaders:n,retryCount:s}){let o={};this.idempotencyHeader&&t!=="get"&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),o[this.idempotencyHeader]=e.idempotencyKey);let i=I([o,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(s),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...gd(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(e),this._options.defaultHeaders,n,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let n=I([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||typeof e=="string"&&n.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:typeof e=="object"&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&typeof e.next=="function")?{bodyHeaders:void 0,body:gi(e)}:b(this,Fi,"f").call(this,{body:e,headers:n})}};wc=D,Fi=new WeakMap;D.OpenAI=wc;D.DEFAULT_TIMEOUT=6e5;D.OpenAIError=T;D.APIError=X;D.APIConnectionError=$t;D.APIConnectionTimeoutError=Ht;D.APIUserAbortError=Q;D.NotFoundError=dn;D.ConflictError=hn;D.RateLimitError=mn;D.BadRequestError=cn;D.AuthenticationError=un;D.InternalServerError=gn;D.PermissionDeniedError=fn;D.UnprocessableEntityError=pn;D.toFile=xi;D.Completions=Ar;D.Chat=Gt;D.Embeddings=Pr;D.Files=Tr;D.Images=Mr;D.Audio=st;D.Moderations=Nr;D.Models=Lr;D.FineTuning=Ve;D.Graders=Qt;D.VectorStores=At;D.Beta=at;D.Batches=_r;D.Uploads=tr;D.Responses=er;D.Evals=Yt;D.Containers=Xt;var ro=class extends D{constructor({baseURL:e=ot("OPENAI_BASE_URL"),apiKey:t=ot("AZURE_OPENAI_API_KEY"),apiVersion:n=ot("OPENAI_API_VERSION"),endpoint:s,deployment:o,azureADTokenProvider:i,dangerouslyAllowBrowser:a,...c}={}){if(!n)throw new T("The OPENAI_API_VERSION environment variable is missing or empty; either provide it, or instantiate the AzureOpenAI client with an apiVersion option, like new AzureOpenAI({ apiVersion: 'My API Version' }).");if(typeof i=="function"&&(a=!0),!i&&!t)throw new T("Missing credentials. Please pass one of `apiKey` and `azureADTokenProvider`, or set the `AZURE_OPENAI_API_KEY` environment variable.");if(i&&t)throw new T("The `apiKey` and `azureADTokenProvider` arguments are mutually exclusive; only one can be passed at a time.");if(t!=null||(t=eh),c.defaultQuery={...c.defaultQuery,"api-version":n},e){if(s)throw new T("baseURL and endpoint are mutually exclusive")}else{if(s||(s=process.env.AZURE_OPENAI_ENDPOINT),!s)throw new T("Must provide one of the `baseURL` or `endpoint` arguments, or the `AZURE_OPENAI_ENDPOINT` environment variable");e=`${s}/openai`}super({apiKey:t,baseURL:e,...c,...a!==void 0?{dangerouslyAllowBrowser:a}:{}}),this.apiVersion="",this._azureADTokenProvider=i,this.apiVersion=n,this.deploymentName=o}buildRequest(e,t={}){var n;if(sw.has(e.path)&&e.method==="post"&&e.body!==void 0){if(!wn(e.body))throw new Error("Expected request body to be an object");let s=this.deploymentName||e.body.model||((n=e.__metadata)==null?void 0:n.model);s!==void 0&&!this.baseURL.includes("/deployments")&&(e.path=`/deployments/${s}${e.path}`)}return super.buildRequest(e,t)}async _getAzureADToken(){if(typeof this._azureADTokenProvider=="function"){let e=await this._azureADTokenProvider();if(!e||typeof e!="string")throw new T(`Expected 'azureADTokenProvider' argument to return a string but it returned ${e}`);return e}}authHeaders(e){}async prepareOptions(e){if(e.headers=I([e.headers]),e.headers.values.get("Authorization")||e.headers.values.get("api-key"))return super.prepareOptions(e);let t=await this._getAzureADToken();if(t)e.headers.values.set("Authorization",`Bearer ${t}`);else if(this.apiKey!==eh)e.headers.values.set("api-key",this.apiKey);else throw new T("Unable to handle auth");return super.prepareOptions(e)}},sw=new Set(["/completions","/chat/completions","/embeddings","/audio/transcriptions","/audio/translations","/audio/speech","/images/generations","/batches","/images/edits"]),eh="<Missing Key>";var Dr=r=>{var n;let e=((n=r.split(".").pop())==null?void 0:n.toLowerCase())||"";return{png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg",gif:"image/gif",webp:"image/webp",svg:"image/svg+xml",bmp:"image/bmp",ico:"image/x-icon",pdf:"application/pdf",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",txt:"text/plain",html:"text/html",css:"text/css",js:"application/javascript",json:"application/json",xml:"application/xml",md:"text/markdown",mp3:"audio/mpeg",wav:"audio/wav",ogg:"audio/ogg",flac:"audio/flac",m4a:"audio/mp4",mp4:"video/mp4",avi:"video/x-msvideo",mov:"video/quicktime",wmv:"video/x-ms-wmv",webm:"video/webm"}[e]||"application/octet-stream"},lt=` 

> [!quote]-  
> `,ct=`

`,Br=r=>{let e="",t=new Uint8Array(r),n=t.byteLength;for(let s=0;s<n;s++)e+=String.fromCharCode(t[s]);return window.btoa(e)},rr=async(r,e)=>{let t=Dr(r.link);if(["image/png","image/jpeg","image/gif","image/webp"].includes(t)===!1)throw new Error(m("Only PNG, JPEG, GIF, and WebP images are supported."));let n=await e(r),s=Br(n);return{type:"image_url",image_url:{url:`data:${t};base64,${s}`}}},Jn=r=>{switch(r){case"Text Generation":return"\u270D\uFE0F";case"Image Vision":return"\u{1F441}\uFE0F";case"PDF Vision":return"\u{1F4C4}";case"Image Generation":return"\u{1F3A8}";case"Image Editing":return"\u270F\uFE0F";case"Web Search":return"\u{1F50D}";case"Reasoning":return"\u{1F9E0}"}};var ow=r=>async function*(e,t,n){var S,_;let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,model:c,endpoint:l,apiVersion:f,...u}=i;if(!a)throw new Error(m("API key is required"));let d=new ro({endpoint:l,apiKey:a,apiVersion:f,deployment:c,dangerouslyAllowBrowser:!0});e=[{role:"system",content:`Initiate your response with "<think>
\u55EF" at the beginning of every output.`},...e];let h=await d.chat.completions.create({model:c,messages:e,stream:!0,...u},{signal:t.signal}),p=!1,y=!1,g=!1;for await(let A of h){A.usage&&A.usage.prompt_tokens&&A.usage.completion_tokens&&console.debug(`Prompt tokens: ${A.usage.prompt_tokens}, completion tokens: ${A.usage.completion_tokens}`);let P=(_=(S=A.choices[0])==null?void 0:S.delta)==null?void 0:_.content;if(P){if(P==="<think>"){if(y)continue;p=!0,y=!0,yield lt;continue}if(P==="</think>"){if(g)continue;p=!1,g=!0,yield ct;continue}yield p?P.replace(/\n/g,`
> `):P}}},th=["o3-mini","deepseek-r1","phi-4","o1","o1-mini","gpt-4o","gpt-4o-mini"],rh={name:"Azure",defaultOptions:{apiKey:"",baseURL:"",model:th[0],endpoint:"",apiVersion:"",parameters:{}},sendRequestFunc:ow,models:th,websiteToObtainKey:"https://portal.azure.com",capabilities:["Text Generation","Reasoning"]};function U(r,e,t,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(r,t):s?s.value=t:e.set(r,t),t}function E(r,e,t,n){if(t==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?n:t==="a"?n.call(r):n?n.value:e.get(r)}var _c=function(){let{crypto:r}=globalThis;if(r!=null&&r.randomUUID)return _c=r.randomUUID.bind(r),r.randomUUID();let e=new Uint8Array(1),t=r?()=>r.getRandomValues(e)[0]:()=>Math.random()*255&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,n=>(+n^t()&15>>+n/4).toString(16))};function Ct(r){return typeof r=="object"&&r!==null&&("name"in r&&r.name==="AbortError"||"message"in r&&String(r.message).includes("FetchRequestCanceledException"))}var no=r=>{if(r instanceof Error)return r;if(typeof r=="object"&&r!==null){try{if(Object.prototype.toString.call(r)==="[object Error]"){let e=new Error(r.message,r.cause?{cause:r.cause}:{});return r.stack&&(e.stack=r.stack),r.cause&&!e.cause&&(e.cause=r.cause),r.name&&(e.name=r.name),e}}catch(e){}try{return new Error(JSON.stringify(r))}catch(e){}}return new Error(r)};var B=class extends Error{},Y=class extends B{constructor(e,t,n,s){super(`${Y.makeMessage(e,t,n)}`),this.status=e,this.headers=s,this.requestID=s==null?void 0:s.get("request-id"),this.error=t}static makeMessage(e,t,n){let s=t!=null&&t.message?typeof t.message=="string"?t.message:JSON.stringify(t.message):t?JSON.stringify(t):n;return e&&s?`${e} ${s}`:e?`${e} status code (no body)`:s||"(no status code or body)"}static generate(e,t,n,s){if(!e||!s)return new nr({message:n,cause:no(t)});let o=t;return e===400?new Xn(e,o,n,s):e===401?new Yn(e,o,n,s):e===403?new Qn(e,o,n,s):e===404?new Zn(e,o,n,s):e===409?new es(e,o,n,s):e===422?new ts(e,o,n,s):e===429?new rs(e,o,n,s):e>=500?new ns(e,o,n,s):new Y(e,o,n,s)}},de=class extends Y{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}},nr=class extends Y{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}},zn=class extends nr{constructor({message:e}={}){super({message:e!=null?e:"Request timed out."})}},Xn=class extends Y{},Yn=class extends Y{},Qn=class extends Y{},Zn=class extends Y{},es=class extends Y{},ts=class extends Y{},rs=class extends Y{},ns=class extends Y{};var aw=/^[a-z][a-z0-9+.-]*:/i,nh=r=>aw.test(r),Sc=r=>(Sc=Array.isArray,Sc(r)),xc=Sc;function Ec(r){return typeof r!="object"?{}:r!=null?r:{}}function sh(r){if(!r)return!0;for(let e in r)return!1;return!0}function oh(r,e){return Object.prototype.hasOwnProperty.call(r,e)}var ih=(r,e)=>{if(typeof e!="number"||!Number.isInteger(e))throw new B(`${r} must be an integer`);if(e<0)throw new B(`${r} must be a positive integer`);return e};var Ui=r=>{try{return JSON.parse(r)}catch(e){return}};var ah=r=>new Promise(e=>setTimeout(e,r));var sr="0.56.0";var uh=()=>typeof window!="undefined"&&typeof window.document!="undefined"&&typeof navigator!="undefined";function lw(){return typeof Deno!="undefined"&&Deno.build!=null?"deno":typeof EdgeRuntime!="undefined"?"edge":Object.prototype.toString.call(typeof globalThis.process!="undefined"?globalThis.process:0)==="[object process]"?"node":"unknown"}var cw=()=>{var t,n,s,o,i;let r=lw();if(r==="deno")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":sr,"X-Stainless-OS":ch(Deno.build.os),"X-Stainless-Arch":lh(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:(n=(t=Deno.version)==null?void 0:t.deno)!=null?n:"unknown"};if(typeof EdgeRuntime!="undefined")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":sr,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if(r==="node")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":sr,"X-Stainless-OS":ch((s=globalThis.process.platform)!=null?s:"unknown"),"X-Stainless-Arch":lh((o=globalThis.process.arch)!=null?o:"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":(i=globalThis.process.version)!=null?i:"unknown"};let e=uw();return e?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":sr,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${e.browser}`,"X-Stainless-Runtime-Version":e.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":sr,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function uw(){if(typeof navigator=="undefined"||!navigator)return null;let r=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(let{key:e,pattern:t}of r){let n=t.exec(navigator.userAgent);if(n){let s=n[1]||0,o=n[2]||0,i=n[3]||0;return{browser:e,version:`${s}.${o}.${i}`}}}return null}var lh=r=>r==="x32"?"x32":r==="x86_64"||r==="x64"?"x64":r==="arm"?"arm":r==="aarch64"||r==="arm64"?"arm64":r?`other:${r}`:"unknown",ch=r=>(r=r.toLowerCase(),r.includes("ios")?"iOS":r==="android"?"Android":r==="darwin"?"MacOS":r==="win32"?"Windows":r==="freebsd"?"FreeBSD":r==="openbsd"?"OpenBSD":r==="linux"?"Linux":r?`Other:${r}`:"Unknown"),qi,fh=()=>qi!=null?qi:qi=cw();function dh(){if(typeof fetch!="undefined")return fetch;throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}function vc(...r){let e=globalThis.ReadableStream;if(typeof e=="undefined")throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new e(...r)}function $i(r){let e=Symbol.asyncIterator in r?r[Symbol.asyncIterator]():r[Symbol.iterator]();return vc({start(){},async pull(t){let{done:n,value:s}=await e.next();n?t.close():t.enqueue(s)},async cancel(){var t;await((t=e.return)==null?void 0:t.call(e))}})}function so(r){if(r[Symbol.asyncIterator])return r;let e=r.getReader();return{async next(){try{let t=await e.read();return t!=null&&t.done&&e.releaseLock(),t}catch(t){throw e.releaseLock(),t}},async return(){let t=e.cancel();return e.releaseLock(),await t,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function hh(r){var n,s;if(r===null||typeof r!="object")return;if(r[Symbol.asyncIterator]){await((s=(n=r[Symbol.asyncIterator]()).return)==null?void 0:s.call(n));return}let e=r.getReader(),t=e.cancel();e.releaseLock(),await t}var ph=({headers:r,body:e})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(e)});function mh(r){let e=0;for(let s of r)e+=s.length;let t=new Uint8Array(e),n=0;for(let s of r)t.set(s,n),n+=s.length;return t}var Hi;function oo(r){let e;return(Hi!=null?Hi:(e=new globalThis.TextEncoder,Hi=e.encode.bind(e)))(r)}var ji;function Ac(r){let e;return(ji!=null?ji:(e=new globalThis.TextDecoder,ji=e.decode.bind(e)))(r)}var Te,Ie,Pt=class{constructor(){Te.set(this,void 0),Ie.set(this,void 0),U(this,Te,new Uint8Array,"f"),U(this,Ie,null,"f")}decode(e){if(e==null)return[];let t=e instanceof ArrayBuffer?new Uint8Array(e):typeof e=="string"?oo(e):e;U(this,Te,mh([E(this,Te,"f"),t]),"f");let n=[],s;for(;(s=hw(E(this,Te,"f"),E(this,Ie,"f")))!=null;){if(s.carriage&&E(this,Ie,"f")==null){U(this,Ie,s.index,"f");continue}if(E(this,Ie,"f")!=null&&(s.index!==E(this,Ie,"f")+1||s.carriage)){n.push(Ac(E(this,Te,"f").subarray(0,E(this,Ie,"f")-1))),U(this,Te,E(this,Te,"f").subarray(E(this,Ie,"f")),"f"),U(this,Ie,null,"f");continue}let o=E(this,Ie,"f")!==null?s.preceding-1:s.preceding,i=Ac(E(this,Te,"f").subarray(0,o));n.push(i),U(this,Te,E(this,Te,"f").subarray(s.index),"f"),U(this,Ie,null,"f")}return n}flush(){return E(this,Te,"f").length?this.decode(`
`):[]}};Te=new WeakMap,Ie=new WeakMap;Pt.NEWLINE_CHARS=new Set([`
`,"\r"]);Pt.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function hw(r,e){for(let s=e!=null?e:0;s<r.length;s++){if(r[s]===10)return{preceding:s,index:s+1,carriage:!1};if(r[s]===13)return{preceding:s,index:s+1,carriage:!0}}return null}function gh(r){for(let n=0;n<r.length-1;n++){if(r[n]===10&&r[n+1]===10||r[n]===13&&r[n+1]===13)return n+2;if(r[n]===13&&r[n+1]===10&&n+3<r.length&&r[n+2]===13&&r[n+3]===10)return n+4}return-1}var Vi={off:0,error:200,warn:300,info:400,debug:500},Cc=(r,e,t)=>{if(r){if(oh(Vi,r))return r;ce(t).warn(`${e} was set to ${JSON.stringify(r)}, expected one of ${JSON.stringify(Object.keys(Vi))}`)}};function io(){}function Wi(r,e,t){return!e||Vi[r]>Vi[t]?io:e[r].bind(e)}var pw={error:io,warn:io,info:io,debug:io},yh=new WeakMap;function ce(r){var o;let e=r.logger,t=(o=r.logLevel)!=null?o:"off";if(!e)return pw;let n=yh.get(e);if(n&&n[0]===t)return n[1];let s={error:Wi("error",e,t),warn:Wi("warn",e,t),info:Wi("info",e,t),debug:Wi("debug",e,t)};return yh.set(e,[t,s]),s}var Rt=r=>(r.options&&(r.options={...r.options},delete r.options.headers),r.headers&&(r.headers=Object.fromEntries((r.headers instanceof Headers?[...r.headers]:Object.entries(r.headers)).map(([e,t])=>[e,e.toLowerCase()==="x-api-key"||e.toLowerCase()==="authorization"||e.toLowerCase()==="cookie"||e.toLowerCase()==="set-cookie"?"***":t]))),"retryOfRequestLogID"in r&&(r.retryOfRequestLogID&&(r.retryOf=r.retryOfRequestLogID),delete r.retryOfRequestLogID),r);var ao,Se=class{constructor(e,t,n){this.iterator=e,ao.set(this,void 0),this.controller=t,U(this,ao,n,"f")}static fromSSEResponse(e,t,n){let s=!1,o=n?ce(n):console;async function*i(){var c;if(s)throw new B("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let a=!1;try{for await(let l of mw(e,t)){if(l.event==="completion")try{yield JSON.parse(l.data)}catch(f){throw o.error("Could not parse message into JSON:",l.data),o.error("From chunk:",l.raw),f}if(l.event==="message_start"||l.event==="message_delta"||l.event==="message_stop"||l.event==="content_block_start"||l.event==="content_block_delta"||l.event==="content_block_stop")try{yield JSON.parse(l.data)}catch(f){throw o.error("Could not parse message into JSON:",l.data),o.error("From chunk:",l.raw),f}if(l.event!=="ping"&&l.event==="error")throw new Y(void 0,(c=Ui(l.data))!=null?c:l.data,void 0,e.headers)}a=!0}catch(l){if(Ct(l))return;throw l}finally{a||t.abort()}}return new Se(i,t,n)}static fromReadableStream(e,t,n){let s=!1;async function*o(){let a=new Pt,c=so(e);for await(let l of c)for(let f of a.decode(l))yield f;for(let l of a.flush())yield l}async function*i(){if(s)throw new B("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let a=!1;try{for await(let c of o())a||c&&(yield JSON.parse(c));a=!0}catch(c){if(Ct(c))return;throw c}finally{a||t.abort()}}return new Se(i,t,n)}[(ao=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let e=[],t=[],n=this.iterator(),s=o=>({next:()=>{if(o.length===0){let i=n.next();e.push(i),t.push(i)}return o.shift()}});return[new Se(()=>s(e),this.controller,E(this,ao,"f")),new Se(()=>s(t),this.controller,E(this,ao,"f"))]}toReadableStream(){let e=this,t;return vc({async start(){t=e[Symbol.asyncIterator]()},async pull(n){try{let{value:s,done:o}=await t.next();if(o)return n.close();let i=oo(JSON.stringify(s)+`
`);n.enqueue(i)}catch(s){n.error(s)}},async cancel(){var n;await((n=t.return)==null?void 0:n.call(t))}})}};async function*mw(r,e){if(!r.body)throw e.abort(),typeof globalThis.navigator!="undefined"&&globalThis.navigator.product==="ReactNative"?new B("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new B("Attempted to iterate over a response with no body");let t=new Pc,n=new Pt,s=so(r.body);for await(let o of gw(s))for(let i of n.decode(o)){let a=t.decode(i);a&&(yield a)}for(let o of n.flush()){let i=t.decode(o);i&&(yield i)}}async function*gw(r){let e=new Uint8Array;for await(let t of r){if(t==null)continue;let n=t instanceof ArrayBuffer?new Uint8Array(t):typeof t=="string"?oo(t):t,s=new Uint8Array(e.length+n.length);s.set(e),s.set(n,e.length),e=s;let o;for(;(o=gh(e))!==-1;)yield e.slice(0,o),e=e.slice(o)}e.length>0&&(yield e)}var Pc=class{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let o={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],o}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,n,s]=yw(e,":");return s.startsWith(" ")&&(s=s.substring(1)),t==="event"?this.event=s:t==="data"&&this.data.push(s),null}};function yw(r,e){let t=r.indexOf(e);return t!==-1?[r.substring(0,t),e,r.substring(t+e.length)]:[r,"",""]}async function Ki(r,e){let{response:t,requestLogID:n,retryOfRequestLogID:s,startTime:o}=e,i=await(async()=>{var u;if(e.options.stream)return ce(r).debug("response",t.status,t.url,t.headers,t.body),e.options.__streamClass?e.options.__streamClass.fromSSEResponse(t,e.controller,r):Se.fromSSEResponse(t,e.controller,r);if(t.status===204)return null;if(e.options.__binaryResponse)return t;let a=t.headers.get("content-type"),c=(u=a==null?void 0:a.split(";")[0])==null?void 0:u.trim();if((c==null?void 0:c.includes("application/json"))||(c==null?void 0:c.endsWith("+json"))){let d=await t.json();return Rc(d,t)}return await t.text()})();return ce(r).debug(`[${n}] response parsed`,Rt({retryOfRequestLogID:s,url:t.url,status:t.status,body:i,durationMs:Date.now()-o})),i}function Rc(r,e){return!r||typeof r!="object"||Array.isArray(r)?r:Object.defineProperty(r,"_request_id",{value:e.headers.get("request-id"),enumerable:!1})}var lo,Tt=class extends Promise{constructor(e,t,n=Ki){super(s=>{s(null)}),this.responsePromise=t,this.parseResponse=n,lo.set(this,void 0),U(this,lo,e,"f")}_thenUnwrap(e){return new Tt(E(this,lo,"f"),this.responsePromise,async(t,n)=>Rc(e(await this.parseResponse(t,n),n),n.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(E(this,lo,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}};lo=new WeakMap;var Gi,Tc=class{constructor(e,t,n,s){Gi.set(this,void 0),U(this,Gi,e,"f"),this.options=s,this.response=t,this.body=n}hasNextPage(){return this.getPaginatedItems().length?this.nextPageRequestOptions()!=null:!1}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new B("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await E(this,Gi,"f").requestAPIList(this.constructor,e)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(Gi=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}},co=class extends Tt{constructor(e,t,n){super(e,t,async(s,o)=>new n(s,o.response,await Ki(s,o),o.options))}async*[Symbol.asyncIterator](){let e=await this;for await(let t of e)yield t}},Ne=class extends Tc{constructor(e,t,n,s){super(e,t,n,s),this.data=n.data||[],this.has_more=n.has_more||!1,this.first_id=n.first_id||null,this.last_id=n.last_id||null}getPaginatedItems(){var e;return(e=this.data)!=null?e:[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageRequestOptions(){var t;if((t=this.options.query)!=null&&t.before_id){let n=this.first_id;return n?{...this.options,query:{...Ec(this.options.query),before_id:n}}:null}let e=this.last_id;return e?{...this.options,query:{...Ec(this.options.query),after_id:e}}:null}};var kc=()=>{var r;if(typeof File=="undefined"){let{process:e}=globalThis,t=typeof((r=e==null?void 0:e.versions)==null?void 0:r.node)=="string"&&parseInt(e.versions.node.split("."))<20;throw new Error("`File` is not defined as a global, which is required for file uploads."+(t?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function Fr(r,e,t){return kc(),new File(r,e!=null?e:"unknown_file",t)}function uo(r){return(typeof r=="object"&&r!==null&&("name"in r&&r.name&&String(r.name)||"url"in r&&r.url&&String(r.url)||"filename"in r&&r.filename&&String(r.filename)||"path"in r&&r.path&&String(r.path))||"").split(/[\\/]/).pop()||void 0}var Oc=r=>r!=null&&typeof r=="object"&&typeof r[Symbol.asyncIterator]=="function";var wh=async(r,e)=>({...r,body:await _w(r.body,e)}),bh=new WeakMap;function ww(r){let e=typeof r=="function"?r:r.fetch,t=bh.get(e);if(t)return t;let n=(async()=>{try{let s="Response"in e?e.Response:(await e("data:,")).constructor,o=new FormData;return o.toString()!==await new s(o).text()}catch(s){return!0}})();return bh.set(e,n),n}var _w=async(r,e)=>{if(!await ww(e))throw new TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let t=new FormData;return await Promise.all(Object.entries(r||{}).map(([n,s])=>Ic(t,n,s))),t},Sw=r=>r instanceof Blob&&"name"in r;var Ic=async(r,e,t)=>{if(t!==void 0){if(t==null)throw new TypeError(`Received null for "${e}"; to pass null in FormData, you must use the string 'null'`);if(typeof t=="string"||typeof t=="number"||typeof t=="boolean")r.append(e,String(t));else if(t instanceof Response){let n={},s=t.headers.get("Content-Type");s&&(n={type:s}),r.append(e,Fr([await t.blob()],uo(t),n))}else if(Oc(t))r.append(e,Fr([await new Response($i(t)).blob()],uo(t)));else if(Sw(t))r.append(e,Fr([t],uo(t),{type:t.type}));else if(Array.isArray(t))await Promise.all(t.map(n=>Ic(r,e+"[]",n)));else if(typeof t=="object")await Promise.all(Object.entries(t).map(([n,s])=>Ic(r,`${e}[${n}]`,s)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${t} instead`)}};var _h=r=>r!=null&&typeof r=="object"&&typeof r.size=="number"&&typeof r.type=="string"&&typeof r.text=="function"&&typeof r.slice=="function"&&typeof r.arrayBuffer=="function",xw=r=>r!=null&&typeof r=="object"&&typeof r.name=="string"&&typeof r.lastModified=="number"&&_h(r),Ew=r=>r!=null&&typeof r=="object"&&typeof r.url=="string"&&typeof r.blob=="function";async function Ji(r,e,t){if(kc(),r=await r,e||(e=uo(r)),xw(r))return r instanceof File&&e==null&&t==null?r:Fr([await r.arrayBuffer()],e!=null?e:r.name,{type:r.type,lastModified:r.lastModified,...t});if(Ew(r)){let s=await r.blob();return e||(e=new URL(r.url).pathname.split(/[\\/]/).pop()),Fr(await Mc(s),e,t)}let n=await Mc(r);if(!(t!=null&&t.type)){let s=n.find(o=>typeof o=="object"&&"type"in o&&o.type);typeof s=="string"&&(t={...t,type:s})}return Fr(n,e,t)}async function Mc(r){var t;let e=[];if(typeof r=="string"||ArrayBuffer.isView(r)||r instanceof ArrayBuffer)e.push(r);else if(_h(r))e.push(r instanceof Blob?r:await r.arrayBuffer());else if(Oc(r))for await(let n of r)e.push(...await Mc(n));else{let n=(t=r==null?void 0:r.constructor)==null?void 0:t.name;throw new Error(`Unexpected data type: ${typeof r}${n?`; constructor: ${n}`:""}${vw(r)}`)}return e}function vw(r){return typeof r!="object"||r===null?"":`; props: [${Object.getOwnPropertyNames(r).map(t=>`"${t}"`).join(", ")}]`}var Z=class{constructor(e){this._client=e}};var Sh=Symbol.for("brand.privateNullableHeaders");function*Cw(r){if(!r)return;if(Sh in r){let{values:n,nulls:s}=r;yield*n.entries();for(let o of s)yield[o,null];return}let e=!1,t;r instanceof Headers?t=r.entries():xc(r)?t=r:(e=!0,t=Object.entries(r!=null?r:{}));for(let n of t){let s=n[0];if(typeof s!="string")throw new TypeError("expected header name to be a string");let o=xc(n[1])?n[1]:[n[1]],i=!1;for(let a of o)a!==void 0&&(e&&!i&&(i=!0,yield[s,null]),yield[s,a])}}var K=r=>{let e=new Headers,t=new Set;for(let n of r){let s=new Set;for(let[o,i]of Cw(n)){let a=o.toLowerCase();s.has(a)||(e.delete(o),s.add(a)),i===null?(e.delete(o),t.add(a)):(e.append(o,i),t.delete(a))}}return{[Sh]:!0,values:e,nulls:t}};function Eh(r){return r.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}var xh=Object.freeze(Object.create(null)),Pw=(r=Eh)=>function(t,...n){if(t.length===1)return t[0];let s=!1,o=[],i=t.reduce((f,u,d)=>{var y,g,S;/[?#]/.test(u)&&(s=!0);let h=n[d],p=(s?encodeURIComponent:r)(""+h);return d!==n.length&&(h==null||typeof h=="object"&&h.toString===((S=Object.getPrototypeOf((g=Object.getPrototypeOf((y=h.hasOwnProperty)!=null?y:xh))!=null?g:xh))==null?void 0:S.toString))&&(p=h+"",o.push({start:f.length+u.length,length:p.length,error:`Value of type ${Object.prototype.toString.call(h).slice(8,-1)} is not a valid path parameter`})),f+u+(d===n.length?"":p)},""),a=i.split(/[?#]/,1)[0],c=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi,l;for(;(l=c.exec(a))!==null;)o.push({start:l.index,length:l[0].length,error:`Value "${l[0]}" can't be safely passed as a path parameter`});if(o.sort((f,u)=>f.start-u.start),o.length>0){let f=0,u=o.reduce((d,h)=>{let p=" ".repeat(h.start-f),y="^".repeat(h.length);return f=h.start+h.length,d+p+y},"");throw new B(`Path parameters result in path with invalid segments:
${o.map(d=>d.error).join(`
`)}
${i}
${u}`)}return i},ge=Pw(Eh);var ss=class extends Z{list(e={},t){let{betas:n,...s}=e!=null?e:{};return this._client.getAPIList("/v1/files",Ne,{query:s,...t,headers:K([{"anthropic-beta":[...n!=null?n:[],"files-api-2025-04-14"].toString()},t==null?void 0:t.headers])})}delete(e,t={},n){let{betas:s}=t!=null?t:{};return this._client.delete(ge`/v1/files/${e}`,{...n,headers:K([{"anthropic-beta":[...s!=null?s:[],"files-api-2025-04-14"].toString()},n==null?void 0:n.headers])})}download(e,t={},n){let{betas:s}=t!=null?t:{};return this._client.get(ge`/v1/files/${e}/content`,{...n,headers:K([{"anthropic-beta":[...s!=null?s:[],"files-api-2025-04-14"].toString(),Accept:"application/binary"},n==null?void 0:n.headers]),__binaryResponse:!0})}retrieveMetadata(e,t={},n){let{betas:s}=t!=null?t:{};return this._client.get(ge`/v1/files/${e}`,{...n,headers:K([{"anthropic-beta":[...s!=null?s:[],"files-api-2025-04-14"].toString()},n==null?void 0:n.headers])})}upload(e,t){let{betas:n,...s}=e;return this._client.post("/v1/files",wh({body:s,...t,headers:K([{"anthropic-beta":[...n!=null?n:[],"files-api-2025-04-14"].toString()},t==null?void 0:t.headers])},this._client))}};var os=class extends Z{retrieve(e,t={},n){let{betas:s}=t!=null?t:{};return this._client.get(ge`/v1/models/${e}?beta=true`,{...n,headers:K([{...(s==null?void 0:s.toString())!=null?{"anthropic-beta":s==null?void 0:s.toString()}:void 0},n==null?void 0:n.headers])})}list(e={},t){let{betas:n,...s}=e!=null?e:{};return this._client.getAPIList("/v1/models?beta=true",Ne,{query:s,...t,headers:K([{...(n==null?void 0:n.toString())!=null?{"anthropic-beta":n==null?void 0:n.toString()}:void 0},t==null?void 0:t.headers])})}};var or=class{constructor(e,t){this.iterator=e,this.controller=t}async*decoder(){let e=new Pt;for await(let t of this.iterator)for(let n of e.decode(t))yield JSON.parse(n);for(let t of e.flush())yield JSON.parse(t)}[Symbol.asyncIterator](){return this.decoder()}static fromResponse(e,t){if(!e.body)throw t.abort(),typeof globalThis.navigator!="undefined"&&globalThis.navigator.product==="ReactNative"?new B("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new B("Attempted to iterate over a response with no body");return new or(so(e.body),t)}};var is=class extends Z{create(e,t){let{betas:n,...s}=e;return this._client.post("/v1/messages/batches?beta=true",{body:s,...t,headers:K([{"anthropic-beta":[...n!=null?n:[],"message-batches-2024-09-24"].toString()},t==null?void 0:t.headers])})}retrieve(e,t={},n){let{betas:s}=t!=null?t:{};return this._client.get(ge`/v1/messages/batches/${e}?beta=true`,{...n,headers:K([{"anthropic-beta":[...s!=null?s:[],"message-batches-2024-09-24"].toString()},n==null?void 0:n.headers])})}list(e={},t){let{betas:n,...s}=e!=null?e:{};return this._client.getAPIList("/v1/messages/batches?beta=true",Ne,{query:s,...t,headers:K([{"anthropic-beta":[...n!=null?n:[],"message-batches-2024-09-24"].toString()},t==null?void 0:t.headers])})}delete(e,t={},n){let{betas:s}=t!=null?t:{};return this._client.delete(ge`/v1/messages/batches/${e}?beta=true`,{...n,headers:K([{"anthropic-beta":[...s!=null?s:[],"message-batches-2024-09-24"].toString()},n==null?void 0:n.headers])})}cancel(e,t={},n){let{betas:s}=t!=null?t:{};return this._client.post(ge`/v1/messages/batches/${e}/cancel?beta=true`,{...n,headers:K([{"anthropic-beta":[...s!=null?s:[],"message-batches-2024-09-24"].toString()},n==null?void 0:n.headers])})}async results(e,t={},n){let s=await this.retrieve(e);if(!s.results_url)throw new B(`No batch \`results_url\`; Has it finished processing? ${s.processing_status} - ${s.id}`);let{betas:o}=t!=null?t:{};return this._client.get(s.results_url,{...n,headers:K([{"anthropic-beta":[...o!=null?o:[],"message-batches-2024-09-24"].toString(),Accept:"application/binary"},n==null?void 0:n.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((i,a)=>or.fromResponse(a.response,a.controller))}};var kw=r=>{let e=0,t=[];for(;e<r.length;){let n=r[e];if(n==="\\"){e++;continue}if(n==="{"){t.push({type:"brace",value:"{"}),e++;continue}if(n==="}"){t.push({type:"brace",value:"}"}),e++;continue}if(n==="["){t.push({type:"paren",value:"["}),e++;continue}if(n==="]"){t.push({type:"paren",value:"]"}),e++;continue}if(n===":"){t.push({type:"separator",value:":"}),e++;continue}if(n===","){t.push({type:"delimiter",value:","}),e++;continue}if(n==='"'){let a="",c=!1;for(n=r[++e];n!=='"';){if(e===r.length){c=!0;break}if(n==="\\"){if(e++,e===r.length){c=!0;break}a+=n+r[e],n=r[++e]}else a+=n,n=r[++e]}n=r[++e],c||t.push({type:"string",value:a});continue}if(n&&/\s/.test(n)){e++;continue}let o=/[0-9]/;if(n&&o.test(n)||n==="-"||n==="."){let a="";for(n==="-"&&(a+=n,n=r[++e]);n&&o.test(n)||n===".";)a+=n,n=r[++e];t.push({type:"number",value:a});continue}let i=/[a-z]/i;if(n&&i.test(n)){let a="";for(;n&&i.test(n)&&e!==r.length;)a+=n,n=r[++e];if(a=="true"||a=="false"||a==="null")t.push({type:"name",value:a});else{e++;continue}continue}e++}return t},as=r=>{if(r.length===0)return r;let e=r[r.length-1];switch(e.type){case"separator":return r=r.slice(0,r.length-1),as(r);break;case"number":let t=e.value[e.value.length-1];if(t==="."||t==="-")return r=r.slice(0,r.length-1),as(r);case"string":let n=r[r.length-2];if((n==null?void 0:n.type)==="delimiter")return r=r.slice(0,r.length-1),as(r);if((n==null?void 0:n.type)==="brace"&&n.value==="{")return r=r.slice(0,r.length-1),as(r);break;case"delimiter":return r=r.slice(0,r.length-1),as(r);break}return r},Ow=r=>{let e=[];return r.map(t=>{t.type==="brace"&&(t.value==="{"?e.push("}"):e.splice(e.lastIndexOf("}"),1)),t.type==="paren"&&(t.value==="["?e.push("]"):e.splice(e.lastIndexOf("]"),1))}),e.length>0&&e.reverse().map(t=>{t==="}"?r.push({type:"brace",value:"}"}):t==="]"&&r.push({type:"paren",value:"]"})}),r},Mw=r=>{let e="";return r.map(t=>{switch(t.type){case"string":e+='"'+t.value+'"';break;default:e+=t.value;break}}),e},zi=r=>JSON.parse(Mw(Ow(as(kw(r)))));var De,ir,fo,Xi,ho,po,Yi,mo,It,go,Qi,Zi,ls,ea,ta,Lc,vh,ra,Nc,Dc,Bc,Ah,Ch="__json_buf";function Ph(r){return r.type==="tool_use"||r.type==="server_tool_use"||r.type==="mcp_tool_use"}var Ur=class{constructor(){De.add(this),this.messages=[],this.receivedMessages=[],ir.set(this,void 0),this.controller=new AbortController,fo.set(this,void 0),Xi.set(this,()=>{}),ho.set(this,()=>{}),po.set(this,void 0),Yi.set(this,()=>{}),mo.set(this,()=>{}),It.set(this,{}),go.set(this,!1),Qi.set(this,!1),Zi.set(this,!1),ls.set(this,!1),ea.set(this,void 0),ta.set(this,void 0),ra.set(this,e=>{if(U(this,Qi,!0,"f"),Ct(e)&&(e=new de),e instanceof de)return U(this,Zi,!0,"f"),this._emit("abort",e);if(e instanceof B)return this._emit("error",e);if(e instanceof Error){let t=new B(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new B(String(e)))}),U(this,fo,new Promise((e,t)=>{U(this,Xi,e,"f"),U(this,ho,t,"f")}),"f"),U(this,po,new Promise((e,t)=>{U(this,Yi,e,"f"),U(this,mo,t,"f")}),"f"),E(this,fo,"f").catch(()=>{}),E(this,po,"f").catch(()=>{})}get response(){return E(this,ea,"f")}get request_id(){return E(this,ta,"f")}async withResponse(){let e=await E(this,fo,"f");if(!e)throw new Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new Ur;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,n){let s=new Ur;for(let o of t.messages)s._addMessageParam(o);return s._run(()=>s._createMessage(e,{...t,stream:!0},{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}})),s}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},E(this,ra,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,n){var i;let s=n==null?void 0:n.signal,o;s&&(s.aborted&&this.controller.abort(),o=this.controller.abort.bind(this.controller),s.addEventListener("abort",o));try{E(this,De,"m",Nc).call(this);let{response:a,data:c}=await e.create({...t,stream:!0},{...n,signal:this.controller.signal}).withResponse();this._connected(a);for await(let l of c)E(this,De,"m",Dc).call(this,l);if((i=c.controller.signal)!=null&&i.aborted)throw new de;E(this,De,"m",Bc).call(this)}finally{s&&o&&s.removeEventListener("abort",o)}}_connected(e){this.ended||(U(this,ea,e,"f"),U(this,ta,e==null?void 0:e.headers.get("request-id"),"f"),E(this,Xi,"f").call(this,e),this._emit("connect"))}get ended(){return E(this,go,"f")}get errored(){return E(this,Qi,"f")}get aborted(){return E(this,Zi,"f")}abort(){this.controller.abort()}on(e,t){return(E(this,It,"f")[e]||(E(this,It,"f")[e]=[])).push({listener:t}),this}off(e,t){let n=E(this,It,"f")[e];if(!n)return this;let s=n.findIndex(o=>o.listener===t);return s>=0&&n.splice(s,1),this}once(e,t){return(E(this,It,"f")[e]||(E(this,It,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,n)=>{U(this,ls,!0,"f"),e!=="error"&&this.once("error",n),this.once(e,t)})}async done(){U(this,ls,!0,"f"),await E(this,po,"f")}get currentMessage(){return E(this,ir,"f")}async finalMessage(){return await this.done(),E(this,De,"m",Lc).call(this)}async finalText(){return await this.done(),E(this,De,"m",vh).call(this)}_emit(e,...t){if(E(this,go,"f"))return;e==="end"&&(U(this,go,!0,"f"),E(this,Yi,"f").call(this));let n=E(this,It,"f")[e];if(n&&(E(this,It,"f")[e]=n.filter(s=>!s.once),n.forEach(({listener:s})=>s(...t))),e==="abort"){let s=t[0];!E(this,ls,"f")&&!(n!=null&&n.length)&&Promise.reject(s),E(this,ho,"f").call(this,s),E(this,mo,"f").call(this,s),this._emit("end");return}if(e==="error"){let s=t[0];!E(this,ls,"f")&&!(n!=null&&n.length)&&Promise.reject(s),E(this,ho,"f").call(this,s),E(this,mo,"f").call(this,s),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",E(this,De,"m",Lc).call(this))}async _fromReadableStream(e,t){var o;let n=t==null?void 0:t.signal,s;n&&(n.aborted&&this.controller.abort(),s=this.controller.abort.bind(this.controller),n.addEventListener("abort",s));try{E(this,De,"m",Nc).call(this),this._connected(null);let i=Se.fromReadableStream(e,this.controller);for await(let a of i)E(this,De,"m",Dc).call(this,a);if((o=i.controller.signal)!=null&&o.aborted)throw new de;E(this,De,"m",Bc).call(this)}finally{n&&s&&n.removeEventListener("abort",s)}}[(ir=new WeakMap,fo=new WeakMap,Xi=new WeakMap,ho=new WeakMap,po=new WeakMap,Yi=new WeakMap,mo=new WeakMap,It=new WeakMap,go=new WeakMap,Qi=new WeakMap,Zi=new WeakMap,ls=new WeakMap,ea=new WeakMap,ta=new WeakMap,ra=new WeakMap,De=new WeakSet,Lc=function(){if(this.receivedMessages.length===0)throw new B("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},vh=function(){if(this.receivedMessages.length===0)throw new B("stream ended without producing a Message with role=assistant");let t=this.receivedMessages.at(-1).content.filter(n=>n.type==="text").map(n=>n.text);if(t.length===0)throw new B("stream ended without producing a content block with type=text");return t.join(" ")},Nc=function(){this.ended||U(this,ir,void 0,"f")},Dc=function(t){var s;if(this.ended)return;let n=E(this,De,"m",Ah).call(this,t);switch(this._emit("streamEvent",t,n),t.type){case"content_block_delta":{let o=n.content.at(-1);switch(t.delta.type){case"text_delta":{o.type==="text"&&this._emit("text",t.delta.text,o.text||"");break}case"citations_delta":{o.type==="text"&&this._emit("citation",t.delta.citation,(s=o.citations)!=null?s:[]);break}case"input_json_delta":{Ph(o)&&o.input&&this._emit("inputJson",t.delta.partial_json,o.input);break}case"thinking_delta":{o.type==="thinking"&&this._emit("thinking",t.delta.thinking,o.thinking);break}case"signature_delta":{o.type==="thinking"&&this._emit("signature",o.signature);break}default:t.delta}break}case"message_stop":{this._addMessageParam(n),this._addMessage(n,!0);break}case"content_block_stop":{this._emit("contentBlock",n.content.at(-1));break}case"message_start":{U(this,ir,n,"f");break}case"content_block_start":case"message_delta":break}},Bc=function(){if(this.ended)throw new B("stream has ended, this shouldn't happen");let t=E(this,ir,"f");if(!t)throw new B("request ended without sending any chunks");return U(this,ir,void 0,"f"),t},Ah=function(t){var s;let n=E(this,ir,"f");if(t.type==="message_start"){if(n)throw new B(`Unexpected event order, got ${t.type} before receiving "message_stop"`);return t.message}if(!n)throw new B(`Unexpected event order, got ${t.type} before "message_start"`);switch(t.type){case"message_stop":return n;case"message_delta":return n.container=t.delta.container,n.stop_reason=t.delta.stop_reason,n.stop_sequence=t.delta.stop_sequence,n.usage.output_tokens=t.usage.output_tokens,t.usage.input_tokens!=null&&(n.usage.input_tokens=t.usage.input_tokens),t.usage.cache_creation_input_tokens!=null&&(n.usage.cache_creation_input_tokens=t.usage.cache_creation_input_tokens),t.usage.cache_read_input_tokens!=null&&(n.usage.cache_read_input_tokens=t.usage.cache_read_input_tokens),t.usage.server_tool_use!=null&&(n.usage.server_tool_use=t.usage.server_tool_use),n;case"content_block_start":return n.content.push(t.content_block),n;case"content_block_delta":{let o=n.content.at(t.index);switch(t.delta.type){case"text_delta":{(o==null?void 0:o.type)==="text"&&(n.content[t.index]={...o,text:(o.text||"")+t.delta.text});break}case"citations_delta":{(o==null?void 0:o.type)==="text"&&(n.content[t.index]={...o,citations:[...(s=o.citations)!=null?s:[],t.delta.citation]});break}case"input_json_delta":{if(o&&Ph(o)){let i=o[Ch]||"";i+=t.delta.partial_json;let a={...o};if(Object.defineProperty(a,Ch,{value:i,enumerable:!1,writable:!0}),i)try{a.input=zi(i)}catch(c){let l=new B(`Unable to parse tool parameter JSON from model. Please retry your request or adjust your prompt. Error: ${c}. JSON: ${i}`);E(this,ra,"f").call(this,l)}n.content[t.index]=a}break}case"thinking_delta":{(o==null?void 0:o.type)==="thinking"&&(n.content[t.index]={...o,thinking:o.thinking+t.delta.thinking});break}case"signature_delta":{(o==null?void 0:o.type)==="thinking"&&(n.content[t.index]={...o,signature:t.delta.signature});break}default:t.delta}return n}case"content_block_stop":return n}},Symbol.asyncIterator)](){let e=[],t=[],n=!1;return this.on("streamEvent",s=>{let o=t.shift();o?o.resolve(s):e.push(s)}),this.on("end",()=>{n=!0;for(let s of t)s.resolve(void 0);t.length=0}),this.on("abort",s=>{n=!0;for(let o of t)o.reject(s);t.length=0}),this.on("error",s=>{n=!0;for(let o of t)o.reject(s);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:n?{value:void 0,done:!0}:new Promise((o,i)=>t.push({resolve:o,reject:i})).then(o=>o?{value:o,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new Se(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}};var na={"claude-opus-4-20250514":8192,"claude-opus-4-0":8192,"claude-4-opus-20250514":8192,"anthropic.claude-opus-4-20250514-v1:0":8192,"claude-opus-4@20250514":8192};var Rh={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-3-opus-20240229":"January 5th, 2026","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"},qr=class extends Z{constructor(){super(...arguments),this.batches=new is(this._client)}create(e,t){var i,a;let{betas:n,...s}=e;s.model in Rh&&console.warn(`The model '${s.model}' is deprecated and will reach end-of-life on ${Rh[s.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let o=this._client._options.timeout;if(!s.stream&&o==null){let c=(i=na[s.model])!=null?i:void 0;o=this._client.calculateNonstreamingTimeout(s.max_tokens,c)}return this._client.post("/v1/messages?beta=true",{body:s,timeout:o!=null?o:6e5,...t,headers:K([{...(n==null?void 0:n.toString())!=null?{"anthropic-beta":n==null?void 0:n.toString()}:void 0},t==null?void 0:t.headers]),stream:(a=e.stream)!=null?a:!1})}stream(e,t){return Ur.createMessage(this,e,t)}countTokens(e,t){let{betas:n,...s}=e;return this._client.post("/v1/messages/count_tokens?beta=true",{body:s,...t,headers:K([{"anthropic-beta":[...n!=null?n:[],"token-counting-2024-11-01"].toString()},t==null?void 0:t.headers])})}};qr.Batches=is;var ut=class extends Z{constructor(){super(...arguments),this.models=new os(this._client),this.messages=new qr(this._client),this.files=new ss(this._client)}};ut.Models=os;ut.Messages=qr;ut.Files=ss;var $r=class extends Z{create(e,t){var o,i;let{betas:n,...s}=e;return this._client.post("/v1/complete",{body:s,timeout:(o=this._client._options.timeout)!=null?o:6e5,...t,headers:K([{...(n==null?void 0:n.toString())!=null?{"anthropic-beta":n==null?void 0:n.toString()}:void 0},t==null?void 0:t.headers]),stream:(i=e.stream)!=null?i:!1})}};var Be,ar,yo,sa,bo,wo,oa,_o,kt,So,ia,aa,cs,la,ca,Fc,Th,Uc,qc,$c,Hc,Ih,kh="__json_buf";function Oh(r){return r.type==="tool_use"||r.type==="server_tool_use"}var Hr=class{constructor(){Be.add(this),this.messages=[],this.receivedMessages=[],ar.set(this,void 0),this.controller=new AbortController,yo.set(this,void 0),sa.set(this,()=>{}),bo.set(this,()=>{}),wo.set(this,void 0),oa.set(this,()=>{}),_o.set(this,()=>{}),kt.set(this,{}),So.set(this,!1),ia.set(this,!1),aa.set(this,!1),cs.set(this,!1),la.set(this,void 0),ca.set(this,void 0),Uc.set(this,e=>{if(U(this,ia,!0,"f"),Ct(e)&&(e=new de),e instanceof de)return U(this,aa,!0,"f"),this._emit("abort",e);if(e instanceof B)return this._emit("error",e);if(e instanceof Error){let t=new B(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new B(String(e)))}),U(this,yo,new Promise((e,t)=>{U(this,sa,e,"f"),U(this,bo,t,"f")}),"f"),U(this,wo,new Promise((e,t)=>{U(this,oa,e,"f"),U(this,_o,t,"f")}),"f"),E(this,yo,"f").catch(()=>{}),E(this,wo,"f").catch(()=>{})}get response(){return E(this,la,"f")}get request_id(){return E(this,ca,"f")}async withResponse(){let e=await E(this,yo,"f");if(!e)throw new Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new Hr;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,n){let s=new Hr;for(let o of t.messages)s._addMessageParam(o);return s._run(()=>s._createMessage(e,{...t,stream:!0},{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}})),s}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},E(this,Uc,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,n){var i;let s=n==null?void 0:n.signal,o;s&&(s.aborted&&this.controller.abort(),o=this.controller.abort.bind(this.controller),s.addEventListener("abort",o));try{E(this,Be,"m",qc).call(this);let{response:a,data:c}=await e.create({...t,stream:!0},{...n,signal:this.controller.signal}).withResponse();this._connected(a);for await(let l of c)E(this,Be,"m",$c).call(this,l);if((i=c.controller.signal)!=null&&i.aborted)throw new de;E(this,Be,"m",Hc).call(this)}finally{s&&o&&s.removeEventListener("abort",o)}}_connected(e){this.ended||(U(this,la,e,"f"),U(this,ca,e==null?void 0:e.headers.get("request-id"),"f"),E(this,sa,"f").call(this,e),this._emit("connect"))}get ended(){return E(this,So,"f")}get errored(){return E(this,ia,"f")}get aborted(){return E(this,aa,"f")}abort(){this.controller.abort()}on(e,t){return(E(this,kt,"f")[e]||(E(this,kt,"f")[e]=[])).push({listener:t}),this}off(e,t){let n=E(this,kt,"f")[e];if(!n)return this;let s=n.findIndex(o=>o.listener===t);return s>=0&&n.splice(s,1),this}once(e,t){return(E(this,kt,"f")[e]||(E(this,kt,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,n)=>{U(this,cs,!0,"f"),e!=="error"&&this.once("error",n),this.once(e,t)})}async done(){U(this,cs,!0,"f"),await E(this,wo,"f")}get currentMessage(){return E(this,ar,"f")}async finalMessage(){return await this.done(),E(this,Be,"m",Fc).call(this)}async finalText(){return await this.done(),E(this,Be,"m",Th).call(this)}_emit(e,...t){if(E(this,So,"f"))return;e==="end"&&(U(this,So,!0,"f"),E(this,oa,"f").call(this));let n=E(this,kt,"f")[e];if(n&&(E(this,kt,"f")[e]=n.filter(s=>!s.once),n.forEach(({listener:s})=>s(...t))),e==="abort"){let s=t[0];!E(this,cs,"f")&&!(n!=null&&n.length)&&Promise.reject(s),E(this,bo,"f").call(this,s),E(this,_o,"f").call(this,s),this._emit("end");return}if(e==="error"){let s=t[0];!E(this,cs,"f")&&!(n!=null&&n.length)&&Promise.reject(s),E(this,bo,"f").call(this,s),E(this,_o,"f").call(this,s),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",E(this,Be,"m",Fc).call(this))}async _fromReadableStream(e,t){var o;let n=t==null?void 0:t.signal,s;n&&(n.aborted&&this.controller.abort(),s=this.controller.abort.bind(this.controller),n.addEventListener("abort",s));try{E(this,Be,"m",qc).call(this),this._connected(null);let i=Se.fromReadableStream(e,this.controller);for await(let a of i)E(this,Be,"m",$c).call(this,a);if((o=i.controller.signal)!=null&&o.aborted)throw new de;E(this,Be,"m",Hc).call(this)}finally{n&&s&&n.removeEventListener("abort",s)}}[(ar=new WeakMap,yo=new WeakMap,sa=new WeakMap,bo=new WeakMap,wo=new WeakMap,oa=new WeakMap,_o=new WeakMap,kt=new WeakMap,So=new WeakMap,ia=new WeakMap,aa=new WeakMap,cs=new WeakMap,la=new WeakMap,ca=new WeakMap,Uc=new WeakMap,Be=new WeakSet,Fc=function(){if(this.receivedMessages.length===0)throw new B("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},Th=function(){if(this.receivedMessages.length===0)throw new B("stream ended without producing a Message with role=assistant");let t=this.receivedMessages.at(-1).content.filter(n=>n.type==="text").map(n=>n.text);if(t.length===0)throw new B("stream ended without producing a content block with type=text");return t.join(" ")},qc=function(){this.ended||U(this,ar,void 0,"f")},$c=function(t){var s;if(this.ended)return;let n=E(this,Be,"m",Ih).call(this,t);switch(this._emit("streamEvent",t,n),t.type){case"content_block_delta":{let o=n.content.at(-1);switch(t.delta.type){case"text_delta":{o.type==="text"&&this._emit("text",t.delta.text,o.text||"");break}case"citations_delta":{o.type==="text"&&this._emit("citation",t.delta.citation,(s=o.citations)!=null?s:[]);break}case"input_json_delta":{Oh(o)&&o.input&&this._emit("inputJson",t.delta.partial_json,o.input);break}case"thinking_delta":{o.type==="thinking"&&this._emit("thinking",t.delta.thinking,o.thinking);break}case"signature_delta":{o.type==="thinking"&&this._emit("signature",o.signature);break}default:t.delta}break}case"message_stop":{this._addMessageParam(n),this._addMessage(n,!0);break}case"content_block_stop":{this._emit("contentBlock",n.content.at(-1));break}case"message_start":{U(this,ar,n,"f");break}case"content_block_start":case"message_delta":break}},Hc=function(){if(this.ended)throw new B("stream has ended, this shouldn't happen");let t=E(this,ar,"f");if(!t)throw new B("request ended without sending any chunks");return U(this,ar,void 0,"f"),t},Ih=function(t){var s;let n=E(this,ar,"f");if(t.type==="message_start"){if(n)throw new B(`Unexpected event order, got ${t.type} before receiving "message_stop"`);return t.message}if(!n)throw new B(`Unexpected event order, got ${t.type} before "message_start"`);switch(t.type){case"message_stop":return n;case"message_delta":return n.stop_reason=t.delta.stop_reason,n.stop_sequence=t.delta.stop_sequence,n.usage.output_tokens=t.usage.output_tokens,t.usage.input_tokens!=null&&(n.usage.input_tokens=t.usage.input_tokens),t.usage.cache_creation_input_tokens!=null&&(n.usage.cache_creation_input_tokens=t.usage.cache_creation_input_tokens),t.usage.cache_read_input_tokens!=null&&(n.usage.cache_read_input_tokens=t.usage.cache_read_input_tokens),t.usage.server_tool_use!=null&&(n.usage.server_tool_use=t.usage.server_tool_use),n;case"content_block_start":return n.content.push({...t.content_block}),n;case"content_block_delta":{let o=n.content.at(t.index);switch(t.delta.type){case"text_delta":{(o==null?void 0:o.type)==="text"&&(n.content[t.index]={...o,text:(o.text||"")+t.delta.text});break}case"citations_delta":{(o==null?void 0:o.type)==="text"&&(n.content[t.index]={...o,citations:[...(s=o.citations)!=null?s:[],t.delta.citation]});break}case"input_json_delta":{if(o&&Oh(o)){let i=o[kh]||"";i+=t.delta.partial_json;let a={...o};Object.defineProperty(a,kh,{value:i,enumerable:!1,writable:!0}),i&&(a.input=zi(i)),n.content[t.index]=a}break}case"thinking_delta":{(o==null?void 0:o.type)==="thinking"&&(n.content[t.index]={...o,thinking:o.thinking+t.delta.thinking});break}case"signature_delta":{(o==null?void 0:o.type)==="thinking"&&(n.content[t.index]={...o,signature:t.delta.signature});break}default:t.delta}return n}case"content_block_stop":return n}},Symbol.asyncIterator)](){let e=[],t=[],n=!1;return this.on("streamEvent",s=>{let o=t.shift();o?o.resolve(s):e.push(s)}),this.on("end",()=>{n=!0;for(let s of t)s.resolve(void 0);t.length=0}),this.on("abort",s=>{n=!0;for(let o of t)o.reject(s);t.length=0}),this.on("error",s=>{n=!0;for(let o of t)o.reject(s);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:n?{value:void 0,done:!0}:new Promise((o,i)=>t.push({resolve:o,reject:i})).then(o=>o?{value:o,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new Se(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}};var us=class extends Z{create(e,t){return this._client.post("/v1/messages/batches",{body:e,...t})}retrieve(e,t){return this._client.get(ge`/v1/messages/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/v1/messages/batches",Ne,{query:e,...t})}delete(e,t){return this._client.delete(ge`/v1/messages/batches/${e}`,t)}cancel(e,t){return this._client.post(ge`/v1/messages/batches/${e}/cancel`,t)}async results(e,t){let n=await this.retrieve(e);if(!n.results_url)throw new B(`No batch \`results_url\`; Has it finished processing? ${n.processing_status} - ${n.id}`);return this._client.get(n.results_url,{...t,headers:K([{Accept:"application/binary"},t==null?void 0:t.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((s,o)=>or.fromResponse(o.response,o.controller))}};var lr=class extends Z{constructor(){super(...arguments),this.batches=new us(this._client)}create(e,t){var s,o;e.model in Mh&&console.warn(`The model '${e.model}' is deprecated and will reach end-of-life on ${Mh[e.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let n=this._client._options.timeout;if(!e.stream&&n==null){let i=(s=na[e.model])!=null?s:void 0;n=this._client.calculateNonstreamingTimeout(e.max_tokens,i)}return this._client.post("/v1/messages",{body:e,timeout:n!=null?n:6e5,...t,stream:(o=e.stream)!=null?o:!1})}stream(e,t){return Hr.createMessage(this,e,t)}countTokens(e,t){return this._client.post("/v1/messages/count_tokens",{body:e,...t})}},Mh={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-3-opus-20240229":"January 5th, 2026","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};lr.Batches=us;var jr=class extends Z{retrieve(e,t={},n){let{betas:s}=t!=null?t:{};return this._client.get(ge`/v1/models/${e}`,{...n,headers:K([{...(s==null?void 0:s.toString())!=null?{"anthropic-beta":s==null?void 0:s.toString()}:void 0},n==null?void 0:n.headers])})}list(e={},t){let{betas:n,...s}=e!=null?e:{};return this._client.getAPIList("/v1/models",Ne,{query:s,...t,headers:K([{...(n==null?void 0:n.toString())!=null?{"anthropic-beta":n==null?void 0:n.toString()}:void 0},t==null?void 0:t.headers])})}};var xo=r=>{var e,t,n,s,o,i;if(typeof globalThis.process!="undefined")return(n=(t=(e=globalThis.process.env)==null?void 0:e[r])==null?void 0:t.trim())!=null?n:void 0;if(typeof globalThis.Deno!="undefined")return(i=(o=(s=globalThis.Deno.env)==null?void 0:s.get)==null?void 0:o.call(s,r))==null?void 0:i.trim()};var jc,Wc,ua,Lh,J=class{constructor({baseURL:e=xo("ANTHROPIC_BASE_URL"),apiKey:t=(o=>(o=xo("ANTHROPIC_API_KEY"))!=null?o:null)(),authToken:n=(i=>(i=xo("ANTHROPIC_AUTH_TOKEN"))!=null?i:null)(),...s}={}){var l,f,u,d,h,p;jc.add(this),ua.set(this,void 0);let a={apiKey:t,authToken:n,...s,baseURL:e||"https://api.anthropic.com"};if(!a.dangerouslyAllowBrowser&&uh())throw new B(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new Anthropic({ apiKey, dangerouslyAllowBrowser: true });
`);this.baseURL=a.baseURL,this.timeout=(l=a.timeout)!=null?l:Wc.DEFAULT_TIMEOUT,this.logger=(f=a.logger)!=null?f:console;let c="warn";this.logLevel=c,this.logLevel=(d=(u=Cc(a.logLevel,"ClientOptions.logLevel",this))!=null?u:Cc(xo("ANTHROPIC_LOG"),"process.env['ANTHROPIC_LOG']",this))!=null?d:c,this.fetchOptions=a.fetchOptions,this.maxRetries=(h=a.maxRetries)!=null?h:2,this.fetch=(p=a.fetch)!=null?p:dh(),U(this,ua,ph,"f"),this._options=a,this.apiKey=t,this.authToken=n}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,authToken:this.authToken,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){if(!(this.apiKey&&e.get("x-api-key"))&&!t.has("x-api-key")&&!(this.authToken&&e.get("authorization"))&&!t.has("authorization"))throw new Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the "X-Api-Key" or "Authorization" headers to be explicitly omitted')}authHeaders(e){return K([this.apiKeyAuth(e),this.bearerAuth(e)])}apiKeyAuth(e){if(this.apiKey!=null)return K([{"X-Api-Key":this.apiKey}])}bearerAuth(e){if(this.authToken!=null)return K([{Authorization:`Bearer ${this.authToken}`}])}stringifyQuery(e){return Object.entries(e).filter(([t,n])=>typeof n!="undefined").map(([t,n])=>{if(typeof n=="string"||typeof n=="number"||typeof n=="boolean")return`${encodeURIComponent(t)}=${encodeURIComponent(n)}`;if(n===null)return`${encodeURIComponent(t)}=`;throw new B(`Cannot stringify type ${typeof n}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}getUserAgent(){return`${this.constructor.name}/JS ${sr}`}defaultIdempotencyKey(){return`stainless-node-retry-${_c()}`}makeStatusError(e,t,n,s){return Y.generate(e,t,n,s)}buildURL(e,t,n){let s=!E(this,jc,"m",Lh).call(this)&&n||this.baseURL,o=nh(e)?new URL(e):new URL(s+(s.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),i=this.defaultQuery();return sh(i)||(t={...i,...t}),typeof t=="object"&&t&&!Array.isArray(t)&&(o.search=this.stringifyQuery(t)),o.toString()}_calculateNonstreamingTimeout(e){if(3600*e/128e3>600)throw new B("Streaming is strongly recommended for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#streaming-responses for more details");return 600*1e3}async prepareOptions(e){}async prepareRequest(e,{url:t,options:n}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,n){return this.request(Promise.resolve(n).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new Tt(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,n){var S,_,A;let s=await e,o=(S=s.maxRetries)!=null?S:this.maxRetries;t==null&&(t=o),await this.prepareOptions(s);let{req:i,url:a,timeout:c}=this.buildRequest(s,{retryCount:o-t});await this.prepareRequest(i,{url:a,options:s});let l="log_"+(Math.random()*(1<<24)|0).toString(16).padStart(6,"0"),f=n===void 0?"":`, retryOf: ${n}`,u=Date.now();if(ce(this).debug(`[${l}] sending request`,Rt({retryOfRequestLogID:n,method:s.method,url:a,options:s,headers:i.headers})),(_=s.signal)!=null&&_.aborted)throw new de;let d=new AbortController,h=await this.fetchWithTimeout(a,i,c,d).catch(no),p=Date.now();if(h instanceof Error){let P=`retrying, ${t} attempts remaining`;if((A=s.signal)!=null&&A.aborted)throw new de;let x=Ct(h)||/timed? ?out/i.test(String(h)+("cause"in h?String(h.cause):""));if(t)return ce(this).info(`[${l}] connection ${x?"timed out":"failed"} - ${P}`),ce(this).debug(`[${l}] connection ${x?"timed out":"failed"} (${P})`,Rt({retryOfRequestLogID:n,url:a,durationMs:p-u,message:h.message})),this.retryRequest(s,t,n!=null?n:l);throw ce(this).info(`[${l}] connection ${x?"timed out":"failed"} - error; no more retries left`),ce(this).debug(`[${l}] connection ${x?"timed out":"failed"} (error; no more retries left)`,Rt({retryOfRequestLogID:n,url:a,durationMs:p-u,message:h.message})),x?new zn:new nr({cause:h})}let y=[...h.headers.entries()].filter(([P])=>P==="request-id").map(([P,x])=>", "+P+": "+JSON.stringify(x)).join(""),g=`[${l}${f}${y}] ${i.method} ${a} ${h.ok?"succeeded":"failed"} with status ${h.status} in ${p-u}ms`;if(!h.ok){let P=this.shouldRetry(h);if(t&&P){let F=`retrying, ${t} attempts remaining`;return await hh(h.body),ce(this).info(`${g} - ${F}`),ce(this).debug(`[${l}] response error (${F})`,Rt({retryOfRequestLogID:n,url:h.url,status:h.status,headers:h.headers,durationMs:p-u})),this.retryRequest(s,t,n!=null?n:l,h.headers)}let x=P?"error; no more retries left":"error; not retryable";ce(this).info(`${g} - ${x}`);let R=await h.text().catch(F=>no(F).message),k=Ui(R),M=k?void 0:R;throw ce(this).debug(`[${l}] response error (${x})`,Rt({retryOfRequestLogID:n,url:h.url,status:h.status,headers:h.headers,message:M,durationMs:Date.now()-u})),this.makeStatusError(h.status,k,M,h.headers)}return ce(this).info(g),ce(this).debug(`[${l}] response start`,Rt({retryOfRequestLogID:n,url:h.url,status:h.status,headers:h.headers,durationMs:p-u})),{response:h,options:s,controller:d,requestLogID:l,retryOfRequestLogID:n,startTime:u}}getAPIList(e,t,n){return this.requestAPIList(t,{method:"get",path:e,...n})}requestAPIList(e,t){let n=this.makeRequest(t,null,void 0);return new co(this,n,e)}async fetchWithTimeout(e,t,n,s){let{signal:o,method:i,...a}=t||{};o&&o.addEventListener("abort",()=>s.abort());let c=setTimeout(()=>s.abort(),n),l=globalThis.ReadableStream&&a.body instanceof globalThis.ReadableStream||typeof a.body=="object"&&a.body!==null&&Symbol.asyncIterator in a.body,f={signal:s.signal,...l?{duplex:"half"}:{},method:"GET",...a};i&&(f.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,f)}finally{clearTimeout(c)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return t==="true"?!0:t==="false"?!1:e.status===408||e.status===409||e.status===429||e.status>=500}async retryRequest(e,t,n,s){var c;let o,i=s==null?void 0:s.get("retry-after-ms");if(i){let l=parseFloat(i);Number.isNaN(l)||(o=l)}let a=s==null?void 0:s.get("retry-after");if(a&&!o){let l=parseFloat(a);Number.isNaN(l)?o=Date.parse(a)-Date.now():o=l*1e3}if(!(o&&0<=o&&o<60*1e3)){let l=(c=e.maxRetries)!=null?c:this.maxRetries;o=this.calculateDefaultRetryTimeoutMillis(t,l)}return await ah(o),this.makeRequest(e,t-1,n)}calculateDefaultRetryTimeoutMillis(e,t){let o=t-e,i=Math.min(.5*Math.pow(2,o),8),a=1-Math.random()*.25;return i*a*1e3}calculateNonstreamingTimeout(e,t){if(36e5*e/128e3>6e5||t!=null&&e>t)throw new B("Streaming is strongly recommended for operations that may token longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details");return 6e5}buildRequest(e,{retryCount:t=0}={}){var h,p,y;let n={...e},{method:s,path:o,query:i,defaultBaseURL:a}=n,c=this.buildURL(o,i,a);"timeout"in n&&ih("timeout",n.timeout),n.timeout=(h=n.timeout)!=null?h:this.timeout;let{bodyHeaders:l,body:f}=this.buildBody({options:n}),u=this.buildHeaders({options:e,method:s,bodyHeaders:l,retryCount:t});return{req:{method:s,headers:u,...n.signal&&{signal:n.signal},...globalThis.ReadableStream&&f instanceof globalThis.ReadableStream&&{duplex:"half"},...f&&{body:f},...(p=this.fetchOptions)!=null?p:{},...(y=n.fetchOptions)!=null?y:{}},url:c,timeout:n.timeout}}buildHeaders({options:e,method:t,bodyHeaders:n,retryCount:s}){let o={};this.idempotencyHeader&&t!=="get"&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),o[this.idempotencyHeader]=e.idempotencyKey);let i=K([o,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(s),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...fh(),...this._options.dangerouslyAllowBrowser?{"anthropic-dangerous-direct-browser-access":"true"}:void 0,"anthropic-version":"2023-06-01"},this.authHeaders(e),this._options.defaultHeaders,n,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let n=K([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||typeof e=="string"&&n.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:typeof e=="object"&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&typeof e.next=="function")?{bodyHeaders:void 0,body:$i(e)}:E(this,ua,"f").call(this,{body:e,headers:n})}};Wc=J,ua=new WeakMap,jc=new WeakSet,Lh=function(){return this.baseURL!=="https://api.anthropic.com"};J.Anthropic=Wc;J.HUMAN_PROMPT=`

Human:`;J.AI_PROMPT=`

Assistant:`;J.DEFAULT_TIMEOUT=6e5;J.AnthropicError=B;J.APIError=Y;J.APIConnectionError=nr;J.APIConnectionTimeoutError=zn;J.APIUserAbortError=de;J.NotFoundError=Zn;J.ConflictError=es;J.RateLimitError=rs;J.BadRequestError=Xn;J.AuthenticationError=Yn;J.InternalServerError=ns;J.PermissionDeniedError=Qn;J.UnprocessableEntityError=ts;J.toFile=Ji;var Ke=class extends J{constructor(){super(...arguments),this.completions=new $r(this),this.messages=new lr(this),this.models=new jr(this),this.beta=new ut(this)}};Ke.Completions=$r;Ke.Messages=lr;Ke.Models=jr;Ke.Beta=ut;var{HUMAN_PROMPT:Bw,AI_PROMPT:Fw}=Ke;var Dh=require("obsidian");var Uw=async(r,e)=>{let t=r.embeds?await Promise.all(r.embeds.map(n=>qw(n,e))):[];return r.content.trim()&&t.push({type:"text",text:r.content}),{role:r.role,content:t}},qw=async(r,e)=>{let t=Dr(r.link);if(t==="application/pdf"){let n=await e(r),s=Br(n);return{type:"document",source:{type:"base64",media_type:t,data:s}}}else if(["image/png","image/jpeg","image/gif","image/webp"].includes(t)){let n=await e(r),s=Br(n);return{type:"image",source:{type:"base64",media_type:t,data:s}}}else throw new Error(m("Only PNG, JPEG, GIF, WebP, and PDF files are supported."))},$w=r=>async function*(e,t,n){let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,baseURL:c,model:l,max_tokens:f,enableWebSearch:u=!1,enableThinking:d=!1,budget_tokens:h=1600}=i,p=c;if(!a)throw new Error(m("API key is required"));p.endsWith("/v1/messages/")?p=p.slice(0,-13):p.endsWith("/v1/messages")&&(p=p.slice(0,-12));let[y,g]=e[0].role==="system"?[e[0],e.slice(1)]:[null,e];g.forEach(R=>{if(R.role==="system")throw new Error("System messages are only allowed as the first message")});let S=await Promise.all(g.map(R=>Uw(R,n))),_=new Ke({apiKey:a,baseURL:p,fetch:globalThis.fetch,dangerouslyAllowBrowser:!0}),A={model:l,max_tokens:f,messages:S,stream:!0,...y&&{system:y.content},...u&&{tools:[{name:"web_search",type:"web_search_20250305"}]},...d&&{thinking:{type:"enabled",budget_tokens:h}}},P=await _.messages.create(A,{signal:t.signal}),x=!1;for await(let R of P)if(R.type==="content_block_delta")R.delta.type==="text_delta"&&(x?(x=!1,yield ct+R.delta.text):yield R.delta.text),R.delta.type==="thinking_delta"&&(yield(x?"":(x=!0,lt))+R.delta.thinking.replace(/\n/g,`
> `));else if(R.type==="content_block_start")R.content_block.type==="server_tool_use"&&R.content_block.name==="web_search"&&new Dh.Notice(Jn("Web Search")+"Web Search");else if(R.type==="message_delta"&&R.delta.stop_reason){let k=R.delta.stop_reason;if(k!=="end_turn")throw new Error(`\u{1F534} Unexpected stop reason: ${k}`)}},Nh=["claude-sonnet-4-0","claude-opus-4-0","claude-3-7-sonnet-latest","claude-3-5-sonnet-latest","claude-3-opus-latest","claude-3-5-haiku-latest"],fa={name:"Claude",defaultOptions:{apiKey:"",baseURL:"https://api.anthropic.com",model:Nh[0],max_tokens:8192,enableWebSearch:!1,enableThinking:!1,budget_tokens:1600,parameters:{}},sendRequestFunc:$w,models:Nh,websiteToObtainKey:"https://console.anthropic.com",capabilities:["Text Generation","Web Search","Reasoning","Image Vision","PDF Vision"]};var Hw=r=>async function*(e,t,n){var p;let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,baseURL:c,model:l,...f}=i;if(!a)throw new Error(m("API key is required"));let d=await new D({apiKey:a,baseURL:c,dangerouslyAllowBrowser:!0}).chat.completions.create({model:l,messages:e,stream:!0,...f},{signal:t.signal}),h=!1;for await(let y of d){y.usage&&y.usage.prompt_tokens&&y.usage.completion_tokens&&console.debug(`Prompt tokens: ${y.usage.prompt_tokens}, completion tokens: ${y.usage.completion_tokens}`);let g=(p=y.choices[0])==null?void 0:p.delta,S=g==null?void 0:g.reasoning_content;if(S)yield(h?"":(h=!0,lt))+S.replace(/\n/g,`
> `);else{let _=h?(h=!1,ct):"";g!=null&&g.content&&(yield _+(g==null?void 0:g.content))}}},Bh=["deepseek-chat","deepseek-reasoner"],Fh={name:"DeepSeek",defaultOptions:{apiKey:"",baseURL:"https://api.deepseek.com",model:Bh[0],parameters:{}},sendRequestFunc:Hw,models:Bh,websiteToObtainKey:"https://platform.deepseek.com",capabilities:["Text Generation","Reasoning"]};var da=require("obsidian");var jw=r=>async function*(e,t,n){let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,baseURL:c,model:l,...f}=i;if(!a)throw new Error(m("API key is required"));if(!l)throw new Error(m("Model is required"));let u={model:l,messages:e,stream:!1,...f};new da.Notice(m("This is a non-streaming request, please wait..."),5*1e3),yield(await(0,da.requestUrl)({url:c,method:"POST",body:JSON.stringify(u),headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}})).json.choices[0].message.content},Uh={name:"Doubao",defaultOptions:{apiKey:"",baseURL:"https://ark.cn-beijing.volces.com/api/v3/chat/completions",model:"",parameters:{}},sendRequestFunc:jw,models:[],websiteToObtainKey:"https://www.volcengine.com",capabilities:["Text Generation"]};var qh;(function(r){r.STRING="string",r.NUMBER="number",r.INTEGER="integer",r.BOOLEAN="boolean",r.ARRAY="array",r.OBJECT="object"})(qh||(qh={}));var $h;(function(r){r.LANGUAGE_UNSPECIFIED="language_unspecified",r.PYTHON="python"})($h||($h={}));var Hh;(function(r){r.OUTCOME_UNSPECIFIED="outcome_unspecified",r.OUTCOME_OK="outcome_ok",r.OUTCOME_FAILED="outcome_failed",r.OUTCOME_DEADLINE_EXCEEDED="outcome_deadline_exceeded"})(Hh||(Hh={}));var jh=["user","model","function","system"],Wh;(function(r){r.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",r.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",r.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",r.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",r.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT"})(Wh||(Wh={}));var Vh;(function(r){r.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",r.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",r.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",r.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",r.BLOCK_NONE="BLOCK_NONE"})(Vh||(Vh={}));var Kh;(function(r){r.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",r.NEGLIGIBLE="NEGLIGIBLE",r.LOW="LOW",r.MEDIUM="MEDIUM",r.HIGH="HIGH"})(Kh||(Kh={}));var Gh;(function(r){r.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",r.SAFETY="SAFETY",r.OTHER="OTHER"})(Gh||(Gh={}));var Eo;(function(r){r.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",r.STOP="STOP",r.MAX_TOKENS="MAX_TOKENS",r.SAFETY="SAFETY",r.RECITATION="RECITATION",r.LANGUAGE="LANGUAGE",r.OTHER="OTHER"})(Eo||(Eo={}));var Jh;(function(r){r.TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",r.RETRIEVAL_QUERY="RETRIEVAL_QUERY",r.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",r.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",r.CLASSIFICATION="CLASSIFICATION",r.CLUSTERING="CLUSTERING"})(Jh||(Jh={}));var zh;(function(r){r.MODE_UNSPECIFIED="MODE_UNSPECIFIED",r.AUTO="AUTO",r.ANY="ANY",r.NONE="NONE"})(zh||(zh={}));var Xh;(function(r){r.MODE_UNSPECIFIED="MODE_UNSPECIFIED",r.MODE_DYNAMIC="MODE_DYNAMIC"})(Xh||(Xh={}));var ye=class extends Error{constructor(e){super(`[GoogleGenerativeAI Error]: ${e}`)}},cr=class extends ye{constructor(e,t){super(e),this.response=t}},pa=class extends ye{constructor(e,t,n,s){super(e),this.status=t,this.statusText=n,this.errorDetails=s}},ft=class extends ye{};var Ww="https://generativelanguage.googleapis.com",Vw="v1beta",Kw="0.21.0",Gw="genai-js",Wr;(function(r){r.GENERATE_CONTENT="generateContent",r.STREAM_GENERATE_CONTENT="streamGenerateContent",r.COUNT_TOKENS="countTokens",r.EMBED_CONTENT="embedContent",r.BATCH_EMBED_CONTENTS="batchEmbedContents"})(Wr||(Wr={}));var Vc=class{constructor(e,t,n,s,o){this.model=e,this.task=t,this.apiKey=n,this.stream=s,this.requestOptions=o}toString(){var e,t;let n=((e=this.requestOptions)===null||e===void 0?void 0:e.apiVersion)||Vw,o=`${((t=this.requestOptions)===null||t===void 0?void 0:t.baseUrl)||Ww}/${n}/${this.model}:${this.task}`;return this.stream&&(o+="?alt=sse"),o}};function Jw(r){let e=[];return r!=null&&r.apiClient&&e.push(r.apiClient),e.push(`${Gw}/${Kw}`),e.join(" ")}async function zw(r){var e;let t=new Headers;t.append("Content-Type","application/json"),t.append("x-goog-api-client",Jw(r.requestOptions)),t.append("x-goog-api-key",r.apiKey);let n=(e=r.requestOptions)===null||e===void 0?void 0:e.customHeaders;if(n){if(!(n instanceof Headers))try{n=new Headers(n)}catch(s){throw new ft(`unable to convert customHeaders value ${JSON.stringify(n)} to Headers: ${s.message}`)}for(let[s,o]of n.entries()){if(s==="x-goog-api-key")throw new ft(`Cannot set reserved header name ${s}`);if(s==="x-goog-api-client")throw new ft(`Header name ${s} can only be set using the apiClient field`);t.append(s,o)}}return t}async function Xw(r,e,t,n,s,o){let i=new Vc(r,e,t,n,o);return{url:i.toString(),fetchOptions:Object.assign(Object.assign({},e_(o)),{method:"POST",headers:await zw(i),body:s})}}async function Co(r,e,t,n,s,o={},i=fetch){let{url:a,fetchOptions:c}=await Xw(r,e,t,n,s,o);return Yw(a,c,i)}async function Yw(r,e,t=fetch){let n;try{n=await t(r,e)}catch(s){Qw(s,r)}return n.ok||await Zw(n,r),n}function Qw(r,e){let t=r;throw r instanceof pa||r instanceof ft||(t=new ye(`Error fetching from ${e.toString()}: ${r.message}`),t.stack=r.stack),t}async function Zw(r,e){let t="",n;try{let s=await r.json();t=s.error.message,s.error.details&&(t+=` ${JSON.stringify(s.error.details)}`,n=s.error.details)}catch(s){}throw new pa(`Error fetching from ${e.toString()}: [${r.status} ${r.statusText}] ${t}`,r.status,r.statusText,n)}function e_(r){let e={};if((r==null?void 0:r.signal)!==void 0||(r==null?void 0:r.timeout)>=0){let t=new AbortController;(r==null?void 0:r.timeout)>=0&&setTimeout(()=>t.abort(),r.timeout),r!=null&&r.signal&&r.signal.addEventListener("abort",()=>{t.abort()}),e.signal=t.signal}return e}function Gc(r){return r.text=()=>{if(r.candidates&&r.candidates.length>0){if(r.candidates.length>1&&console.warn(`This response had ${r.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),ha(r.candidates[0]))throw new cr(`${ur(r)}`,r);return t_(r)}else if(r.promptFeedback)throw new cr(`Text not available. ${ur(r)}`,r);return""},r.functionCall=()=>{if(r.candidates&&r.candidates.length>0){if(r.candidates.length>1&&console.warn(`This response had ${r.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),ha(r.candidates[0]))throw new cr(`${ur(r)}`,r);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),Yh(r)[0]}else if(r.promptFeedback)throw new cr(`Function call not available. ${ur(r)}`,r)},r.functionCalls=()=>{if(r.candidates&&r.candidates.length>0){if(r.candidates.length>1&&console.warn(`This response had ${r.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),ha(r.candidates[0]))throw new cr(`${ur(r)}`,r);return Yh(r)}else if(r.promptFeedback)throw new cr(`Function call not available. ${ur(r)}`,r)},r}function t_(r){var e,t,n,s;let o=[];if(!((t=(e=r.candidates)===null||e===void 0?void 0:e[0].content)===null||t===void 0)&&t.parts)for(let i of(s=(n=r.candidates)===null||n===void 0?void 0:n[0].content)===null||s===void 0?void 0:s.parts)i.text&&o.push(i.text),i.executableCode&&o.push("\n```"+i.executableCode.language+`
`+i.executableCode.code+"\n```\n"),i.codeExecutionResult&&o.push("\n```\n"+i.codeExecutionResult.output+"\n```\n");return o.length>0?o.join(""):""}function Yh(r){var e,t,n,s;let o=[];if(!((t=(e=r.candidates)===null||e===void 0?void 0:e[0].content)===null||t===void 0)&&t.parts)for(let i of(s=(n=r.candidates)===null||n===void 0?void 0:n[0].content)===null||s===void 0?void 0:s.parts)i.functionCall&&o.push(i.functionCall);if(o.length>0)return o}var r_=[Eo.RECITATION,Eo.SAFETY,Eo.LANGUAGE];function ha(r){return!!r.finishReason&&r_.includes(r.finishReason)}function ur(r){var e,t,n;let s="";if((!r.candidates||r.candidates.length===0)&&r.promptFeedback)s+="Response was blocked",!((e=r.promptFeedback)===null||e===void 0)&&e.blockReason&&(s+=` due to ${r.promptFeedback.blockReason}`),!((t=r.promptFeedback)===null||t===void 0)&&t.blockReasonMessage&&(s+=`: ${r.promptFeedback.blockReasonMessage}`);else if(!((n=r.candidates)===null||n===void 0)&&n[0]){let o=r.candidates[0];ha(o)&&(s+=`Candidate was blocked due to ${o.finishReason}`,o.finishMessage&&(s+=`: ${o.finishMessage}`))}return s}function vo(r){return this instanceof vo?(this.v=r,this):new vo(r)}function n_(r,e,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=t.apply(r,e||[]),s,o=[];return s={},i("next"),i("throw"),i("return"),s[Symbol.asyncIterator]=function(){return this},s;function i(d){n[d]&&(s[d]=function(h){return new Promise(function(p,y){o.push([d,h,p,y])>1||a(d,h)})})}function a(d,h){try{c(n[d](h))}catch(p){u(o[0][3],p)}}function c(d){d.value instanceof vo?Promise.resolve(d.value.v).then(l,f):u(o[0][2],d)}function l(d){a("next",d)}function f(d){a("throw",d)}function u(d,h){d(h),o.shift(),o.length&&a(o[0][0],o[0][1])}}var Qh=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;function s_(r){let e=r.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0})),t=a_(e),[n,s]=t.tee();return{stream:i_(n),response:o_(s)}}async function o_(r){let e=[],t=r.getReader();for(;;){let{done:n,value:s}=await t.read();if(n)return Gc(l_(e));e.push(s)}}function i_(r){return n_(this,arguments,function*(){let t=r.getReader();for(;;){let{value:n,done:s}=yield vo(t.read());if(s)break;yield yield vo(Gc(n))}})}function a_(r){let e=r.getReader();return new ReadableStream({start(n){let s="";return o();function o(){return e.read().then(({value:i,done:a})=>{if(a){if(s.trim()){n.error(new ye("Failed to parse stream"));return}n.close();return}s+=i;let c=s.match(Qh),l;for(;c;){try{l=JSON.parse(c[1])}catch(f){n.error(new ye(`Error parsing JSON response: "${c[1]}"`));return}n.enqueue(l),s=s.substring(c[0].length),c=s.match(Qh)}return o()})}}})}function l_(r){let e=r[r.length-1],t={promptFeedback:e==null?void 0:e.promptFeedback};for(let n of r){if(n.candidates)for(let s of n.candidates){let o=s.index;if(t.candidates||(t.candidates=[]),t.candidates[o]||(t.candidates[o]={index:s.index}),t.candidates[o].citationMetadata=s.citationMetadata,t.candidates[o].groundingMetadata=s.groundingMetadata,t.candidates[o].finishReason=s.finishReason,t.candidates[o].finishMessage=s.finishMessage,t.candidates[o].safetyRatings=s.safetyRatings,s.content&&s.content.parts){t.candidates[o].content||(t.candidates[o].content={role:s.content.role||"user",parts:[]});let i={};for(let a of s.content.parts)a.text&&(i.text=a.text),a.functionCall&&(i.functionCall=a.functionCall),a.executableCode&&(i.executableCode=a.executableCode),a.codeExecutionResult&&(i.codeExecutionResult=a.codeExecutionResult),Object.keys(i).length===0&&(i.text=""),t.candidates[o].content.parts.push(i)}}n.usageMetadata&&(t.usageMetadata=n.usageMetadata)}return t}async function rp(r,e,t,n){let s=await Co(e,Wr.STREAM_GENERATE_CONTENT,r,!0,JSON.stringify(t),n);return s_(s)}async function np(r,e,t,n){let o=await(await Co(e,Wr.GENERATE_CONTENT,r,!1,JSON.stringify(t),n)).json();return{response:Gc(o)}}function sp(r){if(r!=null){if(typeof r=="string")return{role:"system",parts:[{text:r}]};if(r.text)return{role:"system",parts:[r]};if(r.parts)return r.role?r:{role:"system",parts:r.parts}}}function Ao(r){let e=[];if(typeof r=="string")e=[{text:r}];else for(let t of r)typeof t=="string"?e.push({text:t}):e.push(t);return c_(e)}function c_(r){let e={role:"user",parts:[]},t={role:"function",parts:[]},n=!1,s=!1;for(let o of r)"functionResponse"in o?(t.parts.push(o),s=!0):(e.parts.push(o),n=!0);if(n&&s)throw new ye("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!n&&!s)throw new ye("No content is provided for sending chat message.");return n?e:t}function u_(r,e){var t;let n={model:e==null?void 0:e.model,generationConfig:e==null?void 0:e.generationConfig,safetySettings:e==null?void 0:e.safetySettings,tools:e==null?void 0:e.tools,toolConfig:e==null?void 0:e.toolConfig,systemInstruction:e==null?void 0:e.systemInstruction,cachedContent:(t=e==null?void 0:e.cachedContent)===null||t===void 0?void 0:t.name,contents:[]},s=r.generateContentRequest!=null;if(r.contents){if(s)throw new ft("CountTokensRequest must have one of contents or generateContentRequest, not both.");n.contents=r.contents}else if(s)n=Object.assign(Object.assign({},n),r.generateContentRequest);else{let o=Ao(r);n.contents=[o]}return{generateContentRequest:n}}function Zh(r){let e;return r.contents?e=r:e={contents:[Ao(r)]},r.systemInstruction&&(e.systemInstruction=sp(r.systemInstruction)),e}function f_(r){return typeof r=="string"||Array.isArray(r)?{content:Ao(r)}:r}var ep=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],d_={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function h_(r){let e=!1;for(let t of r){let{role:n,parts:s}=t;if(!e&&n!=="user")throw new ye(`First content should be with role 'user', got ${n}`);if(!jh.includes(n))throw new ye(`Each item should include role field. Got ${n} but valid roles are: ${JSON.stringify(jh)}`);if(!Array.isArray(s))throw new ye("Content should have 'parts' property with an array of Parts");if(s.length===0)throw new ye("Each Content should have at least one part");let o={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(let a of s)for(let c of ep)c in a&&(o[c]+=1);let i=d_[n];for(let a of ep)if(!i.includes(a)&&o[a]>0)throw new ye(`Content with role '${n}' can't contain '${a}' part`);e=!0}}var tp="SILENT_ERROR",Kc=class{constructor(e,t,n,s={}){this.model=t,this.params=n,this._requestOptions=s,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=e,n!=null&&n.history&&(h_(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(e,t={}){var n,s,o,i,a,c;await this._sendPromise;let l=Ao(e),f={safetySettings:(n=this.params)===null||n===void 0?void 0:n.safetySettings,generationConfig:(s=this.params)===null||s===void 0?void 0:s.generationConfig,tools:(o=this.params)===null||o===void 0?void 0:o.tools,toolConfig:(i=this.params)===null||i===void 0?void 0:i.toolConfig,systemInstruction:(a=this.params)===null||a===void 0?void 0:a.systemInstruction,cachedContent:(c=this.params)===null||c===void 0?void 0:c.cachedContent,contents:[...this._history,l]},u=Object.assign(Object.assign({},this._requestOptions),t),d;return this._sendPromise=this._sendPromise.then(()=>np(this._apiKey,this.model,f,u)).then(h=>{var p;if(h.response.candidates&&h.response.candidates.length>0){this._history.push(l);let y=Object.assign({parts:[],role:"model"},(p=h.response.candidates)===null||p===void 0?void 0:p[0].content);this._history.push(y)}else{let y=ur(h.response);y&&console.warn(`sendMessage() was unsuccessful. ${y}. Inspect response object for details.`)}d=h}),await this._sendPromise,d}async sendMessageStream(e,t={}){var n,s,o,i,a,c;await this._sendPromise;let l=Ao(e),f={safetySettings:(n=this.params)===null||n===void 0?void 0:n.safetySettings,generationConfig:(s=this.params)===null||s===void 0?void 0:s.generationConfig,tools:(o=this.params)===null||o===void 0?void 0:o.tools,toolConfig:(i=this.params)===null||i===void 0?void 0:i.toolConfig,systemInstruction:(a=this.params)===null||a===void 0?void 0:a.systemInstruction,cachedContent:(c=this.params)===null||c===void 0?void 0:c.cachedContent,contents:[...this._history,l]},u=Object.assign(Object.assign({},this._requestOptions),t),d=rp(this._apiKey,this.model,f,u);return this._sendPromise=this._sendPromise.then(()=>d).catch(h=>{throw new Error(tp)}).then(h=>h.response).then(h=>{if(h.candidates&&h.candidates.length>0){this._history.push(l);let p=Object.assign({},h.candidates[0].content);p.role||(p.role="model"),this._history.push(p)}else{let p=ur(h);p&&console.warn(`sendMessageStream() was unsuccessful. ${p}. Inspect response object for details.`)}}).catch(h=>{h.message!==tp&&console.error(h)}),d}};async function p_(r,e,t,n){return(await Co(e,Wr.COUNT_TOKENS,r,!1,JSON.stringify(t),n)).json()}async function m_(r,e,t,n){return(await Co(e,Wr.EMBED_CONTENT,r,!1,JSON.stringify(t),n)).json()}async function g_(r,e,t,n){let s=t.requests.map(i=>Object.assign(Object.assign({},i),{model:e}));return(await Co(e,Wr.BATCH_EMBED_CONTENTS,r,!1,JSON.stringify({requests:s}),n)).json()}var ma=class{constructor(e,t,n={}){this.apiKey=e,this._requestOptions=n,t.model.includes("/")?this.model=t.model:this.model=`models/${t.model}`,this.generationConfig=t.generationConfig||{},this.safetySettings=t.safetySettings||[],this.tools=t.tools,this.toolConfig=t.toolConfig,this.systemInstruction=sp(t.systemInstruction),this.cachedContent=t.cachedContent}async generateContent(e,t={}){var n;let s=Zh(e),o=Object.assign(Object.assign({},this._requestOptions),t);return np(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:(n=this.cachedContent)===null||n===void 0?void 0:n.name},s),o)}async generateContentStream(e,t={}){var n;let s=Zh(e),o=Object.assign(Object.assign({},this._requestOptions),t);return rp(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:(n=this.cachedContent)===null||n===void 0?void 0:n.name},s),o)}startChat(e){var t;return new Kc(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:(t=this.cachedContent)===null||t===void 0?void 0:t.name},e),this._requestOptions)}async countTokens(e,t={}){let n=u_(e,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),s=Object.assign(Object.assign({},this._requestOptions),t);return p_(this.apiKey,this.model,n,s)}async embedContent(e,t={}){let n=f_(e),s=Object.assign(Object.assign({},this._requestOptions),t);return m_(this.apiKey,this.model,n,s)}async batchEmbedContents(e,t={}){let n=Object.assign(Object.assign({},this._requestOptions),t);return g_(this.apiKey,this.model,e,n)}};var ga=class{constructor(e){this.apiKey=e}getGenerativeModel(e,t){if(!e.model)throw new ye("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new ma(this.apiKey,e,t)}getGenerativeModelFromCachedContent(e,t,n){if(!e.name)throw new ft("Cached content must contain a `name` field.");if(!e.model)throw new ft("Cached content must contain a `model` field.");let s=["model","systemInstruction"];for(let i of s)if(t!=null&&t[i]&&e[i]&&(t==null?void 0:t[i])!==e[i]){if(i==="model"){let a=t.model.startsWith("models/")?t.model.replace("models/",""):t.model,c=e.model.startsWith("models/")?e.model.replace("models/",""):e.model;if(a===c)continue}throw new ft(`Different value for "${i}" specified in modelParams (${t[i]}) and cachedContent (${e[i]})`)}let o=Object.assign(Object.assign({},t),{model:e.model,tools:e.tools,toolConfig:e.toolConfig,systemInstruction:e.systemInstruction,cachedContent:e});return new ma(this.apiKey,o,n)}};var y_=r=>async function*(e,t,n){let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,baseURL:c,model:l}=i;if(!a)throw new Error(m("API key is required"));let[f,u,d]=e[0].role==="system"?[e[0],e.slice(1,-1),e[e.length-1]]:[null,e.slice(0,-1),e[e.length-1]],h=f==null?void 0:f.content,p=u.map(A=>({role:A.role==="assistant"?"model":A.role,parts:[{text:A.content}]})),_=await new ga(a).getGenerativeModel({model:l,systemInstruction:h},{baseUrl:c}).startChat({history:p}).sendMessageStream(d.content,{signal:t.signal});for await(let A of _.stream)yield A.text()},op={name:"Gemini",defaultOptions:{apiKey:"",baseURL:"https://generativelanguage.googleapis.com",model:"gemini-1.5-flash",parameters:{}},sendRequestFunc:y_,models:[],websiteToObtainKey:"https://makersuite.google.com/app/apikey",capabilities:["Text Generation"]};var ya=require("obsidian");var ip=["gpt-image-1"],Vr={n:2,displayWidth:400,background:"auto",output_format:"jpeg",output_compression:90,quality:"auto",size:"auto"},b_=r=>async function*(e,t,n,s){let{parameters:o,...i}=r,a={...i,...o},{apiKey:c,baseURL:l,model:f,displayWidth:u,background:d,n:h,output_compression:p,output_format:y,quality:g,size:S}=a;if(!c)throw new Error(m("API key is required"));if(!s)throw new Error("saveAttachment is required");console.debug("messages:",e),console.debug("options:",a),e.length>1&&new ya.Notice(m("Only the last user message is used for image generation. Other messages are ignored."));let _=e.last();if(!_)throw new Error("No user message found in the conversation");let A=_.content,P=new D({apiKey:c,baseURL:l,dangerouslyAllowBrowser:!0});new ya.Notice(m("This is a non-streaming request, please wait..."),5*1e3);let x=null;if(_.embeds&&_.embeds.length>0){_.embeds.length>1&&new ya.Notice(m("Multiple embeds found, only the first one will be used"));let M=_.embeds[0],q=Dr(M.link);if(!["image/png","image/jpeg","image/webp"].includes(q))throw new Error(m("Only PNG, JPEG, and WebP images are supported for editing."));let V=await n(M);if(!V||V.byteLength===0)throw new Error(m("Embed data is empty or invalid"));let H=new File([V],M.link,{type:q});x=await P.images.edit({image:H,prompt:A,background:d,model:f,n:h,size:S,quality:g},{signal:t.signal})}else x=await P.images.generate({prompt:A,background:d,model:f,size:S,n:h,output_compression:y==="jpeg"||y==="webp"?p:void 0,output_format:y,quality:g},{signal:t.signal});if(!x.data||x.data.length===0)throw new Error(m("Failed to generate image. no data received from API"));yield` 
`;let R=new Date,k=`${R.getFullYear()}${String(R.getMonth()+1).padStart(2,"0")}${String(R.getDate()).padStart(2,"0")}_${String(R.getHours()).padStart(2,"0")}${String(R.getMinutes()).padStart(2,"0")}${String(R.getSeconds()).padStart(2,"0")}`;for(let M=0;M<x.data.length;M++){let F=x.data[M].b64_json;if(!F){console.error(`No base64 image data returned for image ${M+1}`);continue}let V=Buffer.from(F,"base64"),H=h>1?`-${M+1}`:"",re=`gptImage-${k}${H}.${y}`;console.debug(`Saving image as ${re}`),await s(re,V),yield`![[${re}|${u}]]

`}},ba={name:"GptImage",defaultOptions:{apiKey:"",baseURL:"https://api.openai.com/v1",model:ip[0],n:Vr.n,displayWidth:Vr.displayWidth,background:Vr.background,output_compression:Vr.output_compression,output_format:Vr.output_format,quality:Vr.quality,size:Vr.size,parameters:{}},sendRequestFunc:b_,models:ip,websiteToObtainKey:"https://platform.openai.com/api-keys",capabilities:["Image Generation","Image Editing"]};function Po(r,e){return function(){return r.apply(e,arguments)}}var{toString:w_}=Object.prototype,{getPrototypeOf:zc}=Object,_a=(r=>e=>{let t=w_.call(e);return r[t]||(r[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),Ge=r=>(r=r.toLowerCase(),e=>_a(e)===r),Sa=r=>e=>typeof e===r,{isArray:fs}=Array,Ro=Sa("undefined");function __(r){return r!==null&&!Ro(r)&&r.constructor!==null&&!Ro(r.constructor)&&ke(r.constructor.isBuffer)&&r.constructor.isBuffer(r)}var lp=Ge("ArrayBuffer");function S_(r){let e;return typeof ArrayBuffer!="undefined"&&ArrayBuffer.isView?e=ArrayBuffer.isView(r):e=r&&r.buffer&&lp(r.buffer),e}var x_=Sa("string"),ke=Sa("function"),cp=Sa("number"),xa=r=>r!==null&&typeof r=="object",E_=r=>r===!0||r===!1,wa=r=>{if(_a(r)!=="object")return!1;let e=zc(r);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in r)&&!(Symbol.iterator in r)},v_=Ge("Date"),A_=Ge("File"),C_=Ge("Blob"),P_=Ge("FileList"),R_=r=>xa(r)&&ke(r.pipe),T_=r=>{let e;return r&&(typeof FormData=="function"&&r instanceof FormData||ke(r.append)&&((e=_a(r))==="formdata"||e==="object"&&ke(r.toString)&&r.toString()==="[object FormData]"))},I_=Ge("URLSearchParams"),[k_,O_,M_,L_]=["ReadableStream","Request","Response","Headers"].map(Ge),N_=r=>r.trim?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function To(r,e,{allOwnKeys:t=!1}={}){if(r===null||typeof r=="undefined")return;let n,s;if(typeof r!="object"&&(r=[r]),fs(r))for(n=0,s=r.length;n<s;n++)e.call(null,r[n],n,r);else{let o=t?Object.getOwnPropertyNames(r):Object.keys(r),i=o.length,a;for(n=0;n<i;n++)a=o[n],e.call(null,r[a],a,r)}}function up(r,e){e=e.toLowerCase();let t=Object.keys(r),n=t.length,s;for(;n-- >0;)if(s=t[n],e===s.toLowerCase())return s;return null}var Kr=(()=>typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:global)(),fp=r=>!Ro(r)&&r!==Kr;function Jc(){let{caseless:r}=fp(this)&&this||{},e={},t=(n,s)=>{let o=r&&up(e,s)||s;wa(e[o])&&wa(n)?e[o]=Jc(e[o],n):wa(n)?e[o]=Jc({},n):fs(n)?e[o]=n.slice():e[o]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&To(arguments[n],t);return e}var D_=(r,e,t,{allOwnKeys:n}={})=>(To(e,(s,o)=>{t&&ke(s)?r[o]=Po(s,t):r[o]=s},{allOwnKeys:n}),r),B_=r=>(r.charCodeAt(0)===65279&&(r=r.slice(1)),r),F_=(r,e,t,n)=>{r.prototype=Object.create(e.prototype,n),r.prototype.constructor=r,Object.defineProperty(r,"super",{value:e.prototype}),t&&Object.assign(r.prototype,t)},U_=(r,e,t,n)=>{let s,o,i,a={};if(e=e||{},r==null)return e;do{for(s=Object.getOwnPropertyNames(r),o=s.length;o-- >0;)i=s[o],(!n||n(i,r,e))&&!a[i]&&(e[i]=r[i],a[i]=!0);r=t!==!1&&zc(r)}while(r&&(!t||t(r,e))&&r!==Object.prototype);return e},q_=(r,e,t)=>{r=String(r),(t===void 0||t>r.length)&&(t=r.length),t-=e.length;let n=r.indexOf(e,t);return n!==-1&&n===t},$_=r=>{if(!r)return null;if(fs(r))return r;let e=r.length;if(!cp(e))return null;let t=new Array(e);for(;e-- >0;)t[e]=r[e];return t},H_=(r=>e=>r&&e instanceof r)(typeof Uint8Array!="undefined"&&zc(Uint8Array)),j_=(r,e)=>{let n=(r&&r[Symbol.iterator]).call(r),s;for(;(s=n.next())&&!s.done;){let o=s.value;e.call(r,o[0],o[1])}},W_=(r,e)=>{let t,n=[];for(;(t=r.exec(e))!==null;)n.push(t);return n},V_=Ge("HTMLFormElement"),K_=r=>r.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,n,s){return n.toUpperCase()+s}),ap=(({hasOwnProperty:r})=>(e,t)=>r.call(e,t))(Object.prototype),G_=Ge("RegExp"),dp=(r,e)=>{let t=Object.getOwnPropertyDescriptors(r),n={};To(t,(s,o)=>{let i;(i=e(s,o,r))!==!1&&(n[o]=i||s)}),Object.defineProperties(r,n)},J_=r=>{dp(r,(e,t)=>{if(ke(r)&&["arguments","caller","callee"].indexOf(t)!==-1)return!1;let n=r[t];if(ke(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},z_=(r,e)=>{let t={},n=s=>{s.forEach(o=>{t[o]=!0})};return fs(r)?n(r):n(String(r).split(e)),t},X_=()=>{},Y_=(r,e)=>r!=null&&Number.isFinite(r=+r)?r:e;function Q_(r){return!!(r&&ke(r.append)&&r[Symbol.toStringTag]==="FormData"&&r[Symbol.iterator])}var Z_=r=>{let e=new Array(10),t=(n,s)=>{if(xa(n)){if(e.indexOf(n)>=0)return;if(!("toJSON"in n)){e[s]=n;let o=fs(n)?[]:{};return To(n,(i,a)=>{let c=t(i,s+1);!Ro(c)&&(o[a]=c)}),e[s]=void 0,o}}return n};return t(r,0)},eS=Ge("AsyncFunction"),tS=r=>r&&(xa(r)||ke(r))&&ke(r.then)&&ke(r.catch),hp=((r,e)=>r?setImmediate:e?((t,n)=>(Kr.addEventListener("message",({source:s,data:o})=>{s===Kr&&o===t&&n.length&&n.shift()()},!1),s=>{n.push(s),Kr.postMessage(t,"*")}))(`axios@${Math.random()}`,[]):t=>setTimeout(t))(typeof setImmediate=="function",ke(Kr.postMessage)),rS=typeof queueMicrotask!="undefined"?queueMicrotask.bind(Kr):typeof process!="undefined"&&process.nextTick||hp,w={isArray:fs,isArrayBuffer:lp,isBuffer:__,isFormData:T_,isArrayBufferView:S_,isString:x_,isNumber:cp,isBoolean:E_,isObject:xa,isPlainObject:wa,isReadableStream:k_,isRequest:O_,isResponse:M_,isHeaders:L_,isUndefined:Ro,isDate:v_,isFile:A_,isBlob:C_,isRegExp:G_,isFunction:ke,isStream:R_,isURLSearchParams:I_,isTypedArray:H_,isFileList:P_,forEach:To,merge:Jc,extend:D_,trim:N_,stripBOM:B_,inherits:F_,toFlatObject:U_,kindOf:_a,kindOfTest:Ge,endsWith:q_,toArray:$_,forEachEntry:j_,matchAll:W_,isHTMLForm:V_,hasOwnProperty:ap,hasOwnProp:ap,reduceDescriptors:dp,freezeMethods:J_,toObjectSet:z_,toCamelCase:K_,noop:X_,toFiniteNumber:Y_,findKey:up,global:Kr,isContextDefined:fp,isSpecCompliantForm:Q_,toJSONObject:Z_,isAsyncFn:eS,isThenable:tS,setImmediate:hp,asap:rS};function ds(r,e,t,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=r,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}w.inherits(ds,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:w.toJSONObject(this.config),code:this.code,status:this.status}}});var pp=ds.prototype,mp={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(r=>{mp[r]={value:r}});Object.defineProperties(ds,mp);Object.defineProperty(pp,"isAxiosError",{value:!0});ds.from=(r,e,t,n,s,o)=>{let i=Object.create(pp);return w.toFlatObject(r,i,function(c){return c!==Error.prototype},a=>a!=="isAxiosError"),ds.call(i,r.message,e,t,n,s),i.cause=r,i.name=r.name,o&&Object.assign(i,o),i};var $=ds;var Ea=null;function Xc(r){return w.isPlainObject(r)||w.isArray(r)}function yp(r){return w.endsWith(r,"[]")?r.slice(0,-2):r}function gp(r,e,t){return r?r.concat(e).map(function(s,o){return s=yp(s),!t&&o?"["+s+"]":s}).join(t?".":""):e}function nS(r){return w.isArray(r)&&!r.some(Xc)}var sS=w.toFlatObject(w,{},null,function(e){return/^is[A-Z]/.test(e)});function oS(r,e,t){if(!w.isObject(r))throw new TypeError("target must be an object");e=e||new(Ea||FormData),t=w.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,g){return!w.isUndefined(g[y])});let n=t.metaTokens,s=t.visitor||f,o=t.dots,i=t.indexes,c=(t.Blob||typeof Blob!="undefined"&&Blob)&&w.isSpecCompliantForm(e);if(!w.isFunction(s))throw new TypeError("visitor must be a function");function l(p){if(p===null)return"";if(w.isDate(p))return p.toISOString();if(!c&&w.isBlob(p))throw new $("Blob is not supported. Use a Buffer instead.");return w.isArrayBuffer(p)||w.isTypedArray(p)?c&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function f(p,y,g){let S=p;if(p&&!g&&typeof p=="object"){if(w.endsWith(y,"{}"))y=n?y:y.slice(0,-2),p=JSON.stringify(p);else if(w.isArray(p)&&nS(p)||(w.isFileList(p)||w.endsWith(y,"[]"))&&(S=w.toArray(p)))return y=yp(y),S.forEach(function(A,P){!(w.isUndefined(A)||A===null)&&e.append(i===!0?gp([y],P,o):i===null?y:y+"[]",l(A))}),!1}return Xc(p)?!0:(e.append(gp(g,y,o),l(p)),!1)}let u=[],d=Object.assign(sS,{defaultVisitor:f,convertValue:l,isVisitable:Xc});function h(p,y){if(!w.isUndefined(p)){if(u.indexOf(p)!==-1)throw Error("Circular reference detected in "+y.join("."));u.push(p),w.forEach(p,function(S,_){(!(w.isUndefined(S)||S===null)&&s.call(e,S,w.isString(_)?_.trim():_,y,d))===!0&&h(S,y?y.concat(_):[_])}),u.pop()}}if(!w.isObject(r))throw new TypeError("data must be an object");return h(r),e}var fr=oS;function bp(r){let e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(r).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function wp(r,e){this._pairs=[],r&&fr(r,this,e)}var _p=wp.prototype;_p.append=function(e,t){this._pairs.push([e,t])};_p.toString=function(e){let t=e?function(n){return e.call(this,n,bp)}:bp;return this._pairs.map(function(s){return t(s[0])+"="+t(s[1])},"").join("&")};var va=wp;function iS(r){return encodeURIComponent(r).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Io(r,e,t){if(!e)return r;let n=t&&t.encode||iS;w.isFunction(t)&&(t={serialize:t});let s=t&&t.serialize,o;if(s?o=s(e,t):o=w.isURLSearchParams(e)?e.toString():new va(e,t).toString(n),o){let i=r.indexOf("#");i!==-1&&(r=r.slice(0,i)),r+=(r.indexOf("?")===-1?"?":"&")+o}return r}var Yc=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){w.forEach(this.handlers,function(n){n!==null&&e(n)})}},Qc=Yc;var Aa={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var Sp=typeof URLSearchParams!="undefined"?URLSearchParams:va;var xp=typeof FormData!="undefined"?FormData:null;var Ep=typeof Blob!="undefined"?Blob:null;var vp={isBrowser:!0,classes:{URLSearchParams:Sp,FormData:xp,Blob:Ep},protocols:["http","https","file","blob","url","data"]};var tu={};td(tu,{hasBrowserEnv:()=>eu,hasStandardBrowserEnv:()=>aS,hasStandardBrowserWebWorkerEnv:()=>lS,navigator:()=>Zc,origin:()=>cS});var eu=typeof window!="undefined"&&typeof document!="undefined",Zc=typeof navigator=="object"&&navigator||void 0,aS=eu&&(!Zc||["ReactNative","NativeScript","NS"].indexOf(Zc.product)<0),lS=(()=>typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),cS=eu&&window.location.href||"http://localhost";var z={...tu,...vp};function ru(r,e){return fr(r,new z.classes.URLSearchParams,Object.assign({visitor:function(t,n,s,o){return z.isNode&&w.isBuffer(t)?(this.append(n,t.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},e))}function uS(r){return w.matchAll(/\w+|\[(\w*)]/g,r).map(e=>e[0]==="[]"?"":e[1]||e[0])}function fS(r){let e={},t=Object.keys(r),n,s=t.length,o;for(n=0;n<s;n++)o=t[n],e[o]=r[o];return e}function dS(r){function e(t,n,s,o){let i=t[o++];if(i==="__proto__")return!0;let a=Number.isFinite(+i),c=o>=t.length;return i=!i&&w.isArray(s)?s.length:i,c?(w.hasOwnProp(s,i)?s[i]=[s[i],n]:s[i]=n,!a):((!s[i]||!w.isObject(s[i]))&&(s[i]=[]),e(t,n,s[i],o)&&w.isArray(s[i])&&(s[i]=fS(s[i])),!a)}if(w.isFormData(r)&&w.isFunction(r.entries)){let t={};return w.forEachEntry(r,(n,s)=>{e(uS(n),s,t,0)}),t}return null}var Ca=dS;function hS(r,e,t){if(w.isString(r))try{return(e||JSON.parse)(r),w.trim(r)}catch(n){if(n.name!=="SyntaxError")throw n}return(t||JSON.stringify)(r)}var nu={transitional:Aa,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){let n=t.getContentType()||"",s=n.indexOf("application/json")>-1,o=w.isObject(e);if(o&&w.isHTMLForm(e)&&(e=new FormData(e)),w.isFormData(e))return s?JSON.stringify(Ca(e)):e;if(w.isArrayBuffer(e)||w.isBuffer(e)||w.isStream(e)||w.isFile(e)||w.isBlob(e)||w.isReadableStream(e))return e;if(w.isArrayBufferView(e))return e.buffer;if(w.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return ru(e,this.formSerializer).toString();if((a=w.isFileList(e))||n.indexOf("multipart/form-data")>-1){let c=this.env&&this.env.FormData;return fr(a?{"files[]":e}:e,c&&new c,this.formSerializer)}}return o||s?(t.setContentType("application/json",!1),hS(e)):e}],transformResponse:[function(e){let t=this.transitional||nu.transitional,n=t&&t.forcedJSONParsing,s=this.responseType==="json";if(w.isResponse(e)||w.isReadableStream(e))return e;if(e&&w.isString(e)&&(n&&!this.responseType||s)){let i=!(t&&t.silentJSONParsing)&&s;try{return JSON.parse(e)}catch(a){if(i)throw a.name==="SyntaxError"?$.from(a,$.ERR_BAD_RESPONSE,this,null,this.response):a}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:z.classes.FormData,Blob:z.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};w.forEach(["delete","get","head","post","put","patch"],r=>{nu.headers[r]={}});var hs=nu;var pS=w.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ap=r=>{let e={},t,n,s;return r&&r.split(`
`).forEach(function(i){s=i.indexOf(":"),t=i.substring(0,s).trim().toLowerCase(),n=i.substring(s+1).trim(),!(!t||e[t]&&pS[t])&&(t==="set-cookie"?e[t]?e[t].push(n):e[t]=[n]:e[t]=e[t]?e[t]+", "+n:n)}),e};var Cp=Symbol("internals");function ko(r){return r&&String(r).trim().toLowerCase()}function Pa(r){return r===!1||r==null?r:w.isArray(r)?r.map(Pa):String(r)}function mS(r){let e=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g,n;for(;n=t.exec(r);)e[n[1]]=n[2];return e}var gS=r=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(r.trim());function su(r,e,t,n,s){if(w.isFunction(n))return n.call(this,e,t);if(s&&(e=t),!!w.isString(e)){if(w.isString(n))return e.indexOf(n)!==-1;if(w.isRegExp(n))return n.test(e)}}function yS(r){return r.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}function bS(r,e){let t=w.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(r,n+t,{value:function(s,o,i){return this[n].call(this,e,s,o,i)},configurable:!0})})}var ps=class{constructor(e){e&&this.set(e)}set(e,t,n){let s=this;function o(a,c,l){let f=ko(c);if(!f)throw new Error("header name must be a non-empty string");let u=w.findKey(s,f);(!u||s[u]===void 0||l===!0||l===void 0&&s[u]!==!1)&&(s[u||c]=Pa(a))}let i=(a,c)=>w.forEach(a,(l,f)=>o(l,f,c));if(w.isPlainObject(e)||e instanceof this.constructor)i(e,t);else if(w.isString(e)&&(e=e.trim())&&!gS(e))i(Ap(e),t);else if(w.isHeaders(e))for(let[a,c]of e.entries())o(c,a,n);else e!=null&&o(t,e,n);return this}get(e,t){if(e=ko(e),e){let n=w.findKey(this,e);if(n){let s=this[n];if(!t)return s;if(t===!0)return mS(s);if(w.isFunction(t))return t.call(this,s,n);if(w.isRegExp(t))return t.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=ko(e),e){let n=w.findKey(this,e);return!!(n&&this[n]!==void 0&&(!t||su(this,this[n],n,t)))}return!1}delete(e,t){let n=this,s=!1;function o(i){if(i=ko(i),i){let a=w.findKey(n,i);a&&(!t||su(n,n[a],a,t))&&(delete n[a],s=!0)}}return w.isArray(e)?e.forEach(o):o(e),s}clear(e){let t=Object.keys(this),n=t.length,s=!1;for(;n--;){let o=t[n];(!e||su(this,this[o],o,e,!0))&&(delete this[o],s=!0)}return s}normalize(e){let t=this,n={};return w.forEach(this,(s,o)=>{let i=w.findKey(n,o);if(i){t[i]=Pa(s),delete t[o];return}let a=e?yS(o):String(o).trim();a!==o&&delete t[o],t[a]=Pa(s),n[a]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return w.forEach(this,(n,s)=>{n!=null&&n!==!1&&(t[s]=e&&w.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let n=new this(e);return t.forEach(s=>n.set(s)),n}static accessor(e){let n=(this[Cp]=this[Cp]={accessors:{}}).accessors,s=this.prototype;function o(i){let a=ko(i);n[a]||(bS(s,i),n[a]=!0)}return w.isArray(e)?e.forEach(o):o(e),this}};ps.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);w.reduceDescriptors(ps.prototype,({value:r},e)=>{let t=e[0].toUpperCase()+e.slice(1);return{get:()=>r,set(n){this[t]=n}}});w.freezeMethods(ps);var ie=ps;function Oo(r,e){let t=this||hs,n=e||t,s=ie.from(n.headers),o=n.data;return w.forEach(r,function(a){o=a.call(t,o,s.normalize(),e?e.status:void 0)}),s.normalize(),o}function Mo(r){return!!(r&&r.__CANCEL__)}function Pp(r,e,t){$.call(this,r==null?"canceled":r,$.ERR_CANCELED,e,t),this.name="CanceledError"}w.inherits(Pp,$,{__CANCEL__:!0});var dt=Pp;function Lo(r,e,t){let n=t.config.validateStatus;!t.status||!n||n(t.status)?r(t):e(new $("Request failed with status code "+t.status,[$.ERR_BAD_REQUEST,$.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}function ou(r){let e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(r);return e&&e[1]||""}function wS(r,e){r=r||10;let t=new Array(r),n=new Array(r),s=0,o=0,i;return e=e!==void 0?e:1e3,function(c){let l=Date.now(),f=n[o];i||(i=l),t[s]=c,n[s]=l;let u=o,d=0;for(;u!==s;)d+=t[u++],u=u%r;if(s=(s+1)%r,s===o&&(o=(o+1)%r),l-i<e)return;let h=f&&l-f;return h?Math.round(d*1e3/h):void 0}}var Rp=wS;function _S(r,e){let t=0,n=1e3/e,s,o,i=(l,f=Date.now())=>{t=f,s=null,o&&(clearTimeout(o),o=null),r.apply(null,l)};return[(...l)=>{let f=Date.now(),u=f-t;u>=n?i(l,f):(s=l,o||(o=setTimeout(()=>{o=null,i(s)},n-u)))},()=>s&&i(s)]}var Tp=_S;var ms=(r,e,t=3)=>{let n=0,s=Rp(50,250);return Tp(o=>{let i=o.loaded,a=o.lengthComputable?o.total:void 0,c=i-n,l=s(c),f=i<=a;n=i;let u={loaded:i,total:a,progress:a?i/a:void 0,bytes:c,rate:l||void 0,estimated:l&&a&&f?(a-i)/l:void 0,event:o,lengthComputable:a!=null,[e?"download":"upload"]:!0};r(u)},t)},iu=(r,e)=>{let t=r!=null;return[n=>e[0]({lengthComputable:t,total:r,loaded:n}),e[1]]},au=r=>(...e)=>w.asap(()=>r(...e));var Ip=z.hasStandardBrowserEnv?((r,e)=>t=>(t=new URL(t,z.origin),r.protocol===t.protocol&&r.host===t.host&&(e||r.port===t.port)))(new URL(z.origin),z.navigator&&/(msie|trident)/i.test(z.navigator.userAgent)):()=>!0;var kp=z.hasStandardBrowserEnv?{write(r,e,t,n,s,o){let i=[r+"="+encodeURIComponent(e)];w.isNumber(t)&&i.push("expires="+new Date(t).toGMTString()),w.isString(n)&&i.push("path="+n),w.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(r){let e=document.cookie.match(new RegExp("(^|;\\s*)("+r+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(r){this.write(r,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function lu(r){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(r)}function cu(r,e){return e?r.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):r}function No(r,e,t){let n=!lu(e);return r&&n||t==!1?cu(r,e):e}var Op=r=>r instanceof ie?{...r}:r;function Je(r,e){e=e||{};let t={};function n(l,f,u,d){return w.isPlainObject(l)&&w.isPlainObject(f)?w.merge.call({caseless:d},l,f):w.isPlainObject(f)?w.merge({},f):w.isArray(f)?f.slice():f}function s(l,f,u,d){if(w.isUndefined(f)){if(!w.isUndefined(l))return n(void 0,l,u,d)}else return n(l,f,u,d)}function o(l,f){if(!w.isUndefined(f))return n(void 0,f)}function i(l,f){if(w.isUndefined(f)){if(!w.isUndefined(l))return n(void 0,l)}else return n(void 0,f)}function a(l,f,u){if(u in e)return n(l,f);if(u in r)return n(void 0,l)}let c={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(l,f,u)=>s(Op(l),Op(f),u,!0)};return w.forEach(Object.keys(Object.assign({},r,e)),function(f){let u=c[f]||s,d=u(r[f],e[f],f);w.isUndefined(d)&&u!==a||(t[f]=d)}),t}var Ra=r=>{let e=Je({},r),{data:t,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:a}=e;e.headers=i=ie.from(i),e.url=Io(No(e.baseURL,e.url,e.allowAbsoluteUrls),r.params,r.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let c;if(w.isFormData(t)){if(z.hasStandardBrowserEnv||z.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((c=i.getContentType())!==!1){let[l,...f]=c?c.split(";").map(u=>u.trim()).filter(Boolean):[];i.setContentType([l||"multipart/form-data",...f].join("; "))}}if(z.hasStandardBrowserEnv&&(n&&w.isFunction(n)&&(n=n(e)),n||n!==!1&&Ip(e.url))){let l=s&&o&&kp.read(o);l&&i.set(s,l)}return e};var SS=typeof XMLHttpRequest!="undefined",Mp=SS&&function(r){return new Promise(function(t,n){let s=Ra(r),o=s.data,i=ie.from(s.headers).normalize(),{responseType:a,onUploadProgress:c,onDownloadProgress:l}=s,f,u,d,h,p;function y(){h&&h(),p&&p(),s.cancelToken&&s.cancelToken.unsubscribe(f),s.signal&&s.signal.removeEventListener("abort",f)}let g=new XMLHttpRequest;g.open(s.method.toUpperCase(),s.url,!0),g.timeout=s.timeout;function S(){if(!g)return;let A=ie.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),x={data:!a||a==="text"||a==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:A,config:r,request:g};Lo(function(k){t(k),y()},function(k){n(k),y()},x),g=null}"onloadend"in g?g.onloadend=S:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(S)},g.onabort=function(){g&&(n(new $("Request aborted",$.ECONNABORTED,r,g)),g=null)},g.onerror=function(){n(new $("Network Error",$.ERR_NETWORK,r,g)),g=null},g.ontimeout=function(){let P=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded",x=s.transitional||Aa;s.timeoutErrorMessage&&(P=s.timeoutErrorMessage),n(new $(P,x.clarifyTimeoutError?$.ETIMEDOUT:$.ECONNABORTED,r,g)),g=null},o===void 0&&i.setContentType(null),"setRequestHeader"in g&&w.forEach(i.toJSON(),function(P,x){g.setRequestHeader(x,P)}),w.isUndefined(s.withCredentials)||(g.withCredentials=!!s.withCredentials),a&&a!=="json"&&(g.responseType=s.responseType),l&&([d,p]=ms(l,!0),g.addEventListener("progress",d)),c&&g.upload&&([u,h]=ms(c),g.upload.addEventListener("progress",u),g.upload.addEventListener("loadend",h)),(s.cancelToken||s.signal)&&(f=A=>{g&&(n(!A||A.type?new dt(null,r,g):A),g.abort(),g=null)},s.cancelToken&&s.cancelToken.subscribe(f),s.signal&&(s.signal.aborted?f():s.signal.addEventListener("abort",f)));let _=ou(s.url);if(_&&z.protocols.indexOf(_)===-1){n(new $("Unsupported protocol "+_+":",$.ERR_BAD_REQUEST,r));return}g.send(o||null)})};var xS=(r,e)=>{let{length:t}=r=r?r.filter(Boolean):[];if(e||t){let n=new AbortController,s,o=function(l){if(!s){s=!0,a();let f=l instanceof Error?l:this.reason;n.abort(f instanceof $?f:new dt(f instanceof Error?f.message:f))}},i=e&&setTimeout(()=>{i=null,o(new $(`timeout ${e} of ms exceeded`,$.ETIMEDOUT))},e),a=()=>{r&&(i&&clearTimeout(i),i=null,r.forEach(l=>{l.unsubscribe?l.unsubscribe(o):l.removeEventListener("abort",o)}),r=null)};r.forEach(l=>l.addEventListener("abort",o));let{signal:c}=n;return c.unsubscribe=()=>w.asap(a),c}},Lp=xS;var ES=function*(r,e){let t=r.byteLength;if(!e||t<e){yield r;return}let n=0,s;for(;n<t;)s=n+e,yield r.slice(n,s),n=s},vS=async function*(r,e){for await(let t of AS(r))yield*ES(t,e)},AS=async function*(r){if(r[Symbol.asyncIterator]){yield*r;return}let e=r.getReader();try{for(;;){let{done:t,value:n}=await e.read();if(t)break;yield n}}finally{await e.cancel()}},uu=(r,e,t,n)=>{let s=vS(r,e),o=0,i,a=c=>{i||(i=!0,n&&n(c))};return new ReadableStream({async pull(c){try{let{done:l,value:f}=await s.next();if(l){a(),c.close();return}let u=f.byteLength;if(t){let d=o+=u;t(d)}c.enqueue(new Uint8Array(f))}catch(l){throw a(l),l}},cancel(c){return a(c),s.return()}},{highWaterMark:2})};var Ia=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Dp=Ia&&typeof ReadableStream=="function",CS=Ia&&(typeof TextEncoder=="function"?(r=>e=>r.encode(e))(new TextEncoder):async r=>new Uint8Array(await new Response(r).arrayBuffer())),Bp=(r,...e)=>{try{return!!r(...e)}catch(t){return!1}},PS=Dp&&Bp(()=>{let r=!1,e=new Request(z.origin,{body:new ReadableStream,method:"POST",get duplex(){return r=!0,"half"}}).headers.has("Content-Type");return r&&!e}),Np=64*1024,fu=Dp&&Bp(()=>w.isReadableStream(new Response("").body)),Ta={stream:fu&&(r=>r.body)};Ia&&(r=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Ta[e]&&(Ta[e]=w.isFunction(r[e])?t=>t[e]():(t,n)=>{throw new $(`Response type '${e}' is not supported`,$.ERR_NOT_SUPPORT,n)})})})(new Response);var RS=async r=>{if(r==null)return 0;if(w.isBlob(r))return r.size;if(w.isSpecCompliantForm(r))return(await new Request(z.origin,{method:"POST",body:r}).arrayBuffer()).byteLength;if(w.isArrayBufferView(r)||w.isArrayBuffer(r))return r.byteLength;if(w.isURLSearchParams(r)&&(r=r+""),w.isString(r))return(await CS(r)).byteLength},TS=async(r,e)=>{let t=w.toFiniteNumber(r.getContentLength());return t==null?RS(e):t},Fp=Ia&&(async r=>{let{url:e,method:t,data:n,signal:s,cancelToken:o,timeout:i,onDownloadProgress:a,onUploadProgress:c,responseType:l,headers:f,withCredentials:u="same-origin",fetchOptions:d}=Ra(r);l=l?(l+"").toLowerCase():"text";let h=Lp([s,o&&o.toAbortSignal()],i),p,y=h&&h.unsubscribe&&(()=>{h.unsubscribe()}),g;try{if(c&&PS&&t!=="get"&&t!=="head"&&(g=await TS(f,n))!==0){let x=new Request(e,{method:"POST",body:n,duplex:"half"}),R;if(w.isFormData(n)&&(R=x.headers.get("content-type"))&&f.setContentType(R),x.body){let[k,M]=iu(g,ms(au(c)));n=uu(x.body,Np,k,M)}}w.isString(u)||(u=u?"include":"omit");let S="credentials"in Request.prototype;p=new Request(e,{...d,signal:h,method:t.toUpperCase(),headers:f.normalize().toJSON(),body:n,duplex:"half",credentials:S?u:void 0});let _=await fetch(p),A=fu&&(l==="stream"||l==="response");if(fu&&(a||A&&y)){let x={};["status","statusText","headers"].forEach(q=>{x[q]=_[q]});let R=w.toFiniteNumber(_.headers.get("content-length")),[k,M]=a&&iu(R,ms(au(a),!0))||[];_=new Response(uu(_.body,Np,k,()=>{M&&M(),y&&y()}),x)}l=l||"text";let P=await Ta[w.findKey(Ta,l)||"text"](_,r);return!A&&y&&y(),await new Promise((x,R)=>{Lo(x,R,{data:P,headers:ie.from(_.headers),status:_.status,statusText:_.statusText,config:r,request:p})})}catch(S){throw y&&y(),S&&S.name==="TypeError"&&/fetch/i.test(S.message)?Object.assign(new $("Network Error",$.ERR_NETWORK,r,p),{cause:S.cause||S}):$.from(S,S&&S.code,r,p)}});var du={http:Ea,xhr:Mp,fetch:Fp};w.forEach(du,(r,e)=>{if(r){try{Object.defineProperty(r,"name",{value:e})}catch(t){}Object.defineProperty(r,"adapterName",{value:e})}});var Up=r=>`- ${r}`,IS=r=>w.isFunction(r)||r===null||r===!1,ka={getAdapter:r=>{r=w.isArray(r)?r:[r];let{length:e}=r,t,n,s={};for(let o=0;o<e;o++){t=r[o];let i;if(n=t,!IS(t)&&(n=du[(i=String(t)).toLowerCase()],n===void 0))throw new $(`Unknown adapter '${i}'`);if(n)break;s[i||"#"+o]=n}if(!n){let o=Object.entries(s).map(([a,c])=>`adapter ${a} `+(c===!1?"is not supported by the environment":"is not available in the build")),i=e?o.length>1?`since :
`+o.map(Up).join(`
`):" "+Up(o[0]):"as no adapter specified";throw new $("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:du};function hu(r){if(r.cancelToken&&r.cancelToken.throwIfRequested(),r.signal&&r.signal.aborted)throw new dt(null,r)}function Oa(r){return hu(r),r.headers=ie.from(r.headers),r.data=Oo.call(r,r.transformRequest),["post","put","patch"].indexOf(r.method)!==-1&&r.headers.setContentType("application/x-www-form-urlencoded",!1),ka.getAdapter(r.adapter||hs.adapter)(r).then(function(n){return hu(r),n.data=Oo.call(r,r.transformResponse,n),n.headers=ie.from(n.headers),n},function(n){return Mo(n)||(hu(r),n&&n.response&&(n.response.data=Oo.call(r,r.transformResponse,n.response),n.response.headers=ie.from(n.response.headers))),Promise.reject(n)})}var Ma="1.8.3";var La={};["object","boolean","number","function","string","symbol"].forEach((r,e)=>{La[r]=function(n){return typeof n===r||"a"+(e<1?"n ":" ")+r}});var qp={};La.transitional=function(e,t,n){function s(o,i){return"[Axios v"+Ma+"] Transitional option '"+o+"'"+i+(n?". "+n:"")}return(o,i,a)=>{if(e===!1)throw new $(s(i," has been removed"+(t?" in "+t:"")),$.ERR_DEPRECATED);return t&&!qp[i]&&(qp[i]=!0,console.warn(s(i," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(o,i,a):!0}};La.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};function kS(r,e,t){if(typeof r!="object")throw new $("options must be an object",$.ERR_BAD_OPTION_VALUE);let n=Object.keys(r),s=n.length;for(;s-- >0;){let o=n[s],i=e[o];if(i){let a=r[o],c=a===void 0||i(a,o,r);if(c!==!0)throw new $("option "+o+" must be "+c,$.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new $("Unknown option "+o,$.ERR_BAD_OPTION)}}var Do={assertOptions:kS,validators:La};var ht=Do.validators,gs=class{constructor(e){this.defaults=e,this.interceptors={request:new Qc,response:new Qc}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;let o=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch(i){}}throw n}}_request(e,t){typeof e=="string"?(t=t||{},t.url=e):t=e||{},t=Je(this.defaults,t);let{transitional:n,paramsSerializer:s,headers:o}=t;n!==void 0&&Do.assertOptions(n,{silentJSONParsing:ht.transitional(ht.boolean),forcedJSONParsing:ht.transitional(ht.boolean),clarifyTimeoutError:ht.transitional(ht.boolean)},!1),s!=null&&(w.isFunction(s)?t.paramsSerializer={serialize:s}:Do.assertOptions(s,{encode:ht.function,serialize:ht.function},!0)),t.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Do.assertOptions(t,{baseUrl:ht.spelling("baseURL"),withXsrfToken:ht.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let i=o&&w.merge(o.common,o[t.method]);o&&w.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),t.headers=ie.concat(i,o);let a=[],c=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(t)===!1||(c=c&&y.synchronous,a.unshift(y.fulfilled,y.rejected))});let l=[];this.interceptors.response.forEach(function(y){l.push(y.fulfilled,y.rejected)});let f,u=0,d;if(!c){let p=[Oa.bind(this),void 0];for(p.unshift.apply(p,a),p.push.apply(p,l),d=p.length,f=Promise.resolve(t);u<d;)f=f.then(p[u++],p[u++]);return f}d=a.length;let h=t;for(u=0;u<d;){let p=a[u++],y=a[u++];try{h=p(h)}catch(g){y.call(this,g);break}}try{f=Oa.call(this,h)}catch(p){return Promise.reject(p)}for(u=0,d=l.length;u<d;)f=f.then(l[u++],l[u++]);return f}getUri(e){e=Je(this.defaults,e);let t=No(e.baseURL,e.url,e.allowAbsoluteUrls);return Io(t,e.params,e.paramsSerializer)}};w.forEach(["delete","get","head","options"],function(e){gs.prototype[e]=function(t,n){return this.request(Je(n||{},{method:e,url:t,data:(n||{}).data}))}});w.forEach(["post","put","patch"],function(e){function t(n){return function(o,i,a){return this.request(Je(a||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}gs.prototype[e]=t(),gs.prototype[e+"Form"]=t(!0)});var Bo=gs;var Fo=class{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(o){t=o});let n=this;this.promise.then(s=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](s);n._listeners=null}),this.promise.then=s=>{let o,i=new Promise(a=>{n.subscribe(a),o=a}).then(s);return i.cancel=function(){n.unsubscribe(o)},i},e(function(o,i,a){n.reason||(n.reason=new dt(o,i,a),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);t!==-1&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=n=>{e.abort(n)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new Fo(function(s){e=s}),cancel:e}}},$p=Fo;function pu(r){return function(t){return r.apply(null,t)}}function mu(r){return w.isObject(r)&&r.isAxiosError===!0}var gu={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(gu).forEach(([r,e])=>{gu[e]=r});var Hp=gu;function jp(r){let e=new Bo(r),t=Po(Bo.prototype.request,e);return w.extend(t,Bo.prototype,e,{allOwnKeys:!0}),w.extend(t,e,null,{allOwnKeys:!0}),t.create=function(s){return jp(Je(r,s))},t}var ee=jp(hs);ee.Axios=Bo;ee.CanceledError=dt;ee.CancelToken=$p;ee.isCancel=Mo;ee.VERSION=Ma;ee.toFormData=fr;ee.AxiosError=$;ee.Cancel=ee.CanceledError;ee.all=function(e){return Promise.all(e)};ee.spread=pu;ee.isAxiosError=mu;ee.mergeConfig=Je;ee.AxiosHeaders=ie;ee.formToJSON=r=>Ca(w.isHTMLForm(r)?new FormData(r):r);ee.getAdapter=ka.getAdapter;ee.HttpStatusCode=Hp;ee.default=ee;var ys=ee;var{Axios:QL,AxiosError:ZL,CanceledError:eN,isCancel:tN,CancelToken:rN,VERSION:nN,all:sN,Cancel:oN,isAxiosError:iN,spread:aN,toFormData:lN,AxiosHeaders:cN,HttpStatusCode:uN,formToJSON:fN,getAdapter:dN,mergeConfig:hN}=ys;var OS=r=>async function*(e,t,n){let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,baseURL:c,model:l,...f}=i;if(!a)throw new Error(m("API key is required"));if(!l)throw new Error(m("Model is required"));let u=await Promise.all(e.map(S=>MS(S,n))),d={model:l,messages:u,stream:!0,...f},p=(await ys.post(c,d,{headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"},adapter:"fetch",responseType:"stream",withCredentials:!1,signal:t.signal})).data.pipeThrough(new TextDecoderStream).getReader(),y=!0,g=!1;for(;y;){let{done:S,value:_}=await p.read();if(S){y=!1;break}let A=_.split(`
`);for(let P of A){if(P.includes("data: [DONE]")){y=!1;break}let x=P.replace(/^data: /,"").trim();if(x){let R=JSON.parse(x);if(R.choices&&R.choices[0].delta){let k=R.choices[0].delta,M=k.reasoning_content;if(M)yield(g?"":(g=!0,lt))+M.replace(/\n/g,`
> `);else{let q=g?(g=!1,ct):"";k.content&&(yield q+k.content)}}}}}},MS=async(r,e)=>{let t=r.embeds?await Promise.all(r.embeds.map(n=>rr(n,e))):[];return t.length===0?{role:r.role,content:r.content}:(r.content.trim()&&t.push({type:"text",text:r.content}),{role:r.role,content:t})},Wp=["moonshot-v1-128k","moonshot-v1-32k","moonshot-v1-32k-vision-preview","kimi-thinking-preview","kimi-k2-0711-preview","kimi-latest-32k"],Vp={name:"Kimi",defaultOptions:{apiKey:"",baseURL:"https://api.moonshot.cn/v1/chat/completions",model:Wp[0],parameters:{}},sendRequestFunc:OS,models:Wp,websiteToObtainKey:"https://www.moonshot.cn",capabilities:["Text Generation","Image Vision","Reasoning"]};var he=typeof globalThis!="undefined"&&globalThis||typeof self!="undefined"&&self||typeof global!="undefined"&&global||{},xe={searchParams:"URLSearchParams"in he,iterable:"Symbol"in he&&"iterator"in Symbol,blob:"FileReader"in he&&"Blob"in he&&function(){try{return new Blob,!0}catch(r){return!1}}(),formData:"FormData"in he,arrayBuffer:"ArrayBuffer"in he};function LS(r){return r&&DataView.prototype.isPrototypeOf(r)}xe.arrayBuffer&&(Kp=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],Jp=ArrayBuffer.isView||function(r){return r&&Kp.indexOf(Object.prototype.toString.call(r))>-1});var Kp,Jp;function bs(r){if(typeof r!="string"&&(r=String(r)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(r)||r==="")throw new TypeError('Invalid character in header field name: "'+r+'"');return r.toLowerCase()}function bu(r){return typeof r!="string"&&(r=String(r)),r}function wu(r){var e={next:function(){var t=r.shift();return{done:t===void 0,value:t}}};return xe.iterable&&(e[Symbol.iterator]=function(){return e}),e}function ae(r){this.map={},r instanceof ae?r.forEach(function(e,t){this.append(t,e)},this):Array.isArray(r)?r.forEach(function(e){if(e.length!=2)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+e.length);this.append(e[0],e[1])},this):r&&Object.getOwnPropertyNames(r).forEach(function(e){this.append(e,r[e])},this)}ae.prototype.append=function(r,e){r=bs(r),e=bu(e);var t=this.map[r];this.map[r]=t?t+", "+e:e};ae.prototype.delete=function(r){delete this.map[bs(r)]};ae.prototype.get=function(r){return r=bs(r),this.has(r)?this.map[r]:null};ae.prototype.has=function(r){return this.map.hasOwnProperty(bs(r))};ae.prototype.set=function(r,e){this.map[bs(r)]=bu(e)};ae.prototype.forEach=function(r,e){for(var t in this.map)this.map.hasOwnProperty(t)&&r.call(e,this.map[t],t,this)};ae.prototype.keys=function(){var r=[];return this.forEach(function(e,t){r.push(t)}),wu(r)};ae.prototype.values=function(){var r=[];return this.forEach(function(e){r.push(e)}),wu(r)};ae.prototype.entries=function(){var r=[];return this.forEach(function(e,t){r.push([t,e])}),wu(r)};xe.iterable&&(ae.prototype[Symbol.iterator]=ae.prototype.entries);function yu(r){if(!r._noBody){if(r.bodyUsed)return Promise.reject(new TypeError("Already read"));r.bodyUsed=!0}}function zp(r){return new Promise(function(e,t){r.onload=function(){e(r.result)},r.onerror=function(){t(r.error)}})}function NS(r){var e=new FileReader,t=zp(e);return e.readAsArrayBuffer(r),t}function DS(r){var e=new FileReader,t=zp(e),n=/charset=([A-Za-z0-9_-]+)/.exec(r.type),s=n?n[1]:"utf-8";return e.readAsText(r,s),t}function BS(r){for(var e=new Uint8Array(r),t=new Array(e.length),n=0;n<e.length;n++)t[n]=String.fromCharCode(e[n]);return t.join("")}function Gp(r){if(r.slice)return r.slice(0);var e=new Uint8Array(r.byteLength);return e.set(new Uint8Array(r)),e.buffer}function Xp(){return this.bodyUsed=!1,this._initBody=function(r){this.bodyUsed=this.bodyUsed,this._bodyInit=r,r?typeof r=="string"?this._bodyText=r:xe.blob&&Blob.prototype.isPrototypeOf(r)?this._bodyBlob=r:xe.formData&&FormData.prototype.isPrototypeOf(r)?this._bodyFormData=r:xe.searchParams&&URLSearchParams.prototype.isPrototypeOf(r)?this._bodyText=r.toString():xe.arrayBuffer&&xe.blob&&LS(r)?(this._bodyArrayBuffer=Gp(r.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):xe.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(r)||Jp(r))?this._bodyArrayBuffer=Gp(r):this._bodyText=r=Object.prototype.toString.call(r):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||(typeof r=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):xe.searchParams&&URLSearchParams.prototype.isPrototypeOf(r)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},xe.blob&&(this.blob=function(){var r=yu(this);if(r)return r;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer){var r=yu(this);return r||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else{if(xe.blob)return this.blob().then(NS);throw new Error("could not read as ArrayBuffer")}},this.text=function(){var r=yu(this);if(r)return r;if(this._bodyBlob)return DS(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(BS(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},xe.formData&&(this.formData=function(){return this.text().then(qS)}),this.json=function(){return this.text().then(JSON.parse)},this}var FS=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function US(r){var e=r.toUpperCase();return FS.indexOf(e)>-1?e:r}function Jr(r,e){if(!(this instanceof Jr))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');e=e||{};var t=e.body;if(r instanceof Jr){if(r.bodyUsed)throw new TypeError("Already read");this.url=r.url,this.credentials=r.credentials,e.headers||(this.headers=new ae(r.headers)),this.method=r.method,this.mode=r.mode,this.signal=r.signal,!t&&r._bodyInit!=null&&(t=r._bodyInit,r.bodyUsed=!0)}else this.url=String(r);if(this.credentials=e.credentials||this.credentials||"same-origin",(e.headers||!this.headers)&&(this.headers=new ae(e.headers)),this.method=US(e.method||this.method||"GET"),this.mode=e.mode||this.mode||null,this.signal=e.signal||this.signal||function(){if("AbortController"in he){var o=new AbortController;return o.signal}}(),this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&t)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(t),(this.method==="GET"||this.method==="HEAD")&&(e.cache==="no-store"||e.cache==="no-cache")){var n=/([?&])_=[^&]*/;if(n.test(this.url))this.url=this.url.replace(n,"$1_="+new Date().getTime());else{var s=/\?/;this.url+=(s.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}Jr.prototype.clone=function(){return new Jr(this,{body:this._bodyInit})};function qS(r){var e=new FormData;return r.trim().split("&").forEach(function(t){if(t){var n=t.split("="),s=n.shift().replace(/\+/g," "),o=n.join("=").replace(/\+/g," ");e.append(decodeURIComponent(s),decodeURIComponent(o))}}),e}function $S(r){var e=new ae,t=r.replace(/\r?\n[\t ]+/g," ");return t.split("\r").map(function(n){return n.indexOf(`
`)===0?n.substr(1,n.length):n}).forEach(function(n){var s=n.split(":"),o=s.shift().trim();if(o){var i=s.join(":").trim();try{e.append(o,i)}catch(a){console.warn("Response "+a.message)}}}),e}Xp.call(Jr.prototype);function pt(r,e){if(!(this instanceof pt))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(e||(e={}),this.type="default",this.status=e.status===void 0?200:e.status,this.status<200||this.status>599)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=e.statusText===void 0?"":""+e.statusText,this.headers=new ae(e.headers),this.url=e.url||"",this._initBody(r)}Xp.call(pt.prototype);pt.prototype.clone=function(){return new pt(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new ae(this.headers),url:this.url})};pt.error=function(){var r=new pt(null,{status:200,statusText:""});return r.ok=!1,r.status=0,r.type="error",r};var HS=[301,302,303,307,308];pt.redirect=function(r,e){if(HS.indexOf(e)===-1)throw new RangeError("Invalid status code");return new pt(null,{status:e,headers:{location:r}})};var Gr=he.DOMException;try{new Gr}catch(r){Gr=function(e,t){this.message=e,this.name=t;var n=Error(e);this.stack=n.stack},Gr.prototype=Object.create(Error.prototype),Gr.prototype.constructor=Gr}function Yp(r,e){return new Promise(function(t,n){var s=new Jr(r,e);if(s.signal&&s.signal.aborted)return n(new Gr("Aborted","AbortError"));var o=new XMLHttpRequest;function i(){o.abort()}o.onload=function(){var l={statusText:o.statusText,headers:$S(o.getAllResponseHeaders()||"")};s.url.indexOf("file://")===0&&(o.status<200||o.status>599)?l.status=200:l.status=o.status,l.url="responseURL"in o?o.responseURL:l.headers.get("X-Request-URL");var f="response"in o?o.response:o.responseText;setTimeout(function(){t(new pt(f,l))},0)},o.onerror=function(){setTimeout(function(){n(new TypeError("Network request failed"))},0)},o.ontimeout=function(){setTimeout(function(){n(new TypeError("Network request timed out"))},0)},o.onabort=function(){setTimeout(function(){n(new Gr("Aborted","AbortError"))},0)};function a(l){try{return l===""&&he.location.href?he.location.href:l}catch(f){return l}}if(o.open(s.method,a(s.url),!0),s.credentials==="include"?o.withCredentials=!0:s.credentials==="omit"&&(o.withCredentials=!1),"responseType"in o&&(xe.blob?o.responseType="blob":xe.arrayBuffer&&(o.responseType="arraybuffer")),e&&typeof e.headers=="object"&&!(e.headers instanceof ae||he.Headers&&e.headers instanceof he.Headers)){var c=[];Object.getOwnPropertyNames(e.headers).forEach(function(l){c.push(bs(l)),o.setRequestHeader(l,bu(e.headers[l]))}),s.headers.forEach(function(l,f){c.indexOf(f)===-1&&o.setRequestHeader(f,l)})}else s.headers.forEach(function(l,f){o.setRequestHeader(f,l)});s.signal&&(s.signal.addEventListener("abort",i),o.onreadystatechange=function(){o.readyState===4&&s.signal.removeEventListener("abort",i)}),o.send(typeof s._bodyInit=="undefined"?null:s._bodyInit)})}Yp.polyfill=!0;he.fetch||(he.fetch=Yp,he.Headers=ae,he.Request=Jr,he.Response=pt);var Zp="11434",em=`http://127.0.0.1:${Zp}`,jS="0.5.16",WS=Object.defineProperty,VS=(r,e,t)=>e in r?WS(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,_u=(r,e,t)=>(VS(r,typeof e!="symbol"?e+"":e,t),t),Uo=class extends Error{constructor(e,t){super(e),this.error=e,this.status_code=t,this.name="ResponseError",Error.captureStackTrace&&Error.captureStackTrace(this,Uo)}},xu=class{constructor(e,t,n){_u(this,"abortController"),_u(this,"itr"),_u(this,"doneCallback"),this.abortController=e,this.itr=t,this.doneCallback=n}abort(){this.abortController.abort()}async*[Symbol.asyncIterator](){for await(let e of this.itr){if("error"in e)throw new Error(e.error);if(yield e,e.done||e.status==="success"){this.doneCallback();return}}throw new Error("Did not receive done or success response in stream.")}},Eu=async r=>{var n;if(r.ok)return;let e=`Error ${r.status}: ${r.statusText}`,t=null;if((n=r.headers.get("content-type"))!=null&&n.includes("application/json"))try{t=await r.json(),e=t.error||e}catch(s){console.log("Failed to parse error response as JSON")}else try{console.log("Getting text from response"),e=await r.text()||e}catch(s){console.log("Failed to get text from error response")}throw new Uo(e,r.status)};function KS(){var r;if(typeof window!="undefined"&&window.navigator){let e=navigator;return"userAgentData"in e&&((r=e.userAgentData)!=null&&r.platform)?`${e.userAgentData.platform.toLowerCase()} Browser/${navigator.userAgent};`:navigator.platform?`${navigator.platform.toLowerCase()} Browser/${navigator.userAgent};`:`unknown Browser/${navigator.userAgent};`}else if(typeof process!="undefined")return`${process.arch} ${process.platform} Node.js/${process.version}`;return""}function GS(r){if(r instanceof Headers){let e={};return r.forEach((t,n)=>{e[n]=t}),e}else return Array.isArray(r)?Object.fromEntries(r):r||{}}var vu=async(r,e,t={})=>{let n={"Content-Type":"application/json",Accept:"application/json","User-Agent":`ollama-js/${jS} (${KS()})`};t.headers=GS(t.headers);let s=Object.fromEntries(Object.entries(t.headers).filter(([o])=>!Object.keys(n).some(i=>i.toLowerCase()===o.toLowerCase())));return t.headers={...n,...s},r(e,t)},Qp=async(r,e,t)=>{let n=await vu(r,e,{headers:t==null?void 0:t.headers});return await Eu(n),n},ws=async(r,e,t,n)=>{let o=(a=>a!==null&&typeof a=="object"&&!Array.isArray(a))(t)?JSON.stringify(t):t,i=await vu(r,e,{method:"POST",body:o,signal:n==null?void 0:n.signal,headers:n==null?void 0:n.headers});return await Eu(i),i},JS=async(r,e,t,n)=>{let s=await vu(r,e,{method:"DELETE",body:JSON.stringify(t),headers:n==null?void 0:n.headers});return await Eu(s),s},zS=async function*(r){var s;let e=new TextDecoder("utf-8"),t="",n=r.getReader();for(;;){let{done:o,value:i}=await n.read();if(o)break;t+=e.decode(i);let a=t.split(`
`);t=(s=a.pop())!=null?s:"";for(let c of a)try{yield JSON.parse(c)}catch(l){console.warn("invalid json: ",c)}}for(let o of t.split(`
`).filter(i=>i!==""))try{yield JSON.parse(o)}catch(i){console.warn("invalid json: ",o)}},XS=r=>{if(!r)return em;let e=r.includes("://");r.startsWith(":")&&(r=`http://127.0.0.1${r}`,e=!0),e||(r=`http://${r}`);let t=new URL(r),n=t.port;n||(e?n=t.protocol==="https:"?"443":"80":n=Zp);let s="";t.username&&(s=t.username,t.password&&(s+=`:${t.password}`),s+="@");let o=`${t.protocol}//${s}${t.hostname}:${n}${t.pathname}`;return o.endsWith("/")&&(o=o.slice(0,-1)),o},YS=Object.defineProperty,QS=(r,e,t)=>e in r?YS(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,Su=(r,e,t)=>(QS(r,typeof e!="symbol"?e+"":e,t),t),Au=class{constructor(e){var t,n;Su(this,"config"),Su(this,"fetch"),Su(this,"ongoingStreamedRequests",[]),this.config={host:"",headers:e==null?void 0:e.headers},e!=null&&e.proxy||(this.config.host=XS((t=e==null?void 0:e.host)!=null?t:em)),this.fetch=(n=e==null?void 0:e.fetch)!=null?n:fetch}abort(){for(let e of this.ongoingStreamedRequests)e.abort();this.ongoingStreamedRequests.length=0}async processStreamableRequest(e,t){var o;t.stream=(o=t.stream)!=null?o:!1;let n=`${this.config.host}/api/${e}`;if(t.stream){let i=new AbortController,a=await ws(this.fetch,n,t,{signal:i.signal,headers:this.config.headers});if(!a.body)throw new Error("Missing body");let c=zS(a.body),l=new xu(i,c,()=>{let f=this.ongoingStreamedRequests.indexOf(l);f>-1&&this.ongoingStreamedRequests.splice(f,1)});return this.ongoingStreamedRequests.push(l),l}return await(await ws(this.fetch,n,t,{headers:this.config.headers})).json()}async encodeImage(e){if(typeof e!="string"){let t=new Uint8Array(e),n="",s=t.byteLength;for(let o=0;o<s;o++)n+=String.fromCharCode(t[o]);return btoa(n)}return e}async generate(e){return e.images&&(e.images=await Promise.all(e.images.map(this.encodeImage.bind(this)))),this.processStreamableRequest("generate",e)}async chat(e){if(e.messages)for(let t of e.messages)t.images&&(t.images=await Promise.all(t.images.map(this.encodeImage.bind(this))));return this.processStreamableRequest("chat",e)}async create(e){return this.processStreamableRequest("create",{...e})}async pull(e){return this.processStreamableRequest("pull",{name:e.model,stream:e.stream,insecure:e.insecure})}async push(e){return this.processStreamableRequest("push",{name:e.model,stream:e.stream,insecure:e.insecure})}async delete(e){return await JS(this.fetch,`${this.config.host}/api/delete`,{name:e.model},{headers:this.config.headers}),{status:"success"}}async copy(e){return await ws(this.fetch,`${this.config.host}/api/copy`,{...e},{headers:this.config.headers}),{status:"success"}}async list(){return await(await Qp(this.fetch,`${this.config.host}/api/tags`,{headers:this.config.headers})).json()}async show(e){return await(await ws(this.fetch,`${this.config.host}/api/show`,{...e},{headers:this.config.headers})).json()}async embed(e){return await(await ws(this.fetch,`${this.config.host}/api/embed`,{...e},{headers:this.config.headers})).json()}async embeddings(e){return await(await ws(this.fetch,`${this.config.host}/api/embeddings`,{...e},{headers:this.config.headers})).json()}async ps(){return await(await Qp(this.fetch,`${this.config.host}/api/ps`,{headers:this.config.headers})).json()}},xN=new Au;var ZS=r=>async function*(e,t,n){let{parameters:s,...o}=r,i={...o,...s},{baseURL:a,model:c,...l}=i,f=new Au({host:a}),u=await f.chat({model:c,messages:e,stream:!0,...l});for await(let d of u){if(t.signal.aborted){f.abort();break}yield d.message.content}},Na={name:"Ollama",defaultOptions:{apiKey:"",baseURL:"http://127.0.0.1:11434",model:"llama3.1",parameters:{}},sendRequestFunc:ZS,models:[],websiteToObtainKey:"https://ollama.com",capabilities:["Text Generation"]};var ex=r=>async function*(e,t,n){var p,y;let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,baseURL:c,model:l,...f}=i;if(!a)throw new Error(m("API key is required"));let u=await Promise.all(e.map(g=>tx(g,n))),h=await new D({apiKey:a,baseURL:c,dangerouslyAllowBrowser:!0}).chat.completions.create({model:l,messages:u,stream:!0,...f},{signal:t.signal});for await(let g of h){let S=(y=(p=g.choices[0])==null?void 0:p.delta)==null?void 0:y.content;S&&(yield S)}},tx=async(r,e)=>{let t=r.embeds?await Promise.all(r.embeds.map(n=>rr(n,e))):[];return r.content.trim()&&t.push({type:"text",text:r.content}),{role:r.role,content:t}},tm={name:"OpenAI",defaultOptions:{apiKey:"",baseURL:"https://api.openai.com/v1",model:"gpt-4.1",parameters:{}},sendRequestFunc:ex,models:[],websiteToObtainKey:"https://platform.openai.com/api-keys",capabilities:["Text Generation","Image Vision"]};var rm=require("obsidian");var rx=r=>async function*(e,t,n){var S;let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,baseURL:c,model:l,...f}=i;if(!a)throw new Error(m("API key is required"));if(!l)throw new Error(m("Model is required"));let u=await Promise.all(e.map(_=>sx(_,n))),d={model:l,messages:u,stream:!0,...f},p=(S=(await fetch(c,{method:"POST",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"},body:JSON.stringify(d),signal:t.signal})).body)==null?void 0:S.getReader();if(!p)throw new Error("Response body is not readable");let y=new TextDecoder,g="";try{for(;;){let{done:_,value:A}=await p.read();if(_)break;for(g+=y.decode(A,{stream:!0});;){let P=g.indexOf(`
`);if(P===-1)break;let x=g.slice(0,P).trim();if(g=g.slice(P+1),x.startsWith("data: ")){let R=x.slice(6);if(R==="[DONE]")break;try{let M=JSON.parse(R).choices[0].delta.content;M&&(yield M)}catch(k){}}}}}finally{p.cancel()}},nx=async(r,e)=>{let t=Dr(r.link);if(["image/png","image/jpeg","image/gif","image/webp"].includes(t)){let n=await e(r),s=Br(n);return{type:"image_url",image_url:{url:`data:${t};base64,${s}`}}}else if(t==="application/pdf"){let n=await e(r),s=Br(n);return{type:"file",file:{filename:r.link,file_data:`data:${t};base64,${s}`}}}else throw new Error(m("Only PNG, JPEG, GIF, WebP, and PDF files are supported."))},sx=async(r,e)=>{let t=r.embeds?await Promise.all(r.embeds.map(n=>nx(n,e))):[];return r.content.trim()&&t.push({type:"text",text:r.content}),{role:r.role,content:t}},Da={name:"OpenRouter",defaultOptions:{apiKey:"",baseURL:"https://openrouter.ai/api/v1/chat/completions",model:"",parameters:{}},sendRequestFunc:rx,models:[],websiteToObtainKey:"https://openrouter.ai",capabilities:["Text Generation","Image Vision","PDF Vision"]},nm=async()=>(await(0,rm.requestUrl)({url:"https://openrouter.ai/api/v1/models",headers:{"Content-Type":"application/json"}})).json.data.map(t=>t.id);var zr=require("obsidian");var ox=async(r,e)=>{if(!r||!e)throw new Error("Invalid API key secret");let t={grant_type:"client_credentials",client_id:r,client_secret:e},n=new URLSearchParams(t).toString(),o=(await(0,zr.requestUrl)(`https://aip.baidubce.com/oauth/2.0/token?${n}`)).json;return{accessToken:o.access_token,exp:Date.now()+o.expires_in,apiKey:r,apiSecret:e}},ix=async(r,e,t)=>{let n=Date.now();if(r&&r.apiKey===e&&r.apiSecret===t&&r.exp>n+3*60*1e3)return{isValid:!0,token:r};let s=await ox(e,t);return console.debug("create new token",s),{isValid:!1,token:s}},ax=(r,e)=>{let t=e.endsWith(`
`)||e.endsWith("\r"),n=e.split(/\r\n|[\n\r]/g);return n.length===1&&!t?(r.push(n[0]),[]):(r.length>0&&(n=[r.join("")+n[0],...n.slice(1)],r=[]),t||(r=[n.pop()||""]),n)},lx=r=>async function*(e,t,n){let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,apiSecret:c,baseURL:l,model:f,token:u,...d}=i;if(!a)throw new Error(m("API key is required"));if(!c)throw new Error(m("API secret is required"));if(!f)throw new Error(m("Model is required"));let{token:h}=await ix(u,a,c);if(r.token=h,zr.Platform.isDesktopApp){let p={messages:e,stream:!0,...d},y=await ys.post(l+`/${f}?access_token=${h.accessToken}`,p,{headers:{"Content-Type":"application/json"},adapter:"fetch",responseType:"stream",withCredentials:!1,signal:t.signal}),g=[],S=new TextDecoder("utf-8");for await(let _ of y.data){let A=S.decode(Buffer.from(_)),P=ax(g,A);for(let x of P)if(x.startsWith("data: ")){let R=x.slice(6),M=JSON.parse(R).result;M&&(yield M)}}}else{let p={messages:e,stream:!1,...d};new zr.Notice(m("This is a non-streaming request, please wait..."),5*1e3);let y=await(0,zr.requestUrl)({url:l+`/${f}?access_token=${h.accessToken}`,method:"POST",body:JSON.stringify(p),headers:{"Content-Type":"application/json"}});console.debug("response",y.json),yield y.json.result}},sm=["ernie-4.0-8k-latest","ernie-4.0-turbo-8k","ernie-3.5-128k","ernie_speed","ernie-speed-128k","gemma_7b_it","yi_34b_chat","mixtral_8x7b_instruct","llama_2_70b"],om={name:"QianFan",defaultOptions:{apiKey:"",apiSecret:"",baseURL:"https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat",model:sm[0],parameters:{}},sendRequestFunc:lx,models:sm,websiteToObtainKey:"https://qianfan.cloud.baidu.com",capabilities:["Text Generation"]};var cx=r=>async function*(e,t,n){var p,y;let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,baseURL:c,model:l,...f}=i;if(!a)throw new Error(m("API key is required"));let u=await Promise.all(e.map(g=>ux(g,n))),h=await new D({apiKey:a,baseURL:c,dangerouslyAllowBrowser:!0}).chat.completions.create({model:l,messages:u,stream:!0,...f},{signal:t.signal});for await(let g of h){let S=(y=(p=g.choices[0])==null?void 0:p.delta)==null?void 0:y.content;S&&(yield S)}},ux=async(r,e)=>{let t=r.embeds?await Promise.all(r.embeds.map(n=>rr(n,e))):[];return r.content.trim()&&t.push({type:"text",text:r.content}),{role:r.role,content:t}},im=["qwen-turbo","qwen-plus","qwen-max","qwen-vl-max"],am={name:"Qwen",defaultOptions:{apiKey:"",baseURL:"https://dashscope.aliyuncs.com/compatible-mode/v1",model:im[0],parameters:{}},sendRequestFunc:cx,models:im,websiteToObtainKey:"https://dashscope.console.aliyun.com",capabilities:["Text Generation","Image Vision"]};var lm=require("obsidian");var fx=r=>async function*(e,t,n){var y;let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,baseURL:c,model:l,...f}=i;if(!a)throw new Error(m("API key is required"));let u=await Promise.all(e.map(g=>dx(g,n))),h=await new D({apiKey:a,baseURL:c,dangerouslyAllowBrowser:!0}).chat.completions.create({model:l,messages:u,stream:!0,...f},{signal:t.signal}),p=!1;for await(let g of h){let S=(y=g.choices[0])==null?void 0:y.delta,_=S==null?void 0:S.reasoning_content;if(_)yield(p?"":(p=!0,lt))+_.replace(/\n/g,`
> `);else{let A=p?(p=!1,ct):"";S!=null&&S.content&&(yield A+(S==null?void 0:S.content))}}},dx=async(r,e)=>{let t=r.embeds?await Promise.all(r.embeds.map(n=>rr(n,e))):[];return r.content.trim()&&t.push({type:"text",text:r.content}),{role:r.role,content:t}},Ba={name:"SiliconFlow",defaultOptions:{apiKey:"",baseURL:"https://api.siliconflow.cn/v1",model:"",parameters:{}},sendRequestFunc:fx,models:[],websiteToObtainKey:"https://siliconflow.cn",capabilities:["Text Generation","Image Vision","Reasoning"]},cm=async r=>(await(0,lm.requestUrl)({url:"https://api.siliconflow.cn/v1/models?type=text&sub_type=chat",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}})).json.data.map(n=>n.id);var _s=crypto,Fa=r=>r instanceof CryptoKey;var Ot=new TextEncoder,qo=new TextDecoder,KN=2**32;function um(...r){let e=r.reduce((s,{length:o})=>s+o,0),t=new Uint8Array(e),n=0;for(let s of r)t.set(s,n),n+=s.length;return t}var hx=r=>{let e=r;typeof e=="string"&&(e=Ot.encode(e));let t=32768,n=[];for(let s=0;s<e.length;s+=t)n.push(String.fromCharCode.apply(null,e.subarray(s,s+t)));return btoa(n.join(""))},Ua=r=>hx(r).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),px=r=>{let e=atob(r),t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t},qa=r=>{let e=r;e instanceof Uint8Array&&(e=qo.decode(e)),e=e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return px(e)}catch(t){throw new TypeError("The input to be decoded is not correctly encoded.")}};var pe=class extends Error{constructor(e,t){var n;super(e,t),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,(n=Error.captureStackTrace)==null||n.call(Error,this,this.constructor)}};pe.code="ERR_JOSE_GENERIC";var Cu=class extends pe{constructor(e,t,n="unspecified",s="unspecified"){super(e,{cause:{claim:n,reason:s,payload:t}}),this.code="ERR_JWT_CLAIM_VALIDATION_FAILED",this.claim=n,this.reason=s,this.payload=t}};Cu.code="ERR_JWT_CLAIM_VALIDATION_FAILED";var Pu=class extends pe{constructor(e,t,n="unspecified",s="unspecified"){super(e,{cause:{claim:n,reason:s,payload:t}}),this.code="ERR_JWT_EXPIRED",this.claim=n,this.reason=s,this.payload=t}};Pu.code="ERR_JWT_EXPIRED";var Ru=class extends pe{constructor(){super(...arguments),this.code="ERR_JOSE_ALG_NOT_ALLOWED"}};Ru.code="ERR_JOSE_ALG_NOT_ALLOWED";var Fe=class extends pe{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}};Fe.code="ERR_JOSE_NOT_SUPPORTED";var Tu=class extends pe{constructor(e="decryption operation failed",t){super(e,t),this.code="ERR_JWE_DECRYPTION_FAILED"}};Tu.code="ERR_JWE_DECRYPTION_FAILED";var Iu=class extends pe{constructor(){super(...arguments),this.code="ERR_JWE_INVALID"}};Iu.code="ERR_JWE_INVALID";var Mt=class extends pe{constructor(){super(...arguments),this.code="ERR_JWS_INVALID"}};Mt.code="ERR_JWS_INVALID";var $o=class extends pe{constructor(){super(...arguments),this.code="ERR_JWT_INVALID"}};$o.code="ERR_JWT_INVALID";var ku=class extends pe{constructor(){super(...arguments),this.code="ERR_JWK_INVALID"}};ku.code="ERR_JWK_INVALID";var Ou=class extends pe{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}};Ou.code="ERR_JWKS_INVALID";var Mu=class extends pe{constructor(e="no applicable key found in the JSON Web Key Set",t){super(e,t),this.code="ERR_JWKS_NO_MATCHING_KEY"}};Mu.code="ERR_JWKS_NO_MATCHING_KEY";var Lu=class extends pe{constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS"}};Lu.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";var Nu=class extends pe{constructor(e="request timed out",t){super(e,t),this.code="ERR_JWKS_TIMEOUT"}};Nu.code="ERR_JWKS_TIMEOUT";var Du=class extends pe{constructor(e="signature verification failed",t){super(e,t),this.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED"}};Du.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";function Lt(r,e="algorithm.name"){return new TypeError(`CryptoKey does not support this operation, its ${e} must be ${r}`)}function $a(r,e){return r.name===e}function Bu(r){return parseInt(r.name.slice(4),10)}function mx(r){switch(r){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw new Error("unreachable")}}function gx(r,e){if(e.length&&!e.some(t=>r.usages.includes(t))){let t="CryptoKey does not support this operation, its usages must include ";if(e.length>2){let n=e.pop();t+=`one of ${e.join(", ")}, or ${n}.`}else e.length===2?t+=`one of ${e[0]} or ${e[1]}.`:t+=`${e[0]}.`;throw new TypeError(t)}}function fm(r,e,...t){switch(e){case"HS256":case"HS384":case"HS512":{if(!$a(r.algorithm,"HMAC"))throw Lt("HMAC");let n=parseInt(e.slice(2),10);if(Bu(r.algorithm.hash)!==n)throw Lt(`SHA-${n}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!$a(r.algorithm,"RSASSA-PKCS1-v1_5"))throw Lt("RSASSA-PKCS1-v1_5");let n=parseInt(e.slice(2),10);if(Bu(r.algorithm.hash)!==n)throw Lt(`SHA-${n}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!$a(r.algorithm,"RSA-PSS"))throw Lt("RSA-PSS");let n=parseInt(e.slice(2),10);if(Bu(r.algorithm.hash)!==n)throw Lt(`SHA-${n}`,"algorithm.hash");break}case"EdDSA":{if(r.algorithm.name!=="Ed25519"&&r.algorithm.name!=="Ed448")throw Lt("Ed25519 or Ed448");break}case"ES256":case"ES384":case"ES512":{if(!$a(r.algorithm,"ECDSA"))throw Lt("ECDSA");let n=mx(e);if(r.algorithm.namedCurve!==n)throw Lt(n,"algorithm.namedCurve");break}default:throw new TypeError("CryptoKey does not support this operation")}gx(r,t)}function dm(r,e,...t){var n;if(t=t.filter(Boolean),t.length>2){let s=t.pop();r+=`one of type ${t.join(", ")}, or ${s}.`}else t.length===2?r+=`one of type ${t[0]} or ${t[1]}.`:r+=`of type ${t[0]}.`;return e==null?r+=` Received ${e}`:typeof e=="function"&&e.name?r+=` Received function ${e.name}`:typeof e=="object"&&e!=null&&(n=e.constructor)!=null&&n.name&&(r+=` Received an instance of ${e.constructor.name}`),r}var Fu=(r,...e)=>dm("Key must be ",r,...e);function Uu(r,e,...t){return dm(`Key for the ${r} algorithm must be `,e,...t)}var qu=r=>Fa(r)?!0:(r==null?void 0:r[Symbol.toStringTag])==="KeyObject",Ss=["CryptoKey"];var yx=(...r)=>{let e=r.filter(Boolean);if(e.length===0||e.length===1)return!0;let t;for(let n of e){let s=Object.keys(n);if(!t||t.size===0){t=new Set(s);continue}for(let o of s){if(t.has(o))return!1;t.add(o)}}return!0},hm=yx;function bx(r){return typeof r=="object"&&r!==null}function Ho(r){if(!bx(r)||Object.prototype.toString.call(r)!=="[object Object]")return!1;if(Object.getPrototypeOf(r)===null)return!0;let e=r;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(r)===e}var pm=(r,e)=>{if(r.startsWith("RS")||r.startsWith("PS")){let{modulusLength:t}=e.algorithm;if(typeof t!="number"||t<2048)throw new TypeError(`${r} requires key modulusLength to be 2048 bits or larger`)}};function Xr(r){return Ho(r)&&typeof r.kty=="string"}function mm(r){return r.kty!=="oct"&&typeof r.d=="string"}function gm(r){return r.kty!=="oct"&&typeof r.d=="undefined"}function ym(r){return Xr(r)&&r.kty==="oct"&&typeof r.k=="string"}function _x(r){let e,t;switch(r.kty){case"RSA":{switch(r.alg){case"PS256":case"PS384":case"PS512":e={name:"RSA-PSS",hash:`SHA-${r.alg.slice(-3)}`},t=r.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":e={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${r.alg.slice(-3)}`},t=r.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":e={name:"RSA-OAEP",hash:`SHA-${parseInt(r.alg.slice(-3),10)||1}`},t=r.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new Fe('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break}case"EC":{switch(r.alg){case"ES256":e={name:"ECDSA",namedCurve:"P-256"},t=r.d?["sign"]:["verify"];break;case"ES384":e={name:"ECDSA",namedCurve:"P-384"},t=r.d?["sign"]:["verify"];break;case"ES512":e={name:"ECDSA",namedCurve:"P-521"},t=r.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":e={name:"ECDH",namedCurve:r.crv},t=r.d?["deriveBits"]:[];break;default:throw new Fe('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break}case"OKP":{switch(r.alg){case"EdDSA":e={name:r.crv},t=r.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":e={name:r.crv},t=r.d?["deriveBits"]:[];break;default:throw new Fe('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break}default:throw new Fe('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:e,keyUsages:t}}var Sx=async r=>{var o,i;if(!r.alg)throw new TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:e,keyUsages:t}=_x(r),n=[e,(o=r.ext)!=null?o:!1,(i=r.key_ops)!=null?i:t],s={...r};return delete s.alg,delete s.use,_s.subtle.importKey("jwk",s,...n)},bm=Sx;var wm=r=>qa(r),xs,Es,_m=r=>(r==null?void 0:r[Symbol.toStringTag])==="KeyObject",Ha=async(r,e,t,n,s=!1)=>{let o=r.get(e);if(o!=null&&o[n])return o[n];let i=await bm({...t,alg:n});return s&&Object.freeze(e),o?o[n]=i:r.set(e,{[n]:i}),i},xx=(r,e)=>{if(_m(r)){let t=r.export({format:"jwk"});return delete t.d,delete t.dp,delete t.dq,delete t.p,delete t.q,delete t.qi,t.k?wm(t.k):(Es||(Es=new WeakMap),Ha(Es,r,t,e))}return Xr(r)?r.k?qa(r.k):(Es||(Es=new WeakMap),Ha(Es,r,r,e,!0)):r},Ex=(r,e)=>{if(_m(r)){let t=r.export({format:"jwk"});return t.k?wm(t.k):(xs||(xs=new WeakMap),Ha(xs,r,t,e))}return Xr(r)?r.k?qa(r.k):(xs||(xs=new WeakMap),Ha(xs,r,r,e,!0)):r},$u={normalizePublicKey:xx,normalizePrivateKey:Ex};var vs=r=>r==null?void 0:r[Symbol.toStringTag],Hu=(r,e,t)=>{var n,s;if(e.use!==void 0&&e.use!=="sig")throw new TypeError("Invalid key for this operation, when present its use must be sig");if(e.key_ops!==void 0&&((s=(n=e.key_ops).includes)==null?void 0:s.call(n,t))!==!0)throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${t}`);if(e.alg!==void 0&&e.alg!==r)throw new TypeError(`Invalid key for this operation, when present its alg must be ${r}`);return!0},vx=(r,e,t,n)=>{if(!(e instanceof Uint8Array)){if(n&&Xr(e)){if(ym(e)&&Hu(r,e,t))return;throw new TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!qu(e))throw new TypeError(Uu(r,e,...Ss,"Uint8Array",n?"JSON Web Key":null));if(e.type!=="secret")throw new TypeError(`${vs(e)} instances for symmetric algorithms must be of type "secret"`)}},Ax=(r,e,t,n)=>{if(n&&Xr(e))switch(t){case"sign":if(mm(e)&&Hu(r,e,t))return;throw new TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(gm(e)&&Hu(r,e,t))return;throw new TypeError("JSON Web Key for this operation be a public JWK")}if(!qu(e))throw new TypeError(Uu(r,e,...Ss,n?"JSON Web Key":null));if(e.type==="secret")throw new TypeError(`${vs(e)} instances for asymmetric algorithms must not be of type "secret"`);if(t==="sign"&&e.type==="public")throw new TypeError(`${vs(e)} instances for asymmetric algorithm signing must be of type "private"`);if(t==="decrypt"&&e.type==="public")throw new TypeError(`${vs(e)} instances for asymmetric algorithm decryption must be of type "private"`);if(e.algorithm&&t==="verify"&&e.type==="private")throw new TypeError(`${vs(e)} instances for asymmetric algorithm verifying must be of type "public"`);if(e.algorithm&&t==="encrypt"&&e.type==="private")throw new TypeError(`${vs(e)} instances for asymmetric algorithm encryption must be of type "public"`)};function Sm(r,e,t,n){e.startsWith("HS")||e==="dir"||e.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(e)?vx(e,t,n,r):Ax(e,t,n,r)}var p3=Sm.bind(void 0,!1),xm=Sm.bind(void 0,!0);function Cx(r,e,t,n,s){if(s.crit!==void 0&&(n==null?void 0:n.crit)===void 0)throw new r('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||n.crit===void 0)return new Set;if(!Array.isArray(n.crit)||n.crit.length===0||n.crit.some(i=>typeof i!="string"||i.length===0))throw new r('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');let o;t!==void 0?o=new Map([...Object.entries(t),...e.entries()]):o=e;for(let i of n.crit){if(!o.has(i))throw new Fe(`Extension Header Parameter "${i}" is not recognized`);if(s[i]===void 0)throw new r(`Extension Header Parameter "${i}" is missing`);if(o.get(i)&&n[i]===void 0)throw new r(`Extension Header Parameter "${i}" MUST be integrity protected`)}return new Set(n.crit)}var Em=Cx;function ju(r,e){let t=`SHA-${r.slice(-3)}`;switch(r){case"HS256":case"HS384":case"HS512":return{hash:t,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:t,name:"RSA-PSS",saltLength:r.slice(-3)>>3};case"RS256":case"RS384":case"RS512":return{hash:t,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:t,name:"ECDSA",namedCurve:e.namedCurve};case"EdDSA":return{name:e.name};default:throw new Fe(`alg ${r} is not supported either by JOSE or your javascript runtime`)}}async function Wu(r,e,t){if(t==="sign"&&(e=await $u.normalizePrivateKey(e,r)),t==="verify"&&(e=await $u.normalizePublicKey(e,r)),Fa(e))return fm(e,r,t),e;if(e instanceof Uint8Array){if(!r.startsWith("HS"))throw new TypeError(Fu(e,...Ss));return _s.subtle.importKey("raw",e,{hash:`SHA-${r.slice(-3)}`,name:"HMAC"},!1,[t])}throw new TypeError(Fu(e,...Ss,"Uint8Array","JSON Web Key"))}var dr=r=>Math.floor(r.getTime()/1e3);var Px=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,ja=r=>{let e=Px.exec(r);if(!e||e[4]&&e[1])throw new TypeError("Invalid time period format");let t=parseFloat(e[2]),n=e[3].toLowerCase(),s;switch(n){case"sec":case"secs":case"second":case"seconds":case"s":s=Math.round(t);break;case"minute":case"minutes":case"min":case"mins":case"m":s=Math.round(t*60);break;case"hour":case"hours":case"hr":case"hrs":case"h":s=Math.round(t*3600);break;case"day":case"days":case"d":s=Math.round(t*86400);break;case"week":case"weeks":case"w":s=Math.round(t*604800);break;default:s=Math.round(t*31557600);break}return e[1]==="-"||e[4]==="ago"?-s:s};var Rx=async(r,e,t)=>{let n=await Wu(r,e,"sign");pm(r,n);let s=await _s.subtle.sign(ju(r,n.algorithm),n,t);return new Uint8Array(s)},vm=Rx;var Wa=class{constructor(e){if(!(e instanceof Uint8Array))throw new TypeError("payload must be an instance of Uint8Array");this._payload=e}setProtectedHeader(e){if(this._protectedHeader)throw new TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw new TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}async sign(e,t){if(!this._protectedHeader&&!this._unprotectedHeader)throw new Mt("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!hm(this._protectedHeader,this._unprotectedHeader))throw new Mt("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let n={...this._protectedHeader,...this._unprotectedHeader},s=Em(Mt,new Map([["b64",!0]]),t==null?void 0:t.crit,this._protectedHeader,n),o=!0;if(s.has("b64")&&(o=this._protectedHeader.b64,typeof o!="boolean"))throw new Mt('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:i}=n;if(typeof i!="string"||!i)throw new Mt('JWS "alg" (Algorithm) Header Parameter missing or invalid');xm(i,e,"sign");let a=this._payload;o&&(a=Ot.encode(Ua(a)));let c;this._protectedHeader?c=Ot.encode(Ua(JSON.stringify(this._protectedHeader))):c=Ot.encode("");let l=um(c,Ot.encode("."),a),f=await vm(i,e,l),u={signature:Ua(f),payload:""};return o&&(u.payload=qo.decode(a)),this._unprotectedHeader&&(u.header=this._unprotectedHeader),this._protectedHeader&&(u.protected=qo.decode(c)),u}};var Va=class{constructor(e){this._flattened=new Wa(e)}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}async sign(e,t){let n=await this._flattened.sign(e,t);if(n.payload===void 0)throw new TypeError("use the flattened module for creating JWS with b64: false");return`${n.protected}.${n.payload}.${n.signature}`}};function Yr(r,e){if(!Number.isFinite(e))throw new TypeError(`Invalid ${r} input`);return e}var Ka=class{constructor(e={}){if(!Ho(e))throw new TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return typeof e=="number"?this._payload={...this._payload,nbf:Yr("setNotBefore",e)}:e instanceof Date?this._payload={...this._payload,nbf:Yr("setNotBefore",dr(e))}:this._payload={...this._payload,nbf:dr(new Date)+ja(e)},this}setExpirationTime(e){return typeof e=="number"?this._payload={...this._payload,exp:Yr("setExpirationTime",e)}:e instanceof Date?this._payload={...this._payload,exp:Yr("setExpirationTime",dr(e))}:this._payload={...this._payload,exp:dr(new Date)+ja(e)},this}setIssuedAt(e){return typeof e=="undefined"?this._payload={...this._payload,iat:dr(new Date)}:e instanceof Date?this._payload={...this._payload,iat:Yr("setIssuedAt",dr(e))}:typeof e=="string"?this._payload={...this._payload,iat:Yr("setIssuedAt",dr(new Date)+ja(e))}:this._payload={...this._payload,iat:Yr("setIssuedAt",e)},this}};var jo=class extends Ka{setProtectedHeader(e){return this._protectedHeader=e,this}async sign(e,t){var s;let n=new Va(Ot.encode(JSON.stringify(this._payload)));if(n.setProtectedHeader(this._protectedHeader),Array.isArray((s=this._protectedHeader)==null?void 0:s.crit)&&this._protectedHeader.crit.includes("b64")&&this._protectedHeader.b64===!1)throw new $o("JWTs MUST NOT use unencoded payload");return n.sign(e,t)}};var Ix=r=>async function*(e,t,n){var _,A;let{parameters:s,...o}=r,i={...o,...s},{apiKey:a,baseURL:c,model:l,token:f,tokenExpireInMinutes:u,enableWebSearch:d,...h}=i;if(!a)throw new Error(m("API key is required"));console.debug("zhipu options",{baseURL:c,apiKey:a,model:l,currentToken:f,tokenExpireInMinutes:u,enableWebSearch:d});let{token:p}=await Ox(f,a,u);r.token=p;let y=new D({apiKey:p.id,baseURL:c,dangerouslyAllowBrowser:!0}),g=d?[{type:"web_search",web_search:{enable:!0}}]:[],S=await y.chat.completions.create({model:l,messages:e,stream:!0,tools:g,...h},{signal:t.signal});for await(let P of S){let x=(A=(_=P.choices[0])==null?void 0:_.delta)==null?void 0:A.content;x&&(yield x)}},kx=async(r,e)=>{let[t,n]=r.split(".");if(!t||!n)throw new Error('Invalid API key secret, must be in the format "apiKey.apiSecret"');let s=Date.now(),o={api_key:t,exp:s+e*60*1e3,timestamp:s};return{id:await new jo({...o}).setProtectedHeader({alg:"HS256",sign_type:"SIGN"}).sign(new TextEncoder().encode(n)),exp:o.exp,apiKeySecret:r}},Ox=async(r,e,t)=>{let n=Date.now();if(r&&r.apiKeySecret===e&&r.exp>n+3*60*1e3)return{isValid:!0,token:r};let s=await kx(e,t);return console.debug("create new token",s),{isValid:!1,token:s}},Am=["glm-4-plus","glm-4-air","glm-4-airx","glm-4-long","glm-4-flash","glm-4-flashx"],Cm={name:"Zhipu",defaultOptions:{apiKey:"",baseURL:"https://open.bigmodel.cn/api/paas/v4/",model:Am[0],tokenExpireInMinutes:60*24,enableWebSearch:!1,parameters:{}},sendRequestFunc:Ix,models:Am,websiteToObtainKey:"https://open.bigmodel.cn/",capabilities:["Text Generation","Web Search"]};var Ue="Tars",mt={editorStatus:{isTextInserting:!1},providers:[],systemTags:["System","\u7CFB\u7EDF"],newChatTags:["NewChat","\u65B0\u5BF9\u8BDD"],userTags:["User","\u6211"],roleEmojis:{assistant:"\u2728",system:"\u{1F527}",newChat:"\u{1F680}",user:"\u{1F4AC}"},promptTemplates:[],enableInternalLink:!0,enableInternalLinkForAssistantMsg:!1,answerDelayInMilliseconds:2e3,confirmRegenerate:!0,enableTagSuggest:!0,tagSuggestMaxLineLength:20,enableExportToJSONL:!1,enableReplaceTag:!1,enableDefaultSystemMsg:!1,defaultSystemMsg:"",enableStreamLog:!1},Wo=[tm,rh,fa,Fh,Uh,op,ba,Vp,Na,Da,om,am,Ba,Cm];var Rm=(r,e)=>async function*(t,n,s,o){let i=Date.now(),a=[];try{for await(let c of r(t,n,s,o)){let l=Date.now();a.push({text:c,time:l-i}),yield c}}finally{let c=t[t.length-1],l=/[<>:"/\\|?*\u0000-\u001F\u007F-\u009F]/g,f=c.content.slice(0,20).replace(l,"").trim()||"untitled",u=(0,Pm.normalizePath)(`${Ue}/${Mx(new Date)}-${f}.json`),d={lastMsg:t[t.length-1].content.trim(),createdAt:new Date().toISOString(),texts:a};await e(u,JSON.stringify(d,null,2)),console.debug("Response logged to:",u)}},Mx=r=>{let e=a=>a.toString().padStart(2,"0"),t=r.getFullYear().toString().slice(-2),n=e(r.getMonth()+1),s=e(r.getDate()),o=e(r.getHours()),i=e(r.getMinutes());return`${t}${n}${s}-${o}${i}`};var Tm=["callout"],ze=async(r,e)=>{var p,y;let t=r.workspace.getActiveFile();if(!t)throw new Error("No active file");let n=r.metadataCache,s=r.vault,o=await s.cachedRead(t),i=t.path,a=n.getFileCache(t);if(!a)throw new Error(m("Waiting for metadata to be ready. Please try again."));let c=((p=a.sections)==null?void 0:p.filter(g=>Tm.includes(g.type)))||[],l=(a.tags||[]).filter(g=>!c.some(S=>S.position.start.offset<=g.position.start.offset&&g.position.end.offset<=S.position.end.offset)),f={newChatTags:e.newChatTags,userTags:e.userTags,assistantTags:e.providers.map(g=>g.tag),systemTags:e.systemTags,enableInternalLink:e.enableInternalLink,enableInternalLinkForAssistantMsg:e.enableInternalLinkForAssistantMsg,enableDefaultSystemMsg:e.enableDefaultSystemMsg,defaultSystemMsg:e.defaultSystemMsg,enableStreamLog:e.enableStreamLog},u=async(g,S)=>{let _=await r.fileManager.getAvailablePathForAttachment(g);await s.createBinary(_,S)},d=async g=>{let{path:S,subpath:_}=(0,Nt.parseLinktext)(g.link);console.debug("resolveEmbed path",S,"subpath",_);let A=n.getFirstLinkpathDest(S,i);if(A===null)throw new Error("LinkText broken: "+g.link.substring(0,20));return await s.readBinary(A)},h=async(g,S)=>{await s.create(g,S)};return{appMeta:n,vault:s,fileText:o,filePath:i,tags:l,sections:((y=a.sections)==null?void 0:y.filter(g=>!Tm.includes(g.type)))||[],links:a.links,embeds:a.embeds,options:f,saveAttachment:u,resolveEmbed:d,createPlainText:h}},Lx=async(r,e)=>{let{appMeta:t,vault:n,filePath:s}=r,{path:o,subpath:i}=(0,Nt.parseLinktext)(e);console.debug("path",o,"subpath",i);let a=t.getFirstLinkpathDest(o,s);if(a===null)throw new Error("LinkText broken: "+e.substring(0,20));let c=t.getFileCache(a);if(c===null)throw new Error(`No metadata found: ${o} ${i}`);let l=await n.cachedRead(a);if(i){let f=(0,Nt.resolveSubpath)(c,i);if(f===null)throw new Error(`no subpath data found: ${i}`);return l.substring(f.start.offset,f.end?f.end.offset:void 0)}else return l},Im=(r,e,t)=>{let{tags:n,sections:s,options:{userTags:o,assistantTags:i,systemTags:a}}=r,c=n.filter(u=>e<=u.position.start.offset&&u.position.end.offset<=t).map(u=>{let d=u.tag.slice(1).split("/")[0].toLowerCase(),h=o.some(p=>p.toLowerCase()===d)?"user":i.some(p=>p.toLowerCase()===d)?"assistant":a.some(p=>p.toLowerCase()===d)?"system":null;return h!=null?{tag:u.tag,role:h,lowerCaseTag:d,tagRange:[u.position.start.offset,u.position.end.offset],tagLine:u.position.start.line}:null}).filter(u=>u!==null);console.debug("roleMappedTags",c);let f=c.map((u,d)=>[u.tagRange[0],c[d+1]?c[d+1].tagRange[0]-1:t]).map((u,d)=>({...c[d],contentRange:[c[d].tagRange[1]+2,c[d+1]?c[d+1].tagRange[0]-1:t],sections:s.filter(h=>h.position.end.offset<=u[1]&&(h.position.start.offset<u[0]?h.position.end.offset>u[0]:!0)),line:[c[d].tagLine,c[d+1]?c[d+1].tagLine-1:1/0]}));return console.debug("taggedBlocks",f),f},Nx=async(r,e,t,n)=>{let{fileText:s,links:o=[],embeds:i=[],options:{enableInternalLink:a,enableInternalLinkForAssistantMsg:c}}=r,l=e.position.start.offset<=t[0]?t[0]:e.position.start.offset,f=e.position.end.offset,u=o.map(_=>({ref:_,type:"link"})),d=i.map(_=>({ref:_,type:"embed"})),p=[...u,...d].sort((_,A)=>_.ref.position.start.offset-A.ref.position.start.offset).filter(_=>l<=_.ref.position.start.offset&&_.ref.position.end.offset<=f),y=n==="assistant"?c:a,S=(await Promise.all(p.map(async _=>{let A=_.ref;return _.type==="link"?{referCache:A,text:y?await Lx(r,A.link):A.original}:{referCache:A,text:""}}))).reduce((_,{referCache:A,text:P})=>({endOffset:A.position.end.offset,text:_.text+s.slice(_.endOffset,A.position.start.offset)+P}),{endOffset:l,text:""});return console.debug("accumulatedText",S),{text:S.text+s.slice(S.endOffset,f),range:[l,f]}},km=async(r,e)=>{let t=await Promise.all(e.sections.map(s=>Nx(r,s,e.contentRange,e.role)));return console.debug("textRanges",t),t.map(s=>s.text).join(`

`).trim()},Dx=(r,e)=>{var t;return(t=r.embeds)==null?void 0:t.filter(n=>e[0]<=n.position.start.offset&&n.position.end.offset<=e[1])},Bx=async(r,e,t)=>{let{tags:n,options:{newChatTags:s}}=r,o=n.findLast(l=>s.some(f=>l.tag.slice(1).split("/")[0].toLowerCase()===f.toLowerCase())&&e<=l.position.start.offset&&l.position.end.offset<=t),i=o?o.position.end.offset:e,a=Im(r,i,t);return await Promise.all(a.map(async l=>{let f={...l,content:await km(r,l)},u=Dx(r,l.contentRange);return u&&u.length>0&&(f.embeds=u),f}))},Fx=(0,Nt.debounce)(r=>{r.isTextInserting=!1},1e3,!0),Ux=(r,e,t,n)=>{t.isTextInserting=!0;let s=r.getCursor("to");n!==null&&(n.line!==s.line||n.ch!==s.ch)&&(s=n);let o=r.getLine(s.line);o.length>s.ch&&(s={line:s.line,ch:o.length});let i=e.split(`
`),a={line:s.line+i.length-1,ch:i.length===1?s.ch+e.length:i[i.length-1].length};return r.replaceRange(e,s),r.setCursor(a),Fx(t),a},Om=async r=>{let{tags:e,options:{newChatTags:t}}=r,o=[0,...e.filter(c=>t.some(l=>c.tag.slice(1).split("/")[0].toLowerCase()===l.toLowerCase())).flatMap(c=>[c.position.start.offset-1,c.position.end.offset+1]),1/0],i=o.filter((c,l)=>l%2===0).map((c,l)=>({startOffset:c,endOffset:o[l*2+1]}));return console.debug("ranges",i),(await Promise.all(i.map(async c=>{let l=Im(r,c.startOffset,c.endOffset);return Promise.all(l.map(async u=>({...u,content:await km(r,u)})))}))).filter(c=>c.length>0)},Mm=(r,e)=>{let{tags:t,sections:n,options:{systemTags:s,userTags:o,assistantTags:i}}=r,a=[...s,...o,...i],c=t.filter(y=>a.some(g=>y.tag.slice(1).split("/")[0].toLowerCase()===g.toLowerCase()));console.debug("msgTagsInMeta",c);let l=c.findLastIndex(y=>y.position.start.line<=e);if(l<0)return[-1,-1];console.debug("msgTag",c[l]);let f=c[l].position.end.offset+2,u=l+1,d=u<c.length?c[u].position.start.offset:1/0;console.debug("nextTag",c[u]);let h=n.findLast(y=>y.position.end.offset<=d&&y.position.start.line>=e);if(!h)return[-1,-1];let p=h.position.end.offset;return console.debug("startOff",f,"endOffset",p),[f,p]},qx=r=>`${(r/1e3).toFixed(2)}s`,$x=async(r,e,t)=>r.options.enableStreamLog?(await r.vault.adapter.exists((0,Nt.normalizePath)(Ue))||await r.vault.createFolder(Ue),console.debug("Using stream logging"),Rm(e.sendRequestFunc(t.options),r.createPlainText)):e.sendRequestFunc(t.options),Qr=async(r,e,t,n,s,o,i)=>{var a;try{let c=Wo.find(R=>R.name===t.vendor);if(!c)throw new Error("No vendor found "+t.vendor);let f=(await Bx(r,0,n)).map(R=>R.embeds?{role:R.role,content:R.content,embeds:R.embeds}:{role:R.role,content:R.content});console.debug("messages",f);let u=f.last();if(!u||u.role!=="user"||u.content.trim().length===0)throw new Error(m("Please add a user message first, or wait for the user message to be parsed."));r.options.enableDefaultSystemMsg&&((a=f[0])==null?void 0:a.role)!=="system"&&r.options.defaultSystemMsg&&(f.unshift({role:"system",content:r.options.defaultSystemMsg}),console.debug("Default system message added:",r.options.defaultSystemMsg));let d=f.filter(R=>R.role==="assistant").length+1,h=await $x(r,c,t),p=new Date;s.setGeneratingStatus(d);let y="",g=i.getController(),S=null,_=null;for await(let R of h(f,g,r.resolveEmbed,r.saveAttachment))_==null&&(_=e.getCursor("to")),S=Ux(e,R,o,S),y+=R,s.updateGeneratingProgress(y.length);let A=new Date,P=qx(A.getTime()-p.getTime()),x={round:d,characters:y.length,duration:P,model:t.options.model,vendor:t.vendor,startTime:p,endTime:A};if(s.setSuccessStatus(x),y.length===0)throw new Error(m("No text generated"));if(console.debug("\u2728 "+m("AI generate")+" \u2728 ",y),_){let R=e.getCursor("to"),k=e.getRange(_,R),M=Hx(y);k!==M&&(console.debug("format text with leading breaks"),e.replaceRange(M,_,R))}if(g.signal.aborted)throw new DOMException("Operation was aborted","AbortError")}finally{i.cleanup()}},Hx=r=>{let e=r.split(`
`)[0];return e.startsWith("#")||e.startsWith("```")?` 
`+r:e.startsWith("| ")?` 

`+r:r};var hr=require("obsidian");var gt=r=>`#${r} : `,Vo=r=>`#${r} `,jx=(r,e,t=!1)=>t?`
#${e}`:r==="newChat"?Vo(e):gt(e),Wx=[" ","  "," :"," \uFF1A"],Lm=r=>{let e=Math.max(0,...r.newChatTags.map(n=>n.length)),t=Math.max(0,...r.systemTags.map(n=>n.length),...r.userTags.map(n=>n.length),...r.providers.map(n=>n.tag.length));return 4+(e+1)+(t+2)},Vx=r=>{let e=[],t=/[^\s#:：]+/g,n;for(let s=0;s<3&&(n=t.exec(r),!!n);s++)e.push(n[0]);return e},Kx=(r,e)=>r.line>=1&&e.getLine(r.line-1).trim().length>0,Ga=class extends hr.EditorSuggest{constructor(t,n,s,o,i){super(t);this.app=t,this.settings=n,this.tagLowerCaseMap=s,this.statusBarManager=o,this.requestController=i}onTrigger(t,n,s){if(this.settings.editorStatus.isTextInserting||t.ch<1||t.ch>this.settings.tagSuggestMaxLineLength)return null;let o=n.getLine(t.line);if(o.length>t.ch)return null;let i=Vx(o);if(i.length===0||i.length>=3)return null;let a=this.tagLowerCaseMap.get(i[0].toLowerCase());if(!a)return null;let c;if(i.length===2&&(c=this.tagLowerCaseMap.get(i[1].toLowerCase()),!c||a.role!=="newChat"))return null;let l=c||a,f=i.length===2?i[1]:i[0],u=o.indexOf(f),d=o.slice(u+f.length);if(d){if(l.role==="newChat")return null;if(!Wx.includes(d))return null}let h=Kx(t,n);return{start:{line:t.line,ch:u>0&&o[u-1]==="#"?u-1:u},end:{line:t.line,ch:t.ch},query:JSON.stringify({...l,replacement:jx(l.role,l.tag,h)})}}async getSuggestions(t){return[JSON.parse(t.query)]}renderSuggestion(t,n){if(t.replacement.includes(`
`)){n.createSpan({text:t.replacement});return}let s=this.settings.roleEmojis.assistant;switch(t.role){case"assistant":{n.createSpan({text:`${t.replacement}  ${s} ${m("AI generate")} ${s}  `});break}case"user":{n.createSpan({text:`${t.replacement}  ${this.settings.roleEmojis.user}  `});break}case"system":{n.createSpan({text:`${t.replacement}  ${this.settings.roleEmojis.system}  `});break}case"newChat":{n.createSpan({text:`${t.replacement}  ${this.settings.roleEmojis.newChat}  `});break}default:n.createSpan({text:t.replacement})}}async selectSuggestion(t,n){if(!this.context)return;let s=this.context.editor;if(s.replaceRange(t.replacement,this.context.start,this.context.end),t.role!=="assistant"||t.replacement.includes(`
`)){this.close();return}console.debug("element",t);try{let o=this.settings.providers.find(c=>c.tag===t.tag);if(!o)throw new Error("No provider found "+t.tag);let i=await ze(this.app,this.settings),a=s.posToOffset(this.context.start);console.debug("endOffset",a),await Qr(i,s,o,a,this.statusBarManager,this.settings.editorStatus,this.requestController),new hr.Notice(m("Text generated successfully"))}catch(o){if(console.error("error",o),o.name==="AbortError"){this.statusBarManager.setCancelledStatus(),new hr.Notice(m("Generation cancelled"));return}this.statusBarManager.setErrorStatus(o),new hr.Notice(`\u{1F534} ${hr.Platform.isDesktopApp?m("Click status bar for error details. "):""}${o}`,10*1e3)}this.close()}};var Vu=`  
`,Gx=r=>{let e=r.listSelections();if(e.length===0)throw new Error("No selection");if(e.length>1)throw new Error("Multiple selections");return e[0]},As=(r,e,t)=>{let n=Ku(r,e);return Jx(r,e,n,t)},Ku=(r,e)=>{let t=Gx(e);console.debug("anchor",t.anchor),console.debug("head",t.head);let n=e.getCursor(),{sections:s}=Dm(r);if(!s)return console.debug("No sections"),{from:{line:n.line,ch:0},to:{line:n.line,ch:e.getLine(n.line).length}};let o=e.posToOffset(t.anchor),i=e.posToOffset(t.head),[a,c]=o<i?[o,i]:[i,o],l=s.filter(f=>a<=f.position.end.offset&&f.position.start.offset<=c);return l.length===0?(console.debug("No overlapping sections"),{from:{line:n.line,ch:0},to:{line:n.line,ch:e.getLine(n.line).length}}):{from:{line:l[0].position.start.line,ch:l[0].position.start.col},to:{line:l[l.length-1].position.end.line,ch:l[l.length-1].position.end.col}}},Cs=(r,e)=>{let{from:t,to:n}=e;return r.getRange(t,n).trim().length===0},Ps=(r,e,t)=>{let n=e.line,s="";return e.line>0&&r.getLine(e.line-1).trim().length>0?(s=`
`+t,n+=1):s=t,r.replaceRange(s,e,e),r.setSelection({line:n,ch:r.getLine(n).length}),n},Rs=(r,e,t)=>{let{from:n,to:s}=e,o="",i=s.line;n.line>0&&r.getLine(n.line-1).trim().length>0&&n.ch===0?(o=`
`+t,i+=1):o=t,r.replaceRange(o,n,n),r.setSelection({line:i,ch:r.getLine(i).length})},Ja=(r,e,t,n)=>{let{to:s}=e;t&&(r.replaceRange("#"+n,t.from,t.to),r.setSelection({line:s.line,ch:r.getLine(s.line).length}))},Jx=(r,e,t,n)=>{let{tags:s}=Dm(r);if(!s)return{tagContent:null,role:null,range:t,tagRange:null};let{from:o,to:i}=t,a=s.find(c=>e.posToOffset(o)<=c.position.start.offset&&c.position.end.offset<=e.posToOffset(i));if(a){let c=n.userTags,l=n.providers.map(p=>p.tag),f=n.systemTags,u=n.newChatTags,d=a.tag.slice(1).split("/")[0].toLowerCase();if(u.some(p=>p.toLowerCase()===d)){let p=s.find(y=>a.position.end.offset<=y.position.start.offset&&y.position.end.offset<=e.posToOffset(i));return p?{tagContent:p.tag.slice(1),role:Nm(p,{userTags:c,assistantTags:l,systemTags:f,newChatTags:u}),range:{from:e.offsetToPos(a.position.end.offset+1),to:t.to},tagRange:{from:e.offsetToPos(p.position.start.offset),to:e.offsetToPos(p.position.end.offset)}}:{tagContent:a.tag.slice(1),role:null,range:{from:e.offsetToPos(a.position.end.offset+1),to:t.to},tagRange:{from:e.offsetToPos(a.position.start.offset),to:e.offsetToPos(a.position.end.offset)}}}return{tagContent:a.tag.slice(1),role:Nm(a,{userTags:c,assistantTags:l,systemTags:f,newChatTags:u}),range:t,tagRange:{from:e.offsetToPos(a.position.start.offset),to:e.offsetToPos(a.position.end.offset)}}}else return{tagContent:null,role:null,range:t,tagRange:null}},Nm=(r,e)=>{let{userTags:t,assistantTags:n,systemTags:s,newChatTags:o}=e,i=r.tag.slice(1).split("/")[0].toLowerCase();return t.some(c=>c.toLowerCase()===i)?"user":n.some(c=>c.toLowerCase()===i)?"assistant":s.some(c=>c.toLowerCase()===i)?"system":o.some(c=>c.toLowerCase()===i)?"newChat":null},Dm=r=>{let e=r.workspace.getActiveFile();if(!e)throw new Error("No active file");let t=r.metadataCache.getFileCache(e);if(!t)throw new Error(m("Waiting for metadata to be ready. Please try again."));let n=t.tags;return{sections:t.sections,tags:n}},Ko=(r,e)=>{let t=r.getCursor("to"),n=e.split(`
`),s={line:t.line+n.length-1,ch:n.length===1?t.ch+e.length:n[n.length-1].length};return r.replaceRange(e,t),r.setCursor(s),s.line};var Ju=({id:r,name:e,tag:t},n,s,o,i)=>({id:r,name:e,editorCallback:async(a,c)=>{try{let l=s.providers.find(g=>g.tag===t);if(!l){new Xe.Notice(`Assistant ${t} not found`);return}let f=gt(s.userTags[0]),u=gt(t),{range:d,role:h,tagContent:p,tagRange:y}=As(n,a,s);if(console.debug("asstTagCmd",{range:d,role:h,tagContent:p,tagRange:y}),Cs(a,d)){let g=Ps(a,d.from,u),S=a.posToOffset({line:g,ch:0}),_=await ze(n,s);await Qr(_,a,l,S,o,s.editorStatus,i);return}if(h===null){Rs(a,d,f),a.setCursor({line:d.to.line,ch:a.getLine(d.to.line).length}),Ko(a,Vu+`
`);let g=await zx(a,u,s.answerDelayInMilliseconds),S=a.posToOffset({line:g,ch:0}),_=await ze(n,s);await Qr(_,a,l,S,o,s.editorStatus,i)}else if(h==="assistant")if(s.confirmRegenerate){let g=async()=>{await Bm(n,s,o,i,a,l,d,u)};new Gu(n,g).open()}else await Bm(n,s,o,i,a,l,d,u);else{a.setCursor({line:d.to.line,ch:a.getLine(d.to.line).length});let g=Ko(a,Vu+`
`+u),S=a.posToOffset({line:g,ch:0}),_=await ze(n,s);await Qr(_,a,l,S,o,s.editorStatus,i),new Xe.Notice(m("Text generated successfully"))}}catch(l){if(console.error(l),l.name==="AbortError"){o.setCancelledStatus(),new Xe.Notice(m("Generation cancelled"));return}o.setErrorStatus(l),new Xe.Notice(`\u{1F534} ${Xe.Platform.isDesktopApp?m("Click status bar for error details. "):""}${l}`,10*1e3)}}}),Bm=async(r,e,t,n,s,o,i,a)=>{s.replaceRange(a,i.from,i.to),s.setCursor({line:i.from.line,ch:s.getLine(i.from.line).length});let c=s.posToOffset({line:i.from.line,ch:0}),l=await ze(r,e);await Qr(l,s,o,c,t,e.editorStatus,n)},Gu=class extends Xe.Modal{constructor(t,n){super(t);this.onConfirm=n}onOpen(){let{contentEl:t}=this;t.createEl("h2",{text:m("Regenerate?")}),t.createEl("p",{text:m("This will delete the current response content. You can configure this in settings to not require confirmation.")}),new Xe.Setting(t).addButton(n=>n.setButtonText(m("Yes")).setCta().onClick(async()=>{this.close(),await this.onConfirm()}))}},zx=async(r,e,t)=>{let n=e.length-2;if(n<=0)throw new Error("mark length must be greater than 2");let s=Math.round(t/n),o=Ko(r,e[0]+e[1]);for(let i=2;i<e.length;i++)await new Promise(a=>setTimeout(a,s)),o=Ko(r,e[i]);return o};var Ts=require("obsidian");var za="export-to-jsonl",Go=(r,e)=>({id:za,name:m("Export conversations to JSONL"),callback:async()=>{await Xx(r,e)}}),Xx=async(r,e)=>{var f,u,d;let t=await ze(r,e),n=await Om(t);console.debug("conversations",n);let s=[];try{s=n.map(Yx)}catch(h){console.error("error",h),new Ts.Notice(`\u{1F534}${m("Error")}: ${h}`,10*1e3);return}if(s.length===0){new Ts.Notice(m("No conversation found"));return}let o=s.map(h=>JSON.stringify(h)).join(`
`),i=(u=(f=r.workspace.getActiveFile())==null?void 0:f.parent)==null?void 0:u.path,a=(d=r.workspace.getActiveFile())==null?void 0:d.basename,c=(0,Ts.normalizePath)(`${i}/${a}.jsonl`),l=r.vault.getFileByPath(c);l?await r.vault.process(l,h=>o):await r.vault.create(c,o),new Ts.Notice(m("Exported to the same directory, Obsidian does not display the JSONL format. Please open with another software."),5*1e3)},Yx=r=>{var c,l;let e=r.slice();if(e.length<2)throw new Error("No conversation");let t=null;((c=e.first())==null?void 0:c.role)==="system"&&(t=(l=e.shift())==null?void 0:l.content);let n=[];for(;e.length>0;){let f=e.shift(),u=e.shift();if((f==null?void 0:f.role)!=="user"||(u==null?void 0:u.role)!=="assistant")throw new Error("Invalid message role");if((f==null?void 0:f.content.trim().length)===0||(u==null?void 0:u.content.trim().length)===0)throw new Error("Empty message content");n.push([f.content,u.content])}let s=n.pop();if(!s)throw new Error("No conversation");let[o,i]=s,a={system:t,query:o,response:i,history:n.length>0?n:null};return Object.fromEntries(Object.entries(a).filter(([f,u])=>u!=null))};var Xa=require("obsidian");var zu=({id:r,name:e,tag:t})=>({id:r,name:e,editorCallback:async(n,s)=>{try{let o=n.getCursor(),i=Vo(t);n.replaceRange(i,o),n.setCursor(o.line,o.ch+i.length)}catch(o){console.error(o),new Xa.Notice(`\u{1F534} ${Xa.Platform.isDesktopApp?m("Check the developer console for error details. "):""}${o}`,10*1e3)}}});var Zr=require("obsidian");var Ya="replace-tag",Jo=r=>({id:Ya,name:m("Replace speaker with tag"),callback:async()=>{let e=r.workspace.getActiveFile();if(!e)return;let t=await r.vault.cachedRead(e),n=eE(t);if(n.length<1){new Zr.Notice(m("No speaker found"));return}console.debug("twoMostFrequentSpeakers",n);let s=n.map(o=>({original:o.name,count:o.count,newTag:Qx(o.name)}));new Xu(r,s,async o=>{await r.vault.process(e,i=>tE(i,o))}).open()}}),Qx=r=>r.trim().startsWith("#")?r:"#"+r.trim().replace(/\s(?!:)/g,"-"),Xu=class extends Zr.Modal{constructor(t,n,s){super(t);this.recommendTags=n,this.onSubmit=s}onOpen(){let{contentEl:t}=this;t.createEl("h2",{text:m("Replace speaker with tag")}),t.createEl("p",{text:m("Replace the names of the two most frequently occurring speakers with tag format.")});for(let n of this.recommendTags)new Zr.Setting(t).setName(n.original+` (${n.count})`).addText(s=>s.setPlaceholder(n.newTag).setValue(n.newTag).onChange(async o=>{n.newTag=o}));new Zr.Setting(t).addButton(n=>n.setButtonText(m("Replace")).setCta().onClick(async()=>{this.close(),await this.onSubmit(this.recommendTags)}))}},Zx=r=>r.reduce((e,t)=>(e[t]=(e[t]||0)+1,e),{}),eE=r=>{let t=r.split(`
`).map(a=>a.match(/^([\u4e00-\u9fa5a-zA-Z0-9# ]+)([:|：]) /g)).flatMap(a=>a||[]);console.debug("allMatches",t);let n=Zx(t),s=Object.entries(n).sort((a,c)=>c[1]-a[1]);console.debug("sortedMatchFrequencies",s);let[o,i]=s.slice(0,2);return!o||!i?[]:[{name:o[0],count:o[1]},{name:i[0],count:i[1]}]},tE=(r,e)=>(e.forEach(({original:t,newTag:n})=>{r=r.replace(new RegExp(`^${t}`,"g"),`${n}`).replace(new RegExp(`
${t}`,"g"),`
${n}`)}),r);var Fm=require("obsidian");var Yu=(r,e)=>({id:"select-message-at-cursor",name:m("Select message at cursor"),editorCallback:async(t,n)=>{let s=await ze(r,e),o=t.getCursor("to").line,[i,a]=Mm(s,o);if(i===-1||a===-1){new Fm.Notice(m("No message found at cursor"));return}t.setSelection(t.offsetToPos(i),t.offsetToPos(a))}});var zo=require("obsidian");var Qu=({id:r,name:e,tag:t},n,s)=>({id:r,name:e,editorCallback:async(o,i)=>{try{let a=gt(t),{range:c,role:l,tagContent:f,tagRange:u}=As(n,o,s);if(console.debug("systemTagCmd",{range:c,role:l,tagContent:f,tagRange:u}),Cs(o,c))return Ps(o,c.from,a);if(l===null)return Rs(o,c,a);if(l==="system"){if(t!==f&&u)return Ja(o,c,u,t)}else o.setSelection(c.from,c.to),new zo.Notice(`${m("Conversion failed. Selected sections is a")} ${m(l)} ${m("message")}`)}catch(a){console.error(a),new zo.Notice(`\u{1F534} ${zo.Platform.isDesktopApp?m("Check the developer console for error details. "):""}${a}`,10*1e3)}}});var Qa=(r,e)=>`${r}#${e}`,rE=r=>{let[e,t]=r.split("#");return{type:e,tag:t}},Zu=r=>{let e=r.newChatTags.map(o=>Qa("newChat",o)),t=r.systemTags.map(o=>Qa("system",o)),n=r.userTags.map(o=>Qa("user",o)),s=r.providers.map(o=>Qa("assistant",o.tag));return[...e,...t,...n,...s]},en=r=>{let{type:e,tag:t}=rE(r);return{id:r,name:nE(e,t),tag:t,role:e}},nE=(r,e)=>r==="newChat"?Vo(e):gt(e);var Xo=require("obsidian");var ef=({id:r,name:e,tag:t},n,s)=>({id:r,name:e,editorCallback:async(o,i)=>{try{let a=gt(t),{range:c,role:l,tagContent:f,tagRange:u}=As(n,o,s);if(console.debug("userTagCmd",{range:c,role:l,tagContent:f,tagRange:u}),Cs(o,c))return Ps(o,c.from,a);if(l===null)return Rs(o,c,a);if(l==="user"){if(t!==f&&u)return Ja(o,c,u,t)}else o.setSelection(c.from,c.to),new Xo.Notice(`${m("Conversion failed. Selected sections is a")} ${m(l)} ${m("message")}`)}catch(a){console.error(a),new Xo.Notice(`\u{1F534} ${Xo.Platform.isDesktopApp?m("Check the developer console for error details. "):""}${a}`,10*1e3)}}});var Ty=By(vy()),je=require("obsidian");var Ay=require("obsidian");var Ol=class extends Ay.Modal{constructor(t,n){super(t);this.reporter=n}onOpen(){let{contentEl:t}=this;t.createEl("h1",{text:m("Syntax Error Report")});let n=this.reporter.join(`
`);t.createEl("textarea",{text:n,cls:"syntax-error-textarea",attr:{readonly:!0,rows:5}})}onClose(){let{contentEl:t}=this;t.empty()}};var Cy=async(r,e)=>{let t=r.vault.getFileByPath(e);if(!t)throw new Error("No prompt file found. "+e);let s=r.metadataCache.getFileCache(t);if(!s)throw new Error(m("Waiting for metadata to be ready. Please try again."));console.debug("sections",s.sections);let o=s.sections;if(!o)throw new Error("No sections found. "+e);let i=s.headings;if(!i)throw new Error("No headings found. "+e);let a=await r.vault.cachedRead(t),c=o.reduce((d,h)=>{if(h.type==="thematicBreak")return[...d,[]];let p=d.length-1;return d[p]=[...d[p],h],d},[[]]).filter(d=>d.length>0);console.debug("sectionGroups",c);let l=c.slice(1);console.debug("slides",l);let f=[],u=[];for(let d of l)try{let h=V0(d,i,a);if(f.some(p=>p.title===h.title))throw new Error(`${m("Duplicate title:")} ${h.title}`);f.push(h)}catch(h){u.push(h.message)}return console.debug("promptTemplates",f),console.debug("reporter",u),{promptTemplates:f,reporter:u}},V0=(r,e,t)=>{if(r.length<2)throw new Error(`Line ${r[0].position.start.line+1}, ${m("Expected at least 2 sections, heading and content")}`);if(r[0].type!=="heading")throw new Error(`Line ${r[0].position.start.line+1} - ${r[0].position.end.line+1}, ${m("Expected heading")}`);let n=e.find(l=>l.position.start.line===r[0].position.start.line);if(!n)throw new Error(`Line ${r[0].position.start.line+1}, ${m("Expected heading")}`);let s=n.heading.trim();if(!s)throw new Error(`Line ${n.position.start.line+1}, ${m("Expected heading")}`);let o=r[1].position.start.offset,i=r[r.length-1].position.end.offset,c=t.slice(o,i).trim();return{title:s,template:c}},Py=(r,e)=>{let t=[],n=new Map;return r.forEach(s=>{n.set(s.title,s.template)}),e.forEach(s=>{let o=n.get(s.title);o!==void 0&&o!==s.template&&t.push(s)}),t};var ui=r=>`Prompt#${r.title}`,Jf=r=>r.slice(r.indexOf("#")+1),zf=(r,e,t,n)=>({id:"LoadTemplateFile",name:m("Load template file: ")+`${Ue}/${m("promptFileName")}.md`,callback:async()=>{try{let s=(0,je.normalizePath)(`${Ue}/${m("promptFileName")}.md`);await K0(r)&&(await Ry(r,s),await new Promise(l=>setTimeout(l,2e3)));let{promptTemplates:i,reporter:a}=await Cy(r,s),c=Py(e.promptTemplates,i);c.length>0&&(console.debug("changed",c),new je.Notice(m("Templates have been updated: ")+c.map(l=>l.title).join(", "))),e.promptTemplates=i,await t(),n(),a&&a.length>0&&(await Ry(r,s),new Ol(r,a).open())}catch(s){console.error(s),new je.Notice(`\u{1F534} ${je.Platform.isDesktopApp?m("Check the developer console for error details. "):""}${s}`,10*1e3)}}}),K0=async r=>{let e=!1;await r.vault.adapter.exists((0,je.normalizePath)(Ue))||await r.vault.createFolder(Ue);let t=(0,je.normalizePath)(`${Ue}/${m("promptFileName")}.md`);return await r.vault.adapter.exists(t)||(await r.vault.create(t,m("PRESET_PROMPT_TEMPLATES")),new je.Notice(m("Create prompt template file")+` ${Ue}/${m("promptFileName")}.md`),e=!0),e},Ry=async(r,e)=>{var t;((t=r.workspace.getActiveFile())==null?void 0:t.path)!=e&&await r.workspace.openLinkText("",e,!0)},Xf=(r,e,t,n)=>({id:r,name:e,editorCallback:async(s,o)=>{try{let i=n.promptTemplates.find(f=>f.title===e);if(!i)throw new Error(`No template found. ${i}`);let a=Ku(t,s),{from:c,to:l}=a;s.setSelection(c,l),await new Promise(f=>setTimeout(f,500)),J0(s,i.template)}catch(i){console.error(i),new je.Notice(`\u{1F534} ${je.Platform.isDesktopApp?m("Check the developer console for error details. "):""}${i}`,10*1e3)}}}),G0=r=>{let e=r.listSelections();if(e.length===0)throw new Error("No selection");if(e.length>1)throw new Error("Multiple selections");return e[0]},J0=(r,e)=>{let t=r.getSelection(),s=Ty.default.compile(e,{strict:!1,noEscape:!0})({s:t}),o=s.includes(t)?s:t+s,{anchor:i,head:a}=G0(r);r.replaceRange(o,i,a)};var L=require("obsidian");var Yf=require("obsidian");var fi=class extends Yf.FuzzySuggestModal{constructor(t,n,s){super(t);this.models=n,this.onChoose=s}getItems(){return this.models}getItemText(t){return t}renderSuggestion(t,n){let s=t.item,o=0,i=n.createEl("div");for(let a of t.match.matches){let c=s.slice(o,a[0]),l=s.slice(a[0],a[0]+a[1]);i.createEl("span",{text:c}),i.createEl("span",{text:l,cls:"fuzzy-match-highlight"}),o=a[0]+a[1]}i.createEl("span",{text:s.slice(o)})}onChooseItem(t,n){this.onChoose(t)}},Ml=class extends Yf.FuzzySuggestModal{constructor(t,n,s){super(t);this.vendors=n,this.onChoose=s}getItems(){return this.vendors}getItemText(t){return t.name}renderSuggestion(t,n){let s=t.item.name,o=0,i=n.createEl("div");for(let c of t.match.matches){let l=s.slice(o,c[0]),f=s.slice(c[0],c[0]+c[1]);i.createEl("span",{text:l}),i.createEl("span",{text:f,cls:"fuzzy-match-highlight"}),o=c[0]+c[1]}i.createEl("span",{text:s.slice(o)});let a=n.createEl("div",{cls:"capability-tags-container"});t.item.capabilities.forEach(c=>{a.createEl("span",{text:`${Jn(c)} ${m(c)}`,cls:"capability-tag"})})}onChooseItem(t,n){this.onChoose(t)}};var Ll=class extends L.PluginSettingTab{constructor(t,n){super(t,n);this.createProviderSetting=(t,n,s=!1)=>{let o=Wo.find(l=>l.name===n.vendor);if(!o)throw new Error("No vendor found "+n.vendor);let{containerEl:i}=this,a=i.createEl("details");a.createEl("summary",{text:Iy(n.tag,o.name),cls:"tars-setting-h4"}),a.open=s;let c=m("Supported features")+" : "+o.capabilities.map(l=>`${Jn(l)} ${m(l)}`).join("    ");this.addTagSection(a,n,t,o.name),o.name===Ba.name?new L.Setting(a).setName(m("Model")).setDesc(c).addButton(l=>{l.setButtonText(n.options.model?n.options.model:m("Select the model to use")).onClick(async()=>{if(!n.options.apiKey){new L.Notice(m("Please input API key first"));return}try{let f=await cm(n.options.apiKey),u=async d=>{n.options.model=d,await this.plugin.saveSettings(),l.setButtonText(d)};new fi(this.app,f,u).open()}catch(f){new L.Notice("\u{1F534}"+f)}})}):o.name===Da.name?new L.Setting(a).setName(m("Model")).setDesc(c).addButton(l=>{l.setButtonText(n.options.model?n.options.model:m("Select the model to use")).onClick(async()=>{try{let f=await nm(),u=async d=>{n.options.model=d,await this.plugin.saveSettings(),l.setButtonText(d)};new fi(this.app,f,u).open()}catch(f){new L.Notice("\u{1F534}"+f)}})}):o.models.length>0?this.addModelDropDownSection(a,n.options,o.models,c):this.addModelTextSection(a,n.options,c),o.name!==Na.name&&this.addAPIkeySection(a,n.options,o.websiteToObtainKey?m("Obtain key from ")+o.websiteToObtainKey:""),"apiSecret"in n.options&&this.addAPISecretOptional(a,n.options),o.capabilities.includes("Web Search")&&new L.Setting(a).setName(m("Web search")).setDesc(m("Enable web search for AI")).addToggle(l=>{var f;return l.setValue((f=n.options.enableWebSearch)!=null?f:!1).onChange(async u=>{n.options.enableWebSearch=u,await this.plugin.saveSettings()})}),o.name===fa.name&&this.addClaudeSections(a,n.options),o.name===ba.name&&this.addGptImageSections(a,n.options),this.addBaseURLSection(a,n.options,o.defaultOptions.baseURL),"endpoint"in n.options&&this.addEndpointOptional(a,n.options),"apiVersion"in n.options&&this.addApiVersionOptional(a,n.options),this.addParametersSection(a,n.options),new L.Setting(a).setName(m("Remove")+" "+o.name).addButton(l=>{l.setWarning().setButtonText(m("Remove")).onClick(async()=>{this.plugin.settings.providers.splice(t,1),await this.plugin.saveSettings(),this.display()})})};this.addTagSection=(t,n,s,o)=>new L.Setting(t).setName("\u2728 "+m("Assistant message tag")).setDesc(m("Tag used to trigger AI text generation")).addText(i=>i.setPlaceholder(o).setValue(n.tag).onChange(async a=>{let c=a.trim();if(c.length===0||!ky(c))return;if(this.plugin.settings.providers.filter((u,d)=>d!==s).map(u=>u.tag.toLowerCase()).includes(c.toLowerCase())){new L.Notice(m("Keyword for tag must be unique"));return}n.tag=c;let f=t.querySelector("summary");f!=null&&(f.textContent=Iy(n.tag,o)),await this.plugin.saveSettings()}));this.addBaseURLSection=(t,n,s)=>{let o=null;new L.Setting(t).setName("baseURL").setDesc(m("Default:")+" "+s).addExtraButton(i=>{i.setIcon("reset").setTooltip(m("Restore default")).onClick(async()=>{n.baseURL=s,await this.plugin.saveSettings(),o&&(o.value=s)})}).addText(i=>{o=i.inputEl,i.setValue(n.baseURL).onChange(async a=>{n.baseURL=a,await this.plugin.saveSettings()})})};this.addAPIkeySection=(t,n,s="")=>new L.Setting(t).setName("API key").setDesc(s).addText(o=>o.setPlaceholder(m("Enter your key")).setValue(n.apiKey).onChange(async i=>{n.apiKey=i,await this.plugin.saveSettings()}));this.addAPISecretOptional=(t,n,s="")=>new L.Setting(t).setName("API Secret").setDesc(s).addText(o=>o.setPlaceholder("").setValue(n.apiSecret).onChange(async i=>{n.apiSecret=i,await this.plugin.saveSettings()}));this.addModelDropDownSection=(t,n,s,o)=>new L.Setting(t).setName(m("Model")).setDesc(o).addDropdown(i=>i.addOptions(s.reduce((a,c)=>(a[c]=c,a),{})).setValue(n.model).onChange(async a=>{n.model=a,await this.plugin.saveSettings()}));this.addModelTextSection=(t,n,s)=>new L.Setting(t).setName(m("Model")).setDesc(s).addText(o=>o.setPlaceholder("").setValue(n.model).onChange(async i=>{n.model=i,await this.plugin.saveSettings()}));this.addClaudeSections=(t,n)=>{new L.Setting(t).setName(m("Thinking")).setDesc(m("When enabled, Claude will show its reasoning process before giving the final answer.")).addToggle(s=>{var o;return s.setValue((o=n.enableThinking)!=null?o:!1).onChange(async i=>{n.enableThinking=i,await this.plugin.saveSettings()})}),new L.Setting(t).setName(m("Budget tokens for thinking")).setDesc(m("Must be \u22651024 and less than max_tokens")).addText(s=>s.setPlaceholder("").setValue(n.budget_tokens?n.budget_tokens.toString():"1600").onChange(async o=>{let i=parseInt(o);if(isNaN(i)){new L.Notice(m("Please enter a number"));return}if(i<1024){new L.Notice(m("Minimum value is 1024"));return}n.budget_tokens=i,await this.plugin.saveSettings()})),new L.Setting(t).setName("Max tokens").setDesc(m("Refer to the technical documentation")).addText(s=>s.setPlaceholder("").setValue(n.max_tokens.toString()).onChange(async o=>{let i=parseInt(o);if(isNaN(i)){new L.Notice(m("Please enter a number"));return}if(i<256){new L.Notice(m("Minimum value is 256"));return}n.max_tokens=i,await this.plugin.saveSettings()}))};this.addEndpointOptional=(t,n)=>new L.Setting(t).setName(m("Endpoint")).setDesc("e.g. https://docs-test-001.openai.azure.com/").addText(s=>s.setPlaceholder("").setValue(n.endpoint).onChange(async o=>{let i=o.trim();if(i.length===0)n.endpoint="",await this.plugin.saveSettings();else if(z0(i))n.endpoint=i,await this.plugin.saveSettings();else{new L.Notice(m("Invalid URL"));return}}));this.addApiVersionOptional=(t,n)=>new L.Setting(t).setName(m("API version")).setDesc("e.g. 2024-xx-xx-preview").addText(s=>s.setPlaceholder("").setValue(n.apiVersion).onChange(async o=>{n.apiVersion=o,await this.plugin.saveSettings()}));this.addParametersSection=(t,n)=>new L.Setting(t).setName(m("Override input parameters")).setDesc(m(`Developer feature, in JSON format. For example, if the model list doesn't have the model you want, enter {"model": "your desired model"}`)).addTextArea(s=>s.setPlaceholder("{}").setValue(JSON.stringify(n.parameters)).onChange(async o=>{try{if(o.trim()===""){n.parameters={},await this.plugin.saveSettings();return}n.parameters=JSON.parse(o),await this.plugin.saveSettings()}catch(i){return}}));this.addGptImageSections=(t,n)=>{new L.Setting(t).setName(m("Image Display Width")).setDesc(m("Example: 400px width would output as ![[image.jpg|400]]")).addSlider(s=>s.setLimits(200,800,100).setValue(n.displayWidth).setDynamicTooltip().onChange(async o=>{n.displayWidth=o,await this.plugin.saveSettings()})),new L.Setting(t).setName(m("Number of images")).setDesc(m("Number of images to generate (1-5)")).addSlider(s=>s.setLimits(1,5,1).setValue(n.n).setDynamicTooltip().onChange(async o=>{n.n=o,await this.plugin.saveSettings()})),new L.Setting(t).setName(m("Image size")).addDropdown(s=>s.addOptions({auto:"Auto","1024x1024":"1024x1024","1536x1024":"1536x1024 "+m("landscape"),"1024x1536":"1024x1536 "+m("portrait")}).setValue(n.size).onChange(async o=>{n.size=o,await this.plugin.saveSettings()})),new L.Setting(t).setName(m("Output format")).addDropdown(s=>s.addOptions({png:"PNG",jpeg:"JPEG",webp:"WEBP"}).setValue(n.output_format).onChange(async o=>{n.output_format=o,await this.plugin.saveSettings()})),new L.Setting(t).setName(m("Quality")).setDesc(m("Quality level for generated images. default: Auto")).addDropdown(s=>s.addOptions({auto:m("Auto"),high:m("High"),medium:m("Medium"),low:m("Low")}).setValue(n.quality).onChange(async o=>{n.quality=o,await this.plugin.saveSettings()})),new L.Setting(t).setName(m("Background")).setDesc(m("Background of the generated image. default: Auto")).addDropdown(s=>s.addOptions({auto:m("Auto"),transparent:m("Transparent"),opaque:m("Opaque")}).setValue(n.background).onChange(async o=>{n.background=o,await this.plugin.saveSettings()})),new L.Setting(t).setName(m("Output compression")).setDesc(m("Compression level of the output image, 10% - 100%. Only for webp or jpeg output format")).addSlider(s=>s.setLimits(10,100,10).setValue(n.output_compression).setDynamicTooltip().onChange(async o=>{n.output_compression=o,await this.plugin.saveSettings()}))};this.plugin=n}hide(){this.plugin.buildTagCommands()}display(t=!1){let{containerEl:n}=this;n.empty(),new L.Setting(n).setName(m("AI assistants")).setHeading(),new L.Setting(n).setName(m("New AI assistant")).setDesc(m("For those compatible with the OpenAI protocol, you can select OpenAI.")).addButton(f=>{f.setButtonText(m("Add AI Provider")).onClick(async()=>{let u=async d=>{let h=d.name,y=this.plugin.settings.providers.map(S=>S.tag).includes(h)?"":h,g=JSON.parse(JSON.stringify(d.defaultOptions));this.plugin.settings.providers.push({tag:y,vendor:d.name,options:g}),await this.plugin.saveSettings(),this.display(!0)};new Ml(this.app,Wo,u).open()})}),this.plugin.settings.providers.length||new L.Setting(n).setDesc(m("Please add at least one AI assistant to start using the plugin."));for(let[f,u]of this.plugin.settings.providers.entries()){let d=f===this.plugin.settings.providers.length-1;this.createProviderSetting(f,u,d&&t)}n.createEl("br"),new L.Setting(n).setName(m("Message tags")).setDesc(m("Keywords for tags in the text box are separated by spaces")).setHeading();let s=null;new L.Setting(n).setName(this.plugin.settings.roleEmojis.newChat+" "+m("New chat tags")).addExtraButton(f=>{f.setIcon("reset").setTooltip(m("Restore default")).onClick(async()=>{this.plugin.settings.newChatTags=mt.newChatTags,await this.plugin.saveSettings(),s&&(s.value=this.plugin.settings.newChatTags.join(" "))})}).addText(f=>{s=f.inputEl,f.setPlaceholder(mt.newChatTags.join(" ")).setValue(this.plugin.settings.newChatTags.join(" ")).onChange(async u=>{let d=u.split(" ").filter(h=>h.length>0);Qf(d)&&(this.plugin.settings.newChatTags=d,await this.plugin.saveSettings())})});let o=null;new L.Setting(n).setName(this.plugin.settings.roleEmojis.user+" "+m("User message tags")).addExtraButton(f=>{f.setIcon("reset").setTooltip(m("Restore default")).onClick(async()=>{this.plugin.settings.userTags=mt.userTags,await this.plugin.saveSettings(),o&&(o.value=this.plugin.settings.userTags.join(" "))})}).addText(f=>{o=f.inputEl,f.setPlaceholder(mt.userTags.join(" ")).setValue(this.plugin.settings.userTags.join(" ")).onChange(async u=>{let d=u.split(" ").filter(h=>h.length>0);Qf(d)&&(this.plugin.settings.userTags=d,await this.plugin.saveSettings())})});let i=null;new L.Setting(n).setName(this.plugin.settings.roleEmojis.system+" "+m("System message tags")).addExtraButton(f=>{f.setIcon("reset").setTooltip(m("Restore default")).onClick(async()=>{this.plugin.settings.systemTags=mt.systemTags,await this.plugin.saveSettings(),i&&(i.value=this.plugin.settings.systemTags.join(" "))})}).addText(f=>{i=f.inputEl,f.setPlaceholder(mt.systemTags.join(" ")).setValue(this.plugin.settings.systemTags.join(" ")).onChange(async u=>{let d=u.split(" ").filter(h=>h.length>0);Qf(d)&&(this.plugin.settings.systemTags=d,await this.plugin.saveSettings())})}),n.createEl("br"),new L.Setting(n).setName(m("System message")).setHeading();let a=null;new L.Setting(n).setName(m("Enable default system message")).setDesc(m("Automatically add a system message when none exists in the conversation")).addToggle(f=>f.setValue(this.plugin.settings.enableDefaultSystemMsg).onChange(async u=>{this.plugin.settings.enableDefaultSystemMsg=u,await this.plugin.saveSettings(),a&&(a.disabled=!u)})),new L.Setting(n).setName(m("Default system message")).addTextArea(f=>{a=f.inputEl,f.setDisabled(!this.plugin.settings.enableDefaultSystemMsg).setValue(this.plugin.settings.defaultSystemMsg).onChange(async u=>{this.plugin.settings.defaultSystemMsg=u.trim(),await this.plugin.saveSettings()})}),n.createEl("br"),new L.Setting(n).setName(m("Confirm before regeneration")).setDesc(m("Confirm before replacing existing assistant responses when using assistant commands")).addToggle(f=>f.setValue(this.plugin.settings.confirmRegenerate).onChange(async u=>{this.plugin.settings.confirmRegenerate=u,await this.plugin.saveSettings()})),new L.Setting(n).setName(m("Internal links")).setDesc(m("Internal links in user and system messages will be replaced with their referenced content. When disabled, only the original text of the links will be used.")).addToggle(f=>f.setValue(this.plugin.settings.enableInternalLink).onChange(async u=>{this.plugin.settings.enableInternalLink=u,await this.plugin.saveSettings()})),n.createEl("br");let c=n.createEl("details");c.createEl("summary",{text:m("Advanced"),cls:"tars-setting-h4"}),new L.Setting(c).setName(m("Internal links for assistant messages")).setDesc(m("Replace internal links in assistant messages with their referenced content. Note: This feature is generally not recommended as assistant-generated content may contain non-existent links.")).addToggle(f=>{var u;return f.setValue((u=this.plugin.settings.enableInternalLinkForAssistantMsg)!=null?u:!1).onChange(async d=>{this.plugin.settings.enableInternalLinkForAssistantMsg=d,await this.plugin.saveSettings()})});let l=null;new L.Setting(c).setName(m("Delay before answer (Seconds)")).setDesc(m("If you encounter errors with missing user messages when executing assistant commands on selected text, it may be due to the need for more time to parse the messages. Please slightly increase the delay time.")).addExtraButton(f=>{f.setIcon("reset").setTooltip(m("Restore default")).onClick(async()=>{this.plugin.settings.answerDelayInMilliseconds=mt.answerDelayInMilliseconds,await this.plugin.saveSettings(),l&&(l.value=(this.plugin.settings.answerDelayInMilliseconds/1e3).toString())})}).addSlider(f=>{l=f.sliderEl,f.setLimits(1.5,4,.5).setValue(this.plugin.settings.answerDelayInMilliseconds/1e3).setDynamicTooltip().onChange(async u=>{this.plugin.settings.answerDelayInMilliseconds=Math.round(u*1e3),await this.plugin.saveSettings()})}),new L.Setting(c).setName(m("Replace tag Command")).setDesc(m("Replace the names of the two most frequently occurring speakers with tag format.")).addToggle(f=>f.setValue(this.plugin.settings.enableReplaceTag).onChange(async u=>{this.plugin.settings.enableReplaceTag=u,await this.plugin.saveSettings(),u?this.plugin.addCommand(Jo(this.app)):this.plugin.removeCommand(Ya)})),new L.Setting(c).setName(m("Export to JSONL Command")).setDesc(m("Export conversations to JSONL")).addToggle(f=>f.setValue(this.plugin.settings.enableExportToJSONL).onChange(async u=>{this.plugin.settings.enableExportToJSONL=u,await this.plugin.saveSettings(),u?this.plugin.addCommand(Go(this.app,this.plugin.settings)):this.plugin.removeCommand(za)})),new L.Setting(c).setName(m("Tag suggest")).setDesc(m("If you only use commands without needing tag suggestions, you can disable this feature. Changes will take effect after restarting the plugin.")).addToggle(f=>f.setValue(this.plugin.settings.enableTagSuggest).onChange(async u=>{this.plugin.settings.enableTagSuggest=u,await this.plugin.saveSettings()}))}},Iy=(r,e)=>r===e?e:r+" ("+e+")",ky=r=>r.includes("#")?(new L.Notice(m("Keyword for tag must not contain #")),!1):r.includes(" ")?(new L.Notice(m("Keyword for tag must not contain space")),!1):!0,Qf=r=>{if(r.length===0)return new L.Notice(m("At least one tag is required")),!1;for(let e of r)if(!ky(e))return!1;return!0},z0=r=>{try{return new URL(r),!0}catch(e){return!1}};var ln=require("obsidian");var Zf=class extends ln.Modal{constructor(t,n){super(t);this.stats=n}onOpen(){let{contentEl:t}=this;t.createEl("h2",{text:m("AI Generation Details")});let n=t.createDiv({cls:"generation-stats"});n.createEl("p",{text:`${m("Round")}: ${this.stats.round}`}),n.createEl("p",{text:`${m("Model")}: ${this.stats.model}`}),n.createEl("p",{text:`${m("Vendor")}: ${this.stats.vendor}`}),n.createEl("p",{text:`${m("Characters")}: ${this.stats.characters}`}),n.createEl("p",{text:`${m("Duration")}: ${this.stats.duration}`}),n.createEl("p",{text:`${m("Start Time")}: ${this.stats.startTime.toLocaleTimeString()}`}),n.createEl("p",{text:`${m("End Time")}: ${this.stats.endTime.toLocaleTimeString()}`})}onClose(){let{contentEl:t}=this;t.empty()}},ed=class extends ln.Modal{constructor(t,n){super(t);this.error=n}onOpen(){let{contentEl:t}=this;t.createEl("h2",{text:m("Error Details")});let n=t.createDiv({cls:"error-details"});if(n.createEl("p",{text:`${m("Error Type")}: ${this.error.name||m("Unknown Error")}`}),n.createEl("p",{text:`${m("Error Message")}: ${this.error.message}`}),n.createEl("p",{text:`${m("Occurrence Time")}: ${this.error.timestamp.toLocaleString()}`}),this.error.stack){let s=n.createDiv({cls:"stack-trace"});s.createEl("h3",{text:m("Stack Trace")});let o=s.createEl("pre",{cls:"stack-trace-pre"});o.textContent=this.error.stack}if(ln.Platform.isDesktopApp){let o=t.createDiv({cls:"error-modal-buttons"}).createEl("button",{text:m("Copy Error Info")});o.onclick=()=>{navigator.clipboard.writeText(JSON.stringify(this.error,null,2)),new ln.Notice(m("Error info copied to clipboard"))}}}onClose(){let{contentEl:t}=this;t.empty()}},Nl=class{constructor(e,t){this.app=e;this.statusBarItem=t;this.autoHideTimer=null;this.state={type:"idle",content:{text:"Tars",tooltip:m("Tars AI assistant is ready")},timestamp:new Date},this.setupClickHandler(),this.refreshStatusBar()}setupClickHandler(){this.statusBarItem.addEventListener("click",()=>{this.state.data&&(this.state.type==="error"?new ed(this.app,this.state.data).open():this.state.type==="success"&&new Zf(this.app,this.state.data).open())}),this.statusBarItem.style.cursor="pointer",this.statusBarItem.style.transition="opacity 0.2s ease"}updateState(e){this.state={...this.state,...e,timestamp:new Date},this.refreshStatusBar()}refreshStatusBar(){let{content:e}=this.state;this.statusBarItem.setText(e.text),this.statusBarItem.setAttribute("title",e.tooltip)}setGeneratingStatus(e){this.autoHideTimer&&(clearTimeout(this.autoHideTimer),this.autoHideTimer=null),this.updateState({type:"generating",content:{text:`Round ${e}...`,tooltip:`${m("Generating round")} ${e} ${m("answer...")}`},data:void 0})}updateGeneratingProgress(e){this.state.type==="generating"&&this.updateState({content:{text:`Tars: ${e}${m("characters")}`,tooltip:`${m("Generating...")} ${e} ${m("characters")}`}})}setSuccessStatus(e){this.autoHideTimer&&(clearTimeout(this.autoHideTimer),this.autoHideTimer=null),this.updateState({type:"success",content:{text:`Tars: ${e.characters}${m("characters")} ${e.duration}`,tooltip:`${m("Round")} ${e.round} | ${e.characters}${m("characters")} | ${e.duration} | ${e.model}`},data:e})}setErrorStatus(e){this.autoHideTimer&&(clearTimeout(this.autoHideTimer),this.autoHideTimer=null);let t={message:e.message,name:e.name,stack:e.stack,timestamp:new Date};this.updateState({type:"error",content:{text:`\u{1F534} Tars: ${m("Error")}`,tooltip:`${m("Error")}: ${e.message}`},data:t}),this.autoHideTimer=setTimeout(()=>this.clearStatus(),1e3*60*2)}setCancelledStatus(){this.autoHideTimer&&(clearTimeout(this.autoHideTimer),this.autoHideTimer=null),this.updateState({type:"idle",content:{text:`\u26A0\uFE0F Tars: ${m("Generation cancelled")}`,tooltip:m("Generation cancelled")},data:void 0}),this.autoHideTimer=setTimeout(()=>this.clearStatus(),1e3*60*1)}clearStatus(){this.autoHideTimer&&(clearTimeout(this.autoHideTimer),this.autoHideTimer=null),this.updateState({type:"idle",content:{text:"Tars",tooltip:m("Tars AI assistant is ready")},data:void 0})}getState(){return{...this.state}}dispose(){this.autoHideTimer&&(clearTimeout(this.autoHideTimer),this.autoHideTimer=null)}};var Dl=class extends qt.Plugin{constructor(){super(...arguments);this.tagCmdIds=[];this.promptCmdIds=[];this.tagLowerCaseMap=new Map;this.aborterInstance=null}async onload(){await this.loadSettings(),console.debug("loading Tars plugin...");let t=this.addStatusBarItem();this.statusBarManager=new Nl(this.app,t),this.buildTagCommands(!0),this.buildPromptCommands(!0),this.addCommand(Yu(this.app,this.settings)),this.addCommand(zf(this.app,this.settings,()=>this.saveSettings(),()=>this.buildPromptCommands())),this.settings.editorStatus={isTextInserting:!1},this.settings.enableTagSuggest&&this.registerEditorSuggest(new Ga(this.app,this.settings,this.tagLowerCaseMap,this.statusBarManager,this.getRequestController())),this.addCommand({id:"cancelGeneration",name:m("Cancel generation"),callback:async()=>{if(this.settings.editorStatus.isTextInserting=!1,this.aborterInstance===null){new qt.Notice(m("No active generation to cancel"));return}if(this.aborterInstance.signal.aborted){new qt.Notice(m("Generation already cancelled"));return}this.aborterInstance.abort()}}),this.settings.enableReplaceTag&&this.addCommand(Jo(this.app)),this.settings.enableExportToJSONL&&this.addCommand(Go(this.app,this.settings)),this.addSettingTab(new Ll(this.app,this))}onunload(){var t;(t=this.statusBarManager)==null||t.dispose()}addTagCommand(t){let n=en(t);switch(n.role){case"newChat":this.addCommand(zu(n));break;case"system":this.addCommand(Qu(n,this.app,this.settings));break;case"user":this.addCommand(ef(n,this.app,this.settings));break;case"assistant":this.addCommand(Ju(n,this.app,this.settings,this.statusBarManager,this.getRequestController()));break;default:throw new Error("Unknown tag role")}}buildTagCommands(t=!1){this.settings.tagSuggestMaxLineLength=Lm(this.settings);let n=Zu(this.settings),s=this.tagCmdIds.filter(c=>!n.includes(c));s.forEach(c=>{this.removeCommand(c);let{tag:l}=en(c);this.tagLowerCaseMap.delete(l.toLowerCase())});let o=n.filter(c=>!this.tagCmdIds.includes(c));if(o.forEach(c=>{this.addTagCommand(c);let{role:l,tag:f}=en(c);this.tagLowerCaseMap.set(f.toLowerCase(),{role:l,tag:f})}),this.tagCmdIds=n,t)return;let i=s.map(c=>en(c).tag);i.length>0&&(console.debug("Removed commands",i),new qt.Notice(`${m("Removed commands")}: ${i.join(", ")}`));let a=o.map(c=>en(c).tag);a.length>0&&(console.debug("Added commands",a),new qt.Notice(`${m("Added commands")}: ${a.join(", ")}`))}buildPromptCommands(t=!1){let n=this.settings.promptTemplates.map(ui),s=this.promptCmdIds.filter(c=>!n.includes(c));s.forEach(c=>this.removeCommand(c));let o=this.settings.promptTemplates.filter(c=>!this.promptCmdIds.includes(ui(c)));if(o.forEach(c=>{this.addCommand(Xf(ui(c),c.title,this.app,this.settings))}),this.promptCmdIds=n,t)return;let i=s.map(c=>Jf(c));i.length>0&&(console.debug("Removed commands",i),new qt.Notice(`${m("Removed commands")}: ${i.join(", ")}`));let a=o.map(c=>c.title);a.length>0&&(console.debug("Added commands",a),new qt.Notice(`${m("Added commands")}: ${a.join(", ")}`))}getRequestController(){return{getController:()=>(this.aborterInstance||(this.aborterInstance=new AbortController),this.aborterInstance),cleanup:()=>{this.settings.editorStatus.isTextInserting=!1,this.aborterInstance=null}}}async loadSettings(){this.settings=Object.assign({},mt,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}};
/*! Bundled license information:

@google/generative-ai/dist/index.mjs:
  (**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@google/generative-ai/dist/index.mjs:
  (**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)
*/

/* nosourcemap */