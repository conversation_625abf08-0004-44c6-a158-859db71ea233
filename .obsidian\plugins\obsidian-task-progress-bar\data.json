{"progressBarDisplayMode": "both", "supportHoverToShowProgressInfo": true, "addProgressBarToNonTaskBullet": false, "addTaskProgressBarToHeading": true, "enableProgressbarInReadingMode": true, "countSubLevel": true, "displayMode": "bracketFraction", "customFormat": "[{{COMPLETED}}/{{TOTAL}}]", "showPercentage": false, "customizeProgressRanges": false, "progressRanges": [{"min": 0, "max": 20, "text": "Just started {{PROGRESS}}%"}, {"min": 20, "max": 40, "text": "Making progress {{PROGRESS}}%"}, {"min": 40, "max": 60, "text": "Half way {{PROGRESS}}%"}, {"min": 60, "max": 80, "text": "Good progress {{PROGRESS}}%"}, {"min": 80, "max": 100, "text": "Almost there {{PROGRESS}}%"}], "allowCustomProgressGoal": false, "hideProgressBarBasedOnConditions": false, "hideProgressBarTags": "no-progress-bar", "hideProgressBarFolders": "", "hideProgressBarMetadata": "hide-progress-bar", "showProgressBarBasedOnHeading": "", "autoCompleteParent": true, "markParentInProgressWhenPartiallyComplete": false, "taskStatuses": {"completed": "x", "inProgress": "/", "abandoned": "-", "notStarted": " |?|!|*|\"|l|b|i|S|I|p|c|f|k|w|u|d", "planned": ">|<"}, "countOtherStatusesAs": "notStarted", "excludeTaskMarks": "", "useOnlyCountMarks": false, "onlyCountTaskMarks": "x|X", "enableTaskStatusSwitcher": false, "enableCustomTaskMarks": false, "enableTextMarkInSourceMode": false, "enableCycleCompleteStatus": true, "taskStatusCycle": ["TODO", "DOING", "IN-PROGRESS", "DONE"], "taskStatusMarks": {"TODO": " ", "DOING": "-", "IN-PROGRESS": ">", "DONE": "x"}, "excludeMarksFromCycle": [], "enableTaskGeniusIcons": false, "enablePriorityPicker": false, "enablePriorityKeyboardShortcuts": false, "enableDatePicker": false, "recurrenceDateBase": "due", "taskFilter": {"enableTaskFilter": true, "keyboardShortcut": "Alt-f", "presetTaskFilters": []}, "taskGutter": {"enableTaskGutter": false}, "completedTaskMover": {"enableCompletedTaskMover": true, "taskMarkerType": "version", "versionMarker": "version 1.0", "dateMarker": "archived on {{date}}", "customMarker": "moved {{DATE:YYYY-MM-DD HH:mm}}", "completeAllMovedTasks": false, "treatAbandonedAsCompleted": false, "withCurrentFileLink": false}, "quickCapture": {"enableQuickCapture": true, "targetFile": "Quick Capture.md", "placeholder": "Capture thoughts, tasks, or ideas...", "appendToFile": "append"}, "workflow": {"enableWorkflow": true, "autoAddTimestamp": true, "autoAddNextTask": false, "autoRemoveLastStageMarker": false, "calculateSpentTime": true, "spentTimeFormat": "HH:mm:ss", "removeTimestampOnTransition": false, "timestampFormat": "YYYY-MM-DD HH:mm:ss", "calculateFullSpentTime": true, "definitions": [{"id": "project_workflow", "name": "Project Workflow", "description": "Standard project management workflow", "stages": [{"id": "planning", "name": "Planning", "type": "linear", "next": "in_progress"}, {"id": "in_progress", "name": "In Progress", "type": "cycle", "subStages": [{"id": "development", "name": "Development", "next": "testing"}, {"id": "testing", "name": "Testing", "next": "development"}], "canProceedTo": ["review", "cancelled"]}, {"id": "review", "name": "Review", "type": "cycle", "canProceedTo": ["in_progress", "completed"]}, {"id": "completed", "name": "Completed", "type": "terminal"}, {"id": "cancelled", "name": "Cancelled", "type": "terminal"}], "metadata": {"version": "1.0", "created": "2024-03-20", "lastModified": "2024-03-20"}}]}, "useDailyNotePathAsDate": false, "dailyNoteFormat": "yyyy-MM-dd", "useAsDateType": "due", "dailyNotePath": "", "preferMetadataFormat": "tasks", "projectTagPrefix": {"tasks": "project", "dataview": "project"}, "contextTagPrefix": {"tasks": "@", "dataview": "context"}, "areaTagPrefix": {"tasks": "area", "dataview": "area"}, "fileMetadataInheritance": {"enabled": true, "inheritFromFrontmatter": false, "inheritFromFrontmatterForSubtasks": false}, "projectConfig": {"enableEnhancedProject": false, "pathMappings": [], "metadataConfig": {"metadataKey": "project", "enabled": false}, "configFile": {"fileName": "project.md", "searchRecursively": false, "enabled": false}, "metadataMappings": [], "defaultProjectNaming": {"strategy": "filename", "stripExtension": false, "enabled": false}}, "fileParsingConfig": {"enableFileMetadataParsing": false, "metadataFieldsToParseAsTasks": ["dueDate", "todo", "complete", "task"], "enableTagBasedTaskParsing": false, "tagsToParseAsTasks": ["#todo", "#task", "#action", "#due"], "taskContentFromMetadata": "title", "defaultTaskStatus": " ", "enableWorkerProcessing": true}, "useRelativeTimeForDate": true, "ignoreHeading": "", "focusHeading": "", "enableView": true, "enableInlineEditor": true, "defaultViewMode": "list", "globalFilterRules": {}, "viewConfiguration": [{"id": "inbox", "name": "Inbox", "icon": "inbox", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": true, "filterRules": {}}, {"id": "forecast", "name": "Forecast", "icon": "calendar-days", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": true, "filterRules": {}}, {"id": "projects", "name": "Projects", "icon": "folders", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": false, "filterRules": {}}, {"id": "tags", "name": "Tags", "icon": "tag", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": false, "filterRules": {}}, {"id": "flagged", "name": "Flagged", "icon": "flag", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": true, "filterRules": {}}, {"id": "review", "name": "Review", "icon": "eye", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": false, "filterRules": {}}, {"id": "calendar", "name": "事件", "icon": "calendar", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": false, "filterRules": {}, "specificConfig": {"viewType": "calendar"}}, {"id": "kanban", "name": "状态", "icon": "kanban", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": false, "filterRules": {}, "specificConfig": {"viewType": "kanban", "showCheckbox": true}}, {"id": "gantt", "name": "计划", "icon": "chart-gantt", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": false, "filterRules": {}, "specificConfig": {"viewType": "gantt", "showTaskLabels": true, "useMarkdownRenderer": true}}, {"id": "habit", "name": "习惯", "icon": "calendar-clock", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": false, "filterRules": {}, "filterBlanks": false}, {"id": "table", "name": "Table", "icon": "table", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": false, "filterRules": {}, "filterBlanks": false, "specificConfig": {"viewType": "table", "enableTreeView": true, "enableLazyLoading": true, "pageSize": 50, "enableInlineEditing": true, "visibleColumns": ["status", "content", "priority", "dueDate", "startDate", "scheduledDate", "tags", "project", "context", "filePath"], "columnWidths": {"status": 80, "content": 300, "priority": 100, "dueDate": 120, "startDate": 120, "scheduledDate": 120, "createdDate": 120, "completedDate": 120, "tags": 150, "project": 150, "context": 120, "recurrence": 120, "estimatedTime": 120, "actualTime": 120, "filePath": 200}, "sortableColumns": true, "resizableColumns": true, "showRowNumbers": true, "enableRowSelection": true, "enableMultiSelect": true, "defaultSortField": "priority", "defaultSortOrder": "asc"}}, {"id": "quadrant", "name": "Matrix", "icon": "layout-grid", "type": "default", "visible": true, "hideCompletedAndAbandonedTasks": false, "filterRules": {}, "filterBlanks": false, "specificConfig": {"viewType": "quadrant", "hideEmptyQuadrants": false, "autoUpdatePriority": true, "autoUpdateTags": true, "showTaskCount": true, "defaultSortField": "priority", "defaultSortOrder": "desc", "urgentTag": "#urgent", "importantTag": "#important", "urgentThresholdDays": 3, "usePriorityForClassification": false, "urgentPriorityThreshold": 4, "importantPriorityThreshold": 3, "customQuadrantColors": false, "quadrantColors": {"urgentImportant": "#dc3545", "notUrgentImportant": "#28a745", "urgentNotImportant": "#ffc107", "notUrgentNotImportant": "#6c757d"}}}], "reviewSettings": {}, "rewards": {"enableRewards": true, "rewardItems": [{"id": "reward-tea", "name": "恭喜你完成了个人报告!", "occurrence": "common", "inventory": -1, "condition": "#个人报告", "imageUrl": "附件/雪奈右/ooAQCorazID5y9PXAXfACW9NQCNrlAAEhcgQAf~tplv-dy-cropcenter323430.jpeg"}, {"id": "reward-series-episode", "name": "Watch an episode of a favorite series", "occurrence": "rare", "inventory": 16}, {"id": "reward-champagne-project", "name": "Play a game", "occurrence": "legendary", "inventory": 1, "condition": "#project AND #milestone"}, {"id": "reward-chocolate-quick", "name": "Eat a piece of chocolate", "occurrence": "common", "inventory": 10, "condition": "#quickwin", "imageUrl": ""}, {"id": "reward-1747121868844-zb3gd", "name": "新奖励", "occurrence": "common", "inventory": -1}], "occurrenceLevels": [{"name": "common", "chance": 70}, {"name": "rare", "chance": 25}, {"name": "legendary", "chance": 5}]}, "habit": {"enableHabits": true, "habits": [{"id": "1746430861706q3kc6u7", "name": "总结复盘今日的事情", "icon": "lucide-alarm-check", "type": "daily", "property": "思考"}, {"id": "1747104293377h3hbpeg", "name": "浏览YoutuBe", "description": "定期获取外网的相关信息", "icon": "lucide-watch", "type": "daily", "property": "Youtube"}]}, "filterConfig": {"enableSavedFilters": true, "savedConfigs": []}, "sortTasks": true, "sortCriteria": [{"field": "status", "order": "asc"}, {"field": "priority", "order": "asc"}, {"field": "dueDate", "order": "asc"}], "autoDateManager": {"enabled": false, "manageCompletedDate": true, "manageStartDate": true, "manageCancelledDate": true, "completedDateFormat": "YYYY-MM-DD", "startDateFormat": "YYYY-MM-DD", "cancelledDateFormat": "YYYY-MM-DD", "completedDateMarker": "✅", "startDateMarker": "🚀", "cancelledDateMarker": "❌"}, "betaTest": {"enableBaseView": false}, "icsIntegration": {"sources": [], "globalRefreshInterval": 60, "maxCacheAge": 24, "enableBackgroundRefresh": false, "networkTimeout": 30, "maxEventsPerSource": 1000, "showInCalendar": false, "showInTaskLists": false, "defaultEventColor": "#3b82f6"}, "timelineSidebar": {"enableTimelineSidebar": false, "autoOpenOnStartup": false, "showCompletedTasks": true, "focusModeByDefault": false, "maxEventsToShow": 100}, "fileFilter": {"enabled": false, "mode": "blacklist", "rules": []}, "onCompletion": {"enableOnCompletion": true, "defaultArchiveFile": "Archive/Completed Tasks.md", "defaultArchiveSection": "Completed Tasks", "showAdvancedOptions": false}, "timeParsing": {"enabled": true, "supportedLanguages": ["en", "zh"], "dateKeywords": {"start": ["start", "begin", "from", "starting", "begins", "开始", "从", "起始", "起", "始于", "自"], "due": ["due", "deadline", "by", "until", "before", "expires", "ends", "截止", "到期", "之前", "期限", "最晚", "结束", "终止", "完成于"], "scheduled": ["scheduled", "on", "at", "planned", "set for", "arranged", "安排", "计划", "在", "定于", "预定", "约定", "设定"]}, "removeOriginalText": true, "perLineProcessing": true, "realTimeReplacement": true}, "dateMark": "📅,📆,⏳,🛫", "dailyNotePathFormat": "YYYY-MM-DD", "alwaysCycleNewTasks": false, "enableHeadingProgressBar": false, "addNumberToProgressBar": false, "showProgressBar": false}