{"choices": [{"id": "8e85ab9d-c069-4c5b-9648-87f43<PERSON><PERSON>eab", "name": "CVE漏洞", "type": "Template", "command": true, "templatePath": "模板/CVE漏洞模板.md", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": true, "folders": ["🏴‍☠️网络安全/技术范畴/漏洞相关/CVE收录"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "62eaf808-0510-4e6b-87a0-045558fa23ed", "name": "新建人物--生物科技", "type": "Template", "command": true, "templatePath": "aliyun-backup/分支仓库/生物科技/模板/人物信息模板.md", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": true, "folders": ["aliyun-backup/分支仓库/生物科技"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "bcb04eb1-40f8-4706-9197-f4371253546b", "name": "🤵‍♂️人物+", "type": "Template", "command": true, "templatePath": "aliyun-backup/笔记模板/人物信息模板.md", "fileNameFormat": {"enabled": false, "format": "/"}, "folder": {"enabled": true, "folders": ["Persona/Social network"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "00adfed8-2dc3-4252-a947-2125f54d04c7", "name": "省份+", "type": "Template", "command": true, "templatePath": "模板/省份模板.md", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": true, "folders": ["地理数据/基础设施/中国省份"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "60e752cb-8421-4308-841d-ad8d36582a19", "name": "📺事件++", "type": "Template", "command": true, "templatePath": "Rational thinking/其他仓库/计算机科学/OSINT-BOX/OSINT-Box/模板/事件分析模板.md", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": true, "folders": ["National/Branch Pack/地理-国家-城市-民族/政治相关/金融系统事件"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "16b3cd1f-e4f2-4a99-8fb2-a7a453b88d27", "name": "🏢组织+", "type": "Template", "command": true, "templatePath": "模板/公司信息模板.md", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": true, "folders": ["National/Branch Pack/公司-组织的力量/公司-组织的力量"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "fb19b3fa-6c63-4c07-a1e8-dabe866da72f", "name": "🗺️地图对象+", "type": "Template", "command": true, "templatePath": "模板/省份模板.md", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": true, "folders": ["地理数据/基础设施"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "63f6d3e3-74e6-45ff-94eb-408ac3442509", "name": "🐺生物对象+", "type": "Template", "command": true, "templatePath": "模板/生物对象模板.md", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": true, "folders": ["aliyun-backup/分支仓库/生物科技/"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "344ed62f-5196-4a0d-ad83-f7f712ab9444", "name": "🏠楼盘对象+", "type": "Template", "command": true, "templatePath": "模板/楼盘模板 - 龙泉驿区(大面).md", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": true, "folders": ["地理数据/基础设施/住宅"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "62deb523-3724-4009-9303-779f083d4fcf", "name": "📕读书笔记+", "type": "Template", "command": true, "templatePath": "模板/读书笔记模板.md", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": true, "folders": ["读书笔记"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "8f06c4be-cd65-4d90-a69c-fa9e1da750c6", "name": "快速添加支出逻辑链", "type": "Macro", "command": true, "macroId": "3880c0ef-6ab5-4e9e-a525-5fe010d2f8be"}, {"id": "ffd5eb89-1148-4ead-b14a-fe1f7174e31e", "name": "🤑财务支出", "type": "Template", "command": true, "templatePath": "财务数据/模板/支出模板.md", "fileNameFormat": {"enabled": true, "format": "{{DATE:YYYY-MM-DD-hh-mm}}"}, "folder": {"enabled": true, "folders": ["财务数据"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}, {"id": "9a9f1f3b-92e9-494f-98fa-f25db954f574", "name": "仓库快速备份", "type": "Macro", "command": true, "macroId": "40ccda77-2ee6-4f07-9493-0f086c300e2d"}, {"id": "edf8824a-83e4-4b26-89c6-c40d28a3be33", "name": "关系图谱增强", "type": "Macro", "command": true, "macroId": "fe367f7c-12b9-428e-abbe-f28279c63c4e"}], "macros": [{"name": "测试", "id": "3880c0ef-6ab5-4e9e-a525-5fe010d2f8be", "commands": [{"name": "🤑财务支出", "type": "Choice", "id": "86c318c6-4426-4e4e-a575-4025bf8ccaf2", "choiceId": "ffd5eb89-1148-4ead-b14a-fe1f7174e31e"}, {"name": "Wait", "type": "Wait", "id": "755782e4-6e50-442f-9198-6daf801b12ec", "time": 100}, {"name": "Wait", "type": "Wait", "id": "a2c0eb7b-99bb-4849-8711-2a5eae65f6b6", "time": 100}, {"name": "MetaEdit: Run MetaEdit", "type": "Obsidian", "id": "61cc12f1-2005-41ec-b911-12db04b7f4c3", "commandId": "metaedit:metaEditRun"}, {"name": "Wait", "type": "Wait", "id": "4c7cad57-e974-4095-9382-8a916869bdd7", "time": 100}, {"name": "Wait", "type": "Wait", "id": "6a507ce5-91c5-4997-8253-254b1fd36ab1", "time": 1000}, {"name": "MetaEdit: Run MetaEdit", "type": "Obsidian", "id": "aba63eee-1820-4944-a528-c5ece3cd41a1", "commandId": "metaedit:metaEditRun"}], "runOnStartup": false}, {"name": "快速添加支出逻辑链", "id": "dd049149-e6ff-4bcf-864e-d2d298b5bd57", "commands": [], "runOnStartup": false}, {"name": "仓库数据备份", "id": "40ccda77-2ee6-4f07-9493-0f086c300e2d", "commands": [{"name": "Wait", "type": "Wait", "id": "253bbe16-2efa-4fd5-8b97-6ea8ed7c90f6", "time": 100}, {"name": "graphSearch", "type": "UserScript", "id": "a79224c5-5ef1-405d-9a7e-9a72fde4b1b6", "path": "Scripts_RunJS/graphSearch.js", "settings": {}}], "runOnStartup": false}, {"name": "关系图谱增强", "id": "fe367f7c-12b9-428e-abbe-f28279c63c4e", "commands": [{"name": "graphSearch", "type": "UserScript", "id": "8d8830ba-8c62-4c6c-9a44-a0d7c7b91819", "path": "Scripts_RunJS/graphSearch.js", "settings": {}}], "runOnStartup": false}], "inputPrompt": "multi-line", "devMode": false, "templateFolderPath": "Quicka_script", "announceUpdates": true, "version": "1.18.1", "disableOnlineFeatures": true, "enableRibbonIcon": true, "ai": {"defaultModel": "Ask me", "defaultSystemPrompt": "As an AI assistant within Obsidian, your primary goal is to help users manage their ideas and knowledge more effectively. Format your responses using Markdown syntax. Please use the [[Obsidian]] link format. You can write aliases for the links by writing [[Obsidian|the alias after the pipe symbol]]. To use mathematical notation, use LaTeX syntax. LaTeX syntax for larger equations should be on separate lines, surrounded with double dollar signs ($$). You can also inline math expressions by wrapping it in $ symbols. For example, use $$w_{ij}^{\text{new}}:=w_{ij}^{\text{current}}+etacdotdelta_jcdot x_{ij}$$ on a separate line, but you can write \"($eta$ = learning rate, $delta_j$ = error term, $x_{ij}$ = input)\" inline.", "promptTemplatesFolderPath": "", "showAssistant": true, "providers": [{"name": "OpenAI", "endpoint": "https://api.openai.com/v1", "apiKey": "", "models": [{"name": "text-davinci-003", "maxTokens": 4096}, {"name": "gpt-3.5-turbo", "maxTokens": 4096}, {"name": "gpt-3.5-turbo-16k", "maxTokens": 16384}, {"name": "gpt-3.5-turbo-1106", "maxTokens": 16385}, {"name": "gpt-4", "maxTokens": 8192}, {"name": "gpt-4-32k", "maxTokens": 32768}, {"name": "gpt-4-1106-preview", "maxTokens": 128000}, {"name": "gpt-4-turbo", "maxTokens": 128000}, {"name": "gpt-4o", "maxTokens": 128000}]}]}, "migrations": {"migrateToMacroIDFromEmbeddedMacro": true, "useQuickAddTemplateFolder": true, "incrementFileNameSettingMoveToDefaultBehavior": true, "mutualExclusionInsertAfterAndWriteToBottomOfFile": true, "setVersionAfterUpdateModalRelease": true, "addDefaultAIProviders": true}}