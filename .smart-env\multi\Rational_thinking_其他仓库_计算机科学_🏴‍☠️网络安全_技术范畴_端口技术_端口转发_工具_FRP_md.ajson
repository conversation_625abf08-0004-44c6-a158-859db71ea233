"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08628837,-0.05860195,0.02573346,-0.01811953,0.01581034,-0.00797483,0.00155189,0.02501294,0.05500248,-0.01585481,0.02678815,-0.06074724,0.03739225,0.00262488,0.01492165,0.05200024,0.0179563,0.03887929,0.02395838,-0.05227077,0.06084154,-0.01536886,0.00444096,-0.10484784,-0.0023879,0.0603305,-0.00237527,-0.00876505,0.02288805,-0.15573402,0.03150025,0.04064725,-0.04160419,0.01680895,0.0208896,-0.02896039,0.00816002,0.0180219,-0.03223446,0.04316963,0.03645829,0.00065451,-0.02331025,-0.01600302,-0.01731028,-0.04763146,-0.01537082,-0.00847763,0.01505366,-0.06955207,-0.0163743,-0.04515882,-0.0137947,-0.06700961,-0.01224162,0.0493832,0.04357249,0.0102459,0.0618735,-0.10511828,0.04150749,0.0254985,-0.22546256,0.06859744,0.07104047,-0.01173931,-0.00001441,-0.00064856,0.01553913,0.05542628,-0.08231489,0.05186268,-0.02245709,0.05301014,0.06768247,-0.02451147,-0.01013297,-0.02255951,-0.00747863,0.00422518,-0.01406252,0.00515378,-0.021355,0.00263593,-0.0357669,0.00762982,-0.00180724,0.00391971,-0.00126792,-0.00820142,-0.0259381,-0.05052589,0.04550911,0.03815584,-0.01760926,0.0067438,0.01606583,0.05476764,-0.12623535,0.08700992,-0.04807895,0.02526322,-0.01526702,-0.05603064,0.0505196,-0.05886219,-0.0285477,-0.02304025,-0.01034755,-0.00215692,-0.04007163,-0.02605468,0.05436355,-0.05302609,0.04622875,0.02630197,-0.00439774,0.00697795,-0.0571119,-0.00228144,-0.02700378,0.00349089,0.04169467,-0.01390859,-0.02391375,-0.06042103,0.06036062,0.07835985,-0.00773262,0.01318745,0.03637406,-0.03356047,0.01608719,0.01379824,0.03295974,0.04018484,0.01507684,-0.05235003,-0.01720574,-0.05779755,0.01907253,-0.08295827,0.01433568,-0.09812737,-0.05603548,0.05936956,-0.06170814,0.04353155,0.05306247,-0.06677296,-0.02512891,0.02316805,-0.00527515,-0.05932558,0.00382987,0.01181252,0.10621005,0.15787168,-0.04240648,-0.02779953,-0.06421627,0.00741703,-0.10089189,0.13006467,0.02742612,-0.05630181,-0.04260026,0.00324968,-0.0186427,-0.02607865,-0.01522968,-0.04289887,0.02097337,-0.02995284,0.07592762,-0.01073893,-0.03380525,-0.03800046,0.02378789,0.02312695,0.03129502,-0.02527165,-0.04407004,-0.01271484,-0.01487859,-0.07107691,-0.06564786,-0.04379311,-0.01050664,-0.05930753,-0.11875987,0.00446177,0.04916147,0.01803366,-0.02445258,-0.0676028,0.02134254,0.01585572,0.08664263,-0.0413457,0.10434753,0.0210479,0.01519789,0.01861522,-0.06747174,-0.00251589,-0.0196948,0.00593357,0.03614054,-0.02328573,-0.03633431,0.0233585,-0.01480859,0.01141893,-0.0121039,0.05266468,-0.01242803,0.01452221,0.00345336,0.05575364,0.08266416,0.01540608,-0.06491511,-0.20599373,-0.04456686,0.00708897,-0.04379893,0.02927554,-0.01891481,0.02572755,0.01202685,0.0100686,0.02390498,0.04505565,-0.01755405,-0.03999947,0.01439417,-0.0024797,-0.01822424,0.0674981,-0.01626235,-0.0144853,-0.00049104,-0.03051921,0.07321011,-0.05972066,-0.049798,0.05073953,0.0233892,0.13951924,-0.00691625,0.05310201,0.02375282,0.03932693,0.07755049,0.01527466,-0.12011077,0.00511874,0.08212261,0.0390881,-0.03054733,0.01038304,0.05068819,-0.01298933,0.04489519,-0.03251004,-0.08194236,-0.03473287,-0.05525956,0.00136013,-0.03320665,-0.03835431,-0.0089603,-0.02248418,-0.02558281,-0.00713211,0.00428311,0.00101016,-0.03767229,-0.04663262,-0.0225943,-0.00376264,0.03163497,0.05345084,0.04045675,-0.00040527,0.03394346,0.05258234,-0.01580993,-0.01915811,0.03642584,0.02647086,-0.01620237,-0.02329062,0.12277316,0.03458124,-0.00098579,0.06527519,-0.03091033,-0.02077541,-0.07577983,0.00989527,0.02489707,0.07436194,0.03594178,0.0685279,0.01840987,0.03310712,0.00782748,0.01418892,0.05181371,0.12271494,-0.02204987,-0.05962616,0.00584353,-0.02159185,0.01078955,0.03588902,0.00263553,-0.29652083,-0.01537654,-0.07016799,0.00898541,0.03802802,0.0220104,0.03912254,0.03570162,-0.04118785,-0.00071026,-0.06815019,0.06499646,0.03013295,-0.01062031,0.0336495,-0.03629394,0.02793951,0.00007277,0.0336572,-0.06016599,-0.00657824,0.06366173,0.20185187,-0.02584534,0.08853743,0.01679874,0.01512704,0.08815368,0.00282377,0.05145423,0.00086511,-0.03728141,0.00412454,-0.03889616,0.05451141,0.05094464,0.0100627,0.01380829,0.05494876,-0.01969814,-0.04562661,0.05473833,-0.05591664,0.01658646,0.06152036,-0.04813758,-0.00521998,-0.04589589,0.00209663,0.04828786,0.00237943,0.00060765,0.00322422,-0.04178243,0.00076237,0.03782206,0.00851908,-0.02316261,-0.05719789,-0.02246196,0.01701434,0.00572097,0.1073041,0.1149981,0.06866664],"last_embed":{"hash":"32de7e1690af61e17d7b4ef7c8d33717db3478c8f756eb72b7884af4e98e9773","tokens":447}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02515483,-0.01857175,-0.00371395,0.01293359,-0.0250887,0.02930239,-0.01009987,-0.01889394,-0.03553721,-0.0313375,-0.00395,-0.02460255,-0.00608296,-0.00350923,0.01702396,0.0079076,-0.03831419,0.01064388,0.03690515,-0.01760469,-0.07104564,0.0243624,0.03092957,0.02282938,0.07172848,0.02950333,-0.00875806,-0.00551781,0.05551071,0.04110532,0.02314494,0.01502595,0.01325842,-0.01686662,-0.05778055,0.04713153,0.02016539,-0.05428192,0.04579868,-0.01480045,-0.02293474,-0.01481283,-0.0041034,0.01182226,-0.12288654,0.04411189,0.00310953,-0.03602261,-0.03471926,-0.01710667,0.02275333,-0.04513243,0.01134761,-0.02917455,0.03218423,-0.00623019,-0.01256571,-0.07081814,-0.05331692,-0.03146023,-0.05242758,-0.02634429,0.02234106,-0.02382508,0.03882661,-0.01572777,-0.00273318,-0.05047407,-0.03256099,0.00449694,-0.00439019,-0.04238972,0.05887322,-0.01337732,0.04336436,0.03838142,0.00627344,-0.00090695,-0.04088641,0.01109222,-0.07247992,0.01832949,0.02408064,0.09862,0.00441136,-0.04990878,0.06762133,-0.1257115,-0.02186331,0.02152383,0.02178424,0.00726536,-0.07416661,-0.00122382,0.01317527,-0.01913454,-0.011591,-0.01109097,-0.02086938,-0.02308529,0.0352309,-0.03266882,-0.10144728,-0.02058757,-0.04100551,-0.04258763,-0.02433255,0.07759793,-0.02468841,-0.00666948,0.00647549,-0.02085769,-0.00220947,-0.04447588,-0.01115524,0.01026493,0.00934088,-0.01606276,-0.0224497,0.03820218,0.02380159,0.02870789,0.01711817,-0.02907596,-0.00367494,0.04332161,0.00028403,0.00259283,-0.04261899,-0.03575381,0.02315425,0.04172764,-0.0364514,0.03963006,0.03227481,0.02456352,0.0128297,-0.06664397,0.00729593,0.00396655,0.05026655,-0.02727591,-0.03422634,-0.02517219,-0.00117458,0.04808485,-0.02304217,-0.01759003,-0.03448504,0.05056504,0.00872982,-0.06663805,-0.05632883,-0.02308791,-0.01437138,0.06042634,0.06039094,0.01820969,0.05785735,-0.02289553,0.00269656,-0.0014019,-0.04792835,-0.01480516,-0.00003148,0.0027798,-0.06756049,0.005172,-0.01323487,-0.02668714,0.06106379,0.00351716,-0.00424184,-0.0125476,0.02831156,-0.01895371,0.02601339,-0.02500207,-0.02591898,0.03652563,0.03509744,0.0192907,0.05951592,0.02005523,0.02042588,0.05369869,-0.07250674,0.0383751,0.09335311,0.0208263,0.00926122,-0.00551605,0.00885258,0.01789294,0.01738039,0.05476981,-0.0190257,-0.04839507,0.01704193,0.04597321,-0.01268455,-0.02018331,-0.01315364,-0.05480087,0.01737879,-0.00499873,-0.05914954,-0.00934421,0.01199737,0.00130107,0.08901639,-0.01013022,0.04669004,0.01213194,0.02886609,-0.00641936,0.0339861,0.05429474,0.00246875,0.00694138,0.0463637,-0.02323716,-0.06358665,-0.01308371,0.01420309,-0.03958109,0.00202601,0.02511841,0.02260123,0.00672657,-0.03023754,0.10177609,0.03970118,-0.01715515,-0.05024324,0.01094302,0.04052734,0.02545498,0.02801967,0.00913667,-0.02345157,-0.01129729,-0.01283566,0.00935764,0.01014287,-0.03004035,-0.04638457,0.00757191,0.023084,0.04023905,0.0596279,0.06921715,-0.01663962,0.04666278,0.02360858,-0.00583871,-0.04521201,0.02563906,0.02215724,-0.05240192,0.02659725,-0.05585296,-0.02584559,0.06060742,-0.00257412,-0.01324161,-0.05324891,0.00588182,-0.0017201,-0.0088024,0.08547211,-0.01375688,0.04568714,0.00049677,-0.00714975,-0.00052001,0.03637376,0.01029033,-0.02824295,-0.03001731,0.03058614,-0.03276195,-0.02141519,-0.00322298,-0.03039631,0.01987776,0.02124345,-0.03540784,0.02614225,0.00711415,0.0122325,0.01346143,0.00244001,0.03022899,-0.0466019,-0.04378338,-0.00232418,-0.02843527,-0.05651015,-0.03265971,0.01057026,0.02370434,-0.05544274,-0.05414712,0.07112391,0.03354919,0.00267331,-0.04100125,-0.01983257,-0.02732143,0.00663803,-0.0278954,0.01972783,-0.02880617,-0.02209309,0.04109747,0.03791867,0.01860408,0.00115786,-0.04047745,-0.01731699,0.04880095,-0.08507109,0.00901115,-0.01598344,-0.0717552,-0.03714765,0.02691734,0.02950962,0.0437903,-0.02864504,0.01949152,-0.00770877,-0.0313983,0.02207975,0.07073186,-0.01223663,0.00792379,0.01512927,-0.03569792,-0.01672114,-0.03766013,-0.0213592,0.00636972,-0.00193211,-0.00040511,0.00576376,0.03158099,-0.0266839,-0.01520205,0.00504786,-0.01192814,-0.06696752,0.01934722,0.00218117,0.03731877,-0.03214289,-0.00631628,-0.01942486,0.01201403,-0.0812069,-0.05462443,0.00386366,0.01593402,-0.02115227,0.02454652,0.04255294,0.07615971,-0.07102457,-0.01379809,-0.04213957,-0.05321126,-0.00466423,0.03422736,-0.00755328,-0.006879,-0.01352654,-0.00490975,0.03843806,-0.02104078,-0.03841755,-0.05504888,-0.03305583,-0.03169875,-0.00901002,-0.02834016,0.00380316,-0.03148554,0.07845464,-0.07112666,0.03905888,0.01717992,-0.04021958,-0.02673999,0.07257088,-0.02160921,0.0232433,0.00115174,0.02591703,0.00404534,0.05761421,-0.01568186,-0.00196252,-0.02972997,-0.03929885,0.02436706,-0.0376734,-0.01121098,-0.03349726,-0.02749204,-0.01413712,0.01581894,-0.00659377,0.03171752,-0.04527192,0.03317302,0.03385375,-0.03324883,-0.00582691,-0.00483007,0.02093933,-0.02427927,0.06434931,0.01747441,-0.02393147,0.0703845,-0.06353289,-0.00147311,0.07559521,0.01663083,-0.02358266,-0.0745516,-0.031814,-0.00631672,-0.04374768,-0.01757943,0.04746706,0.02420755,-0.01336254,0.02597078,-0.03581259,-0.02616488,-0.01532312,0.02541,-0.00148867,0.05014778,-0.01860272,-0.00611071,-0.00120366,0.06283288,0.02789829,0.00932865,0.00079517,-0.00476579,0.02716874,0.02264757,0.00361543,0.07173287,0.04748402,0.02646527,-0.04186299,0.07438687,0.04054594,0.00415767,-0.02668973,0.06365648,0.09990256,0.01315865,-0.09987544,-0.00655761,-0.01686396,-0.01673872,0.02238274,0.0022821,-0.01105584,0.01099766,0.01033546,-0.03511852,-0.07865717,0.07622319,-0.05090093,0.03042424,-0.04275637,-0.01709098,-0.01172463,-0.0156569,-0.00710543,0.04508103,-0.00310935,-0.06084159,-0.02795525,0.02369827,-0.02261638,0.01166748,-0.04621129,0.00568549,0.01518059,0.02762627,0.03206868,-0.00053147,0.01050183,0.03919617,0.00978577,0.01982926,0.00087188,-0.03203212,-0.02699734,-0.02572573,0.05572502,-0.02562044,0.02083826,-0.0213351,0.01563649,0.08966507,-0.02390154,-0.05442139,0.04138318,0.01200027,0.00057135,0.02617718,0.01663517,0.05983383,-0.06350602,0.00477692,0.01940241,-0.03704695,-0.01372417,0.0388002,0.02835648,0.00423112,-0.01745651,0.01702265,0.02338092,-0.05778116,0.04038454,-0.01321419,0.03909748,-0.03737238,-0.02772991,-0.00868825,0.02245174,0.03054453,-0.01781874,0.00380195,0.00172886,0.04025351,0.01543741,-0.03601528,0.02289153,0.03049985,0.0026166,-0.00076202,-0.02345898,-0.03272606,-0.02772872,0.01142798,0.02249609,0.02714582,-0.07353638,0.05970133,0.01179613,0.04726608,0.02716429,0.00634569,0.00634021,0.01441823,-0.03385052,0.00054505,0.11290656,0.03982485,-0.11727212,0.02936233,0.09608173,-0.00197827,-0.03175287,-0.0068406,-0.01915054,-0.0197068,0.0361042,-0.05941809,0.04909593,-0.00946815,0.07238658,-0.02940927,-0.04348034,0.01325725,-0.02329734,0.03594295,0.0118133,-0.01427451,0.06049625,0.02212085,-0.04628197,0.03203024,0.02876061,-0.00337592,-0.06265609,0.05768004,0.00510176,0.00452244,0.01695333,0.01048864,-0.02218534,-0.04395458,0.00690546,-0.02934783,0.03523925,-0.00017428,0.00783634,0.00417858,-0.02085806,0.08993454,-0.04065633,-0.05271617,-0.01089821,-0.01803482,-0.00946005,-0.01099656,-0.03766962,0.00436988,-0.03174023,-0.02219705,-0.03529061,0.02703344,-0.00739467,0.04156249,-0.01474788,0.04935182,0.00379058,0.00396091,0.02875858,-0.05832404,0.01447906,0.07125728,0.05433269,-0.00563968,-0.04424349,-0.00966992,0.06757893,-0.00394348,0.04310947,-0.01472486,-0.02442521,0.0109961,0.02992613,-0.07278816,-0.09267429,-0.01878572,0.02502424,0.01822485,0.06966905,-0.02541517,-0.00688368,-0.05615175,-0.00449328,0.02220379,0.04268147,-0.03826705,0.00153621,0.02671438,-0.01723067,-0.01744148,0.00826034,0.04586796,-0.01489249,-0.00918738,0.09057656,0.01034958,-0.08301987,-0.03995405,0.00930036,-0.0186215,-0.01702855,0.01258168,-0.0456621,-0.00474513,0.03466216,0.01303458,-0.01497731,-0.00844982,-0.08950302,-0.00975722,0.01748121,0.03981936,0.06114474,0.01933845,-0.04777573,0.00907798,0.00427065,-0.03111262,-0.05192775,0.00823351,-0.00861036,-0.02217542,-0.02391428,-0.0271357,0.00825401,0.01302584,-0.03138288,-0.00711697,-0.02445734,-0.03411682,-0.03382383,0.05100502,-0.01681679,0.02984851,-0.01583908,0.02073304,-0.05116462,-0.01156106,0.00348076,0.02493477,-0.00204516,-0.04007542,0.01483698,-0.02597395,-0.02865599,0.04554377,-0.0215744,-0.01257761,0.03621573,0.02277642,-0.01052849,0.0318765,-0.01651895,-0.01179375,-0.05386186,-0.01216641,-0.0490226,0.06114567,0.01308017,-0.08959975,-0.01238205,0.01411455,0.06004608,0.02051779,-0.01109209,-0.00690119,0.03571086,-0.03537016,-0.00804917,-0.0249268,0.00342649,-0.06189125,-0.04846932,0.01675855,0.01165724,0.03653847,-0.00704312,0.03629797,-0.06319386,-0.01846072,-0.0046517,0.05450951,0.0008161,-0.01597526,-0.02123599,-0.07290298,8.4e-7,0.05335348,-0.00019434,0.020325,-0.02832604,-0.027067,0.00053274,-0.05138643,0.02522849,0.01156792],"last_embed":{"tokens":966,"hash":"6dz9ba"}}},"last_read":{"hash":"6dz9ba","at":1752940658911},"class_name":"SmartSource","outlinks":[{"title":"docker","target":"docker","line":13},{"title":"FRP","target":"FRP","line":13},{"title":"linux","target":"linux","line":14},{"title":"windows系统","target":"windows系统","line":15},{"title":"docker","target":"docker","line":16},{"title":"#服务端配置","target":"#服务端配置","line":20},{"title":"docker","target":"docker","line":25},{"title":"#客户端配置","target":"#客户端配置","line":30},{"title":"proxies","target":"proxies","line":77},{"title":"proxies","target":"proxies","line":85},{"title":"proxies","target":"proxies","line":92},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":106},{"title":"TCP协议","target":"TCP协议","line":107},{"title":"SSH协议","target":"SSH协议","line":108},{"title":"TCP协议","target":"TCP协议","line":108}],"blocks":{"#简介(问题记录)":[1,31],"#简介(问题记录)#{1}":[2,17],"#---frontmatter---":[5,17],"#简介(问题记录)#{2}":[18,21],"#简介(问题记录)#{3}":[22,22],"#简介(问题记录)#客户端搭建(FRPC)":[23,31],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建":[25,31],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{1}":[26,28],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{2}":[29,29],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{3}":[30,30],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{4}":[31,31],"#基础设置":[32,111],"#基础设置#服务端配置":[34,66],"#基础设置#服务端配置#{1}":[35,60],"#基础设置#服务端配置#{2}":[61,64],"#基础设置#服务端配置#{3}":[65,66],"#基础设置#客户端配置":[67,111],"#基础设置#客户端配置#{1}":[68,102],"#基础设置#客户端配置#{2}":[103,104],"#基础设置#客户端配置#{3}":[105,107],"#基础设置#客户端配置#{4}":[108,108],"#基础设置#客户端配置#{5}":[109,111]},"last_import":{"mtime":1731396397364,"size":3460,"at":1748488129023,"hash":"6dz9ba"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md","last_embed":{"hash":"6dz9ba","at":1752940658911}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08049636,-0.03771634,-0.02085551,-0.06617755,0.0088058,-0.05444448,0.01383331,0.03219431,0.05196202,0.00154274,0.00970228,-0.08082177,0.02090403,0.04491688,0.02033748,0.03204303,-0.02955866,0.00757067,-0.02608102,0.00719736,0.10555191,-0.037058,-0.03663703,-0.09995133,0.01470564,0.06296198,0.00633078,0.00083468,0.01364634,-0.14111748,-0.01174947,0.00682319,0.00316009,0.01394325,0.0183901,-0.0130985,0.04226031,0.03766961,-0.03984434,0.00876943,-0.0028209,0.01202667,0.00280289,-0.05067609,0.00795756,-0.06269974,-0.04334503,0.01982227,0.0072402,-0.03134548,-0.00882033,-0.00677754,-0.01480283,-0.03613356,-0.03742324,-0.00354023,-0.01661118,0.00425231,0.0297396,-0.03737547,0.04618467,0.04238535,-0.20562051,0.07354406,0.05274628,-0.04358286,-0.00159341,-0.01583462,0.04913446,0.04071657,-0.07183872,0.02074615,-0.02730677,0.04251124,0.03457358,-0.01607482,0.00090238,-0.01695906,0.0041456,-0.05923731,-0.04897878,0.04202566,-0.0142931,-0.00325188,-0.01356806,0.00467265,-0.01297958,-0.01417211,0.01437292,-0.01525103,0.01171357,-0.06436031,0.03558217,-0.00248323,-0.02517992,0.05855427,0.02053583,0.06400425,-0.16151614,0.1090434,-0.04368718,0.00998233,-0.01764521,-0.10906966,0.00292681,-0.030078,-0.01991365,-0.03460252,-0.02951441,0.00159303,-0.07205547,-0.03597641,0.0413691,-0.00450912,0.02473006,0.05668371,-0.00340505,0.00687749,-0.0496645,0.02652672,-0.00089443,-0.0579322,0.05954034,0.0021729,0.0301776,-0.01398351,0.03956445,0.07055175,0.00950411,0.04500326,0.03159874,-0.02874692,-0.00224028,-0.02090445,0.01220698,0.04155132,-0.04929966,0.00504465,0.00007959,-0.00900646,0.01402629,-0.07461491,-0.02385403,-0.09383673,-0.08293546,0.03972372,-0.01694868,0.00166212,0.0540677,-0.09616394,-0.00335125,0.07550598,-0.00502065,0.01351802,-0.03973053,0.02349329,0.08726818,0.16067617,-0.07696366,-0.02247858,-0.02203205,-0.00545398,-0.08323235,0.13553965,0.01351077,-0.066403,-0.05528591,-0.02088976,0.01477724,-0.03478519,0.00822831,-0.01504556,0.01481065,0.01425515,0.07814621,-0.00931544,0.00410288,-0.00913019,0.04143309,0.02317803,0.02581753,-0.03078501,-0.07906173,0.01113931,0.03338067,-0.08243103,-0.03259497,-0.02345934,0.01402596,-0.02506512,-0.10702448,0.01377385,-0.0459029,-0.00469157,-0.05608512,-0.07332212,0.03335802,0.01349981,0.04897265,-0.04908151,0.15723687,0.0685659,-0.03542775,0.04124361,-0.0307467,-0.00744985,-0.00201396,-0.01254327,-0.00189413,0.0218469,-0.03198237,0.02958632,0.02091471,0.03720326,-0.0248201,0.04351704,0.00902227,0.01126948,0.04696564,0.04255708,0.03497487,0.00794812,-0.04746222,-0.21085928,-0.05778747,0.02498244,-0.01783068,0.00832963,-0.00998142,0.0207771,-0.03358347,0.05940482,0.04217276,0.07690045,0.03749723,-0.03811314,-0.00997098,-0.00015969,0.02177368,0.02122738,-0.0314032,0.00050302,0.00940808,-0.0398149,0.03194164,-0.00683548,-0.02853006,0.03546905,-0.03413852,0.13554537,-0.01184687,0.02010124,0.01151144,0.0007689,0.01080565,0.02404286,-0.1016502,0.03551726,0.06676617,-0.03882582,-0.00861362,0.04851148,0.03766152,0.02175541,0.01548868,-0.05293623,-0.08531512,-0.00613811,-0.04719229,-0.01745277,-0.02166525,-0.00704375,0.03013032,0.03975033,0.02013044,0.00673238,0.03529319,-0.03229773,-0.05179352,-0.01446234,-0.02921661,-0.00774374,0.03539212,0.05089354,-0.02342669,0.00489903,0.03674849,0.04469457,-0.01485058,0.00032982,0.01757192,0.02886819,-0.01748693,-0.03466958,0.14582261,-0.00480278,0.00482129,0.07435136,-0.01801427,-0.01482814,-0.02685262,0.0223362,0.05011724,0.0317719,0.03671002,0.02887872,-0.01621655,-0.01609393,0.01757884,0.01287069,0.01132155,0.0865991,-0.043973,-0.05266602,-0.02080966,-0.01554075,0.01267109,0.05359692,0.01235156,-0.32045308,0.00720758,-0.0627971,0.02684101,0.0528972,0.03389257,0.08376719,0.04725429,-0.09320577,0.01105471,-0.03355071,0.02466912,-0.01326946,-0.03823123,0.02078893,-0.01324392,-0.00905343,-0.04292045,0.04120174,-0.03297521,-0.01853487,0.0947773,0.21067823,-0.02439235,0.06276529,0.01240066,0.04598418,0.0631729,0.10871662,-0.03751496,0.04194529,-0.03233418,0.07806576,-0.01572628,0.03813275,0.03194528,-0.02516331,-0.02851626,0.03986571,-0.0320095,-0.06391442,0.0458412,-0.00974413,0.01146052,0.05917973,-0.01273406,-0.02544416,-0.03858228,0.03109243,0.05856996,0.02351559,-0.00936384,-0.02756898,-0.02108381,-0.00487661,0.04036422,0.03938358,-0.00241005,-0.08372992,-0.0210964,-0.03139213,0.03005516,0.10662542,0.11805885,0.02972789],"last_embed":{"hash":"c78f4678490efcf398cea04a084109171e230350c3fb4a817ba2983b9cbf5f27","tokens":505}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.0248849,-0.01297638,-0.00012544,0.0020974,-0.00180898,0.01129445,0.01951749,-0.02677787,-0.00842403,-0.07005318,-0.0136733,0.02452254,-0.01226952,0.01205773,-0.01126081,0.0216022,-0.05938296,0.0187401,-0.00894397,-0.00355054,-0.06180691,-0.00193391,0.01316917,0.01213562,0.06010206,0.00682225,-0.04341029,-0.02622282,0.04037764,0.07035713,0.00105386,-0.01796986,0.01793229,-0.01975457,-0.07844475,0.0285225,0.0145049,-0.00677829,0.01892599,-0.0032199,-0.04755262,-0.01784511,-0.01269868,-0.00497052,-0.10512829,0.06934188,-0.03965939,-0.06935155,-0.00369651,-0.02355892,0.00416167,0.02015725,0.0120326,-0.04899251,0.00488457,0.00829012,-0.0050915,-0.06470536,-0.03734432,-0.07168193,-0.01839055,-0.01749805,-0.02296218,-0.05333046,0.05508742,-0.05140001,0.00859598,-0.04435661,-0.04723796,0.02159728,-0.04324807,-0.04407499,0.04914051,-0.0119926,0.01297641,0.03523419,0.00047157,-0.02307746,-0.02192799,0.04576116,-0.04219079,0.0263289,0.03774984,0.09161307,-0.02102327,-0.04469883,0.03630977,-0.08347001,-0.03111537,0.00924132,0.00421688,0.00392481,-0.05804007,-0.02052771,0.04005492,-0.03560403,0.02306074,0.00274562,0.01147307,-0.03975759,0.01328764,-0.01981208,-0.06729248,0.01720923,-0.02184696,-0.04412666,-0.05998084,0.04190829,-0.04142794,-0.03151898,0.04104785,-0.03258568,-0.02469558,-0.06314614,-0.01761405,0.01992147,-0.00125357,-0.03777347,-0.01360672,0.01561855,0.01658134,0.00988328,0.00949352,-0.02304646,-0.03612124,0.04072642,0.00669822,0.03744657,-0.0353629,-0.03294925,0.0008709,0.0273547,-0.02593393,0.03109944,0.03265928,0.01433549,0.00023728,-0.05439141,0.01505424,-0.01183722,0.03891655,-0.04240992,-0.02949828,-0.01679953,0.06415755,0.01613073,-0.0282731,-0.00850993,-0.03481269,0.04978679,0.03532783,-0.03265308,-0.06170938,-0.02788533,-0.06060787,0.02687373,0.03726286,0.05871927,0.01803827,0.00956772,0.01877565,0.00903637,-0.0618549,-0.02083769,-0.01690695,0.04257671,-0.05982235,-0.00225369,-0.03142252,-0.01280701,0.06054574,0.03363362,-0.01380294,0.0151706,0.04453747,-0.03030145,0.04756573,-0.01212191,-0.00265869,0.04794145,0.05953434,-0.0356723,0.05183421,0.03896291,-0.02524773,0.07611217,-0.04995856,0.03695134,0.07420257,0.03257503,0.03111621,0.01087262,-0.00082377,-0.02521481,0.00857138,0.04480712,-0.02346128,-0.02171541,-0.01228036,0.04693072,-0.03624001,0.00677443,-0.02056676,-0.07507114,0.00563589,0.01390724,-0.04248838,-0.02002903,0.03516417,0.02344436,0.05592578,0.01422558,0.09168421,0.00337936,0.03923803,-0.02893462,0.00967162,0.04650536,-0.00908007,-0.00283579,0.04718276,-0.01828291,-0.05780035,0.01119532,0.02203882,-0.06556335,-0.01253172,0.00398145,0.01228577,0.00135268,-0.05243416,0.09094588,0.02738209,0.01495319,-0.05917222,0.02451442,0.0455538,0.03452892,0.03120879,0.02451869,0.00787328,-0.03603194,-0.0180585,-0.01857904,0.00560039,-0.00816389,-0.05205248,0.01338371,0.02018523,0.05927657,0.07704923,0.05623133,-0.02265658,0.08590425,0.01231826,-0.03598918,-0.04565974,0.04642734,-0.03167741,-0.02649076,0.02497408,-0.03093423,-0.00947273,0.08261311,-0.01968466,-0.01483823,-0.03542624,0.01714393,0.03493129,-0.00269123,0.09825201,-0.02033558,0.04550328,-0.00586736,-0.01621613,0.01621077,0.05472537,-0.01533896,-0.05514552,0.01364938,0.04011437,-0.00724916,-0.00168835,-0.00464492,-0.00353113,0.05842295,-0.0031745,-0.01275317,0.04004418,-0.00408857,0.00897055,-0.03076353,-0.01662626,0.02236767,-0.03908708,-0.03638994,-0.0199341,-0.00382889,-0.01294919,-0.06025575,-0.0165955,0.0285267,-0.07791369,-0.0131778,0.06377269,0.0406684,0.00244759,-0.05374813,0.00442371,-0.02550455,0.00663779,-0.02712135,-0.00262727,0.0157422,-0.04756665,0.01989357,0.03241679,0.03066956,0.00323585,-0.02496031,-0.03589672,0.06052759,-0.07477737,0.02157769,-0.01608748,-0.08989,-0.01734042,0.02550086,0.01562407,0.04375364,-0.04501499,0.02983907,-0.03421364,-0.0328831,0.00660553,0.06080606,0.0243516,-0.03692554,-0.00279015,-0.04128798,0.02885307,-0.02226313,0.01867323,-0.01595086,0.02939852,-0.00298068,0.00325867,0.02110223,-0.02268875,-0.003406,0.03154635,-0.00460869,-0.06998225,0.03754468,0.00914904,0.00191193,-0.01825409,-0.02802306,0.00928674,-0.01549983,-0.07941478,-0.03028303,-0.03529477,0.0136032,0.00903464,0.02070258,0.02413432,0.06047602,-0.08062435,0.02385507,-0.03795104,-0.04339347,-0.02001697,-0.0080577,0.03891034,-0.00937237,-0.04456308,0.0129911,0.00366623,-0.01282032,-0.03726792,-0.06677557,-0.03843559,-0.00948245,0.01910591,-0.00555149,-0.04025351,0.0136363,0.09420628,-0.03130907,0.05092813,0.01007393,-0.07580587,-0.04341921,0.06293561,0.00791543,0.01835818,0.02266165,0.02334052,0.0096997,0.03905006,0.01415492,-0.00950955,-0.02960959,-0.04983023,0.01879744,-0.0504209,-0.00299846,0.00505171,-0.02872982,-0.0079324,-0.02488971,-0.02875944,0.03776379,-0.01820694,0.01347376,0.03040818,-0.02938005,-0.02708238,0.0035564,0.00779409,-0.01704009,0.05371825,0.00751418,-0.00815113,0.05887548,-0.0334159,-0.0025686,0.07162195,0.01691762,-0.02748602,-0.07939044,-0.05166993,0.01717608,-0.00876082,-0.03571701,-0.00066636,0.03524385,-0.02696327,-0.01802299,-0.01310476,-0.00975839,-0.02144893,0.01065237,-0.05274481,0.05163434,0.00526887,-0.01586802,0.01491292,0.06745835,0.01980007,-0.01355468,-0.01465026,-0.03412703,0.03978746,-0.00906273,-0.00656907,0.04670617,0.0693993,0.01904096,-0.03803968,0.05243703,0.02189236,0.03733085,-0.03073684,0.01357372,0.09439704,-0.00511712,-0.08914471,-0.00443929,0.00694349,-0.03237762,-0.00026227,-0.01564133,-0.02007006,-0.00325137,-0.00781808,-0.03555037,-0.08113185,0.05599638,-0.01633141,0.03973609,-0.01727282,-0.01855842,-0.00266432,-0.01364348,0.00924189,0.05011039,-0.02983641,-0.0740784,-0.0121646,0.01543452,0.00350712,-0.02167843,-0.05944191,0.00627605,0.00916605,0.03156029,0.03116239,0.04318719,-0.00920778,0.05923055,-0.0089574,0.04722638,-0.00940664,-0.03498501,-0.00723321,-0.0085627,0.04550256,-0.00673843,0.00348266,-0.03570563,0.05089485,0.1090764,-0.02783971,-0.02907025,0.01237295,0.02919994,-0.01102444,-0.02012113,0.02872506,0.04381227,-0.04710133,0.01246122,0.04758339,-0.05495295,-0.02102192,0.04296131,0.06631844,-0.00711263,0.02078506,-0.00651507,0.03851137,-0.02543501,0.0273337,0.00759944,-0.00540426,-0.02933633,-0.02440586,0.01311227,0.00417592,0.05482342,-0.03597917,0.0533751,0.02536228,0.03462804,0.02587276,-0.02934689,0.02284048,0.02572309,0.00994776,0.04387032,0.01564667,-0.03039742,-0.01342259,0.0359722,-0.00917156,0.00238468,-0.02062612,0.04420969,0.00152453,0.01107679,0.08783659,-0.00006791,-0.00792873,0.0349002,-0.01675905,-0.01306877,0.07109757,0.01358571,-0.10969614,0.01120773,0.08533195,0.01793503,-0.02705169,0.01181432,-0.0532875,-0.01068464,0.0360366,-0.01786435,0.03941775,-0.01539706,0.07446414,-0.0457577,-0.01699405,-0.00127626,0.00632146,0.00580942,0.00871039,0.00597113,0.02694262,0.01323886,-0.05843458,0.04219315,0.0398116,0.02655788,-0.05018397,0.03138095,-0.00798677,0.00209651,0.05119913,0.00688513,-0.01276876,-0.0756827,-0.01578343,-0.02022073,0.00329641,-0.03658539,-0.00736562,-0.02485203,-0.03599441,0.05016775,-0.05724295,-0.08482148,-0.00639344,-0.00764737,-0.06161996,-0.05405935,-0.01227086,-0.01307995,-0.0454153,-0.03126603,0.00755156,0.01110848,0.03338642,0.00948877,-0.01198184,0.04329882,-0.00667225,-0.03424537,0.01767564,-0.05392757,0.02015592,0.05910013,0.04454801,-0.0183815,-0.02964177,0.00073779,0.02993665,-0.0006582,0.04511719,-0.04436832,-0.01987377,0.02608938,0.01523732,-0.04583496,-0.08943962,-0.02686043,0.01346887,0.02042685,0.03412975,0.02018257,-0.00630349,-0.04351909,-0.00290073,0.05367698,0.04146295,-0.04554184,-0.00694018,0.01747911,-0.02392359,-0.01070825,0.03801259,0.02293303,0.01805144,0.00260473,0.11545833,0.01403679,-0.04494632,-0.02620276,0.06083864,-0.01333453,-0.00425929,0.00544311,-0.03008499,0.02895416,0.0405476,-0.00032696,-0.03061792,-0.02346291,-0.06132343,-0.03361998,0.04639228,0.03051442,0.06177685,0.04650797,-0.01310275,0.00574853,0.02000118,-0.04038176,-0.02829219,0.02168975,-0.01348373,-0.02940173,0.01326946,-0.00607019,-0.01888841,0.04512833,-0.04455218,0.00580445,0.00432405,-0.02745578,-0.01746309,0.02415235,-0.01532523,0.01742859,-0.03917902,0.0578062,-0.02478499,-0.01430071,-0.03913796,0.00108159,0.02581128,-0.06939267,0.00540016,-0.03572497,-0.01429928,0.03912662,0.00134657,0.00303721,0.02364611,0.03097254,-0.00106656,0.02359871,-0.06553195,-0.02917143,-0.06195988,0.00269917,-0.02295571,0.06440562,0.00492192,-0.07241626,0.00212306,0.06284329,0.03580103,0.01305226,-0.01147076,-0.0153418,0.03134292,-0.00606376,-0.00265054,0.00179631,0.00517142,-0.04424269,-0.02989724,-0.03501532,0.02752368,0.01852947,-0.01757207,0.07076634,-0.08841498,-0.00813778,-0.01724248,0.02809193,0.01304264,-0.0185357,-0.00426164,-0.06829056,8.7e-7,0.04929681,0.02640244,-0.00284325,-0.03710324,-0.00030241,-0.00775395,-0.05534725,0.03297747,-0.00139104],"last_embed":{"hash":"q0xnvm","tokens":650}}},"text":null,"length":0,"last_read":{"hash":"q0xnvm","at":1748397845503},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置","lines":[32,111],"size":1551,"outlinks":[{"title":"proxies","target":"proxies","line":46},{"title":"proxies","target":"proxies","line":54},{"title":"proxies","target":"proxies","line":61},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":75},{"title":"TCP协议","target":"TCP协议","line":76},{"title":"SSH协议","target":"SSH协议","line":77},{"title":"TCP协议","target":"TCP协议","line":77}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)","lines":[1,31],"size":690,"outlinks":[{"title":"docker","target":"docker","line":13},{"title":"FRP","target":"FRP","line":13},{"title":"linux","target":"linux","line":14},{"title":"windows系统","target":"windows系统","line":15},{"title":"docker","target":"docker","line":16},{"title":"#服务端配置","target":"#服务端配置","line":20},{"title":"docker","target":"docker","line":25},{"title":"#客户端配置","target":"#客户端配置","line":30}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#{1}","lines":[2,17],"size":354,"outlinks":[{"title":"docker","target":"docker","line":12},{"title":"FRP","target":"FRP","line":12},{"title":"linux","target":"linux","line":13},{"title":"windows系统","target":"windows系统","line":14},{"title":"docker","target":"docker","line":15}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#---frontmatter---","lines":[5,17],"size":309,"outlinks":[{"title":"docker","target":"docker","line":9},{"title":"FRP","target":"FRP","line":9},{"title":"linux","target":"linux","line":10},{"title":"windows系统","target":"windows系统","line":11},{"title":"docker","target":"docker","line":12}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#{2}","lines":[18,21],"size":94,"outlinks":[{"title":"#服务端配置","target":"#服务端配置","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#{3}","lines":[22,22],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)","lines":[23,31],"size":225,"outlinks":[{"title":"docker","target":"docker","line":3},{"title":"#客户端配置","target":"#客户端配置","line":8}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建","lines":[25,31],"size":209,"outlinks":[{"title":"docker","target":"docker","line":1},{"title":"#客户端配置","target":"#客户端配置","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{1}","lines":[26,28],"size":129,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{2}","lines":[29,29],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{3}","lines":[30,30],"size":22,"outlinks":[{"title":"#客户端配置","target":"#客户端配置","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{4}","lines":[31,31],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#服务端配置": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#服务端配置","lines":[34,66],"size":693,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#服务端配置#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#服务端配置#{1}","lines":[35,60],"size":579,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#服务端配置#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#服务端配置#{2}","lines":[61,64],"size":99,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#服务端配置#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#服务端配置#{3}","lines":[65,66],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置","lines":[67,111],"size":848,"outlinks":[{"title":"proxies","target":"proxies","line":11},{"title":"proxies","target":"proxies","line":19},{"title":"proxies","target":"proxies","line":26},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":40},{"title":"TCP协议","target":"TCP协议","line":41},{"title":"SSH协议","target":"SSH协议","line":42},{"title":"TCP协议","target":"TCP协议","line":42}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置#{1}","lines":[68,102],"size":623,"outlinks":[{"title":"proxies","target":"proxies","line":10},{"title":"proxies","target":"proxies","line":18},{"title":"proxies","target":"proxies","line":25}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置#{2}","lines":[103,104],"size":53,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置#{3}","lines":[105,107],"size":105,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":2},{"title":"TCP协议","target":"TCP协议","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置#{4}","lines":[108,108],"size":49,"outlinks":[{"title":"SSH协议","target":"SSH协议","line":1},{"title":"TCP协议","target":"TCP协议","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md#基础设置#客户端配置#{5}","lines":[109,111],"size":5,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08628837,-0.05860195,0.02573346,-0.01811953,0.01581034,-0.00797483,0.00155189,0.02501294,0.05500248,-0.01585481,0.02678815,-0.06074724,0.03739225,0.00262488,0.01492165,0.05200024,0.0179563,0.03887929,0.02395838,-0.05227077,0.06084154,-0.01536886,0.00444096,-0.10484784,-0.0023879,0.0603305,-0.00237527,-0.00876505,0.02288805,-0.15573402,0.03150025,0.04064725,-0.04160419,0.01680895,0.0208896,-0.02896039,0.00816002,0.0180219,-0.03223446,0.04316963,0.03645829,0.00065451,-0.02331025,-0.01600302,-0.01731028,-0.04763146,-0.01537082,-0.00847763,0.01505366,-0.06955207,-0.0163743,-0.04515882,-0.0137947,-0.06700961,-0.01224162,0.0493832,0.04357249,0.0102459,0.0618735,-0.10511828,0.04150749,0.0254985,-0.22546256,0.06859744,0.07104047,-0.01173931,-0.00001441,-0.00064856,0.01553913,0.05542628,-0.08231489,0.05186268,-0.02245709,0.05301014,0.06768247,-0.02451147,-0.01013297,-0.02255951,-0.00747863,0.00422518,-0.01406252,0.00515378,-0.021355,0.00263593,-0.0357669,0.00762982,-0.00180724,0.00391971,-0.00126792,-0.00820142,-0.0259381,-0.05052589,0.04550911,0.03815584,-0.01760926,0.0067438,0.01606583,0.05476764,-0.12623535,0.08700992,-0.04807895,0.02526322,-0.01526702,-0.05603064,0.0505196,-0.05886219,-0.0285477,-0.02304025,-0.01034755,-0.00215692,-0.04007163,-0.02605468,0.05436355,-0.05302609,0.04622875,0.02630197,-0.00439774,0.00697795,-0.0571119,-0.00228144,-0.02700378,0.00349089,0.04169467,-0.01390859,-0.02391375,-0.06042103,0.06036062,0.07835985,-0.00773262,0.01318745,0.03637406,-0.03356047,0.01608719,0.01379824,0.03295974,0.04018484,0.01507684,-0.05235003,-0.01720574,-0.05779755,0.01907253,-0.08295827,0.01433568,-0.09812737,-0.05603548,0.05936956,-0.06170814,0.04353155,0.05306247,-0.06677296,-0.02512891,0.02316805,-0.00527515,-0.05932558,0.00382987,0.01181252,0.10621005,0.15787168,-0.04240648,-0.02779953,-0.06421627,0.00741703,-0.10089189,0.13006467,0.02742612,-0.05630181,-0.04260026,0.00324968,-0.0186427,-0.02607865,-0.01522968,-0.04289887,0.02097337,-0.02995284,0.07592762,-0.01073893,-0.03380525,-0.03800046,0.02378789,0.02312695,0.03129502,-0.02527165,-0.04407004,-0.01271484,-0.01487859,-0.07107691,-0.06564786,-0.04379311,-0.01050664,-0.05930753,-0.11875987,0.00446177,0.04916147,0.01803366,-0.02445258,-0.0676028,0.02134254,0.01585572,0.08664263,-0.0413457,0.10434753,0.0210479,0.01519789,0.01861522,-0.06747174,-0.00251589,-0.0196948,0.00593357,0.03614054,-0.02328573,-0.03633431,0.0233585,-0.01480859,0.01141893,-0.0121039,0.05266468,-0.01242803,0.01452221,0.00345336,0.05575364,0.08266416,0.01540608,-0.06491511,-0.20599373,-0.04456686,0.00708897,-0.04379893,0.02927554,-0.01891481,0.02572755,0.01202685,0.0100686,0.02390498,0.04505565,-0.01755405,-0.03999947,0.01439417,-0.0024797,-0.01822424,0.0674981,-0.01626235,-0.0144853,-0.00049104,-0.03051921,0.07321011,-0.05972066,-0.049798,0.05073953,0.0233892,0.13951924,-0.00691625,0.05310201,0.02375282,0.03932693,0.07755049,0.01527466,-0.12011077,0.00511874,0.08212261,0.0390881,-0.03054733,0.01038304,0.05068819,-0.01298933,0.04489519,-0.03251004,-0.08194236,-0.03473287,-0.05525956,0.00136013,-0.03320665,-0.03835431,-0.0089603,-0.02248418,-0.02558281,-0.00713211,0.00428311,0.00101016,-0.03767229,-0.04663262,-0.0225943,-0.00376264,0.03163497,0.05345084,0.04045675,-0.00040527,0.03394346,0.05258234,-0.01580993,-0.01915811,0.03642584,0.02647086,-0.01620237,-0.02329062,0.12277316,0.03458124,-0.00098579,0.06527519,-0.03091033,-0.02077541,-0.07577983,0.00989527,0.02489707,0.07436194,0.03594178,0.0685279,0.01840987,0.03310712,0.00782748,0.01418892,0.05181371,0.12271494,-0.02204987,-0.05962616,0.00584353,-0.02159185,0.01078955,0.03588902,0.00263553,-0.29652083,-0.01537654,-0.07016799,0.00898541,0.03802802,0.0220104,0.03912254,0.03570162,-0.04118785,-0.00071026,-0.06815019,0.06499646,0.03013295,-0.01062031,0.0336495,-0.03629394,0.02793951,0.00007277,0.0336572,-0.06016599,-0.00657824,0.06366173,0.20185187,-0.02584534,0.08853743,0.01679874,0.01512704,0.08815368,0.00282377,0.05145423,0.00086511,-0.03728141,0.00412454,-0.03889616,0.05451141,0.05094464,0.0100627,0.01380829,0.05494876,-0.01969814,-0.04562661,0.05473833,-0.05591664,0.01658646,0.06152036,-0.04813758,-0.00521998,-0.04589589,0.00209663,0.04828786,0.00237943,0.00060765,0.00322422,-0.04178243,0.00076237,0.03782206,0.00851908,-0.02316261,-0.05719789,-0.02246196,0.01701434,0.00572097,0.1073041,0.1149981,0.06866664],"last_embed":{"hash":"32de7e1690af61e17d7b4ef7c8d33717db3478c8f756eb72b7884af4e98e9773","tokens":447}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02515483,-0.01857175,-0.00371395,0.01293359,-0.0250887,0.02930239,-0.01009987,-0.01889394,-0.03553721,-0.0313375,-0.00395,-0.02460255,-0.00608296,-0.00350923,0.01702396,0.0079076,-0.03831419,0.01064388,0.03690515,-0.01760469,-0.07104564,0.0243624,0.03092957,0.02282938,0.07172848,0.02950333,-0.00875806,-0.00551781,0.05551071,0.04110532,0.02314494,0.01502595,0.01325842,-0.01686662,-0.05778055,0.04713153,0.02016539,-0.05428192,0.04579868,-0.01480045,-0.02293474,-0.01481283,-0.0041034,0.01182226,-0.12288654,0.04411189,0.00310953,-0.03602261,-0.03471926,-0.01710667,0.02275333,-0.04513243,0.01134761,-0.02917455,0.03218423,-0.00623019,-0.01256571,-0.07081814,-0.05331692,-0.03146023,-0.05242758,-0.02634429,0.02234106,-0.02382508,0.03882661,-0.01572777,-0.00273318,-0.05047407,-0.03256099,0.00449694,-0.00439019,-0.04238972,0.05887322,-0.01337732,0.04336436,0.03838142,0.00627344,-0.00090695,-0.04088641,0.01109222,-0.07247992,0.01832949,0.02408064,0.09862,0.00441136,-0.04990878,0.06762133,-0.1257115,-0.02186331,0.02152383,0.02178424,0.00726536,-0.07416661,-0.00122382,0.01317527,-0.01913454,-0.011591,-0.01109097,-0.02086938,-0.02308529,0.0352309,-0.03266882,-0.10144728,-0.02058757,-0.04100551,-0.04258763,-0.02433255,0.07759793,-0.02468841,-0.00666948,0.00647549,-0.02085769,-0.00220947,-0.04447588,-0.01115524,0.01026493,0.00934088,-0.01606276,-0.0224497,0.03820218,0.02380159,0.02870789,0.01711817,-0.02907596,-0.00367494,0.04332161,0.00028403,0.00259283,-0.04261899,-0.03575381,0.02315425,0.04172764,-0.0364514,0.03963006,0.03227481,0.02456352,0.0128297,-0.06664397,0.00729593,0.00396655,0.05026655,-0.02727591,-0.03422634,-0.02517219,-0.00117458,0.04808485,-0.02304217,-0.01759003,-0.03448504,0.05056504,0.00872982,-0.06663805,-0.05632883,-0.02308791,-0.01437138,0.06042634,0.06039094,0.01820969,0.05785735,-0.02289553,0.00269656,-0.0014019,-0.04792835,-0.01480516,-0.00003148,0.0027798,-0.06756049,0.005172,-0.01323487,-0.02668714,0.06106379,0.00351716,-0.00424184,-0.0125476,0.02831156,-0.01895371,0.02601339,-0.02500207,-0.02591898,0.03652563,0.03509744,0.0192907,0.05951592,0.02005523,0.02042588,0.05369869,-0.07250674,0.0383751,0.09335311,0.0208263,0.00926122,-0.00551605,0.00885258,0.01789294,0.01738039,0.05476981,-0.0190257,-0.04839507,0.01704193,0.04597321,-0.01268455,-0.02018331,-0.01315364,-0.05480087,0.01737879,-0.00499873,-0.05914954,-0.00934421,0.01199737,0.00130107,0.08901639,-0.01013022,0.04669004,0.01213194,0.02886609,-0.00641936,0.0339861,0.05429474,0.00246875,0.00694138,0.0463637,-0.02323716,-0.06358665,-0.01308371,0.01420309,-0.03958109,0.00202601,0.02511841,0.02260123,0.00672657,-0.03023754,0.10177609,0.03970118,-0.01715515,-0.05024324,0.01094302,0.04052734,0.02545498,0.02801967,0.00913667,-0.02345157,-0.01129729,-0.01283566,0.00935764,0.01014287,-0.03004035,-0.04638457,0.00757191,0.023084,0.04023905,0.0596279,0.06921715,-0.01663962,0.04666278,0.02360858,-0.00583871,-0.04521201,0.02563906,0.02215724,-0.05240192,0.02659725,-0.05585296,-0.02584559,0.06060742,-0.00257412,-0.01324161,-0.05324891,0.00588182,-0.0017201,-0.0088024,0.08547211,-0.01375688,0.04568714,0.00049677,-0.00714975,-0.00052001,0.03637376,0.01029033,-0.02824295,-0.03001731,0.03058614,-0.03276195,-0.02141519,-0.00322298,-0.03039631,0.01987776,0.02124345,-0.03540784,0.02614225,0.00711415,0.0122325,0.01346143,0.00244001,0.03022899,-0.0466019,-0.04378338,-0.00232418,-0.02843527,-0.05651015,-0.03265971,0.01057026,0.02370434,-0.05544274,-0.05414712,0.07112391,0.03354919,0.00267331,-0.04100125,-0.01983257,-0.02732143,0.00663803,-0.0278954,0.01972783,-0.02880617,-0.02209309,0.04109747,0.03791867,0.01860408,0.00115786,-0.04047745,-0.01731699,0.04880095,-0.08507109,0.00901115,-0.01598344,-0.0717552,-0.03714765,0.02691734,0.02950962,0.0437903,-0.02864504,0.01949152,-0.00770877,-0.0313983,0.02207975,0.07073186,-0.01223663,0.00792379,0.01512927,-0.03569792,-0.01672114,-0.03766013,-0.0213592,0.00636972,-0.00193211,-0.00040511,0.00576376,0.03158099,-0.0266839,-0.01520205,0.00504786,-0.01192814,-0.06696752,0.01934722,0.00218117,0.03731877,-0.03214289,-0.00631628,-0.01942486,0.01201403,-0.0812069,-0.05462443,0.00386366,0.01593402,-0.02115227,0.02454652,0.04255294,0.07615971,-0.07102457,-0.01379809,-0.04213957,-0.05321126,-0.00466423,0.03422736,-0.00755328,-0.006879,-0.01352654,-0.00490975,0.03843806,-0.02104078,-0.03841755,-0.05504888,-0.03305583,-0.03169875,-0.00901002,-0.02834016,0.00380316,-0.03148554,0.07845464,-0.07112666,0.03905888,0.01717992,-0.04021958,-0.02673999,0.07257088,-0.02160921,0.0232433,0.00115174,0.02591703,0.00404534,0.05761421,-0.01568186,-0.00196252,-0.02972997,-0.03929885,0.02436706,-0.0376734,-0.01121098,-0.03349726,-0.02749204,-0.01413712,0.01581894,-0.00659377,0.03171752,-0.04527192,0.03317302,0.03385375,-0.03324883,-0.00582691,-0.00483007,0.02093933,-0.02427927,0.06434931,0.01747441,-0.02393147,0.0703845,-0.06353289,-0.00147311,0.07559521,0.01663083,-0.02358266,-0.0745516,-0.031814,-0.00631672,-0.04374768,-0.01757943,0.04746706,0.02420755,-0.01336254,0.02597078,-0.03581259,-0.02616488,-0.01532312,0.02541,-0.00148867,0.05014778,-0.01860272,-0.00611071,-0.00120366,0.06283288,0.02789829,0.00932865,0.00079517,-0.00476579,0.02716874,0.02264757,0.00361543,0.07173287,0.04748402,0.02646527,-0.04186299,0.07438687,0.04054594,0.00415767,-0.02668973,0.06365648,0.09990256,0.01315865,-0.09987544,-0.00655761,-0.01686396,-0.01673872,0.02238274,0.0022821,-0.01105584,0.01099766,0.01033546,-0.03511852,-0.07865717,0.07622319,-0.05090093,0.03042424,-0.04275637,-0.01709098,-0.01172463,-0.0156569,-0.00710543,0.04508103,-0.00310935,-0.06084159,-0.02795525,0.02369827,-0.02261638,0.01166748,-0.04621129,0.00568549,0.01518059,0.02762627,0.03206868,-0.00053147,0.01050183,0.03919617,0.00978577,0.01982926,0.00087188,-0.03203212,-0.02699734,-0.02572573,0.05572502,-0.02562044,0.02083826,-0.0213351,0.01563649,0.08966507,-0.02390154,-0.05442139,0.04138318,0.01200027,0.00057135,0.02617718,0.01663517,0.05983383,-0.06350602,0.00477692,0.01940241,-0.03704695,-0.01372417,0.0388002,0.02835648,0.00423112,-0.01745651,0.01702265,0.02338092,-0.05778116,0.04038454,-0.01321419,0.03909748,-0.03737238,-0.02772991,-0.00868825,0.02245174,0.03054453,-0.01781874,0.00380195,0.00172886,0.04025351,0.01543741,-0.03601528,0.02289153,0.03049985,0.0026166,-0.00076202,-0.02345898,-0.03272606,-0.02772872,0.01142798,0.02249609,0.02714582,-0.07353638,0.05970133,0.01179613,0.04726608,0.02716429,0.00634569,0.00634021,0.01441823,-0.03385052,0.00054505,0.11290656,0.03982485,-0.11727212,0.02936233,0.09608173,-0.00197827,-0.03175287,-0.0068406,-0.01915054,-0.0197068,0.0361042,-0.05941809,0.04909593,-0.00946815,0.07238658,-0.02940927,-0.04348034,0.01325725,-0.02329734,0.03594295,0.0118133,-0.01427451,0.06049625,0.02212085,-0.04628197,0.03203024,0.02876061,-0.00337592,-0.06265609,0.05768004,0.00510176,0.00452244,0.01695333,0.01048864,-0.02218534,-0.04395458,0.00690546,-0.02934783,0.03523925,-0.00017428,0.00783634,0.00417858,-0.02085806,0.08993454,-0.04065633,-0.05271617,-0.01089821,-0.01803482,-0.00946005,-0.01099656,-0.03766962,0.00436988,-0.03174023,-0.02219705,-0.03529061,0.02703344,-0.00739467,0.04156249,-0.01474788,0.04935182,0.00379058,0.00396091,0.02875858,-0.05832404,0.01447906,0.07125728,0.05433269,-0.00563968,-0.04424349,-0.00966992,0.06757893,-0.00394348,0.04310947,-0.01472486,-0.02442521,0.0109961,0.02992613,-0.07278816,-0.09267429,-0.01878572,0.02502424,0.01822485,0.06966905,-0.02541517,-0.00688368,-0.05615175,-0.00449328,0.02220379,0.04268147,-0.03826705,0.00153621,0.02671438,-0.01723067,-0.01744148,0.00826034,0.04586796,-0.01489249,-0.00918738,0.09057656,0.01034958,-0.08301987,-0.03995405,0.00930036,-0.0186215,-0.01702855,0.01258168,-0.0456621,-0.00474513,0.03466216,0.01303458,-0.01497731,-0.00844982,-0.08950302,-0.00975722,0.01748121,0.03981936,0.06114474,0.01933845,-0.04777573,0.00907798,0.00427065,-0.03111262,-0.05192775,0.00823351,-0.00861036,-0.02217542,-0.02391428,-0.0271357,0.00825401,0.01302584,-0.03138288,-0.00711697,-0.02445734,-0.03411682,-0.03382383,0.05100502,-0.01681679,0.02984851,-0.01583908,0.02073304,-0.05116462,-0.01156106,0.00348076,0.02493477,-0.00204516,-0.04007542,0.01483698,-0.02597395,-0.02865599,0.04554377,-0.0215744,-0.01257761,0.03621573,0.02277642,-0.01052849,0.0318765,-0.01651895,-0.01179375,-0.05386186,-0.01216641,-0.0490226,0.06114567,0.01308017,-0.08959975,-0.01238205,0.01411455,0.06004608,0.02051779,-0.01109209,-0.00690119,0.03571086,-0.03537016,-0.00804917,-0.0249268,0.00342649,-0.06189125,-0.04846932,0.01675855,0.01165724,0.03653847,-0.00704312,0.03629797,-0.06319386,-0.01846072,-0.0046517,0.05450951,0.0008161,-0.01597526,-0.02123599,-0.07290298,8.4e-7,0.05335348,-0.00019434,0.020325,-0.02832604,-0.027067,0.00053274,-0.05138643,0.02522849,0.01156792],"last_embed":{"tokens":966,"hash":"6dz9ba"}}},"last_read":{"hash":"6dz9ba","at":1752940887613},"class_name":"SmartSource","outlinks":[{"title":"docker","target":"docker","line":13},{"title":"FRP","target":"FRP","line":13},{"title":"linux","target":"linux","line":14},{"title":"windows系统","target":"windows系统","line":15},{"title":"docker","target":"docker","line":16},{"title":"#服务端配置","target":"#服务端配置","line":20},{"title":"docker","target":"docker","line":25},{"title":"#客户端配置","target":"#客户端配置","line":30},{"title":"proxies","target":"proxies","line":77},{"title":"proxies","target":"proxies","line":85},{"title":"proxies","target":"proxies","line":92},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":106},{"title":"TCP协议","target":"TCP协议","line":107},{"title":"SSH协议","target":"SSH协议","line":108},{"title":"TCP协议","target":"TCP协议","line":108}],"blocks":{"#简介(问题记录)":[1,31],"#简介(问题记录)#{1}":[2,17],"#---frontmatter---":[5,17],"#简介(问题记录)#{2}":[18,21],"#简介(问题记录)#{3}":[22,22],"#简介(问题记录)#客户端搭建(FRPC)":[23,31],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建":[25,31],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{1}":[26,28],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{2}":[29,29],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{3}":[30,30],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{4}":[31,31],"#基础设置":[32,111],"#基础设置#服务端配置":[34,66],"#基础设置#服务端配置#{1}":[35,60],"#基础设置#服务端配置#{2}":[61,64],"#基础设置#服务端配置#{3}":[65,66],"#基础设置#客户端配置":[67,111],"#基础设置#客户端配置#{1}":[68,102],"#基础设置#客户端配置#{2}":[103,104],"#基础设置#客户端配置#{3}":[105,107],"#基础设置#客户端配置#{4}":[108,108],"#基础设置#客户端配置#{5}":[109,111]},"last_import":{"mtime":1731396397364,"size":3460,"at":1748488129023,"hash":"6dz9ba"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口转发/工具/FRP.md","last_embed":{"hash":"6dz9ba","at":1752940887613}},