"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11426497,-0.0195824,-0.06379683,-0.05906891,-0.01351962,-0.04894095,0.00524221,0.0178392,0.04540066,-0.05544154,0.00370894,-0.04507824,0.05772775,0.0116925,0.08707165,0.03628514,0.0119059,-0.0238302,-0.01669275,0.02763928,0.07045386,-0.01485091,-0.01662807,-0.02359227,0.00931976,0.01172837,-0.01670836,-0.00438661,-0.0184426,-0.12074815,-0.00191433,-0.00044173,0.01481403,0.0597949,-0.00507483,-0.01064387,-0.01964816,0.03259818,-0.04615322,0.06524918,0.06343809,-0.00862682,0.0196218,-0.04589752,-0.04875998,-0.0938203,-0.0443275,-0.01801799,0.02409931,-0.0537103,-0.02890933,0.01500521,-0.01780408,0.01806333,-0.03646534,-0.01560775,-0.00692391,0.05062263,0.05773541,-0.03293594,0.07029048,0.06502786,-0.22047691,0.09141105,0.05308576,-0.02691628,-0.01081431,0.05509753,0.03154047,0.01263146,-0.07651097,0.03658526,-0.0328643,0.03494384,0.02937374,0.02035425,0.01560248,-0.01949535,-0.01739072,-0.02717894,-0.03186987,0.04359232,0.00494072,-0.00687797,-0.03358212,-0.0256909,-0.02686569,-0.01101274,0.02630623,0.01265847,-0.067151,-0.04985254,-0.01303553,0.01429411,0.0236141,0.01546265,0.01335807,0.07542305,-0.15345468,0.0972024,-0.06581631,-0.00790372,-0.01105618,-0.09203634,0.07097234,-0.0088387,-0.01233892,-0.05390059,-0.00960907,-0.00954682,-0.07881577,-0.06551588,0.03038744,-0.01398129,0.04506676,0.04704009,-0.00854813,-0.0096788,-0.00956857,-0.02830605,-0.03407426,0.00435828,0.07212,-0.02297441,-0.00393117,-0.04601837,0.06697993,0.07848355,0.01241012,0.05809681,0.04684474,-0.03260336,-0.04619027,-0.02332652,-0.02990024,0.02237032,-0.03093948,0.02027391,-0.0495258,-0.0114522,-0.0037509,-0.10373084,0.03101909,-0.06382563,-0.06388959,0.08877739,-0.02623187,0.00978923,0.04620687,-0.07639032,0.01487903,0.07631843,-0.02903661,-0.018541,-0.03591191,0.02018815,0.1049431,0.15243219,-0.01606604,0.00221172,0.02436458,-0.02167819,-0.08503183,0.1475506,0.03666994,-0.11291794,-0.05885043,0.01462535,0.01913304,-0.0716196,0.01579196,0.00160553,0.01367197,-0.00347545,0.03616584,-0.04204555,-0.00446978,-0.05109392,-0.00092159,-0.00851371,-0.0249206,-0.03010016,-0.05791406,0.04747128,0.00304926,-0.06187334,-0.04014894,-0.02563742,0.01685188,-0.04794428,-0.07335892,0.000078,-0.05889293,0.00871408,-0.0408597,-0.07157893,0.02529579,0.00354318,0.03702347,-0.06068975,0.11229236,0.03940436,-0.05536396,-0.00246551,-0.07955462,-0.01811754,0.00924499,0.0058816,0.04965731,0.00286655,0.00318805,0.02694583,0.02565052,0.02001187,-0.0298063,-0.01915109,0.02196485,0.0218385,0.03476236,0.04388653,0.00932509,0.0429344,-0.10117033,-0.19865264,-0.03256474,0.06428048,-0.02952302,0.00060978,-0.02884522,0.00339213,-0.02108788,0.08603836,0.06721629,0.1262795,0.02728384,-0.03205343,-0.02103485,-0.00543476,0.01418382,-0.02058794,0.01340763,0.01007661,0.01414952,-0.01284852,0.04769115,-0.03861591,0.03079608,0.04223583,-0.04069494,0.09991509,0.01247688,0.0215461,0.02568346,0.03596856,0.04339521,0.00595694,-0.11943956,0.00449234,0.06018933,-0.00918497,0.0164808,0.02579584,-0.00021591,-0.01737886,0.04174518,0.00890889,-0.10183171,0.00068824,-0.0598217,0.0036126,-0.03318145,-0.02847419,0.0251344,0.03373314,0.02386386,0.03805557,0.03881098,0.01713763,-0.04953398,-0.03175786,-0.06879619,-0.00858932,0.0304712,0.02113244,-0.00488829,0.01438572,0.00249777,-0.01020418,-0.02470378,-0.03923961,0.03378019,-0.02503466,0.00516881,0.00932649,0.16567519,0.00126607,0.00115045,0.01128512,0.01547772,0.00304528,-0.07006398,0.03471506,0.03766396,0.03420444,0.03174507,0.0499886,-0.03299953,-0.04753675,0.04764023,0.04302963,0.02188501,0.06857786,-0.02794755,-0.05464035,-0.02349425,-0.02511872,-0.0008503,0.09180109,-0.0056286,-0.27633104,0.03357691,-0.09042439,0.01141744,0.01785175,0.03500887,0.05437356,0.03928611,-0.0732063,0.01923011,0.00061461,0.04306859,-0.01283601,0.01041154,-0.0343441,-0.00977426,0.0290437,-0.02218219,0.02615958,-0.02036938,-0.05035169,0.0581005,0.17901213,-0.01561901,0.07362719,0.03210806,0.02024162,0.02995221,0.02699186,0.00736034,0.06023843,-0.05904736,0.01691647,-0.02256291,0.09407543,-0.00872248,-0.0004988,0.00247463,0.01358035,-0.02132598,-0.04579899,0.09847467,-0.05527426,0.04473349,0.07382185,0.03715083,-0.01734663,-0.05028501,0.03978996,0.06596731,-0.00709884,-0.03995107,0.0001328,0.01587009,0.0364146,0.03248233,0.06637155,0.01906992,-0.06185075,-0.00400551,0.02064224,-0.03311409,0.06668058,0.08501703,0.05316731],"last_embed":{"hash":"a7917470e0e72fd3eea5f1077c3509519a768c0104f6a4d1e24a1aacb602a0af","tokens":476}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[0.01806307,-0.01612236,-0.01767646,-0.01481474,-0.02837381,-0.05016099,0.03052727,-0.01208628,0.00608446,-0.02170607,-0.01264039,0.01617331,-0.03439267,-0.08163925,0.08855486,0.10420728,0.00275193,-0.02390168,-0.00976947,-0.00917558,-0.0027475,-0.01116243,-0.04074186,0.02593618,-0.02557436,-0.01353032,-0.0316744,-0.0119061,0.00513792,0.01582869,0.10783698,-0.04771902,0.06609817,0.02118573,0.01460489,0.01696448,0.02657193,0.01682672,0.0561661,-0.01058853,-0.08270167,-0.00640864,-0.06512711,-0.00007597,-0.08483507,0.0120641,-0.0497499,-0.0013092,0.03500261,0.00346131,0.00270996,-0.0345199,0.02286746,-0.00749359,-0.03673755,0.02929282,-0.07954479,0.04414904,0.00159461,-0.08108185,-0.0080731,-0.01211455,0.03727697,0.01378153,-0.00129409,0.02336079,0.01885376,-0.0214544,-0.05093404,0.0195175,-0.00556788,-0.05645608,0.06531917,0.00443644,0.03126913,0.02635686,-0.01913228,-0.0033767,-0.01550314,0.06050721,0.0133084,0.00503646,-0.00149608,0.04317307,-0.02159337,-0.0521051,0.05930443,-0.0054561,0.02557991,0.01226955,-0.0376916,-0.0225685,0.00909455,-0.06509002,0.02608607,0.02628054,0.01412929,-0.00256304,0.04215723,-0.02843447,-0.05608575,-0.02078421,-0.02264732,0.0180432,0.03130004,-0.02744152,-0.00490163,0.06623287,-0.02586573,-0.01795734,0.01455483,-0.03834454,0.00202766,-0.0140929,-0.00872616,-0.00787775,0.02133155,0.02264449,0.0585621,0.06613854,-0.00553388,0.06894107,-0.01727482,0.01334854,-0.09441116,-0.04554616,0.02209328,-0.02468342,0.03265607,-0.05765055,-0.00716534,0.058321,-0.04375404,-0.03847741,0.07094086,0.02220398,0.02233753,-0.00169568,-0.09964512,-0.01654769,0.05055179,-0.03994914,0.00762219,-0.03729594,0.01877642,0.03299567,-0.07843861,0.04293424,0.01450048,-0.0065719,0.08043187,-0.03077595,-0.03052122,-0.01400553,-0.03192414,-0.02526989,-0.01998396,-0.01339482,0.00942028,-0.00025859,0.01633228,0.00753379,0.00161175,0.00301773,-0.00431452,0.04780358,-0.00814098,0.03845168,0.02511269,0.00960514,0.01232064,0.05494662,-0.00510625,-0.02813722,0.00233654,-0.03182923,0.02169088,-0.01568944,-0.00336768,0.00336779,0.00964811,-0.05172667,-0.00416623,0.02330108,-0.02725505,0.02902637,-0.04778339,-0.02353657,-0.0259236,-0.001244,0.05096725,0.00757238,0.03139407,0.00334135,0.04119525,0.00007525,-0.01925988,-0.06552651,0.01334175,0.02074455,-0.05392771,-0.02761741,-0.02932057,-0.09516039,0.0593393,0.04850941,0.04989641,-0.07985611,-0.06600365,-0.03470536,-0.11173725,0.02901939,0.0278114,-0.0062613,-0.0007333,0.03893516,0.00351771,-0.02373559,0.0285061,-0.02625151,-0.00717829,0.04278228,-0.02278543,0.0096596,0.01827585,-0.05371701,0.02419236,-0.01207946,0.01794174,-0.00401284,-0.05693714,0.07053414,0.04842528,-0.04158771,-0.0526948,0.03840737,0.03040762,0.01245975,0.02482058,-0.04856035,-0.01222571,0.00939636,0.05922034,-0.03592771,-0.03843559,-0.024727,-0.06773846,0.05622455,0.0359208,0.07298242,0.02492408,0.05407578,-0.03419307,0.07268839,0.00427643,-0.04831652,-0.04074702,-0.04638235,0.04212336,-0.03295736,0.04155732,0.01543692,0.03048035,0.00583853,-0.00977977,-0.04065808,-0.00917317,0.03776231,0.03845757,0.00978776,0.07420148,0.01755632,0.01107378,-0.02991425,-0.05203811,0.03804985,0.04417291,0.01063053,0.0060329,-0.00716554,-0.04509754,-0.0016693,0.01088234,-0.00942814,0.02801093,0.09069715,-0.03630644,0.01805717,0.10773417,0.04863377,0.0123034,-0.03894421,-0.02199333,-0.00644156,-0.02853049,-0.04055993,-0.00432917,0.0656222,0.02795527,-0.07628538,0.01763993,0.02388899,-0.0478957,-0.00491821,0.02078363,0.03363078,0.00289502,-0.00545414,0.02963039,0.00538511,-0.02760068,0.00070745,-0.03896307,-0.0269463,-0.03323526,0.00739907,0.03356837,0.03713875,-0.01226845,-0.05303161,-0.06168042,0.03723238,-0.03849715,0.02210541,-0.04985556,-0.04507976,0.04435746,0.01757923,0.06939674,0.03425286,-0.05161482,0.00322797,-0.04007256,-0.02997715,0.02414296,0.09576324,-0.03470117,-0.04361213,0.0313019,0.00991068,-0.06981479,-0.00924699,0.04735222,-0.05080613,0.00158801,0.04285507,-0.02518577,0.01234285,-0.03113527,-0.04555965,0.00104326,0.0298038,-0.0470063,0.0031067,0.04559789,0.02124106,-0.02475617,-0.02350731,-0.04841038,-0.02562074,-0.03086221,-0.03406069,-0.09035238,0.00326667,0.03843225,-0.0124814,0.06150355,-0.01212231,-0.04130275,-0.01774155,0.02526936,-0.02101704,-0.00198205,0.00487864,-0.00966623,-0.00992835,-0.05732845,-0.00808176,-0.00141465,-0.00045272,-0.02806111,-0.04552929,-0.00224849,-0.02120568,-0.01413144,-0.03219588,-0.02972561,-0.0022738,-0.02385148,-0.07360753,0.03048874,-0.03924365,-0.08818819,0.01518582,0.08207855,0.01707313,-0.01873512,0.01976608,0.010823,-0.00227577,0.01360999,-0.049543,-0.02252696,-0.00901155,0.02031312,0.01326025,-0.03821994,0.03231432,0.0065995,-0.02290181,0.0368527,0.04185499,-0.03594749,0.01192575,0.00714273,-0.04691358,0.04343778,-0.0294613,-0.05499447,-0.01758631,-0.01351411,-0.01440729,0.01499138,-0.00509332,-0.0604815,0.03573142,-0.04690153,0.00685839,-0.01008886,0.0617918,0.00444976,-0.05026169,-0.0344901,-0.05064448,0.03307465,-0.0037654,-0.00818719,0.01205719,0.0210453,-0.02057258,-0.0419663,-0.0349608,-0.01795009,-0.04573079,-0.04542148,0.06452304,0.01985261,-0.07108463,0.014496,0.00812225,-0.00484631,-0.0294047,-0.0460135,0.00568031,0.01746766,0.03087372,0.05068969,0.022608,0.01223282,0.01260965,0.02301774,0.00880052,-0.0294348,0.02582433,0.01414227,0.02321408,0.06154461,0.01059175,-0.10105883,-0.00533442,-0.00133618,-0.05454294,0.01362182,-0.07629338,0.00325239,0.0113151,0.03507467,-0.0128061,-0.01181803,0.02742451,-0.04067657,0.01931735,-0.06898411,-0.01464744,-0.00341569,0.01854735,-0.01514149,0.07387705,-0.00802517,-0.06505643,-0.00550692,0.01686313,0.02784764,-0.01355341,-0.05469019,0.02509777,0.0194636,0.04948663,0.00160419,0.03050064,0.01845393,0.04004027,-0.00168403,0.02668696,0.01406291,-0.0054744,0.00728502,-0.0095463,0.02019433,-0.00715825,-0.05788092,0.02333077,0.00389723,0.05123991,-0.03471164,0.00509666,0.02145167,0.04779961,0.01814983,0.04234797,0.03119756,0.04910255,-0.01376301,0.00195352,0.028877,-0.06032598,-0.00326955,-0.02544168,0.0485027,0.02805935,0.04028377,-0.022067,0.06135606,-0.01429885,0.00264178,-0.00255936,0.08654007,-0.0160518,0.02555777,-0.00589324,0.04101673,0.05004231,-0.00195248,-0.00186784,0.06311131,-0.023666,-0.00655367,-0.04719155,0.01910851,0.00013213,-0.00207284,0.06232656,0.01158106,0.02621984,-0.01271264,0.03021904,-0.02248849,-0.05745412,-0.01808723,0.08720341,-0.0052264,-0.01206017,0.01546484,0.03193493,0.00838622,0.02255609,-0.04218518,-0.01716988,-0.05624509,0.02739088,-0.09928708,-0.04486778,0.07320549,0.0136849,-0.02745975,-0.02870321,-0.0443141,0.02583184,0.02334841,0.02982701,-0.00516568,-0.00781139,0.00039624,-0.01502942,-0.02987284,-0.01912264,0.0290035,0.0555127,0.03204774,-0.02245802,0.07264446,0.01329966,-0.00024212,-0.03264522,-0.02284247,0.04357819,-0.02444414,-0.00930055,0.06044788,-0.05029388,-0.00648624,-0.02772384,-0.03836696,-0.04569617,-0.02884215,-0.05000774,0.01188264,-0.05913365,0.01093362,0.05315797,0.01405366,0.09451301,-0.03564861,-0.00822519,-0.00765893,-0.02777409,0.01149289,-0.00306085,0.03217624,-0.00574986,-0.03850161,-0.02435894,-0.01120711,-0.01700437,0.03710324,0.00868093,0.02459246,0.05741584,0.00866814,0.00825132,-0.07117079,-0.01432609,-0.02866716,0.01207139,0.03260222,0.02126843,-0.02631975,0.03295775,0.01605139,-0.02068239,0.10178196,-0.00405783,0.02499616,0.01100793,-0.00410598,-0.00292709,-0.04850444,-0.02289651,0.00187345,0.02696957,0.05071834,-0.00207527,0.01666579,-0.00328555,0.00399549,-0.00173263,-0.00611066,-0.03460075,-0.01443954,0.05037058,-0.02630429,0.0117269,-0.04826744,0.06214136,-0.06050802,0.0156287,0.04503542,0.02653306,-0.00663957,-0.07019798,0.03904621,0.02638653,0.01940268,-0.00257812,-0.02289175,0.03482546,0.01875811,0.00613837,0.01695759,0.0017047,-0.03711027,-0.02411018,0.08121055,-0.05290554,-0.00336557,-0.01645235,-0.00437285,-0.03479953,-0.0366736,-0.0181089,-0.01596957,0.01508213,0.00406343,-0.02498733,0.00428361,-0.00612539,0.03200436,0.0318288,-0.04077432,-0.01047365,-0.00462411,0.03550956,-0.01138803,0.01450023,-0.00068541,0.02114856,-0.03178724,0.00769464,0.01227369,0.0115776,-0.01555728,0.01824013,-0.05080853,-0.11455926,0.03036513,-0.01479244,0.0007634,0.0335351,-0.00418476,0.00926853,0.00658144,0.02710447,0.01346482,0.00288911,-0.03472697,0.01113156,-0.01231546,-0.01649848,-0.00473054,0.02305974,-0.03889797,-0.06454917,0.02641684,0.01445349,0.03674437,-0.02442446,0.02172082,-0.01128582,0.03170194,-0.00594868,0.02890383,-0.02760657,-0.01994487,-0.02707118,0.03829699,0.04121079,-0.07029802,0.0433885,0.02898701,0.02216072,-0.00578338,-0.01247546,-0.00606365,0.05469861,0.04814071,-0.0065856,-0.02247148,-0.04801481,0.00000103,-0.01893986,0.01762762,-0.00649784,-0.04722556,-0.01020237,-0.03027065,-0.05094223,0.02117495,0.00629691],"last_embed":{"tokens":473,"hash":"rhcjvv"}}},"last_read":{"hash":"rhcjvv","at":1752940658063},"class_name":"SmartSource","outlinks":[{"title":"POP3","target":"POP3","line":14},{"title":"IMAP","target":"IMAP","line":15},{"title":"IMAP","target":"IMAP","line":20},{"title":"500","target":"Pasted image 20240617171900.png","line":51}],"metadata":{"tags":["计算机网络/OSI模型/应用层"],"类型":["邮件管理"],"aliases":["Internet Message Access Protocol"],"英文":"Internet Message Access Protocol","协议层级":"应用层"},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,23],"#简介#{1}":[12,12],"#简介#{2}":[13,14],"#简介#{3}":[15,17],"#简介#{4}":[18,23],"#基本操作":[24,91],"#基本操作#{1}":[27,29],"#基本操作#连接到IMAP服务器":[30,44],"#基本操作#连接到IMAP服务器#{1}":[31,35],"#基本操作#连接到IMAP服务器#{2}":[33,35],"#基本操作#连接到IMAP服务器#{3}":[36,40],"#基本操作#连接到IMAP服务器#{4}":[38,40],"#基本操作#连接到IMAP服务器#{5}":[41,41],"#基本操作#连接到IMAP服务器#{6}":[42,43],"#基本操作#连接到IMAP服务器#{7}":[44,44],"#基本操作#登录账户":[45,53],"#基本操作#登录账户#{1}":[47,49],"#基本操作#登录账户#{2}":[50,50],"#基本操作#登录账户#{3}":[51,51],"#基本操作#登录账户#{4}":[52,53],"#基本操作#列出邮箱：":[54,60],"#基本操作#列出邮箱：#{1}":[55,60],"#基本操作#选择邮箱：":[61,68],"#基本操作#选择邮箱：#{1}":[63,68],"#基本操作#检索邮件头：":[69,77],"#基本操作#检索邮件头：#{1}":[72,77],"#基本操作#检索邮件内容：":[78,86],"#基本操作#检索邮件内容：#{1}":[81,86],"#基本操作#退出 IMAP 会话":[87,91],"#基本操作#退出 IMAP 会话#{1}":[88,91],"#基本操作#退出 IMAP 会话#{2}":[89,91]},"last_import":{"mtime":1726560538053,"size":1660,"at":1749024987637,"hash":"rhcjvv"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md","last_embed":{"hash":"rhcjvv","at":1752940658063}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#---frontmatter---","lines":[1,10],"size":139,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#简介","lines":[11,23],"size":233,"outlinks":[{"title":"POP3","target":"POP3","line":4},{"title":"IMAP","target":"IMAP","line":5},{"title":"IMAP","target":"IMAP","line":10}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#简介#{1}","lines":[12,12],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#简介#{2}","lines":[13,14],"size":59,"outlinks":[{"title":"POP3","target":"POP3","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#简介#{3}","lines":[15,17],"size":70,"outlinks":[{"title":"IMAP","target":"IMAP","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#简介#{4}","lines":[18,23],"size":68,"outlinks":[{"title":"IMAP","target":"IMAP","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作","lines":[24,91],"size":677,"outlinks":[{"title":"500","target":"Pasted image 20240617171900.png","line":28}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#{1}","lines":[27,29],"size":37,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器","lines":[30,44],"size":225,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{1}","lines":[31,35],"size":82,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{2}","lines":[33,35],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{3}","lines":[36,40],"size":88,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{4}","lines":[38,40],"size":59,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{5}","lines":[41,41],"size":13,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{6}","lines":[42,43],"size":21,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#连接到IMAP服务器#{7}","lines":[44,44],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#登录账户": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#登录账户","lines":[45,53],"size":129,"outlinks":[{"title":"500","target":"Pasted image 20240617171900.png","line":7}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#登录账户#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#登录账户#{1}","lines":[47,49],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#登录账户#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#登录账户#{2}","lines":[50,50],"size":26,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#登录账户#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#登录账户#{3}","lines":[51,51],"size":40,"outlinks":[{"title":"500","target":"Pasted image 20240617171900.png","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#登录账户#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#登录账户#{4}","lines":[52,53],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#列出邮箱：": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#列出邮箱：","lines":[54,60],"size":45,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#列出邮箱：#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#列出邮箱：#{1}","lines":[55,60],"size":36,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#选择邮箱：": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#选择邮箱：","lines":[61,68],"size":45,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#选择邮箱：#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#选择邮箱：#{1}","lines":[63,68],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#检索邮件头：": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#检索邮件头：","lines":[69,77],"size":55,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#检索邮件头：#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#检索邮件头：#{1}","lines":[72,77],"size":43,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#检索邮件内容：": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#检索邮件内容：","lines":[78,86],"size":54,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#检索邮件内容：#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#检索邮件内容：#{1}","lines":[81,86],"size":41,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#退出 IMAP 会话": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#退出 IMAP 会话","lines":[87,91],"size":71,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#退出 IMAP 会话#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#退出 IMAP 会话#{1}","lines":[88,91],"size":57,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#退出 IMAP 会话#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md#基本操作#退出 IMAP 会话#{2}","lines":[89,91],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11426497,-0.0195824,-0.06379683,-0.05906891,-0.01351962,-0.04894095,0.00524221,0.0178392,0.04540066,-0.05544154,0.00370894,-0.04507824,0.05772775,0.0116925,0.08707165,0.03628514,0.0119059,-0.0238302,-0.01669275,0.02763928,0.07045386,-0.01485091,-0.01662807,-0.02359227,0.00931976,0.01172837,-0.01670836,-0.00438661,-0.0184426,-0.12074815,-0.00191433,-0.00044173,0.01481403,0.0597949,-0.00507483,-0.01064387,-0.01964816,0.03259818,-0.04615322,0.06524918,0.06343809,-0.00862682,0.0196218,-0.04589752,-0.04875998,-0.0938203,-0.0443275,-0.01801799,0.02409931,-0.0537103,-0.02890933,0.01500521,-0.01780408,0.01806333,-0.03646534,-0.01560775,-0.00692391,0.05062263,0.05773541,-0.03293594,0.07029048,0.06502786,-0.22047691,0.09141105,0.05308576,-0.02691628,-0.01081431,0.05509753,0.03154047,0.01263146,-0.07651097,0.03658526,-0.0328643,0.03494384,0.02937374,0.02035425,0.01560248,-0.01949535,-0.01739072,-0.02717894,-0.03186987,0.04359232,0.00494072,-0.00687797,-0.03358212,-0.0256909,-0.02686569,-0.01101274,0.02630623,0.01265847,-0.067151,-0.04985254,-0.01303553,0.01429411,0.0236141,0.01546265,0.01335807,0.07542305,-0.15345468,0.0972024,-0.06581631,-0.00790372,-0.01105618,-0.09203634,0.07097234,-0.0088387,-0.01233892,-0.05390059,-0.00960907,-0.00954682,-0.07881577,-0.06551588,0.03038744,-0.01398129,0.04506676,0.04704009,-0.00854813,-0.0096788,-0.00956857,-0.02830605,-0.03407426,0.00435828,0.07212,-0.02297441,-0.00393117,-0.04601837,0.06697993,0.07848355,0.01241012,0.05809681,0.04684474,-0.03260336,-0.04619027,-0.02332652,-0.02990024,0.02237032,-0.03093948,0.02027391,-0.0495258,-0.0114522,-0.0037509,-0.10373084,0.03101909,-0.06382563,-0.06388959,0.08877739,-0.02623187,0.00978923,0.04620687,-0.07639032,0.01487903,0.07631843,-0.02903661,-0.018541,-0.03591191,0.02018815,0.1049431,0.15243219,-0.01606604,0.00221172,0.02436458,-0.02167819,-0.08503183,0.1475506,0.03666994,-0.11291794,-0.05885043,0.01462535,0.01913304,-0.0716196,0.01579196,0.00160553,0.01367197,-0.00347545,0.03616584,-0.04204555,-0.00446978,-0.05109392,-0.00092159,-0.00851371,-0.0249206,-0.03010016,-0.05791406,0.04747128,0.00304926,-0.06187334,-0.04014894,-0.02563742,0.01685188,-0.04794428,-0.07335892,0.000078,-0.05889293,0.00871408,-0.0408597,-0.07157893,0.02529579,0.00354318,0.03702347,-0.06068975,0.11229236,0.03940436,-0.05536396,-0.00246551,-0.07955462,-0.01811754,0.00924499,0.0058816,0.04965731,0.00286655,0.00318805,0.02694583,0.02565052,0.02001187,-0.0298063,-0.01915109,0.02196485,0.0218385,0.03476236,0.04388653,0.00932509,0.0429344,-0.10117033,-0.19865264,-0.03256474,0.06428048,-0.02952302,0.00060978,-0.02884522,0.00339213,-0.02108788,0.08603836,0.06721629,0.1262795,0.02728384,-0.03205343,-0.02103485,-0.00543476,0.01418382,-0.02058794,0.01340763,0.01007661,0.01414952,-0.01284852,0.04769115,-0.03861591,0.03079608,0.04223583,-0.04069494,0.09991509,0.01247688,0.0215461,0.02568346,0.03596856,0.04339521,0.00595694,-0.11943956,0.00449234,0.06018933,-0.00918497,0.0164808,0.02579584,-0.00021591,-0.01737886,0.04174518,0.00890889,-0.10183171,0.00068824,-0.0598217,0.0036126,-0.03318145,-0.02847419,0.0251344,0.03373314,0.02386386,0.03805557,0.03881098,0.01713763,-0.04953398,-0.03175786,-0.06879619,-0.00858932,0.0304712,0.02113244,-0.00488829,0.01438572,0.00249777,-0.01020418,-0.02470378,-0.03923961,0.03378019,-0.02503466,0.00516881,0.00932649,0.16567519,0.00126607,0.00115045,0.01128512,0.01547772,0.00304528,-0.07006398,0.03471506,0.03766396,0.03420444,0.03174507,0.0499886,-0.03299953,-0.04753675,0.04764023,0.04302963,0.02188501,0.06857786,-0.02794755,-0.05464035,-0.02349425,-0.02511872,-0.0008503,0.09180109,-0.0056286,-0.27633104,0.03357691,-0.09042439,0.01141744,0.01785175,0.03500887,0.05437356,0.03928611,-0.0732063,0.01923011,0.00061461,0.04306859,-0.01283601,0.01041154,-0.0343441,-0.00977426,0.0290437,-0.02218219,0.02615958,-0.02036938,-0.05035169,0.0581005,0.17901213,-0.01561901,0.07362719,0.03210806,0.02024162,0.02995221,0.02699186,0.00736034,0.06023843,-0.05904736,0.01691647,-0.02256291,0.09407543,-0.00872248,-0.0004988,0.00247463,0.01358035,-0.02132598,-0.04579899,0.09847467,-0.05527426,0.04473349,0.07382185,0.03715083,-0.01734663,-0.05028501,0.03978996,0.06596731,-0.00709884,-0.03995107,0.0001328,0.01587009,0.0364146,0.03248233,0.06637155,0.01906992,-0.06185075,-0.00400551,0.02064224,-0.03311409,0.06668058,0.08501703,0.05316731],"last_embed":{"hash":"a7917470e0e72fd3eea5f1077c3509519a768c0104f6a4d1e24a1aacb602a0af","tokens":476}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[0.01806307,-0.01612236,-0.01767646,-0.01481474,-0.02837381,-0.05016099,0.03052727,-0.01208628,0.00608446,-0.02170607,-0.01264039,0.01617331,-0.03439267,-0.08163925,0.08855486,0.10420728,0.00275193,-0.02390168,-0.00976947,-0.00917558,-0.0027475,-0.01116243,-0.04074186,0.02593618,-0.02557436,-0.01353032,-0.0316744,-0.0119061,0.00513792,0.01582869,0.10783698,-0.04771902,0.06609817,0.02118573,0.01460489,0.01696448,0.02657193,0.01682672,0.0561661,-0.01058853,-0.08270167,-0.00640864,-0.06512711,-0.00007597,-0.08483507,0.0120641,-0.0497499,-0.0013092,0.03500261,0.00346131,0.00270996,-0.0345199,0.02286746,-0.00749359,-0.03673755,0.02929282,-0.07954479,0.04414904,0.00159461,-0.08108185,-0.0080731,-0.01211455,0.03727697,0.01378153,-0.00129409,0.02336079,0.01885376,-0.0214544,-0.05093404,0.0195175,-0.00556788,-0.05645608,0.06531917,0.00443644,0.03126913,0.02635686,-0.01913228,-0.0033767,-0.01550314,0.06050721,0.0133084,0.00503646,-0.00149608,0.04317307,-0.02159337,-0.0521051,0.05930443,-0.0054561,0.02557991,0.01226955,-0.0376916,-0.0225685,0.00909455,-0.06509002,0.02608607,0.02628054,0.01412929,-0.00256304,0.04215723,-0.02843447,-0.05608575,-0.02078421,-0.02264732,0.0180432,0.03130004,-0.02744152,-0.00490163,0.06623287,-0.02586573,-0.01795734,0.01455483,-0.03834454,0.00202766,-0.0140929,-0.00872616,-0.00787775,0.02133155,0.02264449,0.0585621,0.06613854,-0.00553388,0.06894107,-0.01727482,0.01334854,-0.09441116,-0.04554616,0.02209328,-0.02468342,0.03265607,-0.05765055,-0.00716534,0.058321,-0.04375404,-0.03847741,0.07094086,0.02220398,0.02233753,-0.00169568,-0.09964512,-0.01654769,0.05055179,-0.03994914,0.00762219,-0.03729594,0.01877642,0.03299567,-0.07843861,0.04293424,0.01450048,-0.0065719,0.08043187,-0.03077595,-0.03052122,-0.01400553,-0.03192414,-0.02526989,-0.01998396,-0.01339482,0.00942028,-0.00025859,0.01633228,0.00753379,0.00161175,0.00301773,-0.00431452,0.04780358,-0.00814098,0.03845168,0.02511269,0.00960514,0.01232064,0.05494662,-0.00510625,-0.02813722,0.00233654,-0.03182923,0.02169088,-0.01568944,-0.00336768,0.00336779,0.00964811,-0.05172667,-0.00416623,0.02330108,-0.02725505,0.02902637,-0.04778339,-0.02353657,-0.0259236,-0.001244,0.05096725,0.00757238,0.03139407,0.00334135,0.04119525,0.00007525,-0.01925988,-0.06552651,0.01334175,0.02074455,-0.05392771,-0.02761741,-0.02932057,-0.09516039,0.0593393,0.04850941,0.04989641,-0.07985611,-0.06600365,-0.03470536,-0.11173725,0.02901939,0.0278114,-0.0062613,-0.0007333,0.03893516,0.00351771,-0.02373559,0.0285061,-0.02625151,-0.00717829,0.04278228,-0.02278543,0.0096596,0.01827585,-0.05371701,0.02419236,-0.01207946,0.01794174,-0.00401284,-0.05693714,0.07053414,0.04842528,-0.04158771,-0.0526948,0.03840737,0.03040762,0.01245975,0.02482058,-0.04856035,-0.01222571,0.00939636,0.05922034,-0.03592771,-0.03843559,-0.024727,-0.06773846,0.05622455,0.0359208,0.07298242,0.02492408,0.05407578,-0.03419307,0.07268839,0.00427643,-0.04831652,-0.04074702,-0.04638235,0.04212336,-0.03295736,0.04155732,0.01543692,0.03048035,0.00583853,-0.00977977,-0.04065808,-0.00917317,0.03776231,0.03845757,0.00978776,0.07420148,0.01755632,0.01107378,-0.02991425,-0.05203811,0.03804985,0.04417291,0.01063053,0.0060329,-0.00716554,-0.04509754,-0.0016693,0.01088234,-0.00942814,0.02801093,0.09069715,-0.03630644,0.01805717,0.10773417,0.04863377,0.0123034,-0.03894421,-0.02199333,-0.00644156,-0.02853049,-0.04055993,-0.00432917,0.0656222,0.02795527,-0.07628538,0.01763993,0.02388899,-0.0478957,-0.00491821,0.02078363,0.03363078,0.00289502,-0.00545414,0.02963039,0.00538511,-0.02760068,0.00070745,-0.03896307,-0.0269463,-0.03323526,0.00739907,0.03356837,0.03713875,-0.01226845,-0.05303161,-0.06168042,0.03723238,-0.03849715,0.02210541,-0.04985556,-0.04507976,0.04435746,0.01757923,0.06939674,0.03425286,-0.05161482,0.00322797,-0.04007256,-0.02997715,0.02414296,0.09576324,-0.03470117,-0.04361213,0.0313019,0.00991068,-0.06981479,-0.00924699,0.04735222,-0.05080613,0.00158801,0.04285507,-0.02518577,0.01234285,-0.03113527,-0.04555965,0.00104326,0.0298038,-0.0470063,0.0031067,0.04559789,0.02124106,-0.02475617,-0.02350731,-0.04841038,-0.02562074,-0.03086221,-0.03406069,-0.09035238,0.00326667,0.03843225,-0.0124814,0.06150355,-0.01212231,-0.04130275,-0.01774155,0.02526936,-0.02101704,-0.00198205,0.00487864,-0.00966623,-0.00992835,-0.05732845,-0.00808176,-0.00141465,-0.00045272,-0.02806111,-0.04552929,-0.00224849,-0.02120568,-0.01413144,-0.03219588,-0.02972561,-0.0022738,-0.02385148,-0.07360753,0.03048874,-0.03924365,-0.08818819,0.01518582,0.08207855,0.01707313,-0.01873512,0.01976608,0.010823,-0.00227577,0.01360999,-0.049543,-0.02252696,-0.00901155,0.02031312,0.01326025,-0.03821994,0.03231432,0.0065995,-0.02290181,0.0368527,0.04185499,-0.03594749,0.01192575,0.00714273,-0.04691358,0.04343778,-0.0294613,-0.05499447,-0.01758631,-0.01351411,-0.01440729,0.01499138,-0.00509332,-0.0604815,0.03573142,-0.04690153,0.00685839,-0.01008886,0.0617918,0.00444976,-0.05026169,-0.0344901,-0.05064448,0.03307465,-0.0037654,-0.00818719,0.01205719,0.0210453,-0.02057258,-0.0419663,-0.0349608,-0.01795009,-0.04573079,-0.04542148,0.06452304,0.01985261,-0.07108463,0.014496,0.00812225,-0.00484631,-0.0294047,-0.0460135,0.00568031,0.01746766,0.03087372,0.05068969,0.022608,0.01223282,0.01260965,0.02301774,0.00880052,-0.0294348,0.02582433,0.01414227,0.02321408,0.06154461,0.01059175,-0.10105883,-0.00533442,-0.00133618,-0.05454294,0.01362182,-0.07629338,0.00325239,0.0113151,0.03507467,-0.0128061,-0.01181803,0.02742451,-0.04067657,0.01931735,-0.06898411,-0.01464744,-0.00341569,0.01854735,-0.01514149,0.07387705,-0.00802517,-0.06505643,-0.00550692,0.01686313,0.02784764,-0.01355341,-0.05469019,0.02509777,0.0194636,0.04948663,0.00160419,0.03050064,0.01845393,0.04004027,-0.00168403,0.02668696,0.01406291,-0.0054744,0.00728502,-0.0095463,0.02019433,-0.00715825,-0.05788092,0.02333077,0.00389723,0.05123991,-0.03471164,0.00509666,0.02145167,0.04779961,0.01814983,0.04234797,0.03119756,0.04910255,-0.01376301,0.00195352,0.028877,-0.06032598,-0.00326955,-0.02544168,0.0485027,0.02805935,0.04028377,-0.022067,0.06135606,-0.01429885,0.00264178,-0.00255936,0.08654007,-0.0160518,0.02555777,-0.00589324,0.04101673,0.05004231,-0.00195248,-0.00186784,0.06311131,-0.023666,-0.00655367,-0.04719155,0.01910851,0.00013213,-0.00207284,0.06232656,0.01158106,0.02621984,-0.01271264,0.03021904,-0.02248849,-0.05745412,-0.01808723,0.08720341,-0.0052264,-0.01206017,0.01546484,0.03193493,0.00838622,0.02255609,-0.04218518,-0.01716988,-0.05624509,0.02739088,-0.09928708,-0.04486778,0.07320549,0.0136849,-0.02745975,-0.02870321,-0.0443141,0.02583184,0.02334841,0.02982701,-0.00516568,-0.00781139,0.00039624,-0.01502942,-0.02987284,-0.01912264,0.0290035,0.0555127,0.03204774,-0.02245802,0.07264446,0.01329966,-0.00024212,-0.03264522,-0.02284247,0.04357819,-0.02444414,-0.00930055,0.06044788,-0.05029388,-0.00648624,-0.02772384,-0.03836696,-0.04569617,-0.02884215,-0.05000774,0.01188264,-0.05913365,0.01093362,0.05315797,0.01405366,0.09451301,-0.03564861,-0.00822519,-0.00765893,-0.02777409,0.01149289,-0.00306085,0.03217624,-0.00574986,-0.03850161,-0.02435894,-0.01120711,-0.01700437,0.03710324,0.00868093,0.02459246,0.05741584,0.00866814,0.00825132,-0.07117079,-0.01432609,-0.02866716,0.01207139,0.03260222,0.02126843,-0.02631975,0.03295775,0.01605139,-0.02068239,0.10178196,-0.00405783,0.02499616,0.01100793,-0.00410598,-0.00292709,-0.04850444,-0.02289651,0.00187345,0.02696957,0.05071834,-0.00207527,0.01666579,-0.00328555,0.00399549,-0.00173263,-0.00611066,-0.03460075,-0.01443954,0.05037058,-0.02630429,0.0117269,-0.04826744,0.06214136,-0.06050802,0.0156287,0.04503542,0.02653306,-0.00663957,-0.07019798,0.03904621,0.02638653,0.01940268,-0.00257812,-0.02289175,0.03482546,0.01875811,0.00613837,0.01695759,0.0017047,-0.03711027,-0.02411018,0.08121055,-0.05290554,-0.00336557,-0.01645235,-0.00437285,-0.03479953,-0.0366736,-0.0181089,-0.01596957,0.01508213,0.00406343,-0.02498733,0.00428361,-0.00612539,0.03200436,0.0318288,-0.04077432,-0.01047365,-0.00462411,0.03550956,-0.01138803,0.01450023,-0.00068541,0.02114856,-0.03178724,0.00769464,0.01227369,0.0115776,-0.01555728,0.01824013,-0.05080853,-0.11455926,0.03036513,-0.01479244,0.0007634,0.0335351,-0.00418476,0.00926853,0.00658144,0.02710447,0.01346482,0.00288911,-0.03472697,0.01113156,-0.01231546,-0.01649848,-0.00473054,0.02305974,-0.03889797,-0.06454917,0.02641684,0.01445349,0.03674437,-0.02442446,0.02172082,-0.01128582,0.03170194,-0.00594868,0.02890383,-0.02760657,-0.01994487,-0.02707118,0.03829699,0.04121079,-0.07029802,0.0433885,0.02898701,0.02216072,-0.00578338,-0.01247546,-0.00606365,0.05469861,0.04814071,-0.0065856,-0.02247148,-0.04801481,0.00000103,-0.01893986,0.01762762,-0.00649784,-0.04722556,-0.01020237,-0.03027065,-0.05094223,0.02117495,0.00629691],"last_embed":{"tokens":473,"hash":"rhcjvv"}}},"last_read":{"hash":"rhcjvv","at":1752940886768},"class_name":"SmartSource","outlinks":[{"title":"POP3","target":"POP3","line":14},{"title":"IMAP","target":"IMAP","line":15},{"title":"IMAP","target":"IMAP","line":20},{"title":"500","target":"Pasted image 20240617171900.png","line":51}],"metadata":{"tags":["计算机网络/OSI模型/应用层"],"类型":["邮件管理"],"aliases":["Internet Message Access Protocol"],"英文":"Internet Message Access Protocol","协议层级":"应用层"},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,23],"#简介#{1}":[12,12],"#简介#{2}":[13,14],"#简介#{3}":[15,17],"#简介#{4}":[18,23],"#基本操作":[24,91],"#基本操作#{1}":[27,29],"#基本操作#连接到IMAP服务器":[30,44],"#基本操作#连接到IMAP服务器#{1}":[31,35],"#基本操作#连接到IMAP服务器#{2}":[33,35],"#基本操作#连接到IMAP服务器#{3}":[36,40],"#基本操作#连接到IMAP服务器#{4}":[38,40],"#基本操作#连接到IMAP服务器#{5}":[41,41],"#基本操作#连接到IMAP服务器#{6}":[42,43],"#基本操作#连接到IMAP服务器#{7}":[44,44],"#基本操作#登录账户":[45,53],"#基本操作#登录账户#{1}":[47,49],"#基本操作#登录账户#{2}":[50,50],"#基本操作#登录账户#{3}":[51,51],"#基本操作#登录账户#{4}":[52,53],"#基本操作#列出邮箱：":[54,60],"#基本操作#列出邮箱：#{1}":[55,60],"#基本操作#选择邮箱：":[61,68],"#基本操作#选择邮箱：#{1}":[63,68],"#基本操作#检索邮件头：":[69,77],"#基本操作#检索邮件头：#{1}":[72,77],"#基本操作#检索邮件内容：":[78,86],"#基本操作#检索邮件内容：#{1}":[81,86],"#基本操作#退出 IMAP 会话":[87,91],"#基本操作#退出 IMAP 会话#{1}":[88,91],"#基本操作#退出 IMAP 会话#{2}":[89,91]},"last_import":{"mtime":1726560538053,"size":1660,"at":1749024987637,"hash":"rhcjvv"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/邮件协议/IMAP.md","last_embed":{"hash":"rhcjvv","at":1752940886768}},