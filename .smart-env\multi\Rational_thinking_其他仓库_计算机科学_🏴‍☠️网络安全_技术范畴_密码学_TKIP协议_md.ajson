"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md","last_embed":{"hash":"1kmkcgb","at":1752940642752},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06159853,-0.03706147,-0.00987728,-0.05991895,-0.02696908,-0.0090353,0.02639242,0.05928378,0.02370152,-0.01829622,0.00290274,-0.04822562,0.06863526,0.03893419,0.07530455,0.03619738,-0.02470818,0.0442465,0.03132711,-0.0018168,0.09657073,-0.03434613,-0.00961872,-0.03261191,0.00293312,0.04065052,0.01740073,-0.04507333,0.00738311,-0.17354579,0.0082957,-0.00672253,0.00993197,0.04151689,-0.00566478,-0.05158646,-0.01873654,0.03440474,-0.03607693,0.04721799,-0.01767613,0.0114615,-0.02163987,-0.07951374,-0.01750921,-0.08934708,-0.01133219,-0.05287271,-0.03446983,-0.02298017,-0.09358175,-0.02392379,-0.01737366,0.01234542,-0.05025704,0.02530301,0.06364224,0.03195503,0.05470051,-0.08935347,0.03263486,0.07605848,-0.19369178,0.05938255,0.0282327,0.0407208,-0.02370415,-0.0158193,0.0322371,0.01916357,-0.02377141,0.03647567,-0.01906746,0.02412187,0.01073857,0.04696381,0.01530734,-0.03600989,-0.0226964,-0.05628733,-0.02369855,0.0319033,0.03666161,-0.00001433,0.00554221,0.01195515,-0.02791204,-0.01946824,-0.0179699,-0.00923244,-0.01442846,-0.05062491,0.04337508,0.03224692,-0.01844443,0.004927,-0.00021997,0.06279325,-0.09712953,0.09536493,-0.06561174,0.01783006,-0.05320477,-0.03708239,0.03248242,-0.02823794,-0.00879545,-0.08257531,0.01350538,0.02170246,-0.08294629,-0.03319592,0.0432605,-0.01150569,0.04205386,0.04351373,0.03885775,0.03266557,-0.00269045,-0.0023285,-0.00484726,0.03208389,0.07358477,-0.00388261,-0.05720918,-0.05744446,0.02527411,0.04548164,0.01299847,0.05014984,0.05002735,-0.01079683,-0.03381402,0.03161193,-0.03638369,0.01057897,-0.05669268,-0.01902163,0.01575819,-0.05915683,-0.03986644,-0.01976879,-0.01046594,-0.07278704,-0.07553925,0.12032512,-0.02561926,0.05021003,0.03911863,-0.01417501,0.00827721,0.04035123,-0.005363,-0.00619255,0.0097001,0.01303032,0.08798289,0.12079618,0.00276959,-0.00765636,-0.04556068,-0.00332245,-0.06525657,0.20770563,0.0150913,-0.09410025,0.01829152,0.02081394,-0.00465209,-0.0816358,0.02842468,-0.04208788,0.05153221,0.0066027,0.04037908,0.01541572,-0.00007685,-0.03310702,-0.02198691,0.0348105,0.03882792,-0.03918387,-0.11948248,0.01436316,0.02440527,-0.03581909,-0.02891362,-0.03282298,0.01255599,-0.01298815,-0.09370614,0.06271205,-0.047391,0.02629146,-0.08030258,-0.0909547,0.04002439,-0.01708561,0.0810994,-0.04580271,0.03341117,0.02349071,-0.02983863,0.01089059,-0.07915252,-0.00524844,-0.00310522,-0.00388485,0.0031252,0.07975088,-0.00012958,0.01607564,-0.03671927,0.00621611,-0.0010153,0.02668251,0.05037927,0.030131,-0.00139911,0.06587785,-0.01591932,0.01655861,-0.05038153,-0.20725474,-0.00513269,-0.00989423,-0.042672,0.04745183,-0.09735931,-0.01960975,-0.00106611,0.04453478,0.06554779,0.09359728,0.06539612,-0.08228239,-0.01488062,-0.01844737,0.03735407,0.04853829,-0.04527688,-0.00281176,-0.01377813,-0.00786255,0.05631418,-0.04266396,-0.0763526,0.03493383,-0.02393478,0.12553443,-0.01976739,0.02065732,0.02706652,0.01398505,0.04115612,-0.01303293,-0.13102108,0.02161847,0.04052307,-0.03090482,-0.01665044,0.01851524,-0.05625131,-0.0311959,0.01433778,-0.04661352,-0.11130641,-0.00164716,-0.05006779,-0.04258047,0.01918482,0.01263578,0.01323663,0.00419832,0.020638,0.01892786,0.00793407,-0.00342205,-0.03055432,-0.02475618,-0.06367789,-0.01268972,0.07257795,0.02770241,0.0490446,0.00733302,-0.02023696,0.03861502,-0.01367415,-0.0101931,-0.00899458,-0.02316689,-0.01108877,-0.0033971,0.16183554,-0.00416026,-0.01545354,0.05590088,0.01369988,-0.0000508,-0.05161396,0.03577339,0.04275771,0.06600655,0.02869666,0.02167015,-0.00484798,-0.00050726,0.03645925,0.02441329,-0.00976917,0.05828902,-0.04600823,-0.02942754,-0.02360568,-0.04293907,0.00488847,0.06115514,0.01872434,-0.28396448,0.03057062,-0.01889653,0.02790427,0.05056833,0.0344183,0.02319433,-0.03913695,-0.06825286,0.00094083,-0.04967995,0.05977092,0.03046816,-0.02664089,-0.01368187,-0.02118077,0.07574052,-0.02040841,0.0137772,-0.03047078,-0.00869184,0.06730802,0.20967005,0.00239988,0.04271086,0.01040728,0.01597748,0.04623501,0.04828044,0.00051346,0.01122745,-0.04127106,0.0110748,0.03125474,0.04380348,0.03965651,-0.02145102,-0.00224976,-0.00731401,0.04057641,-0.06132149,0.02839838,-0.08112521,0.00988773,0.08057413,0.01647152,-0.04096562,-0.05307673,-0.03193901,0.05628395,0.04284283,0.01946798,0.00509495,0.01090404,0.04511404,0.09487233,0.04063652,-0.01607239,-0.07921364,0.01167762,0.00137631,0.04643978,0.05731574,0.07474111,0.04625088],"last_embed":{"hash":"371583826586165fb8656656777438af36438884b8947b7a8a09899f7deb9d45","tokens":181}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.01757724,0.03034209,-0.02409652,0.02315179,-0.00529531,0.01732778,0.02159757,-0.00469437,0.02939115,-0.02904507,0.02118628,-0.05275894,-0.00270356,-0.03108656,0.01047936,0.10283131,0.0161999,0.03829346,0.04642871,-0.05980827,0.00540507,0.0262644,-0.01720617,0.03402602,0.01740023,-0.0317152,-0.0197502,-0.05129292,0.03403004,0.02890635,0.03247663,-0.07438853,0.00089129,-0.01092776,-0.02976027,0.03554533,0.01154972,0.02592513,-0.00130491,0.02739689,-0.02625314,-0.02227665,-0.00293375,0.01045222,0.00377602,-0.00230009,-0.00943976,-0.01513699,0.00686323,0.04523002,0.04885574,-0.02439775,0.03147888,0.05424224,-0.03282357,0.10020802,0.02503175,-0.10291764,0.02818683,-0.00101448,0.03079451,-0.05263677,0.05662105,-0.01855208,0.04680457,-0.02633748,-0.04577958,-0.04441851,0.03720845,-0.00891927,-0.07963715,-0.05963646,0.02565132,0.00705985,-0.03612261,-0.06854852,0.02665637,0.00100608,-0.0126918,0.01761432,0.00236418,-0.00137773,0.04221056,-0.04514971,0.04425578,0.01039594,0.00690893,-0.10041703,-0.0072442,-0.00541967,-0.00445519,-0.048868,-0.03363941,-0.06737615,0.04866281,-0.0597826,-0.02198759,0.00277189,0.01880877,-0.01182244,-0.00782858,-0.01104523,0.0123717,-0.03271987,-0.00466065,-0.0529905,-0.0006558,0.06945924,-0.03477592,-0.05322192,0.01665005,0.01117149,-0.04701031,-0.02182888,-0.00853816,0.02076014,-0.02987227,-0.0384136,0.04510754,0.06457063,0.05482628,0.02619123,-0.02080368,-0.01132372,-0.02917323,-0.05339014,0.02877366,-0.01826377,0.03148774,-0.02674333,0.03798721,0.0083718,-0.00188612,-0.06598873,0.02347374,0.03340953,0.02954928,-0.0007657,-0.03450349,0.01748717,0.01942142,0.0076138,-0.00490235,0.01755214,-0.02820018,0.00561544,-0.07625517,-0.04919856,0.01729883,0.04850347,0.03222705,-0.00836136,-0.05470689,-0.04544793,-0.03387249,0.00062256,0.01266051,0.01337323,0.0162876,0.0533656,-0.05437696,-0.01262351,0.02997708,0.00261564,-0.02401785,-0.00191269,-0.02578779,0.08178817,0.0492033,0.01438117,-0.08259326,0.04656337,0.07877694,0.01662489,0.01219586,-0.00453327,0.01583908,0.0357064,-0.02882618,-0.00393872,0.04554716,-0.01196747,-0.01668816,0.05424735,0.00610898,0.01419343,-0.06175114,-0.03033506,0.05095667,0.00748031,0.01629536,-0.02091016,0.00539227,0.04129934,0.03084081,0.03779091,0.0542471,-0.07539383,0.00592571,0.02535916,0.00492181,-0.04859629,-0.03058867,-0.1008965,0.04048383,0.01220086,0.03348347,-0.06207674,0.02298289,0.06798094,-0.01861982,0.03099524,0.05779428,0.03801617,0.01472051,0.03374049,-0.01702597,0.01659109,0.03446631,-0.09096445,0.01677808,0.03708328,-0.04544708,0.05824513,0.00742016,-0.00009026,0.01460445,0.02423518,-0.0418161,0.00336714,-0.04618374,0.08793462,-0.0347129,-0.05428148,-0.03933463,0.04043357,0.03319452,0.00143726,0.02082284,-0.0057735,-0.0347001,-0.0173371,0.01762555,-0.02668414,-0.07048815,-0.00138939,-0.0484058,0.04062995,0.04289442,0.08957641,-0.00015071,0.00193699,-0.04987924,-0.00176762,-0.03644785,-0.04245392,0.00345769,-0.02793298,-0.01500961,-0.01927776,0.06199683,-0.05041804,0.01937094,0.01516095,-0.04823941,0.00477215,0.01655066,-0.00493362,-0.00081825,0.011426,-0.02295427,-0.02107125,0.03262081,-0.01872638,0.02487464,-0.0146009,0.01811061,0.02089426,0.00420222,-0.01509881,-0.0249863,-0.02065467,0.02933494,0.01050472,0.00299249,0.03377889,-0.00480168,0.03223483,0.06654029,-0.00652621,-0.08262619,0.0193854,-0.00190095,-0.02547722,-0.02443863,-0.06994582,-0.01833944,0.00573395,-0.0074221,-0.06100108,0.015691,0.01630129,0.01191912,-0.04485119,-0.00758931,-0.02120288,0.00009046,-0.02128968,0.01334191,-0.00448786,-0.01897428,-0.07928195,-0.02177091,-0.0626581,0.00374787,0.06700642,0.06477388,0.00704203,-0.05990083,0.00435009,-0.01693589,0.01005548,0.0006359,0.01835884,-0.08410919,-0.03783572,0.02455252,0.00755929,0.00972809,-0.01543177,-0.03104357,-0.02229132,0.00039482,0.00655213,-0.01107256,0.02614069,-0.02658004,-0.06599043,0.03475199,-0.00385424,-0.01456819,-0.0792104,0.06112261,0.01687375,0.03352015,0.02012697,-0.00727889,0.02220251,-0.03487531,-0.00669515,0.02381575,0.02470724,0.00603829,0.02109336,0.00836254,-0.00494381,-0.02558296,0.02627453,-0.06037509,-0.01880981,0.02690064,-0.04236132,-0.08052881,0.00808319,0.00425354,0.03618588,0.05979678,0.03082785,-0.05542723,0.03128071,-0.02622028,0.04023197,0.02058721,-0.04738495,-0.04989274,0.001342,0.01048045,0.00244959,-0.01440562,-0.04395197,0.00029948,0.00798659,-0.01025632,-0.02657875,-0.02670924,-0.02361983,-0.05848538,0.03022424,0.03238486,-0.0367443,-0.00413593,0.01628373,-0.06474376,0.00570298,0.01838961,0.00812411,-0.02398742,-0.0134733,-0.00971689,-0.01696174,0.00919893,-0.0172123,0.01424605,0.02020413,0.00511744,0.00773489,-0.04912433,0.10589262,-0.00361958,-0.02511119,0.02145997,0.03996805,0.02811692,0.02415645,0.07035405,-0.03771789,-0.00201549,-0.06472933,-0.04399035,0.0183292,0.02163794,-0.01766795,0.04524411,0.02335136,-0.04437553,0.03309558,-0.01102418,0.01283588,-0.01181607,0.00173907,0.00568677,0.03049851,-0.01974829,-0.05903853,-0.0086203,-0.03254455,0.04230957,-0.02405241,0.02177032,-0.06725308,0.00624676,-0.04716326,-0.05575622,-0.01086142,-0.04455195,0.00545113,0.00575704,0.02789817,0.01970309,0.02271708,0.02126418,-0.0316105,-0.05080951,0.00633544,0.01693329,-0.02171743,0.04069105,-0.00126488,0.06442265,0.02863381,-0.02465212,0.0093247,-0.06362738,-0.01940552,0.05527695,-0.00724289,0.04743969,0.02710309,0.01279863,0.02058475,-0.00799848,-0.06129783,0.0232619,0.01904123,0.04223381,0.00972406,0.06337225,0.00829656,0.04382619,-0.00419587,-0.02872258,-0.05764193,-0.03331663,0.00451398,0.0066947,0.05566779,-0.00689625,0.05103613,0.05520417,-0.02509948,0.05650439,0.01231873,0.00118737,0.00232968,-0.03899215,0.03494765,0.03696726,0.03178119,-0.02915699,0.02103328,0.01525664,-0.02212731,-0.07922339,0.00052357,0.0258001,0.03581307,-0.00612096,-0.00145119,0.04740915,-0.04253366,0.00411023,0.04417469,0.05644128,0.06309927,-0.00609874,-0.01932091,-0.04494142,0.04628278,-0.01177547,-0.05833697,-0.05819144,0.02438409,-0.05119571,0.02767672,0.00022233,-0.00489131,0.0095396,0.01386204,0.0252537,0.02168117,-0.03173897,-0.0375105,0.01029116,-0.01563881,0.01235142,0.00273103,0.00756791,-0.03795188,-0.01226602,0.06382065,-0.00228121,-0.00135014,-0.01590136,-0.04605193,0.03681218,-0.00236949,0.03402443,-0.04245681,0.04222946,0.00957084,0.03642891,0.00480959,0.02560242,0.04568681,-0.00513704,0.0076344,0.00450581,-0.01507619,0.00973208,0.04526468,-0.01992133,0.01742267,0.01104963,0.04116106,-0.04366997,-0.00795956,-0.08644006,0.00583742,0.00243133,-0.00210377,-0.002281,-0.02182574,0.05160382,0.000917,-0.08728416,-0.02541738,-0.00714905,-0.01061113,0.04645449,0.08609443,-0.00240489,-0.03826259,0.07742693,-0.0421844,0.00898242,-0.02044077,-0.00617932,-0.02170139,0.00451544,-0.01806646,0.01408031,-0.05207546,0.00522704,-0.06863923,0.0239542,-0.01067074,-0.0259735,0.03759015,0.11356379,-0.04192049,0.02622163,0.02004352,0.05305646,-0.00558222,0.01486332,-0.03859301,-0.01835961,-0.07506137,-0.03688676,0.03089656,0.0480185,0.02085287,0.01405658,-0.04475814,0.01362503,-0.00301752,0.01493331,-0.06630359,-0.00026365,0.01500168,-0.06127043,-0.02649401,0.02722301,-0.07674654,0.04715552,0.0466999,0.04407703,0.00656859,-0.00268938,0.00553991,-0.00663902,-0.06544,0.0131754,0.04477401,-0.02226002,0.07576152,-0.03673809,-0.00157493,-0.05900104,-0.02056381,0.07922205,0.03606813,0.06461366,0.01084185,0.01177615,-0.02906176,-0.01485003,-0.03729684,-0.02775856,-0.04307806,-0.00232723,-0.00191711,-0.01882719,0.02348418,-0.01991098,-0.02252812,0.0229844,-0.00040866,0.00202186,0.05735107,-0.05801504,-0.0619854,-0.02884027,-0.00325397,-0.00932726,0.01202178,0.00695911,0.03134989,-0.03321749,0.02871589,0.02219154,-0.01215818,0.01799813,0.01904091,0.00699696,0.04095124,0.02811873,0.03759513,-0.01386305,-0.01507781,-0.00167029,0.02982132,0.01012127,0.05602633,0.0365678,0.02010252,0.04367116,-0.01461629,-0.07029489,-0.04301105,-0.01682515,0.03728966,0.0461247,-0.06571759,0.00953017,-0.03328501,0.00330243,0.04139251,-0.02761284,0.04747585,-0.00726231,0.04157972,-0.05536377,0.01593708,-0.04820296,0.00097064,0.016122,0.0264678,-0.02388885,0.0130418,0.00789165,0.02075232,-0.0427573,-0.01642741,0.02472305,0.00800774,-0.02521263,0.04111459,-0.02708606,-0.00676666,-0.03390647,0.03923443,-0.02902238,-0.01871938,-0.08504029,-0.02503825,-0.04198693,0.03398398,0.01499414,0.0246093,-0.01385545,-0.02278767,0.06299459,0.02863646,0.07370681,-0.03283135,0.0524798,-0.01811758,0.04541058,0.0075405,0.04812423,-0.03225376,-0.04600281,-0.05513762,0.05612924,-0.03051004,0.01683208,0.03292334,-0.0102962,-0.00740794,0.01112019,0.02143274,-0.0760038,0.05951158,0.06005898,0.02528236,-0.0027485,-0.03461944,8e-7,-0.02281478,-0.02472742,0.06571431,-0.08680372,-0.01276192,-0.05828679,-0.01052704,0.01918256,0.01223386],"last_embed":{"tokens":145,"hash":"1kmkcgb"}}},"last_read":{"hash":"1kmkcgb","at":1752940642752},"class_name":"SmartSource","outlinks":[{"title":"WEP","target":"WEP","line":16},{"title":"Wired Equivalent Privacy","target":"WEP","line":16}],"metadata":{"aliases":["Temporal Key Integrity Protocol","临时密钥完整性协议"],"tags":["网络安全/密码学"],"cssclasses":["editor-full"],"created":"2024-09-09T21:06","updated":"2025-07-19T15:31"},"blocks":{"#---frontmatter---":[1,11],"#简介":[13,17],"#简介#临时密钥完整性协议":[14,17],"#简介#临时密钥完整性协议#{1}":[15,15],"#简介#临时密钥完整性协议#{2}":[16,16],"#简介#临时密钥完整性协议#{3}":[17,17]},"last_import":{"mtime":1752910301759,"size":500,"at":1752910349566,"hash":"1kmkcgb"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#---frontmatter---","lines":[1,7],"size":85,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#简介","lines":[9,13],"size":162,"outlinks":[{"title":"WEP","target":"WEP","line":4},{"title":"Wired Equivalent Privacy","target":"WEP","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#简介#{1}","lines":[10,10],"size":21,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#简介#{2}","lines":[11,11],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#简介#{3}","lines":[12,12],"size":61,"outlinks":[{"title":"WEP","target":"WEP","line":1},{"title":"Wired Equivalent Privacy","target":"WEP","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md#简介#{4}","lines":[13,13],"size":49,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md","last_embed":{"hash":"1kmkcgb","at":1752940870893},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06159853,-0.03706147,-0.00987728,-0.05991895,-0.02696908,-0.0090353,0.02639242,0.05928378,0.02370152,-0.01829622,0.00290274,-0.04822562,0.06863526,0.03893419,0.07530455,0.03619738,-0.02470818,0.0442465,0.03132711,-0.0018168,0.09657073,-0.03434613,-0.00961872,-0.03261191,0.00293312,0.04065052,0.01740073,-0.04507333,0.00738311,-0.17354579,0.0082957,-0.00672253,0.00993197,0.04151689,-0.00566478,-0.05158646,-0.01873654,0.03440474,-0.03607693,0.04721799,-0.01767613,0.0114615,-0.02163987,-0.07951374,-0.01750921,-0.08934708,-0.01133219,-0.05287271,-0.03446983,-0.02298017,-0.09358175,-0.02392379,-0.01737366,0.01234542,-0.05025704,0.02530301,0.06364224,0.03195503,0.05470051,-0.08935347,0.03263486,0.07605848,-0.19369178,0.05938255,0.0282327,0.0407208,-0.02370415,-0.0158193,0.0322371,0.01916357,-0.02377141,0.03647567,-0.01906746,0.02412187,0.01073857,0.04696381,0.01530734,-0.03600989,-0.0226964,-0.05628733,-0.02369855,0.0319033,0.03666161,-0.00001433,0.00554221,0.01195515,-0.02791204,-0.01946824,-0.0179699,-0.00923244,-0.01442846,-0.05062491,0.04337508,0.03224692,-0.01844443,0.004927,-0.00021997,0.06279325,-0.09712953,0.09536493,-0.06561174,0.01783006,-0.05320477,-0.03708239,0.03248242,-0.02823794,-0.00879545,-0.08257531,0.01350538,0.02170246,-0.08294629,-0.03319592,0.0432605,-0.01150569,0.04205386,0.04351373,0.03885775,0.03266557,-0.00269045,-0.0023285,-0.00484726,0.03208389,0.07358477,-0.00388261,-0.05720918,-0.05744446,0.02527411,0.04548164,0.01299847,0.05014984,0.05002735,-0.01079683,-0.03381402,0.03161193,-0.03638369,0.01057897,-0.05669268,-0.01902163,0.01575819,-0.05915683,-0.03986644,-0.01976879,-0.01046594,-0.07278704,-0.07553925,0.12032512,-0.02561926,0.05021003,0.03911863,-0.01417501,0.00827721,0.04035123,-0.005363,-0.00619255,0.0097001,0.01303032,0.08798289,0.12079618,0.00276959,-0.00765636,-0.04556068,-0.00332245,-0.06525657,0.20770563,0.0150913,-0.09410025,0.01829152,0.02081394,-0.00465209,-0.0816358,0.02842468,-0.04208788,0.05153221,0.0066027,0.04037908,0.01541572,-0.00007685,-0.03310702,-0.02198691,0.0348105,0.03882792,-0.03918387,-0.11948248,0.01436316,0.02440527,-0.03581909,-0.02891362,-0.03282298,0.01255599,-0.01298815,-0.09370614,0.06271205,-0.047391,0.02629146,-0.08030258,-0.0909547,0.04002439,-0.01708561,0.0810994,-0.04580271,0.03341117,0.02349071,-0.02983863,0.01089059,-0.07915252,-0.00524844,-0.00310522,-0.00388485,0.0031252,0.07975088,-0.00012958,0.01607564,-0.03671927,0.00621611,-0.0010153,0.02668251,0.05037927,0.030131,-0.00139911,0.06587785,-0.01591932,0.01655861,-0.05038153,-0.20725474,-0.00513269,-0.00989423,-0.042672,0.04745183,-0.09735931,-0.01960975,-0.00106611,0.04453478,0.06554779,0.09359728,0.06539612,-0.08228239,-0.01488062,-0.01844737,0.03735407,0.04853829,-0.04527688,-0.00281176,-0.01377813,-0.00786255,0.05631418,-0.04266396,-0.0763526,0.03493383,-0.02393478,0.12553443,-0.01976739,0.02065732,0.02706652,0.01398505,0.04115612,-0.01303293,-0.13102108,0.02161847,0.04052307,-0.03090482,-0.01665044,0.01851524,-0.05625131,-0.0311959,0.01433778,-0.04661352,-0.11130641,-0.00164716,-0.05006779,-0.04258047,0.01918482,0.01263578,0.01323663,0.00419832,0.020638,0.01892786,0.00793407,-0.00342205,-0.03055432,-0.02475618,-0.06367789,-0.01268972,0.07257795,0.02770241,0.0490446,0.00733302,-0.02023696,0.03861502,-0.01367415,-0.0101931,-0.00899458,-0.02316689,-0.01108877,-0.0033971,0.16183554,-0.00416026,-0.01545354,0.05590088,0.01369988,-0.0000508,-0.05161396,0.03577339,0.04275771,0.06600655,0.02869666,0.02167015,-0.00484798,-0.00050726,0.03645925,0.02441329,-0.00976917,0.05828902,-0.04600823,-0.02942754,-0.02360568,-0.04293907,0.00488847,0.06115514,0.01872434,-0.28396448,0.03057062,-0.01889653,0.02790427,0.05056833,0.0344183,0.02319433,-0.03913695,-0.06825286,0.00094083,-0.04967995,0.05977092,0.03046816,-0.02664089,-0.01368187,-0.02118077,0.07574052,-0.02040841,0.0137772,-0.03047078,-0.00869184,0.06730802,0.20967005,0.00239988,0.04271086,0.01040728,0.01597748,0.04623501,0.04828044,0.00051346,0.01122745,-0.04127106,0.0110748,0.03125474,0.04380348,0.03965651,-0.02145102,-0.00224976,-0.00731401,0.04057641,-0.06132149,0.02839838,-0.08112521,0.00988773,0.08057413,0.01647152,-0.04096562,-0.05307673,-0.03193901,0.05628395,0.04284283,0.01946798,0.00509495,0.01090404,0.04511404,0.09487233,0.04063652,-0.01607239,-0.07921364,0.01167762,0.00137631,0.04643978,0.05731574,0.07474111,0.04625088],"last_embed":{"hash":"371583826586165fb8656656777438af36438884b8947b7a8a09899f7deb9d45","tokens":181}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.01757724,0.03034209,-0.02409652,0.02315179,-0.00529531,0.01732778,0.02159757,-0.00469437,0.02939115,-0.02904507,0.02118628,-0.05275894,-0.00270356,-0.03108656,0.01047936,0.10283131,0.0161999,0.03829346,0.04642871,-0.05980827,0.00540507,0.0262644,-0.01720617,0.03402602,0.01740023,-0.0317152,-0.0197502,-0.05129292,0.03403004,0.02890635,0.03247663,-0.07438853,0.00089129,-0.01092776,-0.02976027,0.03554533,0.01154972,0.02592513,-0.00130491,0.02739689,-0.02625314,-0.02227665,-0.00293375,0.01045222,0.00377602,-0.00230009,-0.00943976,-0.01513699,0.00686323,0.04523002,0.04885574,-0.02439775,0.03147888,0.05424224,-0.03282357,0.10020802,0.02503175,-0.10291764,0.02818683,-0.00101448,0.03079451,-0.05263677,0.05662105,-0.01855208,0.04680457,-0.02633748,-0.04577958,-0.04441851,0.03720845,-0.00891927,-0.07963715,-0.05963646,0.02565132,0.00705985,-0.03612261,-0.06854852,0.02665637,0.00100608,-0.0126918,0.01761432,0.00236418,-0.00137773,0.04221056,-0.04514971,0.04425578,0.01039594,0.00690893,-0.10041703,-0.0072442,-0.00541967,-0.00445519,-0.048868,-0.03363941,-0.06737615,0.04866281,-0.0597826,-0.02198759,0.00277189,0.01880877,-0.01182244,-0.00782858,-0.01104523,0.0123717,-0.03271987,-0.00466065,-0.0529905,-0.0006558,0.06945924,-0.03477592,-0.05322192,0.01665005,0.01117149,-0.04701031,-0.02182888,-0.00853816,0.02076014,-0.02987227,-0.0384136,0.04510754,0.06457063,0.05482628,0.02619123,-0.02080368,-0.01132372,-0.02917323,-0.05339014,0.02877366,-0.01826377,0.03148774,-0.02674333,0.03798721,0.0083718,-0.00188612,-0.06598873,0.02347374,0.03340953,0.02954928,-0.0007657,-0.03450349,0.01748717,0.01942142,0.0076138,-0.00490235,0.01755214,-0.02820018,0.00561544,-0.07625517,-0.04919856,0.01729883,0.04850347,0.03222705,-0.00836136,-0.05470689,-0.04544793,-0.03387249,0.00062256,0.01266051,0.01337323,0.0162876,0.0533656,-0.05437696,-0.01262351,0.02997708,0.00261564,-0.02401785,-0.00191269,-0.02578779,0.08178817,0.0492033,0.01438117,-0.08259326,0.04656337,0.07877694,0.01662489,0.01219586,-0.00453327,0.01583908,0.0357064,-0.02882618,-0.00393872,0.04554716,-0.01196747,-0.01668816,0.05424735,0.00610898,0.01419343,-0.06175114,-0.03033506,0.05095667,0.00748031,0.01629536,-0.02091016,0.00539227,0.04129934,0.03084081,0.03779091,0.0542471,-0.07539383,0.00592571,0.02535916,0.00492181,-0.04859629,-0.03058867,-0.1008965,0.04048383,0.01220086,0.03348347,-0.06207674,0.02298289,0.06798094,-0.01861982,0.03099524,0.05779428,0.03801617,0.01472051,0.03374049,-0.01702597,0.01659109,0.03446631,-0.09096445,0.01677808,0.03708328,-0.04544708,0.05824513,0.00742016,-0.00009026,0.01460445,0.02423518,-0.0418161,0.00336714,-0.04618374,0.08793462,-0.0347129,-0.05428148,-0.03933463,0.04043357,0.03319452,0.00143726,0.02082284,-0.0057735,-0.0347001,-0.0173371,0.01762555,-0.02668414,-0.07048815,-0.00138939,-0.0484058,0.04062995,0.04289442,0.08957641,-0.00015071,0.00193699,-0.04987924,-0.00176762,-0.03644785,-0.04245392,0.00345769,-0.02793298,-0.01500961,-0.01927776,0.06199683,-0.05041804,0.01937094,0.01516095,-0.04823941,0.00477215,0.01655066,-0.00493362,-0.00081825,0.011426,-0.02295427,-0.02107125,0.03262081,-0.01872638,0.02487464,-0.0146009,0.01811061,0.02089426,0.00420222,-0.01509881,-0.0249863,-0.02065467,0.02933494,0.01050472,0.00299249,0.03377889,-0.00480168,0.03223483,0.06654029,-0.00652621,-0.08262619,0.0193854,-0.00190095,-0.02547722,-0.02443863,-0.06994582,-0.01833944,0.00573395,-0.0074221,-0.06100108,0.015691,0.01630129,0.01191912,-0.04485119,-0.00758931,-0.02120288,0.00009046,-0.02128968,0.01334191,-0.00448786,-0.01897428,-0.07928195,-0.02177091,-0.0626581,0.00374787,0.06700642,0.06477388,0.00704203,-0.05990083,0.00435009,-0.01693589,0.01005548,0.0006359,0.01835884,-0.08410919,-0.03783572,0.02455252,0.00755929,0.00972809,-0.01543177,-0.03104357,-0.02229132,0.00039482,0.00655213,-0.01107256,0.02614069,-0.02658004,-0.06599043,0.03475199,-0.00385424,-0.01456819,-0.0792104,0.06112261,0.01687375,0.03352015,0.02012697,-0.00727889,0.02220251,-0.03487531,-0.00669515,0.02381575,0.02470724,0.00603829,0.02109336,0.00836254,-0.00494381,-0.02558296,0.02627453,-0.06037509,-0.01880981,0.02690064,-0.04236132,-0.08052881,0.00808319,0.00425354,0.03618588,0.05979678,0.03082785,-0.05542723,0.03128071,-0.02622028,0.04023197,0.02058721,-0.04738495,-0.04989274,0.001342,0.01048045,0.00244959,-0.01440562,-0.04395197,0.00029948,0.00798659,-0.01025632,-0.02657875,-0.02670924,-0.02361983,-0.05848538,0.03022424,0.03238486,-0.0367443,-0.00413593,0.01628373,-0.06474376,0.00570298,0.01838961,0.00812411,-0.02398742,-0.0134733,-0.00971689,-0.01696174,0.00919893,-0.0172123,0.01424605,0.02020413,0.00511744,0.00773489,-0.04912433,0.10589262,-0.00361958,-0.02511119,0.02145997,0.03996805,0.02811692,0.02415645,0.07035405,-0.03771789,-0.00201549,-0.06472933,-0.04399035,0.0183292,0.02163794,-0.01766795,0.04524411,0.02335136,-0.04437553,0.03309558,-0.01102418,0.01283588,-0.01181607,0.00173907,0.00568677,0.03049851,-0.01974829,-0.05903853,-0.0086203,-0.03254455,0.04230957,-0.02405241,0.02177032,-0.06725308,0.00624676,-0.04716326,-0.05575622,-0.01086142,-0.04455195,0.00545113,0.00575704,0.02789817,0.01970309,0.02271708,0.02126418,-0.0316105,-0.05080951,0.00633544,0.01693329,-0.02171743,0.04069105,-0.00126488,0.06442265,0.02863381,-0.02465212,0.0093247,-0.06362738,-0.01940552,0.05527695,-0.00724289,0.04743969,0.02710309,0.01279863,0.02058475,-0.00799848,-0.06129783,0.0232619,0.01904123,0.04223381,0.00972406,0.06337225,0.00829656,0.04382619,-0.00419587,-0.02872258,-0.05764193,-0.03331663,0.00451398,0.0066947,0.05566779,-0.00689625,0.05103613,0.05520417,-0.02509948,0.05650439,0.01231873,0.00118737,0.00232968,-0.03899215,0.03494765,0.03696726,0.03178119,-0.02915699,0.02103328,0.01525664,-0.02212731,-0.07922339,0.00052357,0.0258001,0.03581307,-0.00612096,-0.00145119,0.04740915,-0.04253366,0.00411023,0.04417469,0.05644128,0.06309927,-0.00609874,-0.01932091,-0.04494142,0.04628278,-0.01177547,-0.05833697,-0.05819144,0.02438409,-0.05119571,0.02767672,0.00022233,-0.00489131,0.0095396,0.01386204,0.0252537,0.02168117,-0.03173897,-0.0375105,0.01029116,-0.01563881,0.01235142,0.00273103,0.00756791,-0.03795188,-0.01226602,0.06382065,-0.00228121,-0.00135014,-0.01590136,-0.04605193,0.03681218,-0.00236949,0.03402443,-0.04245681,0.04222946,0.00957084,0.03642891,0.00480959,0.02560242,0.04568681,-0.00513704,0.0076344,0.00450581,-0.01507619,0.00973208,0.04526468,-0.01992133,0.01742267,0.01104963,0.04116106,-0.04366997,-0.00795956,-0.08644006,0.00583742,0.00243133,-0.00210377,-0.002281,-0.02182574,0.05160382,0.000917,-0.08728416,-0.02541738,-0.00714905,-0.01061113,0.04645449,0.08609443,-0.00240489,-0.03826259,0.07742693,-0.0421844,0.00898242,-0.02044077,-0.00617932,-0.02170139,0.00451544,-0.01806646,0.01408031,-0.05207546,0.00522704,-0.06863923,0.0239542,-0.01067074,-0.0259735,0.03759015,0.11356379,-0.04192049,0.02622163,0.02004352,0.05305646,-0.00558222,0.01486332,-0.03859301,-0.01835961,-0.07506137,-0.03688676,0.03089656,0.0480185,0.02085287,0.01405658,-0.04475814,0.01362503,-0.00301752,0.01493331,-0.06630359,-0.00026365,0.01500168,-0.06127043,-0.02649401,0.02722301,-0.07674654,0.04715552,0.0466999,0.04407703,0.00656859,-0.00268938,0.00553991,-0.00663902,-0.06544,0.0131754,0.04477401,-0.02226002,0.07576152,-0.03673809,-0.00157493,-0.05900104,-0.02056381,0.07922205,0.03606813,0.06461366,0.01084185,0.01177615,-0.02906176,-0.01485003,-0.03729684,-0.02775856,-0.04307806,-0.00232723,-0.00191711,-0.01882719,0.02348418,-0.01991098,-0.02252812,0.0229844,-0.00040866,0.00202186,0.05735107,-0.05801504,-0.0619854,-0.02884027,-0.00325397,-0.00932726,0.01202178,0.00695911,0.03134989,-0.03321749,0.02871589,0.02219154,-0.01215818,0.01799813,0.01904091,0.00699696,0.04095124,0.02811873,0.03759513,-0.01386305,-0.01507781,-0.00167029,0.02982132,0.01012127,0.05602633,0.0365678,0.02010252,0.04367116,-0.01461629,-0.07029489,-0.04301105,-0.01682515,0.03728966,0.0461247,-0.06571759,0.00953017,-0.03328501,0.00330243,0.04139251,-0.02761284,0.04747585,-0.00726231,0.04157972,-0.05536377,0.01593708,-0.04820296,0.00097064,0.016122,0.0264678,-0.02388885,0.0130418,0.00789165,0.02075232,-0.0427573,-0.01642741,0.02472305,0.00800774,-0.02521263,0.04111459,-0.02708606,-0.00676666,-0.03390647,0.03923443,-0.02902238,-0.01871938,-0.08504029,-0.02503825,-0.04198693,0.03398398,0.01499414,0.0246093,-0.01385545,-0.02278767,0.06299459,0.02863646,0.07370681,-0.03283135,0.0524798,-0.01811758,0.04541058,0.0075405,0.04812423,-0.03225376,-0.04600281,-0.05513762,0.05612924,-0.03051004,0.01683208,0.03292334,-0.0102962,-0.00740794,0.01112019,0.02143274,-0.0760038,0.05951158,0.06005898,0.02528236,-0.0027485,-0.03461944,8e-7,-0.02281478,-0.02472742,0.06571431,-0.08680372,-0.01276192,-0.05828679,-0.01052704,0.01918256,0.01223386],"last_embed":{"tokens":145,"hash":"1kmkcgb"}}},"last_read":{"hash":"1kmkcgb","at":1752940870893},"class_name":"SmartSource","outlinks":[{"title":"WEP","target":"WEP","line":16},{"title":"Wired Equivalent Privacy","target":"WEP","line":16}],"metadata":{"aliases":["Temporal Key Integrity Protocol","临时密钥完整性协议"],"tags":["网络安全/密码学"],"cssclasses":["editor-full"],"created":"2024-09-09T21:06","updated":"2025-07-19T15:31"},"blocks":{"#---frontmatter---":[1,11],"#简介":[13,17],"#简介#临时密钥完整性协议":[14,17],"#简介#临时密钥完整性协议#{1}":[15,15],"#简介#临时密钥完整性协议#{2}":[16,16],"#简介#临时密钥完整性协议#{3}":[17,17]},"last_import":{"mtime":1752910301759,"size":500,"at":1752910349566,"hash":"1kmkcgb"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/TKIP协议.md"},