{"id": "68d2d91f-f2db-454f-83fc-1af0517f4400", "title": "hello", "messages": [{"role": "user", "applyStatus": 0, "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "hello", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "<task>\nhello\n</task>\n\n<current_tab_note path=\"数据分析/端粒.md\">\nNote Path: 数据分析/端粒.md\n\n## Metadata (Frontmatter)\ncssclasses: [\"editor-full\"]\ncreated: \"2025-07-20T11:32\"\nupdated: \"2025-07-20T11:37\"\naliases: [\"Telomeres\"]\nalias: null\n\n## Outline\n- 简介\n  - 调控衰老的特殊 DNA 序列\n  - **关键特征**\n  - **意义与研究热点**\n  - **未来展望**\n</current_tab_note>\n\n<environment_details>\n# Current File\n数据分析/端粒.md\n\n# Current Workspace\nUnited (entire vault)\n\n# Assistant & User State\n## Current Time\n2025/7/20 上午11:45:16 (Asia/Hong_Kong, UTC+8:00)\n\n## Current Mode\n<slug>write</slug>\n<name>Write</name>\n</environment_details>"}], "id": "887cc067-6f83-42b4-b8c4-88f01a4a4cdd", "mentionables": [{"type": "current-file", "file": "数据分析/端粒.md"}]}, {"role": "assistant", "applyStatus": 0, "content": "", "reasoningContent": "", "id": "bc4cfae2-b841-40b8-9233-a9d990bde9b2", "metadata": {}}, {"role": "user", "applyStatus": 0, "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "去除掉 ", "type": "text", "version": 1}, {"detail": 1, "format": 0, "mode": "token", "style": "", "text": "@约翰·卡马克.md", "type": "mention", "version": 1, "mentionName": "约翰·卡马克.md", "mentionable": {"type": "file", "file": "Clippings/约翰·卡马克.md"}}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "  当中的外部链接", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "<feedback>\n去除掉 @约翰·卡马克.md  当中的外部链接\n</feedback>\n\n<user_mention_file path=\"Clippings/约翰·卡马克.md\">\nNote Path: Clippings/约翰·卡马克.md\n\n## Metadata (Frontmatter)\ntags: [\"社会体系/角色\"]\ncssclasses: [\"editor-full\"]\naliases: [\"John D. Carmack\"]\n性别: [\"#Man\"]\n职业: [\"程序员\",\"游戏开发者\"]\ncreated: \"2024-12-22T15:49\"\nupdated: \"2025-07-20T12:00\"\n</user_mention_file>\n\n<current_tab_note path=\"地理数据/笔记创建面板.md\">\nNote Path: 地理数据/笔记创建面板.md\n\n## Metadata (Frontmatter)\ncssclasses: [\"editor-full\"]\nexcalidraw-css: null\nlocation: null\n</current_tab_note>\n\n<environment_details>\n# Current File\n地理数据/笔记创建面板.md\n\n# Current Workspace\nUnited (entire vault)\n\n# Assistant & User State\n## Current Time\n2025/7/20 下午12:02:09 (Asia/Hong_Kong, UTC+8:00)\n\n## Current Mode\n<slug>write</slug>\n<name>Write</name>\n</environment_details>"}], "id": "7cab478b-b183-4cda-ab1d-9c640a2b38ef", "mentionables": [{"type": "current-file", "file": "地理数据/笔记创建面板.md"}, {"type": "file", "file": "Clippings/约翰·卡马克.md"}]}, {"role": "assistant", "applyStatus": 1, "content": "<thinking>\n1. 需要修改的文件是 [Clippings/约翰·卡马克.md](Clippings/约翰·卡马克.md)\n2. 任务要求是去除文件中的所有外部链接\n3. 需要先查看文件完整内容才能确定哪些是外部链接需要删除\n4. 将使用 read_file 工具获取文件完整内容\n</thinking>\n\n<read_file>\n<path>Clippings/约翰·卡马克.md</path>\n</read_file>", "reasoningContent": "", "id": "3c56d7ce-bb9a-4ec1-9fec-441bca0bba63", "metadata": {"model": {"provider": "Deepseek", "modelId": "deepseek-chat"}}}, {"role": "assistant", "applyStatus": 0, "content": "<tool_result>[read_file for 'Clippings/约翰·卡马克.md'] Result:\n 1 | ---\n 2 | tags:\n 3 |   - 社会体系/角色\n 4 | cssclasses:\n 5 |   - editor-full\n 6 | aliases:\n 7 |   - <PERSON>\n 8 | 性别:\n 9 |   - \"#Man\"\n10 | 职业:\n11 |   - 程序员\n12 |   - 游戏开发者\n13 | created: 2024-12-22T15:49\n14 | updated: 2025-07-20T12:01\n15 | ---\n16 | 约翰·卡马克（John D. Carmack II），是美国的电玩游戏程序员 <sup data-tag=\"ref\"> [2]<em id=\"sup-2\"></em></sup>、id Software的创始人之一。卡马克是一个众人皆知的[开源软件](https://baike.baidu.com/item/%E5%BC%80%E6%BA%90%E8%BD%AF%E4%BB%B6/8105369?fromModule=lemma_inlink)的倡导者，他也再三强调反对“[软件专利](https://baike.baidu.com/item/%E8%BD%AF%E4%BB%B6%E4%B8%93%E5%88%A9/10009189?fromModule=lemma_inlink)”，但是他一直处于势单力孤的状态\n17 | \n18 | 卡马克在[慈善事业](https://baike.baidu.com/item/%E6%85%88%E5%96%84%E4%BA%8B%E4%B8%9A/2384321?fromModule=lemma_inlink)方面的贡献以及对[游戏社区](https://baike.baidu.com/item/%E6%B8%B8%E6%88%8F%E7%A4%BE%E5%8C%BA/7783297?fromModule=lemma_inlink)的关心也同样出名。一个以卡马克名字命名的基金用来资助他的母校（高中）、开源软件的倡导者、游戏专利的反对者和热心的玩家。1997年，他把他的一辆法拉利跑车作为奖品，送给了一次[雷神之锤](https://baike.baidu.com/item/%E9%9B%B7%E7%A5%9E%E4%B9%8B%E9%94%A4/1020067?fromModule=lemma_inlink)比赛“赤色全歼(Red Annihilation)”的胜利者——丹尼斯·方(Dennis Fong)\n19 | \n20 | 2022年12月，卡马克辞去了Meta虚拟现实部门的领导职务。\n21 | \n22 | 小名约翰。出生在物质生活相对优渥的家庭。其父亲由于工作能力出色还晋升为一家电视台的晚间[新闻主播](https://baike.baidu.com/item/%E6%96%B0%E9%97%BB%E4%B8%BB%E6%92%AD/6395180?fromModule=lemma_inlink)，这家电视台是[密苏里州](https://baike.baidu.com/item/%E5%AF%86%E8%8B%8F%E9%87%8C%E5%B7%9E/3438672?fromModule=lemma_inlink)[堪萨斯城](https://baike.baidu.com/item/%E5%A0%AA%E8%90%A8%E6%96%AF%E5%9F%8E/2930792?fromModule=lemma_inlink)最大的三家电视台之一，他们随后搬迁到了郊外的[富人区](https://baike.baidu.com/item/%E5%AF%8C%E4%BA%BA%E5%8C%BA/319143?fromModule=lemma_inlink)。在那里，约翰有了个小弟弟，彼得。同年，约翰进了圣母玛利亚天主教小学，这是当地最好的学校之一。这个从一岁起就戴上眼镜，长着一头凌乱金发的瘦小男孩在学校里很快就显示出了与众不同。二年级的时候，只有七岁的他几乎在所有[标准化测验](https://baike.baidu.com/item/%E6%A0%87%E5%87%86%E5%8C%96%E6%B5%8B%E9%AA%8C/4404792?fromModule=lemma_inlink)里都得到了满分，这相当于九年级的水平。约翰还养成了一种很独特的口吃习惯：在每句话后面加上简短的类似机器人那样的嗡嗡声，就像一台计算机在[处理数据](https://baike.baidu.com/item/%E5%A4%84%E7%90%86%E6%95%B0%E6%8D%AE/18940739?fromModule=lemma_inlink)一样：“12乘以12等于144……嗯嗯。”卡马克的[父母在](https://baike.baidu.com/item/%E7%88%B6%E6%AF%8D%E5%9C%A8/12692305?fromModule=lemma_inlink)他十二岁的时候突然离婚了，他们在如何抚养孩子的事情上还闹的很紧张。茵戈觉得这给卡马克造成了无法弥补的创伤。就当卡马克开始在学校里找到点乐趣的时候，他和弟弟彼得又不得不在双亲之间来回轮换，不停地转学。卡马克不喜欢和父亲分开，更糟糕的是，当他和母亲住在一起的时候，他得自己照顾自己。\n23 | \n24 | 尚在就读高中时，卡马克就被认为是“怪才”，他还因此被学校视为有[心理问题](https://baike.baidu.com/item/%E5%BF%83%E7%90%86%E9%97%AE%E9%A2%98/11031400?fromModule=lemma_inlink)，并停课一年。复课后，卡马克还是拿到了毕业证书，并考入了当地著名的[Kansas](https://baike.baidu.com/item/Kansas/2771883?fromModule=lemma_inlink)大学。在编程方面，卡马克几乎是无师自通。大一还没读完，他编写的一些小游戏就被不少软件公司买走，他本人也成为了几家软件公司的兼职程序员，并逐渐在游戏软件领域小有名气。\n25 | \n26 | 1990年，正值[计算机技术](https://baike.baidu.com/item/%E8%AE%A1%E7%AE%97%E6%9C%BA%E6%8A%80%E6%9C%AF/1127562?fromModule=lemma_inlink)的黄金发展时期，[IBM](https://baike.baidu.com/item/IBM/9190?fromModule=lemma_inlink)[个人电脑](https://baike.baidu.com/item/%E4%B8%AA%E4%BA%BA%E7%94%B5%E8%84%91/3688503?fromModule=lemma_inlink)为整个软件业带来了前所未有的机遇。但这时的软件多以商业用途为目的，游戏软件却少人问津。当时一家名为Softdisk的软件公司找到了在读大二的卡马克，希望他加入公司，一起开发游戏软件。出于对游戏软件的热爱，他毫不犹豫的同意了对方的邀请。卡马克首先遇到的问题是如何将游戏程序移植到个人电脑上，经过一番考虑，他选择了[IBM PC](https://baike.baidu.com/item/IBM%20PC/4837823?fromModule=lemma_inlink)作为最早的游戏运行平台，游戏软件就是任天堂公司风靡全球的《[超级马里奥兄弟3](https://baike.baidu.com/item/%E8%B6%85%E7%BA%A7%E9%A9%AC%E9%87%8C%E5%A5%A5%E5%85%84%E5%BC%9F3/12508299?fromModule=lemma_inlink)》。经过实验，他开发出了一种名为[EGA](https://baike.baidu.com/item/EGA/7623934?fromModule=lemma_inlink)([增强型图形](https://baike.baidu.com/item/%E5%A2%9E%E5%BC%BA%E5%9E%8B%E5%9B%BE%E5%BD%A2/22068654?fromModule=lemma_inlink)[适配器](https://baike.baidu.com/item/%E9%80%82%E9%85%8D%E5%99%A8/908851?fromModule=lemma_inlink)，3D图形加速卡的雏形)的PC[显示技术](https://baike.baidu.com/item/%E6%98%BE%E7%A4%BA%E6%8A%80%E6%9C%AF/5851114?fromModule=lemma_inlink)，这是一种16色的[显示模式](https://baike.baidu.com/item/%E6%98%BE%E7%A4%BA%E6%A8%A1%E5%BC%8F/12724843?fromModule=lemma_inlink)。之后，他又设计出屏幕刷新技术以提高游戏[图形显示](https://baike.baidu.com/item/%E5%9B%BE%E5%BD%A2%E6%98%BE%E7%A4%BA/2612527?fromModule=lemma_inlink)的速度。不久，IBM PC的第一款[2D](https://baike.baidu.com/item/2D/11445?fromModule=lemma_inlink)游戏成功问世，迈出了游戏软件历史性的一步。一年后，他决定自己创业，与他人共同创办了ID Software游戏公司。在[id](https://baike.baidu.com/item/id/22219508?fromModule=lemma_inlink) Software公司里，卡马克和他的同事通过一部又一部血腥暴力的作品创造并不断革新着这一游戏类别。id所获得的无数荣誉应当归属于它的全体员工，而约翰·卡马克无疑是将整个公司凝聚在一起的[粘合剂](https://baike.baidu.com/item/%E7%B2%98%E5%90%88%E5%89%82/3705684?fromModule=lemma_inlink)。\n27 | \n28 | 雄心勃勃的卡马克当然不会停留在人人都能设计的2D游戏上，他把目标锁定在了3D游戏的研发上。如果是在几年前，他的“3D计划”可能很难实现，好在那时的计算机硬件技术已发展到了相当的水平—高分辨率显示器、大容量硬盘和内存、高速运算的[CPU](https://baike.baidu.com/item/CPU/120556?fromModule=lemma_inlink)和[图形加速卡](https://baike.baidu.com/item/%E5%9B%BE%E5%BD%A2%E5%8A%A0%E9%80%9F%E5%8D%A1/3549886?fromModule=lemma_inlink)等等，这些都是他实现梦想的绝好基础。两年后，他与另一个游戏软件天才John Romero[共同开发](https://baike.baidu.com/item/%E5%85%B1%E5%90%8C%E5%BC%80%E5%8F%91/12674474?fromModule=lemma_inlink)出了全球首款[3D射击游戏](https://baike.baidu.com/item/3D%E5%B0%84%E5%87%BB%E6%B8%B8%E6%88%8F/14696892?fromModule=lemma_inlink)《德军总部3D》，这款3D游戏采用了他独创的[3D游戏引擎](https://baike.baidu.com/item/3D%E6%B8%B8%E6%88%8F%E5%BC%95%E6%93%8E/410573?fromModule=lemma_inlink)。紧接着，他又开发出了《Doom》和《Quake》两款[3D游戏](https://baike.baidu.com/item/3D%E6%B8%B8%E6%88%8F/1747524?fromModule=lemma_inlink)。一时间，所有的电脑用户都争相购买这些游戏，人们甚至为了能玩上3D游戏而去购买昂贵的PC。不到一年的时间，仅《Doom》一款游戏就售出了几百万张拷贝，带来了上亿[美元](https://baike.baidu.com/item/%E7%BE%8E%E5%85%83/484146?fromModule=lemma_inlink)的[商业利润](https://baike.baidu.com/item/%E5%95%86%E4%B8%9A%E5%88%A9%E6%B6%A6/5000877?fromModule=lemma_inlink)。\n29 | \n30 | id Software的联合创始人约翰·卡马克(John Carmack)已经正式加入Oculus Rift团队，并且担任[首席技术官](https://baike.baidu.com/item/%E9%A6%96%E5%B8%AD%E6%8A%80%E6%9C%AF%E5%AE%98/783926?fromModule=lemma_inlink)一职。这位游戏界先锋人物将在[Oculus Rift](https://baike.baidu.com/item/Oculus%20Rift/352914?fromModule=lemma_inlink)公司位于德州达拉斯的新办公室上班。在一篇[新闻稿](https://baike.baidu.com/item/%E6%96%B0%E9%97%BB%E7%A8%BF/6869821?fromModule=lemma_inlink)中，卡马克表示，虚拟现实的梦想已经在幕后憋了几十年，但现在，人与技术终于走到了一起，使其能够触碰到很多潜在的想象。卡马克对此感到非常兴奋，并认为这是[技术变革](https://baike.baidu.com/item/%E6%8A%80%E6%9C%AF%E5%8F%98%E9%9D%A9/7668980?fromModule=lemma_inlink)的一个标记。[Bethesda](https://baike.baidu.com/item/Bethesda/1435551?fromModule=lemma_inlink)的一名发言人对Gamasutra说到，卡马克一直对Oculus VR很感兴趣，并希望在该项目上投入时间。此外，他在id Software提供游戏发展的领导与此并无影响。但与此同时，Oculus公司的发言人也指出，卡马克将是Oculus VR的一名全职员工。\n31 | \n32 | 卡马克与Oculus Rift的交集至少有一年了。在2012年6月的一次采访中，它就表示过自己正在努力打造一个[虚拟现实](https://baike.baidu.com/item/%E8%99%9A%E6%8B%9F%E7%8E%B0%E5%AE%9E/207123?fromModule=lemma_inlink)头戴式装置，并且在2011年的[E3](https://baike.baidu.com/item/E3/324804?fromModule=lemma_inlink)大展上花了大把时间来演示《毁灭战士3》(Doom 3 BFG Edition)。该项目最终在[Kickstarter](https://baike.baidu.com/item/Kickstarter/8332973?fromModule=lemma_inlink)上筹集到了250万美元的资金。\n33 | \n34 | 最后，该公司计划为市场带来目标价为300美元的Oculus Rift，不过尚未设定确切的商用发布日期。\n35 | \n36 | 卡马克加盟Oculus Rift（虚拟现实设备制造公司）则使这种若即若离的关系更加脆弱。大神发推称：\"我本想保留一个id技术顾问的头衔，但于事无补。这样也许对双方都好，专注于两条战线是很难办到的。\" <sup data-tag=\"ref\"> [1]<em id=\"sup-1\"></em></sup>\n37 | \n38 | 坦白地说，卡马克主要是一名技术天才，id公司出品的每一款游戏都是围绕他所编写的引擎制作而成的，卡马克不断把引擎技术推向新的高度。他为游戏业作出的第一个贡献是实现了卷轴游戏[背景图像](https://baike.baidu.com/item/%E8%83%8C%E6%99%AF%E5%9B%BE%E5%83%8F/9071410?fromModule=lemma_inlink)的流畅性，这一技术在1990年的《[指挥官基恩](https://baike.baidu.com/item/%E6%8C%87%E6%8C%A5%E5%AE%98%E5%9F%BA%E6%81%A9/10345602?fromModule=lemma_inlink)》（Commander Keen）中得到了应用，此前电脑平台上的横向卷轴游戏的背景图像都很不稳定，根本无法同当时游戏机平台上的横向卷轴游戏相比。尽管这一技术在今天看来算不上什么，但它足以显示出卡马克高超的编程能力。\n39 | \n40 | 卡马克今天的地位主要归功于他在3D方面的成就，尤其是对3D加速技术不遗余力的倡导和传播。《[雷神之锤](https://baike.baidu.com/item/%E9%9B%B7%E7%A5%9E%E4%B9%8B%E9%94%A4/1020067?fromModule=lemma_inlink)》刚刚问世的时候，[3D加速卡](https://baike.baidu.com/item/3D%E5%8A%A0%E9%80%9F%E5%8D%A1/1128938?fromModule=lemma_inlink)在人们眼里还只是一个可笑的空想而已，只有卡马克对[3D技术](https://baike.baidu.com/item/3D%E6%8A%80%E6%9C%AF/8202338?fromModule=lemma_inlink)的威力深信不疑，他为《雷神之锤》制作了一个专门在Verite显卡上运行的特别版本，画面看上去非常漂亮，可惜的是Verite显卡未能在市场上站稳脚跟。随后卡马克又采用[OpenGL](https://baike.baidu.com/item/OpenGL/238984?fromModule=lemma_inlink)标准为《雷神之锤》制作了一个新的版本，使所有具备3D加速能力的显卡都能以更快的速度、更高的分辨率渲染出更华丽的图像。到了今天，一些显卡生产商在研发新产品之前甚至会先同卡马克商量一下，以确保他们的硬件可以支持id出品的游戏。卡马克所作的这一切决定性地改变了显卡产业未来的发展。\n41 | \n42 | 除了3D方面的成就外，约翰·卡马克还为游戏业带来了许多其它的技术革新，例如网络代码中的客户端侦测、多重纹理、便于修改和可扩展的[游戏代码](https://baike.baidu.com/item/%E6%B8%B8%E6%88%8F%E4%BB%A3%E7%A0%81/6654927?fromModule=lemma_inlink)、游戏内部的[命令行](https://baike.baidu.com/item/%E5%91%BD%E4%BB%A4%E8%A1%8C/196110?fromModule=lemma_inlink)指令……看来所有该做的都已经被卡马克做完了，可谁知道呢，也许这一切只不过是开始而已。\n43 | \n44 | 。 <sup data-tag=\"ref\"> [3]<em id=\"sup-3\"></em></sup>\n45 | \n46 | 根据Dallas Innovates的采访，约翰·卡马克指出，AI可能会在短短十年内像人类一样行动和思考。 这位虚拟现实的梦想家表示，[[通用人工智能]]是人工智能的“大黄铜环”，到2030年将成长为一个万亿美元的产业。\n47 | \n48 | 卡马克在1995年放出了[德军总部3D](https://baike.baidu.com/item/%E5%BE%B7%E5%86%9B%E6%80%BB%E9%83%A83D/0?fromModule=lemma_inlink)的[源代码](https://baike.baidu.com/item/%E6%BA%90%E4%BB%A3%E7%A0%81/3969?fromModule=lemma_inlink)，之后的1996年时候，他放出了[雷神之锤](https://baike.baidu.com/item/%E9%9B%B7%E7%A5%9E%E4%B9%8B%E9%94%A4/0?fromModule=lemma_inlink)的源代码，Quake社区中的一名不太出名的程序员将其改写成了Linux版本，并且将修改后的游戏发给了卡马克。卡马克没有认为这是[侵权行为](https://baike.baidu.com/item/%E4%BE%B5%E6%9D%83%E8%A1%8C%E4%B8%BA/3356335?fromModule=lemma_inlink)然后付诸法律，而是要求id Software的员工们用这个版本作为雷神之锤linux版本的基础。id Software在后来的日子里也同样公布了[雷神之锤II](https://baike.baidu.com/item/%E9%9B%B7%E7%A5%9E%E4%B9%8B%E9%94%A4II/1000299?fromModule=lemma_inlink)的代码，[雷神之锤III](https://baike.baidu.com/item/%E9%9B%B7%E7%A5%9E%E4%B9%8B%E9%94%A4III/0?fromModule=lemma_inlink)的代码也于2005年8月19日公布，这些代码的公布全遵循了[GPL](https://baike.baidu.com/item/GPL/0?fromModule=lemma_inlink)准则。毁灭战士的代码也使用GPL准则在1999年重新公布。\n49 | \n50 | ---\n51 | \n52 | * 1997\n53 | \t* @ 放出了毁灭战士的代码。\n54 | * 2013\n55 | \t* 加入 VR 开发商 Oculus\n56 | * 2014\n57 | \t* Meta（当时还叫 Facebook）收购了 Oculus。他最初在 Oculus 担任首席技术官\n58 | * 2022-12\n59 | \t* 卡马克辞去了 Meta 虚拟现实部门的领导职务\n</tool_result>", "reasoningContent": "", "id": "390a2ed6-8a60-41eb-8283-f79473dd83ef", "metadata": {}}, {"role": "user", "applyStatus": 0, "content": null, "promptContent": "[read_file for 'Clippings/约翰·卡马克.md'] Result:\n 1 | ---\n 2 | tags:\n 3 |   - 社会体系/角色\n 4 | cssclasses:\n 5 |   - editor-full\n 6 | aliases:\n 7 |   - <PERSON>\n 8 | 性别:\n 9 |   - \"#Man\"\n10 | 职业:\n11 |   - 程序员\n12 |   - 游戏开发者\n13 | created: 2024-12-22T15:49\n14 | updated: 2025-07-20T12:01\n15 | ---\n16 | 约翰·卡马克（John D. Carmack II），是美国的电玩游戏程序员 <sup data-tag=\"ref\"> [2]<em id=\"sup-2\"></em></sup>、id Software的创始人之一。卡马克是一个众人皆知的[开源软件](https://baike.baidu.com/item/%E5%BC%80%E6%BA%90%E8%BD%AF%E4%BB%B6/8105369?fromModule=lemma_inlink)的倡导者，他也再三强调反对“[软件专利](https://baike.baidu.com/item/%E8%BD%AF%E4%BB%B6%E4%B8%93%E5%88%A9/10009189?fromModule=lemma_inlink)”，但是他一直处于势单力孤的状态\n17 | \n18 | 卡马克在[慈善事业](https://baike.baidu.com/item/%E6%85%88%E5%96%84%E4%BA%8B%E4%B8%9A/2384321?fromModule=lemma_inlink)方面的贡献以及对[游戏社区](https://baike.baidu.com/item/%E6%B8%B8%E6%88%8F%E7%A4%BE%E5%8C%BA/7783297?fromModule=lemma_inlink)的关心也同样出名。一个以卡马克名字命名的基金用来资助他的母校（高中）、开源软件的倡导者、游戏专利的反对者和热心的玩家。1997年，他把他的一辆法拉利跑车作为奖品，送给了一次[雷神之锤](https://baike.baidu.com/item/%E9%9B%B7%E7%A5%9E%E4%B9%8B%E9%94%A4/1020067?fromModule=lemma_inlink)比赛“赤色全歼(Red Annihilation)”的胜利者——丹尼斯·方(Dennis Fong)\n19 | \n20 | 2022年12月，卡马克辞去了Meta虚拟现实部门的领导职务。\n21 | \n22 | 小名约翰。出生在物质生活相对优渥的家庭。其父亲由于工作能力出色还晋升为一家电视台的晚间[新闻主播](https://baike.baidu.com/item/%E6%96%B0%E9%97%BB%E4%B8%BB%E6%92%AD/6395180?fromModule=lemma_inlink)，这家电视台是[密苏里州](https://baike.baidu.com/item/%E5%AF%86%E8%8B%8F%E9%87%8C%E5%B7%9E/3438672?fromModule=lemma_inlink)[堪萨斯城](https://baike.baidu.com/item/%E5%A0%AA%E8%90%A8%E6%96%AF%E5%9F%8E/2930792?fromModule=lemma_inlink)最大的三家电视台之一，他们随后搬迁到了郊外的[富人区](https://baike.baidu.com/item/%E5%AF%8C%E4%BA%BA%E5%8C%BA/319143?fromModule=lemma_inlink)。在那里，约翰有了个小弟弟，彼得。同年，约翰进了圣母玛利亚天主教小学，这是当地最好的学校之一。这个从一岁起就戴上眼镜，长着一头凌乱金发的瘦小男孩在学校里很快就显示出了与众不同。二年级的时候，只有七岁的他几乎在所有[标准化测验](https://baike.baidu.com/item/%E6%A0%87%E5%87%86%E5%8C%96%E6%B5%8B%E9%AA%8C/4404792?fromModule=lemma_inlink)里都得到了满分，这相当于九年级的水平。约翰还养成了一种很独特的口吃习惯：在每句话后面加上简短的类似机器人那样的嗡嗡声，就像一台计算机在[处理数据](https://baike.baidu.com/item/%E5%A4%84%E7%90%86%E6%95%B0%E6%8D%AE/18940739?fromModule=lemma_inlink)一样：“12乘以12等于144……嗯嗯。”卡马克的[父母在](https://baike.baidu.com/item/%E7%88%B6%E6%AF%8D%E5%9C%A8/12692305?fromModule=lemma_inlink)他十二岁的时候突然离婚了，他们在如何抚养孩子的事情上还闹的很紧张。茵戈觉得这给卡马克造成了无法弥补的创伤。就当卡马克开始在学校里找到点乐趣的时候，他和弟弟彼得又不得不在双亲之间来回轮换，不停地转学。卡马克不喜欢和父亲分开，更糟糕的是，当他和母亲住在一起的时候，他得自己照顾自己。\n23 | \n24 | 尚在就读高中时，卡马克就被认为是“怪才”，他还因此被学校视为有[心理问题](https://baike.baidu.com/item/%E5%BF%83%E7%90%86%E9%97%AE%E9%A2%98/11031400?fromModule=lemma_inlink)，并停课一年。复课后，卡马克还是拿到了毕业证书，并考入了当地著名的[Kansas](https://baike.baidu.com/item/Kansas/2771883?fromModule=lemma_inlink)大学。在编程方面，卡马克几乎是无师自通。大一还没读完，他编写的一些小游戏就被不少软件公司买走，他本人也成为了几家软件公司的兼职程序员，并逐渐在游戏软件领域小有名气。\n25 | \n26 | 1990年，正值[计算机技术](https://baike.baidu.com/item/%E8%AE%A1%E7%AE%97%E6%9C%BA%E6%8A%80%E6%9C%AF/1127562?fromModule=lemma_inlink)的黄金发展时期，[IBM](https://baike.baidu.com/item/IBM/9190?fromModule=lemma_inlink)[个人电脑](https://baike.baidu.com/item/%E4%B8%AA%E4%BA%BA%E7%94%B5%E8%84%91/3688503?fromModule=lemma_inlink)为整个软件业带来了前所未有的机遇。但这时的软件多以商业用途为目的，游戏软件却少人问津。当时一家名为Softdisk的软件公司找到了在读大二的卡马克，希望他加入公司，一起开发游戏软件。出于对游戏软件的热爱，他毫不犹豫的同意了对方的邀请。卡马克首先遇到的问题是如何将游戏程序移植到个人电脑上，经过一番考虑，他选择了[IBM PC](https://baike.baidu.com/item/IBM%20PC/4837823?fromModule=lemma_inlink)作为最早的游戏运行平台，游戏软件就是任天堂公司风靡全球的《[超级马里奥兄弟3](https://baike.baidu.com/item/%E8%B6%85%E7%BA%A7%E9%A9%AC%E9%87%8C%E5%A5%A5%E5%85%84%E5%BC%9F3/12508299?fromModule=lemma_inlink)》。经过实验，他开发出了一种名为[EGA](https://baike.baidu.com/item/EGA/7623934?fromModule=lemma_inlink)([增强型图形](https://baike.baidu.com/item/%E5%A2%9E%E5%BC%BA%E5%9E%8B%E5%9B%BE%E5%BD%A2/22068654?fromModule=lemma_inlink)[适配器](https://baike.baidu.com/item/%E9%80%82%E9%85%8D%E5%99%A8/908851?fromModule=lemma_inlink)，3D图形加速卡的雏形)的PC[显示技术](https://baike.baidu.com/item/%E6%98%BE%E7%A4%BA%E6%8A%80%E6%9C%AF/5851114?fromModule=lemma_inlink)，这是一种16色的[显示模式](https://baike.baidu.com/item/%E6%98%BE%E7%A4%BA%E6%A8%A1%E5%BC%8F/12724843?fromModule=lemma_inlink)。之后，他又设计出屏幕刷新技术以提高游戏[图形显示](https://baike.baidu.com/item/%E5%9B%BE%E5%BD%A2%E6%98%BE%E7%A4%BA/2612527?fromModule=lemma_inlink)的速度。不久，IBM PC的第一款[2D](https://baike.baidu.com/item/2D/11445?fromModule=lemma_inlink)游戏成功问世，迈出了游戏软件历史性的一步。一年后，他决定自己创业，与他人共同创办了ID Software游戏公司。在[id](https://baike.baidu.com/item/id/22219508?fromModule=lemma_inlink) Software公司里，卡马克和他的同事通过一部又一部血腥暴力的作品创造并不断革新着这一游戏类别。id所获得的无数荣誉应当归属于它的全体员工，而约翰·卡马克无疑是将整个公司凝聚在一起的[粘合剂](https://baike.baidu.com/item/%E7%B2%98%E5%90%88%E5%89%82/3705684?fromModule=lemma_inlink)。\n27 | \n28 | 雄心勃勃的卡马克当然不会停留在人人都能设计的2D游戏上，他把目标锁定在了3D游戏的研发上。如果是在几年前，他的“3D计划”可能很难实现，好在那时的计算机硬件技术已发展到了相当的水平—高分辨率显示器、大容量硬盘和内存、高速运算的[CPU](https://baike.baidu.com/item/CPU/120556?fromModule=lemma_inlink)和[图形加速卡](https://baike.baidu.com/item/%E5%9B%BE%E5%BD%A2%E5%8A%A0%E9%80%9F%E5%8D%A1/3549886?fromModule=lemma_inlink)等等，这些都是他实现梦想的绝好基础。两年后，他与另一个游戏软件天才John Romero[共同开发](https://baike.baidu.com/item/%E5%85%B1%E5%90%8C%E5%BC%80%E5%8F%91/12674474?fromModule=lemma_inlink)出了全球首款[3D射击游戏](https://baike.baidu.com/item/3D%E5%B0%84%E5%87%BB%E6%B8%B8%E6%88%8F/14696892?fromModule=lemma_inlink)《德军总部3D》，这款3D游戏采用了他独创的[3D游戏引擎](https://baike.baidu.com/item/3D%E6%B8%B8%E6%88%8F%E5%BC%95%E6%93%8E/410573?fromModule=lemma_inlink)。紧接着，他又开发出了《Doom》和《Quake》两款[3D游戏](https://baike.baidu.com/item/3D%E6%B8%B8%E6%88%8F/1747524?fromModule=lemma_inlink)。一时间，所有的电脑用户都争相购买这些游戏，人们甚至为了能玩上3D游戏而去购买昂贵的PC。不到一年的时间，仅《Doom》一款游戏就售出了几百万张拷贝，带来了上亿[美元](https://baike.baidu.com/item/%E7%BE%8E%E5%85%83/484146?fromModule=lemma_inlink)的[商业利润](https://baike.baidu.com/item/%E5%95%86%E4%B8%9A%E5%88%A9%E6%B6%A6/5000877?fromModule=lemma_inlink)。\n29 | \n30 | id Software的联合创始人约翰·卡马克(John Carmack)已经正式加入Oculus Rift团队，并且担任[首席技术官](https://baike.baidu.com/item/%E9%A6%96%E5%B8%AD%E6%8A%80%E6%9C%AF%E5%AE%98/783926?fromModule=lemma_inlink)一职。这位游戏界先锋人物将在[Oculus Rift](https://baike.baidu.com/item/Oculus%20Rift/352914?fromModule=lemma_inlink)公司位于德州达拉斯的新办公室上班。在一篇[新闻稿](https://baike.baidu.com/item/%E6%96%B0%E9%97%BB%E7%A8%BF/6869821?fromModule=lemma_inlink)中，卡马克表示，虚拟现实的梦想已经在幕后憋了几十年，但现在，人与技术终于走到了一起，使其能够触碰到很多潜在的想象。卡马克对此感到非常兴奋，并认为这是[技术变革](https://baike.baidu.com/item/%E6%8A%80%E6%9C%AF%E5%8F%98%E9%9D%A9/7668980?fromModule=lemma_inlink)的一个标记。[Bethesda](https://baike.baidu.com/item/Bethesda/1435551?fromModule=lemma_inlink)的一名发言人对Gamasutra说到，卡马克一直对Oculus VR很感兴趣，并希望在该项目上投入时间。此外，他在id Software提供游戏发展的领导与此并无影响。但与此同时，Oculus公司的发言人也指出，卡马克将是Oculus VR的一名全职员工。\n31 | \n32 | 卡马克与Oculus Rift的交集至少有一年了。在2012年6月的一次采访中，它就表示过自己正在努力打造一个[虚拟现实](https://baike.baidu.com/item/%E8%99%9A%E6%8B%9F%E7%8E%B0%E5%AE%9E/207123?fromModule=lemma_inlink)头戴式装置，并且在2011年的[E3](https://baike.baidu.com/item/E3/324804?fromModule=lemma_inlink)大展上花了大把时间来演示《毁灭战士3》(Doom 3 BFG Edition)。该项目最终在[Kickstarter](https://baike.baidu.com/item/Kickstarter/8332973?fromModule=lemma_inlink)上筹集到了250万美元的资金。\n33 | \n34 | 最后，该公司计划为市场带来目标价为300美元的Oculus Rift，不过尚未设定确切的商用发布日期。\n35 | \n36 | 卡马克加盟Oculus Rift（虚拟现实设备制造公司）则使这种若即若离的关系更加脆弱。大神发推称：\"我本想保留一个id技术顾问的头衔，但于事无补。这样也许对双方都好，专注于两条战线是很难办到的。\" <sup data-tag=\"ref\"> [1]<em id=\"sup-1\"></em></sup>\n37 | \n38 | 坦白地说，卡马克主要是一名技术天才，id公司出品的每一款游戏都是围绕他所编写的引擎制作而成的，卡马克不断把引擎技术推向新的高度。他为游戏业作出的第一个贡献是实现了卷轴游戏[背景图像](https://baike.baidu.com/item/%E8%83%8C%E6%99%AF%E5%9B%BE%E5%83%8F/9071410?fromModule=lemma_inlink)的流畅性，这一技术在1990年的《[指挥官基恩](https://baike.baidu.com/item/%E6%8C%87%E6%8C%A5%E5%AE%98%E5%9F%BA%E6%81%A9/10345602?fromModule=lemma_inlink)》（Commander Keen）中得到了应用，此前电脑平台上的横向卷轴游戏的背景图像都很不稳定，根本无法同当时游戏机平台上的横向卷轴游戏相比。尽管这一技术在今天看来算不上什么，但它足以显示出卡马克高超的编程能力。\n39 | \n40 | 卡马克今天的地位主要归功于他在3D方面的成就，尤其是对3D加速技术不遗余力的倡导和传播。《[雷神之锤](https://baike.baidu.com/item/%E9%9B%B7%E7%A5%9E%E4%B9%8B%E9%94%A4/1020067?fromModule=lemma_inlink)》刚刚问世的时候，[3D加速卡](https://baike.baidu.com/item/3D%E5%8A%A0%E9%80%9F%E5%8D%A1/1128938?fromModule=lemma_inlink)在人们眼里还只是一个可笑的空想而已，只有卡马克对[3D技术](https://baike.baidu.com/item/3D%E6%8A%80%E6%9C%AF/8202338?fromModule=lemma_inlink)的威力深信不疑，他为《雷神之锤》制作了一个专门在Verite显卡上运行的特别版本，画面看上去非常漂亮，可惜的是Verite显卡未能在市场上站稳脚跟。随后卡马克又采用[OpenGL](https://baike.baidu.com/item/OpenGL/238984?fromModule=lemma_inlink)标准为《雷神之锤》制作了一个新的版本，使所有具备3D加速能力的显卡都能以更快的速度、更高的分辨率渲染出更华丽的图像。到了今天，一些显卡生产商在研发新产品之前甚至会先同卡马克商量一下，以确保他们的硬件可以支持id出品的游戏。卡马克所作的这一切决定性地改变了显卡产业未来的发展。\n41 | \n42 | 除了3D方面的成就外，约翰·卡马克还为游戏业带来了许多其它的技术革新，例如网络代码中的客户端侦测、多重纹理、便于修改和可扩展的[游戏代码](https://baike.baidu.com/item/%E6%B8%B8%E6%88%8F%E4%BB%A3%E7%A0%81/6654927?fromModule=lemma_inlink)、游戏内部的[命令行](https://baike.baidu.com/item/%E5%91%BD%E4%BB%A4%E8%A1%8C/196110?fromModule=lemma_inlink)指令……看来所有该做的都已经被卡马克做完了，可谁知道呢，也许这一切只不过是开始而已。\n43 | \n44 | 。 <sup data-tag=\"ref\"> [3]<em id=\"sup-3\"></em></sup>\n45 | \n46 | 根据Dallas Innovates的采访，约翰·卡马克指出，AI可能会在短短十年内像人类一样行动和思考。 这位虚拟现实的梦想家表示，[[通用人工智能]]是人工智能的“大黄铜环”，到2030年将成长为一个万亿美元的产业。\n47 | \n48 | 卡马克在1995年放出了[德军总部3D](https://baike.baidu.com/item/%E5%BE%B7%E5%86%9B%E6%80%BB%E9%83%A83D/0?fromModule=lemma_inlink)的[源代码](https://baike.baidu.com/item/%E6%BA%90%E4%BB%A3%E7%A0%81/3969?fromModule=lemma_inlink)，之后的1996年时候，他放出了[雷神之锤](https://baike.baidu.com/item/%E9%9B%B7%E7%A5%9E%E4%B9%8B%E9%94%A4/0?fromModule=lemma_inlink)的源代码，Quake社区中的一名不太出名的程序员将其改写成了Linux版本，并且将修改后的游戏发给了卡马克。卡马克没有认为这是[侵权行为](https://baike.baidu.com/item/%E4%BE%B5%E6%9D%83%E8%A1%8C%E4%B8%BA/3356335?fromModule=lemma_inlink)然后付诸法律，而是要求id Software的员工们用这个版本作为雷神之锤linux版本的基础。id Software在后来的日子里也同样公布了[雷神之锤II](https://baike.baidu.com/item/%E9%9B%B7%E7%A5%9E%E4%B9%8B%E9%94%A4II/1000299?fromModule=lemma_inlink)的代码，[雷神之锤III](https://baike.baidu.com/item/%E9%9B%B7%E7%A5%9E%E4%B9%8B%E9%94%A4III/0?fromModule=lemma_inlink)的代码也于2005年8月19日公布，这些代码的公布全遵循了[GPL](https://baike.baidu.com/item/GPL/0?fromModule=lemma_inlink)准则。毁灭战士的代码也使用GPL准则在1999年重新公布。\n49 | \n50 | ---\n51 | \n52 | * 1997\n53 | \t* @ 放出了毁灭战士的代码。\n54 | * 2013\n55 | \t* 加入 VR 开发商 Oculus\n56 | * 2014\n57 | \t* Meta（当时还叫 Facebook）收购了 Oculus。他最初在 Oculus 担任首席技术官\n58 | * 2022-12\n59 | \t* 卡马克辞去了 Meta 虚拟现实部门的领导职务\n", "id": "cbe35411-e079-4450-90d7-d2856825657b", "mentionables": []}, {"role": "assistant", "applyStatus": 1, "content": "<thinking>\n1. 已获取文件完整内容，识别出需要去除的外部链接格式为：[链接文本](http://...)\n2. 需要保留内部链接格式 [[链接文本]] 和脚注标记 <sup>\n3. 将使用 search_and_replace 工具批量移除所有外部链接\n4. 操作会保留链接文本，仅去除URL部分\n</thinking>\n\n<search_and_replace>\n<path>Clippings/约翰·卡马克.md</path>\n<operations>[\n  {\n    \"search\": \"\\\\[([^\\\\]]+)\\\\]\\\\(https?:\\\\/\\\\/[^\\\\)]+\\\\)\",\n    \"replace\": \"$1\",\n    \"use_regex\": true,\n    \"ignore_case\": true\n  }\n]</operations>\n</search_and_replace>", "reasoningContent": "", "id": "6eb8a62a-4323-48e9-b63a-0a313cc86093", "metadata": {"usage": {"prompt_tokens": 29823, "completion_tokens": 167, "total_tokens": 29990, "prompt_tokens_details": {"cached_tokens": 25024}, "prompt_cache_hit_tokens": 25024, "prompt_cache_miss_tokens": 4799}, "model": {"provider": "Deepseek", "modelId": "deepseek-chat"}}}, {"role": "assistant", "applyStatus": 0, "content": "<tool_result>[search_and_replace for 'Clippings/约翰·卡马克.md'] Result:\nChanges successfully applied\n</tool_result>", "reasoningContent": "", "id": "d4e89021-ed5d-4b3f-9d1b-29137e04accf", "metadata": {}}, {"role": "user", "applyStatus": 0, "content": null, "promptContent": "[search_and_replace for 'Clippings/约翰·卡马克.md'] Result:\nChanges successfully applied\n", "id": "eed42b56-fb45-41db-af50-90ad73740268", "mentionables": []}, {"role": "assistant", "applyStatus": 0, "content": "<attempt_completion>\n<result>\n已从 [Clippings/约翰·卡马克.md](Clippings/约翰·卡马克.md) 文件中移除所有外部链接，仅保留链接文本。\n</result>\n</attempt_completion>", "reasoningContent": "", "id": "7213a72f-a96e-4948-b12e-29d559d32d53", "metadata": {"usage": {"prompt_tokens": 30014, "completion_tokens": 49, "total_tokens": 30063, "prompt_tokens_details": {"cached_tokens": 29952}, "prompt_cache_hit_tokens": 29952, "prompt_cache_miss_tokens": 62}, "model": {"provider": "Deepseek", "modelId": "deepseek-chat"}}}], "createdAt": 1752983116940, "updatedAt": 1752984160239, "schemaVersion": 2, "workspace": "vault"}