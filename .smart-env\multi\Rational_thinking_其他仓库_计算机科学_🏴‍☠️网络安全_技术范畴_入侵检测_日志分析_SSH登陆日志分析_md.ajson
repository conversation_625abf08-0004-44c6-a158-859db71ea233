"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03610308,-0.02223433,-0.04874452,-0.04696332,-0.02032561,-0.02725162,-0.01783658,-0.00159332,0.03591052,0.00620527,-0.00257682,-0.0504418,0.05879099,0.02888208,0.04708361,-0.00669672,-0.03880778,0.0291331,-0.02635303,0.03558588,0.05815452,-0.0246552,0.00566496,-0.08703788,-0.03151957,0.05494474,0.00823776,-0.02679445,-0.03765654,-0.16090706,-0.04172329,0.0086934,0.00338879,0.03595791,-0.00490879,-0.01142401,0.02130219,0.03396247,-0.0350118,0.02519218,0.02203238,0.0127933,0.00015224,-0.03277498,-0.01143047,-0.05700861,-0.02574833,0.00285715,0.03761164,-0.07178132,-0.00288747,-0.02590065,0.00739802,-0.01612666,-0.05449998,-0.02523948,0.03522375,0.00063453,0.03976111,-0.0471704,0.03379853,0.0185057,-0.23011248,0.0547423,0.05424663,-0.03519712,-0.01076753,-0.03924274,0.02034028,0.02779856,-0.05982924,-0.05053914,-0.03500967,0.05185954,0.03093437,0.02352634,-0.00241382,-0.03810083,-0.02426004,-0.04969488,-0.0356206,0.035633,-0.02130126,0.0366538,-0.00169969,0.00614642,-0.00330907,0.00054502,0.03076467,-0.00819734,0.03783074,-0.02826179,0.00022066,0.02525932,0.00650523,-0.00333188,0.041322,0.06998595,-0.13762261,0.1196731,-0.01595047,0.00872432,0.00465325,-0.12263055,0.04559906,-0.02077844,-0.02437492,-0.01124806,-0.01931379,-0.02159455,-0.02974625,-0.02208177,0.08781833,-0.0181561,0.02227825,0.02562664,0.01839618,0.02260523,-0.05030046,0.05574115,0.0287015,-0.04551495,0.0707652,-0.0212104,0.00131072,-0.05638496,-0.00644709,0.10989212,0.046223,0.04135703,0.05641677,-0.04513275,0.00129262,-0.01646521,0.02484351,0.000477,-0.05310603,0.02259901,0.00016476,-0.04373905,0.02941438,-0.07630359,-0.00349923,-0.05831103,-0.07917839,0.05151471,-0.04053137,0.00842735,0.03874966,-0.076888,0.01593734,0.03004206,0.00644696,-0.04210484,-0.00535264,-0.01603173,0.07887296,0.15880173,-0.01989765,-0.04344076,-0.01838015,-0.01029336,-0.09084187,0.15300593,0.03518939,-0.02370479,-0.0272864,-0.00927907,0.00600501,-0.06793545,0.04172116,-0.02230777,-0.01402893,-0.0090728,0.06572494,-0.01767455,0.03230657,-0.00965848,0.01440624,0.06404822,0.0243063,-0.04040101,-0.0995092,0.06582785,0.01899539,-0.07905442,-0.03718274,-0.01177611,-0.02304321,-0.00255056,-0.1049192,-0.01049597,-0.03406901,-0.03878269,-0.02849228,-0.08563908,-0.00285887,-0.01746221,0.01498934,-0.04101765,0.11606566,0.05145086,-0.04093503,-0.02800888,-0.03195075,0.00326366,0.00072467,0.02272306,0.02373378,0.03300467,0.00950525,0.05967338,0.00730894,0.00126222,-0.02031832,-0.00319997,-0.00308742,0.01376644,0.02558484,0.0196883,0.01426842,-0.01913713,-0.07260674,-0.22788079,-0.07606491,0.05466841,-0.03990918,0.04110557,0.02241893,0.02059882,-0.00172045,0.0817316,0.04172263,0.10582437,0.06545421,-0.04862997,-0.00132954,0.02665001,0.00029429,0.02617627,-0.01747704,-0.01618023,0.05097532,-0.00910981,0.03677922,0.01938755,-0.01291984,0.04947308,-0.02501644,0.10820545,0.02134336,0.06034761,0.00228233,0.00453977,0.02092615,0.02243779,-0.10248106,0.07460561,0.0362693,-0.06071506,-0.00319058,0.01080729,-0.04873727,-0.00835243,0.01757803,-0.01650904,-0.04242983,0.00644407,-0.03244313,-0.01013342,-0.03988619,-0.02745237,0.03848726,0.06289066,0.02372508,0.00404441,0.00218742,-0.00636982,-0.03477696,-0.02942905,-0.0372283,-0.01831344,0.06003102,0.03879741,-0.03241197,0.0046158,0.02246038,0.00770697,-0.04612193,-0.08906557,-0.00299307,-0.02612736,0.01865711,-0.04059606,0.14527024,0.02601353,0.002697,0.02802002,-0.0067331,-0.00800114,-0.06209206,0.02238578,-0.02563795,0.06693041,0.00963762,0.04412201,0.0082219,0.0132026,0.07864308,0.01613524,0.01098856,0.11681496,-0.075193,-0.05813839,-0.00212363,-0.014166,0.01409389,0.02810156,0.01384687,-0.28596148,0.00130843,-0.06475594,0.01006608,0.02440681,0.01208529,0.05408984,0.00222217,-0.08362144,0.016984,-0.00593364,0.04594931,-0.01934822,-0.02770172,-0.02053772,-0.05003486,0.02881895,-0.03389983,0.03736933,-0.02816945,-0.02162444,0.06810457,0.18315309,0.00826644,0.0822763,0.00690842,0.03688378,0.05097713,0.06864864,0.03798355,0.03173514,-0.07968681,0.0695159,-0.02955962,0.06177793,0.0522476,-0.02985489,-0.02017893,0.03023168,-0.05359133,-0.0673714,0.07870376,0.00682709,-0.00243747,0.09666414,-0.01798064,-0.01742597,-0.04832726,0.04103338,0.0490521,0.00167643,0.00380361,-0.01597264,-0.00724861,0.01453157,0.11035912,0.06001401,-0.00377362,-0.05992167,-0.0220925,0.00057035,0.03127945,0.11289895,0.10188916,0.02143068],"last_embed":{"hash":"fad16e06a529d03553d151bb7610e35035693def05b1a2193980e5f4c8f6b0d4","tokens":471}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.07609833,-0.00716038,-0.04512878,-0.01007694,0.00258813,-0.00626871,0.03016798,-0.02127868,-0.02852032,-0.02283473,-0.01438158,0.03826522,-0.04522117,0.02979556,0.02098626,0.04555626,-0.08147848,0.03066289,0.0019355,0.00304225,0.02631346,0.02752522,0.02312867,0.03741011,-0.0155793,-0.05126061,0.02972008,-0.05177854,-0.00562586,0.02192537,0.04979325,-0.03090555,-0.01903621,-0.01090574,-0.05585114,0.00452681,-0.00570185,0.02587283,0.00152472,0.05055107,-0.06363253,-0.01429399,-0.00521014,0.01879388,-0.07576396,0.01737093,-0.06606878,-0.05624468,-0.01503291,0.01044974,0.03682577,-0.05472854,0.01593673,-0.02467847,0.00218791,0.05587407,-0.00982201,-0.01296324,0.01717251,-0.00125151,0.03177859,-0.02542483,0.05859331,0.01666159,-0.0118869,0.00977072,-0.00903399,-0.01922242,-0.05517446,0.01135202,-0.04931566,-0.05995127,0.03770717,0.00844446,0.03304235,-0.02233766,0.03363081,-0.00988319,-0.06611592,0.04424376,-0.0666253,-0.02083932,-0.01490307,0.1008454,-0.03462835,-0.07203151,0.04714655,-0.08055175,-0.00988055,0.00326993,0.03425612,-0.00308151,0.00976568,-0.00081848,0.07190169,-0.05632779,0.03519321,0.05373378,0.00875738,-0.01985365,-0.03506765,-0.00139563,-0.04414415,-0.01361623,-0.00743714,-0.0139832,-0.0616473,0.095031,-0.05066519,-0.00870451,-0.00740764,-0.02637278,-0.02338683,-0.03085257,0.02394125,0.00494797,-0.03838809,-0.01426086,0.06567146,0.02580549,0.01541397,0.02863283,-0.02579132,-0.01385387,-0.03426644,-0.01255693,0.01298951,0.01278707,0.00848962,-0.04752566,0.03985693,-0.01347595,-0.0664008,-0.01659377,0.00449895,-0.00090804,-0.0085058,-0.03247252,0.01041995,-0.00917393,0.01776383,-0.04367012,0.02182599,-0.00485173,0.06913861,-0.02172408,-0.05403665,-0.05032231,-0.03643045,0.01063521,0.07299131,-0.01856073,0.00288796,-0.06137423,-0.02239931,0.00596484,0.00168742,0.04895012,-0.03318438,-0.02899381,-0.03134834,0.03743643,0.01101813,0.01697526,-0.00270643,0.03449358,-0.00014044,0.02265078,0.03276477,-0.03957267,-0.00561264,0.03326579,-0.05319413,0.02829882,0.03493867,-0.0102076,0.07216009,0.05988428,-0.0459054,-0.02168531,0.05734758,-0.02521769,-0.00641955,0.01207012,0.01468809,0.01397377,0.00336076,0.03090853,0.0081459,-0.00766035,0.03028504,-0.01393626,0.01398961,-0.00353755,0.04507305,0.04439513,-0.03389662,-0.0065964,-0.02022736,0.02575146,-0.10115375,-0.03569391,-0.00130672,-0.06623057,0.0278652,-0.00354946,-0.02732633,-0.0777696,-0.0098427,0.10043133,-0.01937756,-0.01011989,0.04856993,0.00911964,0.00023595,0.00532903,-0.01976061,0.00111728,0.01615863,0.00470872,0.01044472,0.01168585,-0.06496709,-0.02865195,0.00909335,-0.01958607,0.00514001,-0.00691818,0.04400656,0.01765431,-0.00499411,0.10528281,-0.00441006,-0.05141533,-0.05027135,0.04281968,-0.00865076,0.04170231,0.00057175,0.00194682,0.03340152,0.01336395,-0.0194821,0.02231886,-0.03853073,-0.02037824,-0.05023319,0.0071779,-0.0005602,0.04812669,0.07532212,0.02047977,0.01271117,0.03559244,0.05075337,-0.04427456,-0.03688502,0.03649667,0.01285167,-0.005149,0.01452543,-0.07316407,0.00515875,0.05672689,-0.02830665,-0.06042274,-0.00203951,0.02218335,0.01863446,0.03779403,0.04145782,0.01100538,0.06032891,-0.00230066,-0.02520766,0.03103037,0.03014805,-0.02578607,-0.05716466,0.03230446,-0.03584552,-0.01103365,-0.02221115,-0.01478095,-0.04233059,0.00516938,0.01251761,-0.01425938,0.08065742,0.03684353,-0.02889499,0.04242035,-0.03133355,0.05554977,-0.06888552,-0.02299403,0.0306175,-0.00424742,-0.00897611,-0.03546286,-0.03434326,-0.0168153,0.00770463,0.04763746,0.04592365,0.03069371,0.00192891,-0.04411774,-0.04186393,-0.01318794,-0.01233633,-0.02553751,0.05146929,-0.02616574,-0.00106683,0.09364024,0.06238483,0.01386087,-0.00876814,-0.01404727,-0.04190749,-0.03619831,-0.02507437,-0.00957012,0.01227903,-0.08376625,-0.01375805,0.0485543,0.02793353,0.02141219,-0.02686791,0.0524041,-0.00970884,-0.01244043,0.04120163,0.03313191,-0.02354034,-0.08384342,0.06203362,-0.05820283,0.04721957,-0.04142582,0.03859242,-0.10067499,0.02825317,0.05390531,-0.01058237,0.02683803,-0.08878845,-0.02579256,0.02196724,0.02056346,-0.05298885,0.02602663,-0.00699458,-0.00975466,-0.00458718,-0.006871,-0.01993472,0.00838492,-0.00586046,0.00104335,-0.07036503,-0.05064591,-0.05115328,-0.01734064,0.02577151,0.02339695,-0.03150156,0.00580214,-0.0055875,0.03343001,0.00721655,-0.01613319,0.04874619,-0.00960851,-0.04096578,0.04029141,-0.0263796,-0.00269079,0.00106425,-0.0763968,-0.03076511,-0.02079076,-0.04937398,0.01983995,-0.04441152,-0.01943715,-0.00467349,-0.00547045,-0.01846312,-0.00231494,-0.07007119,-0.01030645,0.0616692,0.01954176,0.02210595,0.04270437,-0.03780001,0.0470606,0.05855569,-0.0181086,0.04310751,-0.040897,-0.02274167,0.0183208,-0.06347485,0.06280474,0.02036755,-0.06435962,-0.00739016,0.06145091,-0.02484059,0.05087952,-0.03458847,-0.00599741,0.03938646,0.0217303,-0.03321772,-0.0035985,0.04502638,-0.00242031,0.02663929,0.02561605,0.01392589,0.00014315,0.01317189,-0.05765919,0.1067055,0.01876354,-0.02217197,-0.03002613,-0.07780894,-0.04417549,0.01111078,-0.0289241,0.03605074,0.02071318,0.00051857,0.00131834,-0.01210225,-0.00938362,0.0249791,0.03024505,0.02876179,0.02702252,-0.0096906,-0.02285956,0.04615359,0.04159763,-0.0150039,0.00343082,-0.09172581,-0.03053424,-0.03697514,-0.03675677,0.00135389,0.0054282,0.0141551,0.02317891,-0.04235084,0.08401239,0.01670261,0.01975113,-0.03293945,-0.01799015,-0.0031512,0.02211939,-0.06284287,0.00678743,0.03194457,0.04450849,0.0159489,-0.04618396,0.05407283,0.02804478,-0.01677349,-0.02559118,-0.02929701,0.06492734,-0.00116532,-0.01283375,-0.0135902,0.02407139,-0.02958019,0.06322512,0.00153231,-0.00490173,0.00468886,0.00092796,0.04770374,0.00254693,0.02390471,-0.06673338,0.00660427,-0.02012726,0.01769435,0.05127592,0.00557814,0.05236937,-0.01065144,0.00974884,0.00960598,0.05973592,-0.00151918,-0.00811737,-0.05779677,0.00985623,0.04735651,0.00126231,-0.02262738,-0.04575755,0.08310148,0.08078083,-0.00580956,-0.0229509,0.02924094,-0.00666205,0.03899699,-0.00384908,-0.01227537,0.0542684,-0.03335751,0.01890395,0.03365818,-0.02088515,-0.07310122,0.01070032,0.06867811,0.01800914,-0.03028789,-0.02614195,0.02650167,-0.03968803,0.06602944,-0.03629313,0.00422427,-0.00595573,0.01791952,0.01731296,-0.00117357,0.02871957,0.01139327,0.05495143,-0.02273104,-0.01551574,-0.02759152,-0.01868724,0.07291806,-0.00518061,0.03672935,0.0050056,0.01385947,-0.03192668,0.00832425,0.0060463,0.02432267,-0.04573577,0.00443864,0.08778062,0.0043623,0.00592323,0.01334237,-0.02419493,-0.08015202,0.00091662,-0.02885579,0.03200771,0.02896056,0.05730148,-0.04400745,-0.04989407,0.05702138,0.01366899,-0.03641664,0.05241484,0.01410641,-0.02291354,0.03964087,0.06801932,0.04208951,-0.00582386,0.0648018,-0.03292326,0.0333184,-0.00502617,-0.00848422,0.02915475,0.05396035,-0.00479974,0.04462116,-0.06695628,-0.02240877,-0.05887104,0.04438619,-0.01582859,-0.01947577,0.02745321,0.07879741,-0.01220173,-0.0017554,0.01954586,0.00091446,-0.01396814,-0.05685411,-0.04969363,-0.00251372,-0.06224013,0.01366194,0.00716146,0.01575853,0.04790585,-0.03754207,-0.08926151,-0.03954219,0.00140405,-0.05334223,-0.02271334,-0.01360652,0.03565533,-0.02156625,0.00035297,-0.0003047,0.00395387,-0.00971893,0.04364893,0.02108565,0.04371773,0.02976632,-0.04414365,-0.02683489,0.00968813,-0.0888883,0.05511729,0.02224624,0.01664959,-0.00519371,-0.01839506,0.0057968,-0.00884429,0.04657459,-0.0611654,0.01617089,0.00767128,0.05664398,-0.01827672,-0.02270508,-0.0288111,-0.00695149,-0.03436665,-0.0090392,-0.0173788,-0.03123414,0.00811448,-0.01143984,0.0228781,0.00581012,0.02132742,0.01949322,0.02303303,-0.02959336,-0.02558846,-0.03007344,-0.04176277,0.00835254,0.04559093,0.0392991,-0.00785504,0.00551297,-0.00161677,0.00768505,-0.05729496,-0.0215587,0.02492568,-0.02009412,-0.00097615,0.01764894,0.00645967,0.0568102,-0.0063552,-0.04824466,-0.02282349,-0.00136284,-0.02230883,0.02359816,0.06470405,-0.00668479,-0.043559,-0.02074026,-0.00189874,0.02009402,-0.00408978,-0.02722429,-0.01135703,0.03967885,-0.04770481,0.00518052,0.00535426,-0.02358605,0.01254094,-0.01618304,-0.0138614,0.01269929,-0.01315377,-0.00735988,-0.05466863,0.03217542,0.03642379,0.01608406,-0.03163538,-0.07245144,-0.02813871,0.009994,0.0135264,0.03481857,-0.03222547,-0.02509804,0.05111349,-0.01039841,-0.03949464,-0.00601106,0.03877342,0.00875572,0.01058067,-0.0551558,-0.00879782,-0.03085533,0.05228803,-0.03777824,0.0035837,0.0155416,-0.03216926,-0.00190309,0.04929439,0.00664124,-0.05165849,0.0091531,0.00207131,0.01514544,0.01859394,-0.00147826,0.00975785,-0.04934559,-0.03167868,-0.063271,-0.04730423,-0.03008378,0.05816077,0.02604108,0.04153433,-0.08699743,0.03364555,-0.00049766,0.03343463,0.01304992,-0.00909211,0.01873475,-0.05495898,6.4e-7,0.06264663,-0.01582426,0.00939731,-0.06223147,0.00035394,-0.03944181,-0.04789258,-0.00709669,0.01635078],"last_embed":{"tokens":930,"hash":"6zbks7"}}},"last_read":{"hash":"6zbks7","at":1752940651791},"class_name":"SmartSource","outlinks":[{"title":"grep","target":"grep","line":25},{"title":"awk使用手册","target":"awk使用手册","line":29},{"title":"awk使用手册","target":"awk使用手册","line":30}],"metadata":{"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,4],"#基于auth.log的分析":[6,77],"#基于auth.log的分析#{1}":[7,10],"#基于auth.log的分析#{2}":[11,11],"#基于auth.log的分析#统计登陆失败/成功":[12,35],"#基于auth.log的分析#统计登陆失败/成功#{1}":[13,15],"#基于auth.log的分析#统计登陆失败/成功#{2}":[16,19],"#基于auth.log的分析#统计登陆失败/成功#{3}":[17,19],"#基于auth.log的分析#统计登陆失败/成功#{4}":[20,23],"#基于auth.log的分析#统计登陆失败/成功#{5}":[21,23],"#基于auth.log的分析#统计登陆失败/成功#{6}":[24,24],"#基于auth.log的分析#统计登陆失败/成功#{7}":[25,28],"#基于auth.log的分析#统计登陆失败/成功#{8}":[26,28],"#基于auth.log的分析#统计登陆失败/成功#{9}":[29,29],"#基于auth.log的分析#统计登陆失败/成功#{10}":[30,30],"#基于auth.log的分析#统计登陆失败/成功#{11}":[31,33],"#基于auth.log的分析#统计登陆失败/成功#{12}":[34,35],"#基于auth.log的分析#定位日志文件":[36,44],"#基于auth.log的分析#定位日志文件#{1}":[38,40],"#基于auth.log的分析#定位日志文件#{2}":[41,42],"#基于auth.log的分析#定位日志文件#{3}":[43,44],"#基于auth.log的分析#统计攻击者的IP":[45,50],"#基于auth.log的分析#统计攻击者的IP#{1}":[47,50],"#基于auth.log的分析#统计扫描过服务器的IP数量；":[51,65],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{1}":[52,54],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{2}":[55,55],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{3}":[56,56],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{4}":[57,60],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{5}":[61,61],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{6}":[62,63],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{7}":[64,65],"#基于auth.log的分析#统计攻击者尝试的用户名":[66,77],"#基于auth.log的分析#统计攻击者尝试的用户名#{1}":[68,70],"#基于auth.log的分析#统计攻击者尝试的用户名#{2}":[71,74],"#基于auth.log的分析#统计攻击者尝试的用户名#{3}":[72,74],"#基于auth.log的分析#统计攻击者尝试的用户名#{4}":[75,77]},"last_import":{"mtime":1735464406903,"size":2694,"at":1748488128974,"hash":"6zbks7"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md","last_embed":{"hash":"6zbks7","at":1752940651791}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.07746624,-0.00676115,-0.04602143,-0.00456668,-0.00257392,-0.00470887,0.0372407,-0.02030304,-0.03211443,-0.01942397,-0.01820423,0.03894334,-0.05605704,0.02690709,0.02161941,0.03775816,-0.08070909,0.031516,0.00409618,0.00741578,0.01856315,0.03133504,0.02388524,0.03708843,-0.00626565,-0.04459801,0.02981116,-0.04880429,-0.00855058,0.0176223,0.03506479,-0.03210897,-0.02069656,-0.01078456,-0.05313076,0.00333456,-0.00818034,0.02413049,0.01094109,0.0471938,-0.05956803,-0.01474355,-0.0080418,0.02303178,-0.07826472,0.02259952,-0.06374754,-0.05304594,-0.01746463,0.00836695,0.03305385,-0.05269743,0.01361546,-0.02991747,0.00083176,0.05873299,-0.0149438,-0.00890887,0.01982691,0.00307219,0.03119578,-0.01764194,0.06337854,0.01521955,-0.00953829,0.00826979,-0.01362533,-0.011631,-0.05910325,0.00814348,-0.05279353,-0.06687158,0.03814432,0.00424544,0.03493676,-0.02159118,0.03019311,-0.00616599,-0.07389613,0.04850079,-0.06137557,-0.02331518,-0.0130915,0.11331671,-0.0304351,-0.06687502,0.03927539,-0.07348631,-0.00331053,0.00534299,0.03724115,-0.00431646,0.01059488,0.00042277,0.06900129,-0.06359887,0.029613,0.0470509,0.01256374,-0.02384721,-0.03262658,-0.00901736,-0.04193399,-0.01599871,-0.01427397,-0.00797297,-0.05387032,0.09708616,-0.0426225,-0.00751875,-0.01311484,-0.03308093,-0.01819456,-0.02838705,0.02119149,0.00157719,-0.03598822,-0.01223143,0.06886207,0.02250392,0.01775799,0.02730781,-0.0257252,-0.01562913,-0.03917998,-0.00979017,0.01312937,0.016292,0.00875947,-0.04235112,0.0417431,-0.01017443,-0.06628799,-0.01740505,0.00361512,-0.00205345,-0.01528007,-0.03064505,0.00905335,-0.01168713,0.01815487,-0.04233451,0.02175273,-0.00014331,0.05585451,-0.01271752,-0.05237626,-0.04855421,-0.03867943,0.01238458,0.07016118,-0.01703425,0.01101039,-0.06223856,-0.02223147,0.00481659,-0.00163247,0.05307277,-0.03483363,-0.03123356,-0.0356517,0.03172325,0.00871852,0.02217965,-0.01142378,0.03381724,-0.00180809,0.02357423,0.02901148,-0.03898966,-0.01316582,0.03498183,-0.05102366,0.03282147,0.03787836,-0.00805466,0.06915074,0.06053557,-0.04652669,-0.01875574,0.05071315,-0.02235451,-0.00762074,0.00708037,0.01639553,0.01345196,0.00230703,0.03323545,0.0074017,-0.00916886,0.02649556,-0.01181854,0.00883737,-0.00371684,0.0486876,0.04302698,-0.03009599,0.0026537,-0.02047065,0.02652845,-0.10934837,-0.05122688,0.00165559,-0.06412143,0.02432016,-0.004026,-0.02278998,-0.0858006,-0.01152718,0.10429179,-0.02456323,-0.01190426,0.04491487,0.00943179,0.00226727,0.00739234,-0.01398589,0.00579023,0.01824377,0.00807554,0.00559008,0.01343163,-0.07093153,-0.03513803,0.00873213,-0.02124639,0.00929594,0.00066695,0.04453764,0.01656394,-0.015265,0.10539163,-0.00871965,-0.04942903,-0.05430603,0.04858763,-0.01322852,0.03803245,-0.00043761,0.00070528,0.03199202,0.01858291,-0.01476869,0.0219665,-0.03801059,-0.01756169,-0.04654754,0.01271775,-0.00349699,0.04644999,0.07081376,0.02316616,0.01892661,0.03335108,0.05183776,-0.04471731,-0.02939581,0.03273338,0.00750031,-0.00699253,0.0170807,-0.079177,0.01054294,0.0553974,-0.03011234,-0.05848581,-0.00581313,0.01692414,0.013536,0.04141449,0.04078695,0.01051033,0.06346782,-0.00433983,-0.01965345,0.03048614,0.02582588,-0.02371136,-0.05508172,0.02974265,-0.03787547,-0.00963971,-0.02063101,-0.01024731,-0.03947644,0.00517617,0.00891332,-0.01641286,0.07728919,0.03200522,-0.03254154,0.04574786,-0.02309653,0.05619544,-0.07005119,-0.01584831,0.0311834,-0.00302861,-0.01494736,-0.03482356,-0.02948294,-0.02357081,0.00244619,0.04489437,0.0522823,0.0300376,0.00198451,-0.04321323,-0.04280816,-0.01392586,-0.02190904,-0.02785716,0.05177248,-0.02775082,0.00023008,0.08830818,0.06575844,0.01278124,-0.00463686,-0.01319293,-0.0440298,-0.03742778,-0.02497033,-0.00748705,0.01334195,-0.0793549,-0.01345876,0.05004633,0.03194921,0.02621064,-0.0275656,0.05209769,-0.014489,-0.01438691,0.03768476,0.03442385,-0.02998732,-0.08377283,0.05896458,-0.06433368,0.05607484,-0.04259076,0.03857036,-0.09785621,0.02810399,0.06020073,-0.01147038,0.02756364,-0.08618907,-0.02372389,0.01748478,0.01873475,-0.04709455,0.02494348,-0.00171415,-0.00606562,-0.00679698,-0.01250643,-0.01768911,0.00738361,0.00041738,-0.00119242,-0.06715763,-0.05102483,-0.0541718,-0.01422256,0.03264575,0.02037308,-0.0311945,0.00936037,-0.00440703,0.03383259,-0.00036847,-0.01684955,0.04451607,-0.00994116,-0.04492752,0.03804298,-0.02793896,-0.00312667,-0.00129756,-0.07508852,-0.02840753,-0.02341123,-0.05044209,0.01972901,-0.045249,-0.0165528,-0.01380784,-0.00069934,-0.02099984,0.00322947,-0.06921205,-0.00504669,0.06178828,0.0174426,0.01621427,0.03897421,-0.03656563,0.0534556,0.05311554,-0.01902959,0.05469078,-0.03688642,-0.0303884,0.02003501,-0.06142193,0.06592868,0.02314597,-0.06716941,-0.00585753,0.06272533,-0.02770996,0.04872645,-0.04398122,-0.00719543,0.04386739,0.03012422,-0.04370037,-0.00080571,0.04370023,-0.00621449,0.0329998,0.02661739,0.01303824,0.00430607,0.01244103,-0.05387961,0.10912365,0.01874203,-0.01707421,-0.03385785,-0.08604721,-0.04116034,0.01185423,-0.02605689,0.03381324,0.01368235,-0.00441012,0.00176589,-0.0120067,-0.0091272,0.01896378,0.02974183,0.01939655,0.0210191,-0.00445538,-0.0265849,0.03819767,0.03477656,-0.01248682,0.00521965,-0.09197173,-0.02684147,-0.0325087,-0.03531789,0.00023239,0.00883654,0.02096587,0.03101844,-0.04847894,0.09175044,0.01931624,0.01172809,-0.02687992,-0.01533077,-0.00006462,0.01758518,-0.06585699,0.00225646,0.03378946,0.04048059,0.01669033,-0.05135544,0.05269377,0.02049523,-0.01153195,-0.02631664,-0.02748756,0.06824636,0.00057457,-0.00834443,-0.01749936,0.02535374,-0.02248242,0.07361157,-0.00278013,-0.00580819,0.00322184,0.00075171,0.04753114,-0.00430982,0.02026074,-0.06522621,-0.00173693,-0.01196439,0.01374171,0.05116854,0.00840064,0.0519172,-0.00946652,0.00601906,0.01882668,0.05180283,0.00119056,-0.00426736,-0.06164129,0.00635288,0.0505938,-0.00336462,-0.02155861,-0.0447988,0.08782386,0.08404869,-0.01647155,-0.02327814,0.03101097,-0.00203963,0.04248314,-0.00749855,-0.01432925,0.05458556,-0.03232816,0.01683577,0.03035597,-0.01721243,-0.06441875,0.01122808,0.05792201,0.01953542,-0.03328698,-0.02385267,0.02554607,-0.04097863,0.06518028,-0.04034301,0.00832407,-0.00846542,0.01216521,0.01352561,-0.00069359,0.02412568,0.01399414,0.05263707,-0.01861421,-0.01453081,-0.02978562,-0.01566869,0.07484801,-0.00439358,0.0363017,0.00822663,0.00496584,-0.0272972,0.01344712,0.00720727,0.02785622,-0.03919967,0.00493443,0.09091235,0.00071977,0.00745054,0.01699996,-0.0242279,-0.07352739,-0.00049473,-0.03264623,0.03236046,0.02429627,0.05715071,-0.04326868,-0.04654687,0.05628249,0.01113786,-0.03865227,0.0601451,0.01248514,-0.02365807,0.03980234,0.06207136,0.0358067,-0.00144169,0.06381937,-0.03316307,0.03709753,-0.00790851,-0.00921039,0.02773591,0.04840254,-0.01088253,0.0528964,-0.06935196,-0.01360942,-0.06062615,0.04634286,-0.01343337,-0.02229798,0.0254112,0.07331169,-0.01081432,-0.00190652,0.01567453,0.00255185,-0.01654392,-0.06095424,-0.05664196,0.00573448,-0.05314254,0.01628805,0.01560301,0.01585116,0.04279956,-0.0391128,-0.08227479,-0.03855846,-0.00185115,-0.05416745,-0.01608167,-0.00916373,0.03498734,-0.01847152,-0.00324198,-0.00243818,0.00621854,-0.00952585,0.04424065,0.02214034,0.04431912,0.02845486,-0.04170024,-0.02153861,0.01138201,-0.0876468,0.05927388,0.02462039,0.01494187,-0.00690584,-0.0206026,0.00781711,-0.00509543,0.04547041,-0.05357933,0.02425317,0.00571748,0.05806462,-0.02123817,-0.02135943,-0.03411534,-0.00367334,-0.03711926,-0.00673494,-0.01986205,-0.03055575,0.00566231,-0.01178047,0.02762478,0.00358417,0.02016713,0.01611475,0.02795884,-0.03363177,-0.02059651,-0.02514949,-0.03851623,0.00474417,0.04792398,0.03639372,-0.00710052,0.00209904,0.00260552,0.00640375,-0.0633197,-0.02675681,0.02679237,-0.02493709,0.00120439,0.01926966,0.00611061,0.06267069,-0.00937374,-0.04848812,-0.01775432,-0.00580168,-0.02564926,0.02711098,0.05676746,-0.00502783,-0.04336054,-0.03186752,0.0010951,0.0200763,-0.01070083,-0.02724711,-0.01193432,0.04381513,-0.04958835,0.00569083,0.00800862,-0.02566309,0.0132937,-0.02310948,-0.01325964,0.00812604,-0.01537098,-0.0035314,-0.05153215,0.03137577,0.03433173,0.01795112,-0.02736157,-0.07091101,-0.01861843,0.00627019,0.01281957,0.03864269,-0.0350314,-0.02370013,0.04939358,-0.00718341,-0.04417388,-0.00432826,0.03255604,0.00315153,0.00708054,-0.04987536,-0.00913624,-0.03120112,0.04328765,-0.04131412,0.00535941,0.01106985,-0.03226618,0.0001836,0.05104482,0.00912225,-0.0551666,0.01318485,0.00485961,0.01490114,0.01505723,-0.00522501,0.00847544,-0.05611217,-0.03209536,-0.05839137,-0.03862422,-0.03319015,0.06143879,0.02570639,0.04426125,-0.08845305,0.03720152,-0.0012036,0.04276169,0.00237947,-0.0015268,0.01814092,-0.04523472,6.3e-7,0.06169355,-0.01646089,0.01036379,-0.05696354,0.00207267,-0.03592777,-0.04850455,-0.01001,0.02250665],"last_embed":{"hash":"1d0yodn","tokens":920}}},"text":null,"length":0,"last_read":{"hash":"1d0yodn","at":1749002763462},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析","lines":[6,77],"size":1863,"outlinks":[{"title":"grep","target":"grep","line":20},{"title":"awk使用手册","target":"awk使用手册","line":24},{"title":"awk使用手册","target":"awk使用手册","line":25}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#---frontmatter---","lines":[1,4],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#{1}","lines":[7,10],"size":87,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#{2}","lines":[11,11],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功","lines":[12,35],"size":509,"outlinks":[{"title":"grep","target":"grep","line":14},{"title":"awk使用手册","target":"awk使用手册","line":18},{"title":"awk使用手册","target":"awk使用手册","line":19}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{1}","lines":[13,15],"size":36,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{2}","lines":[16,19],"size":81,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{3}","lines":[17,19],"size":61,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{4}","lines":[20,23],"size":100,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{5}","lines":[21,23],"size":81,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{6}","lines":[24,24],"size":17,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{7}","lines":[25,28],"size":126,"outlinks":[{"title":"grep","target":"grep","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{8}","lines":[26,28],"size":104,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{9}","lines":[29,29],"size":35,"outlinks":[{"title":"awk使用手册","target":"awk使用手册","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{10}","lines":[30,30],"size":40,"outlinks":[{"title":"awk使用手册","target":"awk使用手册","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{11}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{11}","lines":[31,33],"size":49,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{12}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计登陆失败/成功#{12}","lines":[34,35],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#定位日志文件": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#定位日志文件","lines":[36,44],"size":96,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#定位日志文件#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#定位日志文件#{1}","lines":[38,40],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#定位日志文件#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#定位日志文件#{2}","lines":[41,42],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#定位日志文件#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#定位日志文件#{3}","lines":[43,44],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者的IP": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者的IP","lines":[45,50],"size":200,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者的IP#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者的IP#{1}","lines":[47,50],"size":187,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；","lines":[51,65],"size":445,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{1}","lines":[52,54],"size":200,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{2}","lines":[55,55],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{3}","lines":[56,56],"size":18,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{4}","lines":[57,60],"size":108,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{5}","lines":[61,61],"size":20,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{6}","lines":[62,63],"size":48,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计扫描过服务器的IP数量；#{7}","lines":[64,65],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者尝试的用户名": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者尝试的用户名","lines":[66,77],"size":501,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者尝试的用户名#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者尝试的用户名#{1}","lines":[68,70],"size":210,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者尝试的用户名#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者尝试的用户名#{2}","lines":[71,74],"size":231,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者尝试的用户名#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者尝试的用户名#{3}","lines":[72,74],"size":210,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者尝试的用户名#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md#基于auth.log的分析#统计攻击者尝试的用户名#{4}","lines":[75,77],"size":41,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03610308,-0.02223433,-0.04874452,-0.04696332,-0.02032561,-0.02725162,-0.01783658,-0.00159332,0.03591052,0.00620527,-0.00257682,-0.0504418,0.05879099,0.02888208,0.04708361,-0.00669672,-0.03880778,0.0291331,-0.02635303,0.03558588,0.05815452,-0.0246552,0.00566496,-0.08703788,-0.03151957,0.05494474,0.00823776,-0.02679445,-0.03765654,-0.16090706,-0.04172329,0.0086934,0.00338879,0.03595791,-0.00490879,-0.01142401,0.02130219,0.03396247,-0.0350118,0.02519218,0.02203238,0.0127933,0.00015224,-0.03277498,-0.01143047,-0.05700861,-0.02574833,0.00285715,0.03761164,-0.07178132,-0.00288747,-0.02590065,0.00739802,-0.01612666,-0.05449998,-0.02523948,0.03522375,0.00063453,0.03976111,-0.0471704,0.03379853,0.0185057,-0.23011248,0.0547423,0.05424663,-0.03519712,-0.01076753,-0.03924274,0.02034028,0.02779856,-0.05982924,-0.05053914,-0.03500967,0.05185954,0.03093437,0.02352634,-0.00241382,-0.03810083,-0.02426004,-0.04969488,-0.0356206,0.035633,-0.02130126,0.0366538,-0.00169969,0.00614642,-0.00330907,0.00054502,0.03076467,-0.00819734,0.03783074,-0.02826179,0.00022066,0.02525932,0.00650523,-0.00333188,0.041322,0.06998595,-0.13762261,0.1196731,-0.01595047,0.00872432,0.00465325,-0.12263055,0.04559906,-0.02077844,-0.02437492,-0.01124806,-0.01931379,-0.02159455,-0.02974625,-0.02208177,0.08781833,-0.0181561,0.02227825,0.02562664,0.01839618,0.02260523,-0.05030046,0.05574115,0.0287015,-0.04551495,0.0707652,-0.0212104,0.00131072,-0.05638496,-0.00644709,0.10989212,0.046223,0.04135703,0.05641677,-0.04513275,0.00129262,-0.01646521,0.02484351,0.000477,-0.05310603,0.02259901,0.00016476,-0.04373905,0.02941438,-0.07630359,-0.00349923,-0.05831103,-0.07917839,0.05151471,-0.04053137,0.00842735,0.03874966,-0.076888,0.01593734,0.03004206,0.00644696,-0.04210484,-0.00535264,-0.01603173,0.07887296,0.15880173,-0.01989765,-0.04344076,-0.01838015,-0.01029336,-0.09084187,0.15300593,0.03518939,-0.02370479,-0.0272864,-0.00927907,0.00600501,-0.06793545,0.04172116,-0.02230777,-0.01402893,-0.0090728,0.06572494,-0.01767455,0.03230657,-0.00965848,0.01440624,0.06404822,0.0243063,-0.04040101,-0.0995092,0.06582785,0.01899539,-0.07905442,-0.03718274,-0.01177611,-0.02304321,-0.00255056,-0.1049192,-0.01049597,-0.03406901,-0.03878269,-0.02849228,-0.08563908,-0.00285887,-0.01746221,0.01498934,-0.04101765,0.11606566,0.05145086,-0.04093503,-0.02800888,-0.03195075,0.00326366,0.00072467,0.02272306,0.02373378,0.03300467,0.00950525,0.05967338,0.00730894,0.00126222,-0.02031832,-0.00319997,-0.00308742,0.01376644,0.02558484,0.0196883,0.01426842,-0.01913713,-0.07260674,-0.22788079,-0.07606491,0.05466841,-0.03990918,0.04110557,0.02241893,0.02059882,-0.00172045,0.0817316,0.04172263,0.10582437,0.06545421,-0.04862997,-0.00132954,0.02665001,0.00029429,0.02617627,-0.01747704,-0.01618023,0.05097532,-0.00910981,0.03677922,0.01938755,-0.01291984,0.04947308,-0.02501644,0.10820545,0.02134336,0.06034761,0.00228233,0.00453977,0.02092615,0.02243779,-0.10248106,0.07460561,0.0362693,-0.06071506,-0.00319058,0.01080729,-0.04873727,-0.00835243,0.01757803,-0.01650904,-0.04242983,0.00644407,-0.03244313,-0.01013342,-0.03988619,-0.02745237,0.03848726,0.06289066,0.02372508,0.00404441,0.00218742,-0.00636982,-0.03477696,-0.02942905,-0.0372283,-0.01831344,0.06003102,0.03879741,-0.03241197,0.0046158,0.02246038,0.00770697,-0.04612193,-0.08906557,-0.00299307,-0.02612736,0.01865711,-0.04059606,0.14527024,0.02601353,0.002697,0.02802002,-0.0067331,-0.00800114,-0.06209206,0.02238578,-0.02563795,0.06693041,0.00963762,0.04412201,0.0082219,0.0132026,0.07864308,0.01613524,0.01098856,0.11681496,-0.075193,-0.05813839,-0.00212363,-0.014166,0.01409389,0.02810156,0.01384687,-0.28596148,0.00130843,-0.06475594,0.01006608,0.02440681,0.01208529,0.05408984,0.00222217,-0.08362144,0.016984,-0.00593364,0.04594931,-0.01934822,-0.02770172,-0.02053772,-0.05003486,0.02881895,-0.03389983,0.03736933,-0.02816945,-0.02162444,0.06810457,0.18315309,0.00826644,0.0822763,0.00690842,0.03688378,0.05097713,0.06864864,0.03798355,0.03173514,-0.07968681,0.0695159,-0.02955962,0.06177793,0.0522476,-0.02985489,-0.02017893,0.03023168,-0.05359133,-0.0673714,0.07870376,0.00682709,-0.00243747,0.09666414,-0.01798064,-0.01742597,-0.04832726,0.04103338,0.0490521,0.00167643,0.00380361,-0.01597264,-0.00724861,0.01453157,0.11035912,0.06001401,-0.00377362,-0.05992167,-0.0220925,0.00057035,0.03127945,0.11289895,0.10188916,0.02143068],"last_embed":{"hash":"fad16e06a529d03553d151bb7610e35035693def05b1a2193980e5f4c8f6b0d4","tokens":471}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.07609833,-0.00716038,-0.04512878,-0.01007694,0.00258813,-0.00626871,0.03016798,-0.02127868,-0.02852032,-0.02283473,-0.01438158,0.03826522,-0.04522117,0.02979556,0.02098626,0.04555626,-0.08147848,0.03066289,0.0019355,0.00304225,0.02631346,0.02752522,0.02312867,0.03741011,-0.0155793,-0.05126061,0.02972008,-0.05177854,-0.00562586,0.02192537,0.04979325,-0.03090555,-0.01903621,-0.01090574,-0.05585114,0.00452681,-0.00570185,0.02587283,0.00152472,0.05055107,-0.06363253,-0.01429399,-0.00521014,0.01879388,-0.07576396,0.01737093,-0.06606878,-0.05624468,-0.01503291,0.01044974,0.03682577,-0.05472854,0.01593673,-0.02467847,0.00218791,0.05587407,-0.00982201,-0.01296324,0.01717251,-0.00125151,0.03177859,-0.02542483,0.05859331,0.01666159,-0.0118869,0.00977072,-0.00903399,-0.01922242,-0.05517446,0.01135202,-0.04931566,-0.05995127,0.03770717,0.00844446,0.03304235,-0.02233766,0.03363081,-0.00988319,-0.06611592,0.04424376,-0.0666253,-0.02083932,-0.01490307,0.1008454,-0.03462835,-0.07203151,0.04714655,-0.08055175,-0.00988055,0.00326993,0.03425612,-0.00308151,0.00976568,-0.00081848,0.07190169,-0.05632779,0.03519321,0.05373378,0.00875738,-0.01985365,-0.03506765,-0.00139563,-0.04414415,-0.01361623,-0.00743714,-0.0139832,-0.0616473,0.095031,-0.05066519,-0.00870451,-0.00740764,-0.02637278,-0.02338683,-0.03085257,0.02394125,0.00494797,-0.03838809,-0.01426086,0.06567146,0.02580549,0.01541397,0.02863283,-0.02579132,-0.01385387,-0.03426644,-0.01255693,0.01298951,0.01278707,0.00848962,-0.04752566,0.03985693,-0.01347595,-0.0664008,-0.01659377,0.00449895,-0.00090804,-0.0085058,-0.03247252,0.01041995,-0.00917393,0.01776383,-0.04367012,0.02182599,-0.00485173,0.06913861,-0.02172408,-0.05403665,-0.05032231,-0.03643045,0.01063521,0.07299131,-0.01856073,0.00288796,-0.06137423,-0.02239931,0.00596484,0.00168742,0.04895012,-0.03318438,-0.02899381,-0.03134834,0.03743643,0.01101813,0.01697526,-0.00270643,0.03449358,-0.00014044,0.02265078,0.03276477,-0.03957267,-0.00561264,0.03326579,-0.05319413,0.02829882,0.03493867,-0.0102076,0.07216009,0.05988428,-0.0459054,-0.02168531,0.05734758,-0.02521769,-0.00641955,0.01207012,0.01468809,0.01397377,0.00336076,0.03090853,0.0081459,-0.00766035,0.03028504,-0.01393626,0.01398961,-0.00353755,0.04507305,0.04439513,-0.03389662,-0.0065964,-0.02022736,0.02575146,-0.10115375,-0.03569391,-0.00130672,-0.06623057,0.0278652,-0.00354946,-0.02732633,-0.0777696,-0.0098427,0.10043133,-0.01937756,-0.01011989,0.04856993,0.00911964,0.00023595,0.00532903,-0.01976061,0.00111728,0.01615863,0.00470872,0.01044472,0.01168585,-0.06496709,-0.02865195,0.00909335,-0.01958607,0.00514001,-0.00691818,0.04400656,0.01765431,-0.00499411,0.10528281,-0.00441006,-0.05141533,-0.05027135,0.04281968,-0.00865076,0.04170231,0.00057175,0.00194682,0.03340152,0.01336395,-0.0194821,0.02231886,-0.03853073,-0.02037824,-0.05023319,0.0071779,-0.0005602,0.04812669,0.07532212,0.02047977,0.01271117,0.03559244,0.05075337,-0.04427456,-0.03688502,0.03649667,0.01285167,-0.005149,0.01452543,-0.07316407,0.00515875,0.05672689,-0.02830665,-0.06042274,-0.00203951,0.02218335,0.01863446,0.03779403,0.04145782,0.01100538,0.06032891,-0.00230066,-0.02520766,0.03103037,0.03014805,-0.02578607,-0.05716466,0.03230446,-0.03584552,-0.01103365,-0.02221115,-0.01478095,-0.04233059,0.00516938,0.01251761,-0.01425938,0.08065742,0.03684353,-0.02889499,0.04242035,-0.03133355,0.05554977,-0.06888552,-0.02299403,0.0306175,-0.00424742,-0.00897611,-0.03546286,-0.03434326,-0.0168153,0.00770463,0.04763746,0.04592365,0.03069371,0.00192891,-0.04411774,-0.04186393,-0.01318794,-0.01233633,-0.02553751,0.05146929,-0.02616574,-0.00106683,0.09364024,0.06238483,0.01386087,-0.00876814,-0.01404727,-0.04190749,-0.03619831,-0.02507437,-0.00957012,0.01227903,-0.08376625,-0.01375805,0.0485543,0.02793353,0.02141219,-0.02686791,0.0524041,-0.00970884,-0.01244043,0.04120163,0.03313191,-0.02354034,-0.08384342,0.06203362,-0.05820283,0.04721957,-0.04142582,0.03859242,-0.10067499,0.02825317,0.05390531,-0.01058237,0.02683803,-0.08878845,-0.02579256,0.02196724,0.02056346,-0.05298885,0.02602663,-0.00699458,-0.00975466,-0.00458718,-0.006871,-0.01993472,0.00838492,-0.00586046,0.00104335,-0.07036503,-0.05064591,-0.05115328,-0.01734064,0.02577151,0.02339695,-0.03150156,0.00580214,-0.0055875,0.03343001,0.00721655,-0.01613319,0.04874619,-0.00960851,-0.04096578,0.04029141,-0.0263796,-0.00269079,0.00106425,-0.0763968,-0.03076511,-0.02079076,-0.04937398,0.01983995,-0.04441152,-0.01943715,-0.00467349,-0.00547045,-0.01846312,-0.00231494,-0.07007119,-0.01030645,0.0616692,0.01954176,0.02210595,0.04270437,-0.03780001,0.0470606,0.05855569,-0.0181086,0.04310751,-0.040897,-0.02274167,0.0183208,-0.06347485,0.06280474,0.02036755,-0.06435962,-0.00739016,0.06145091,-0.02484059,0.05087952,-0.03458847,-0.00599741,0.03938646,0.0217303,-0.03321772,-0.0035985,0.04502638,-0.00242031,0.02663929,0.02561605,0.01392589,0.00014315,0.01317189,-0.05765919,0.1067055,0.01876354,-0.02217197,-0.03002613,-0.07780894,-0.04417549,0.01111078,-0.0289241,0.03605074,0.02071318,0.00051857,0.00131834,-0.01210225,-0.00938362,0.0249791,0.03024505,0.02876179,0.02702252,-0.0096906,-0.02285956,0.04615359,0.04159763,-0.0150039,0.00343082,-0.09172581,-0.03053424,-0.03697514,-0.03675677,0.00135389,0.0054282,0.0141551,0.02317891,-0.04235084,0.08401239,0.01670261,0.01975113,-0.03293945,-0.01799015,-0.0031512,0.02211939,-0.06284287,0.00678743,0.03194457,0.04450849,0.0159489,-0.04618396,0.05407283,0.02804478,-0.01677349,-0.02559118,-0.02929701,0.06492734,-0.00116532,-0.01283375,-0.0135902,0.02407139,-0.02958019,0.06322512,0.00153231,-0.00490173,0.00468886,0.00092796,0.04770374,0.00254693,0.02390471,-0.06673338,0.00660427,-0.02012726,0.01769435,0.05127592,0.00557814,0.05236937,-0.01065144,0.00974884,0.00960598,0.05973592,-0.00151918,-0.00811737,-0.05779677,0.00985623,0.04735651,0.00126231,-0.02262738,-0.04575755,0.08310148,0.08078083,-0.00580956,-0.0229509,0.02924094,-0.00666205,0.03899699,-0.00384908,-0.01227537,0.0542684,-0.03335751,0.01890395,0.03365818,-0.02088515,-0.07310122,0.01070032,0.06867811,0.01800914,-0.03028789,-0.02614195,0.02650167,-0.03968803,0.06602944,-0.03629313,0.00422427,-0.00595573,0.01791952,0.01731296,-0.00117357,0.02871957,0.01139327,0.05495143,-0.02273104,-0.01551574,-0.02759152,-0.01868724,0.07291806,-0.00518061,0.03672935,0.0050056,0.01385947,-0.03192668,0.00832425,0.0060463,0.02432267,-0.04573577,0.00443864,0.08778062,0.0043623,0.00592323,0.01334237,-0.02419493,-0.08015202,0.00091662,-0.02885579,0.03200771,0.02896056,0.05730148,-0.04400745,-0.04989407,0.05702138,0.01366899,-0.03641664,0.05241484,0.01410641,-0.02291354,0.03964087,0.06801932,0.04208951,-0.00582386,0.0648018,-0.03292326,0.0333184,-0.00502617,-0.00848422,0.02915475,0.05396035,-0.00479974,0.04462116,-0.06695628,-0.02240877,-0.05887104,0.04438619,-0.01582859,-0.01947577,0.02745321,0.07879741,-0.01220173,-0.0017554,0.01954586,0.00091446,-0.01396814,-0.05685411,-0.04969363,-0.00251372,-0.06224013,0.01366194,0.00716146,0.01575853,0.04790585,-0.03754207,-0.08926151,-0.03954219,0.00140405,-0.05334223,-0.02271334,-0.01360652,0.03565533,-0.02156625,0.00035297,-0.0003047,0.00395387,-0.00971893,0.04364893,0.02108565,0.04371773,0.02976632,-0.04414365,-0.02683489,0.00968813,-0.0888883,0.05511729,0.02224624,0.01664959,-0.00519371,-0.01839506,0.0057968,-0.00884429,0.04657459,-0.0611654,0.01617089,0.00767128,0.05664398,-0.01827672,-0.02270508,-0.0288111,-0.00695149,-0.03436665,-0.0090392,-0.0173788,-0.03123414,0.00811448,-0.01143984,0.0228781,0.00581012,0.02132742,0.01949322,0.02303303,-0.02959336,-0.02558846,-0.03007344,-0.04176277,0.00835254,0.04559093,0.0392991,-0.00785504,0.00551297,-0.00161677,0.00768505,-0.05729496,-0.0215587,0.02492568,-0.02009412,-0.00097615,0.01764894,0.00645967,0.0568102,-0.0063552,-0.04824466,-0.02282349,-0.00136284,-0.02230883,0.02359816,0.06470405,-0.00668479,-0.043559,-0.02074026,-0.00189874,0.02009402,-0.00408978,-0.02722429,-0.01135703,0.03967885,-0.04770481,0.00518052,0.00535426,-0.02358605,0.01254094,-0.01618304,-0.0138614,0.01269929,-0.01315377,-0.00735988,-0.05466863,0.03217542,0.03642379,0.01608406,-0.03163538,-0.07245144,-0.02813871,0.009994,0.0135264,0.03481857,-0.03222547,-0.02509804,0.05111349,-0.01039841,-0.03949464,-0.00601106,0.03877342,0.00875572,0.01058067,-0.0551558,-0.00879782,-0.03085533,0.05228803,-0.03777824,0.0035837,0.0155416,-0.03216926,-0.00190309,0.04929439,0.00664124,-0.05165849,0.0091531,0.00207131,0.01514544,0.01859394,-0.00147826,0.00975785,-0.04934559,-0.03167868,-0.063271,-0.04730423,-0.03008378,0.05816077,0.02604108,0.04153433,-0.08699743,0.03364555,-0.00049766,0.03343463,0.01304992,-0.00909211,0.01873475,-0.05495898,6.4e-7,0.06264663,-0.01582426,0.00939731,-0.06223147,0.00035394,-0.03944181,-0.04789258,-0.00709669,0.01635078],"last_embed":{"tokens":930,"hash":"6zbks7"}}},"last_read":{"hash":"6zbks7","at":1752940880433},"class_name":"SmartSource","outlinks":[{"title":"grep","target":"grep","line":25},{"title":"awk使用手册","target":"awk使用手册","line":29},{"title":"awk使用手册","target":"awk使用手册","line":30}],"metadata":{"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,4],"#基于auth.log的分析":[6,77],"#基于auth.log的分析#{1}":[7,10],"#基于auth.log的分析#{2}":[11,11],"#基于auth.log的分析#统计登陆失败/成功":[12,35],"#基于auth.log的分析#统计登陆失败/成功#{1}":[13,15],"#基于auth.log的分析#统计登陆失败/成功#{2}":[16,19],"#基于auth.log的分析#统计登陆失败/成功#{3}":[17,19],"#基于auth.log的分析#统计登陆失败/成功#{4}":[20,23],"#基于auth.log的分析#统计登陆失败/成功#{5}":[21,23],"#基于auth.log的分析#统计登陆失败/成功#{6}":[24,24],"#基于auth.log的分析#统计登陆失败/成功#{7}":[25,28],"#基于auth.log的分析#统计登陆失败/成功#{8}":[26,28],"#基于auth.log的分析#统计登陆失败/成功#{9}":[29,29],"#基于auth.log的分析#统计登陆失败/成功#{10}":[30,30],"#基于auth.log的分析#统计登陆失败/成功#{11}":[31,33],"#基于auth.log的分析#统计登陆失败/成功#{12}":[34,35],"#基于auth.log的分析#定位日志文件":[36,44],"#基于auth.log的分析#定位日志文件#{1}":[38,40],"#基于auth.log的分析#定位日志文件#{2}":[41,42],"#基于auth.log的分析#定位日志文件#{3}":[43,44],"#基于auth.log的分析#统计攻击者的IP":[45,50],"#基于auth.log的分析#统计攻击者的IP#{1}":[47,50],"#基于auth.log的分析#统计扫描过服务器的IP数量；":[51,65],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{1}":[52,54],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{2}":[55,55],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{3}":[56,56],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{4}":[57,60],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{5}":[61,61],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{6}":[62,63],"#基于auth.log的分析#统计扫描过服务器的IP数量；#{7}":[64,65],"#基于auth.log的分析#统计攻击者尝试的用户名":[66,77],"#基于auth.log的分析#统计攻击者尝试的用户名#{1}":[68,70],"#基于auth.log的分析#统计攻击者尝试的用户名#{2}":[71,74],"#基于auth.log的分析#统计攻击者尝试的用户名#{3}":[72,74],"#基于auth.log的分析#统计攻击者尝试的用户名#{4}":[75,77]},"last_import":{"mtime":1735464406903,"size":2694,"at":1748488128974,"hash":"6zbks7"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/入侵检测/日志分析/SSH登陆日志分析.md","last_embed":{"hash":"6zbks7","at":1752940880433}},