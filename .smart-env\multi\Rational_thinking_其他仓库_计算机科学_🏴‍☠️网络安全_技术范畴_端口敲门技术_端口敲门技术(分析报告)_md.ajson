"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md","last_embed":{"hash":"kfhi8e","at":1752940644398},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07396318,-0.03973293,-0.03339858,-0.02422358,-0.01535518,-0.01289813,0.00031753,0.08146851,0.01720657,0.0225245,-0.01065998,-0.06218088,0.00869697,0.07668775,0.03175303,0.00774071,-0.025794,0.01336595,-0.04133448,0.01093959,0.09617749,-0.0772938,-0.03581835,-0.05531349,-0.00695237,0.03364505,0.01509626,-0.01496399,-0.01383581,-0.18310998,0.00629972,0.02373311,0.00922241,0.02908129,-0.04841499,0.00677347,0.00086434,0.06711993,-0.02084977,0.02703261,-0.02424177,-0.0066238,0.05078385,-0.05116918,-0.00023424,-0.0607822,-0.04510811,0.01386999,-0.01042176,-0.02295169,-0.05863037,-0.06463706,-0.00766142,-0.04313604,-0.04002631,0.01698551,0.00186204,-0.00972056,0.04616061,-0.02577691,0.04505663,-0.00438929,-0.21285087,0.04511909,-0.0081196,-0.00047855,-0.03243151,-0.00781482,0.05183474,0.00032098,-0.06568044,-0.00254789,0.02420522,0.05131244,0.10146967,0.00692079,0.02997287,-0.04634042,-0.04741961,-0.02657185,-0.01506368,0.03366069,-0.01506691,-0.02624076,0.05606214,0.03185466,0.00251846,-0.02943302,-0.00374222,-0.02697565,-0.0304666,-0.05726863,-0.01805578,0.04659118,-0.00197909,0.01776018,0.04910061,0.00483542,-0.03403967,0.14109683,-0.0536232,0.03226839,0.0028621,-0.02067925,0.02774336,-0.06045868,0.00075021,-0.05311964,-0.02556562,-0.04420264,-0.07481512,-0.04969751,0.03293942,0.00825322,0.00007157,0.05297357,0.03819921,0.01033903,-0.01774008,-0.00241883,-0.00422088,-0.0029695,0.05737972,-0.05933704,-0.02258041,-0.04611864,0.06621666,0.08259637,0.0263697,-0.00695904,0.07931743,-0.04774447,-0.07523135,0.00596421,-0.00559898,-0.02077597,-0.02437455,-0.0084269,-0.0045283,-0.02378349,0.01321249,-0.07985704,0.00596258,-0.10157003,-0.0582345,0.03140577,-0.06374563,0.0363169,0.02942263,-0.00107018,0.00341887,0.06701856,-0.00130869,-0.0363576,0.0109321,0.01881693,0.0362261,0.12733562,-0.03928606,-0.0045216,0.01001384,0.05476858,-0.0234011,0.13801681,0.00641155,-0.03569042,0.0260225,0.01424416,0.00096657,-0.01750144,0.01392394,0.02055545,0.00871844,0.04439993,0.08832166,0.00908139,-0.00508072,-0.02082121,0.02542246,0.05208154,0.05545031,-0.08530837,-0.03629429,0.05781969,0.0550509,-0.1126908,-0.02313225,-0.03593553,0.00086539,-0.06533597,-0.01169123,-0.01014913,0.0067555,0.04774579,-0.06481603,-0.09230085,0.03412278,-0.00934865,0.01611495,-0.01935288,0.05358056,0.00491751,-0.0311306,0.01337099,0.02427549,-0.00031302,0.02824807,-0.0353264,0.03757401,0.0050081,-0.009943,0.00321109,-0.02447401,0.02749999,-0.00253727,0.06776065,0.02994085,0.02851601,0.03056616,0.04773133,0.02749359,-0.03755425,-0.08395042,-0.23008537,-0.07012238,0.04279108,-0.05251354,-0.01606564,-0.00771215,0.01007673,-0.01795154,0.08035249,0.0568598,0.08502348,0.02717802,-0.04760175,-0.03132628,0.00528032,0.02179401,0.0212726,-0.02315811,-0.01101372,0.05215707,-0.02476403,0.05634585,-0.00748488,-0.0286407,0.02337048,-0.02963678,0.16405176,0.05602226,0.00953295,0.06369708,0.01725614,-0.01762411,-0.05002233,-0.05378165,0.013495,0.05983201,-0.05455565,-0.04473857,-0.0160548,-0.0428457,-0.05891382,0.03097655,-0.0602239,-0.0789161,-0.05519959,-0.03195256,-0.07622287,0.04449959,0.01431242,0.05960337,-0.02938898,0.05732375,0.00829166,-0.01795705,0.02066318,-0.00697115,-0.08155377,-0.05380967,-0.05831492,0.02768962,0.0017631,-0.00164376,-0.01445606,0.07241251,0.00139098,-0.02689779,-0.02910265,0.00296029,-0.03244057,0.03516234,-0.03998102,0.12049491,0.02003881,-0.04927512,0.0849724,-0.0070089,-0.03548899,-0.06067116,-0.00824459,0.0096848,0.05367906,0.01208502,0.00593924,0.05993926,0.0089921,0.03919571,0.01772881,-0.00201324,0.00961416,-0.04022918,-0.04631404,0.03813946,-0.06616069,0.06249639,0.00983455,0.02645529,-0.28976157,0.0414755,-0.02401465,0.03060916,0.07087858,0.0275175,0.06385811,0.02217874,-0.03479819,0.0456688,-0.07707819,0.03695739,0.02369981,-0.02117387,0.01050388,-0.04199247,0.01310979,0.02319918,0.08885539,-0.02313002,0.02541082,0.09580693,0.21243422,-0.01379917,0.03722062,-0.00089667,0.02525306,0.07621601,0.05095452,0.04164776,0.01702415,-0.00936368,0.03412317,-0.05825689,0.01005967,0.01623245,-0.04198705,-0.00666449,0.03232052,-0.02303234,-0.03865467,0.03828693,-0.06341287,0.02591329,0.12938343,0.07156785,0.00105577,-0.05154462,-0.03794846,0.06658951,-0.02658204,0.05121671,-0.0247258,-0.03926047,0.00620285,0.06548078,-0.00365567,-0.02411537,-0.03088675,-0.04896791,-0.00079583,0.01455737,0.04698129,0.11235534,0.04051978],"last_embed":{"hash":"899c341ff9d726e16f54deae21ee0481c7218667fed9715980182f0c95fc50d3","tokens":434}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06539217,-0.06652012,0.02770454,-0.02680176,-0.01654894,0.04226364,0.03853914,-0.01756942,-0.00914642,-0.07787655,0.03075417,0.04145346,-0.00267192,-0.06064465,0.03330358,-0.02714958,0.00263589,0.01700492,0.02530101,-0.01721691,-0.03203565,0.00538184,-0.04227775,0.01483802,0.06668475,0.00505494,0.04479863,-0.05348925,0.03821336,0.0246966,0.04036816,-0.02198392,0.03517796,-0.04002578,-0.01671578,0.04176306,-0.01151362,-0.00567592,0.04266566,-0.02467958,-0.00526644,0.02357373,-0.0260487,0.01019398,-0.02433452,0.03206105,0.00149493,-0.039062,-0.00246239,-0.03161505,0.04738355,-0.06341997,0.01985957,0.02396874,-0.02270126,0.04402975,-0.00347507,-0.08833523,-0.03162691,-0.00282302,-0.00795982,-0.01494063,0.05296407,-0.01446144,0.0321852,0.00568397,-0.00505704,0.00737976,-0.00288309,-0.03396548,-0.00004901,-0.06240332,0.03886204,-0.00818443,-0.00450833,0.00079453,0.02440715,-0.00223883,-0.03763016,0.08946002,-0.03907002,-0.00332774,-0.01067187,0.07937079,0.03645374,-0.0177131,0.03511203,-0.04844286,-0.04326883,0.00951483,0.04119848,-0.04033311,-0.04792371,0.02408108,0.03531813,0.00119355,0.0254897,0.02354341,0.01138297,-0.09875432,0.04493836,0.00786138,-0.06870781,-0.00640564,0.01109622,0.02478088,0.01654132,0.05952875,-0.02279794,-0.04262063,-0.00065914,0.03508429,-0.05722586,-0.05443183,-0.06160453,0.01613475,0.01391756,-0.04270358,-0.02957839,0.01533325,0.08951539,0.05234273,-0.02263741,-0.06014356,-0.04571627,0.06207382,-0.00499879,-0.01233272,-0.001509,-0.03409643,-0.04873897,0.03464208,-0.07902753,-0.00937741,0.0538195,-0.0039107,0.07657377,-0.01606704,-0.05744538,-0.00291154,0.04335165,-0.01966395,-0.01805814,0.00682485,-0.02229537,0.06370348,-0.02499566,-0.06613442,0.03499963,-0.00583954,0.07359818,0.02560039,-0.01168037,-0.02135474,-0.01334586,-0.02479704,0.08270616,0.03600474,-0.06856611,-0.03525216,0.00068844,0.00313058,-0.02701954,-0.00967533,0.00301601,-0.02084896,-0.00554097,-0.00602682,-0.03596416,-0.05925905,0.00198927,0.00441501,-0.02032009,0.00034692,0.04059397,0.01554202,0.02879409,0.05433811,-0.06981496,0.0166671,0.03035056,-0.01752505,-0.0104967,-0.00041802,0.04661132,0.029398,-0.00926926,0.00743684,0.08685976,-0.02548329,0.01203777,0.01133028,0.02824655,0.00460412,0.01130921,0.05048688,-0.02997356,0.00596789,-0.00033562,0.00069174,-0.05913064,-0.02142666,-0.01619559,-0.08065568,0.04011217,-0.01409371,-0.03093979,-0.02848987,-0.00617454,0.08907138,-0.01597003,-0.00833501,0.00461787,0.02131006,-0.04239767,0.01672002,0.00662209,0.07278546,-0.00863776,-0.00088261,-0.02820937,0.02803502,-0.07763145,-0.07561261,-0.02349912,-0.02574603,0.00600882,0.01842556,0.05039242,0.03134418,0.04628631,0.10092668,-0.05800023,-0.06034302,-0.05152763,0.0220044,0.00526238,0.09408663,-0.03602685,0.03021132,0.00659401,-0.03328976,-0.02222274,0.02271979,-0.02257491,-0.00974242,-0.02076025,-0.00963689,0.02805382,0.03397033,0.04764519,0.00843172,0.02429217,0.03772042,0.02339451,-0.01953232,0.00624185,0.03142221,-0.02976011,-0.04562612,0.03231752,-0.07381285,-0.02454112,0.03890914,-0.00983407,-0.00380197,-0.01892374,-0.01156348,-0.06214764,0.05538744,0.02571189,-0.01135681,-0.05091932,0.04579848,0.04241952,0.03306745,0.01640806,-0.02426077,-0.00683659,-0.0005495,0.00745537,-0.02837777,0.02103854,-0.02164104,-0.02642016,0.01781992,-0.01903039,-0.04267947,0.02374232,0.0488673,0.00167148,0.00403363,-0.04141307,0.00429099,-0.01851903,-0.00822217,0.07738791,-0.02246557,0.00198095,-0.06533038,-0.00043526,-0.04043866,0.00879121,-0.04157678,0.0183507,0.05930639,0.00208669,-0.06947626,-0.05988842,-0.02593534,-0.05829924,-0.04740202,0.00591405,-0.0136107,0.01718318,0.0410938,0.01350949,0.01185947,-0.01854953,-0.07277467,-0.00528165,-0.00811003,-0.03612067,0.0378492,-0.01609866,-0.0923249,-0.01417662,0.0428583,0.02988185,0.06890529,0.00118838,0.0097891,-0.04923366,-0.00203251,0.02539853,0.01922631,-0.01576575,-0.07892989,0.01360514,-0.06246264,0.01674148,-0.03939148,0.01203988,-0.0189894,-0.03755227,0.04651152,0.02023895,-0.00864888,-0.00459362,-0.04606526,0.00678026,0.00972735,-0.02579303,-0.01607803,-0.03008067,0.07598882,-0.00579343,-0.02612584,-0.02322733,0.02769657,0.05433214,-0.00020861,0.06645168,-0.00606279,-0.03309376,0.00216008,0.03183752,0.06306442,-0.0017985,-0.01921044,-0.02994554,-0.00273123,-0.02708224,-0.03236299,0.04826419,-0.0089031,-0.01971026,0.0559743,0.02692381,0.01394977,-0.01734267,0.01774964,-0.02784144,-0.02937977,-0.07485639,-0.01382348,-0.06280208,0.00567662,-0.0134737,-0.0556891,0.04060879,0.01784843,-0.05094153,-0.0342465,0.02710222,0.0096503,0.02968493,0.02040444,-0.00358147,0.02716432,0.08466564,-0.02315816,0.02059919,0.02281221,-0.03590835,0.06396119,-0.04371132,0.0548597,-0.00756623,-0.05289852,-0.00882871,0.02264633,-0.03087712,0.01015522,-0.03092804,-0.02640175,0.01223711,-0.04633418,-0.02510452,0.00120154,-0.0117284,-0.00826104,0.02223714,0.0529084,-0.02881948,-0.0027431,-0.0140497,-0.04095189,0.03077041,-0.03876122,-0.00491565,-0.01255875,-0.11005432,-0.01431409,-0.02562712,-0.00675966,0.04164753,0.00888881,0.02487898,-0.00230232,-0.01351117,-0.01308455,0.03273831,-0.03149609,-0.00166879,-0.02370978,0.00339386,-0.06382195,-0.01462182,0.05049086,0.01405128,-0.01197703,-0.05848613,0.0403281,0.02997801,-0.0183324,-0.03523437,0.03961735,0.04013556,0.05217457,-0.12284144,0.00815227,0.02204335,0.00868364,0.0173774,0.04145585,0.00483475,-0.01697745,-0.03790171,-0.00374097,-0.03703902,-0.01723476,0.04548965,-0.04662428,0.10126803,-0.00647883,0.00952572,0.0451945,-0.0424144,0.0110069,0.01506229,0.0001691,-0.06021212,-0.02545095,-0.02399736,0.01874328,0.00647651,-0.00861632,0.02422385,-0.0408854,0.03026218,-0.00451992,-0.00171538,-0.01477279,-0.04717473,0.03689504,0.01223294,0.06264263,-0.00514967,0.03692916,-0.05742654,0.05965344,-0.03837433,0.03056933,0.03621041,-0.00060926,-0.03597677,0.01649265,0.00782452,-0.07557435,0.0519898,-0.02634036,0.08100718,-0.00046225,-0.00332711,-0.00364622,0.0274088,0.04485271,0.03809351,0.0100931,0.06213751,0.0234837,-0.00815255,-0.00178935,0.02274334,-0.01915917,-0.02801371,-0.02114262,0.00489659,-0.02321696,-0.01451635,0.0149986,0.03013005,-0.02254025,0.01584754,-0.02066164,0.03706279,-0.04680432,0.01060508,0.04872549,0.04082453,-0.00397387,0.02110932,-0.00413597,-0.00184204,0.01535843,0.01146877,-0.00884488,-0.002995,-0.01406773,0.01433093,-0.02917519,-0.00616594,-0.03696937,0.01758924,0.04874681,0.04086097,-0.07306278,-0.0169501,0.03403466,0.05087816,0.05322848,0.02042315,-0.02423423,-0.03601697,0.01745627,-0.07610234,0.03319521,-0.00025005,-0.02570913,-0.11440623,-0.00848128,0.04773896,0.04646105,-0.0449676,0.02544009,-0.02598554,-0.02404546,0.04393701,0.03186266,0.02212893,0.01946394,0.05861926,-0.02547158,0.00618902,-0.02779781,-0.02221841,0.04561125,0.03857617,-0.04418453,0.05319859,-0.02319342,-0.04376291,-0.00654434,-0.03237062,0.0299857,-0.03497091,0.02104718,-0.00120676,-0.01182727,0.02323142,-0.0152568,-0.05016034,-0.02682798,0.00180466,-0.03982282,-0.01463744,-0.009633,-0.00634922,0.02886372,0.02412816,0.03337887,-0.0076007,-0.04774027,0.03703655,-0.02298554,0.01921502,0.04139204,-0.00266589,0.03705035,-0.03440771,-0.00603555,-0.0653548,-0.00604837,-0.04910081,0.02342197,0.01687061,0.029729,0.00913577,-0.00989559,-0.00217897,-0.02612118,-0.01854324,0.01832309,0.05596972,0.00437281,-0.01686834,-0.01635402,0.02037401,0.04887003,-0.0083257,-0.04046054,0.06204359,-0.00070129,0.02907374,-0.02358775,-0.03559195,-0.00117056,-0.02382561,-0.04376504,0.04989391,-0.01481303,0.05487145,0.0003494,-0.00275954,0.04331346,0.03053154,-0.03630996,0.03413258,0.08317757,-0.02549859,-0.01341112,0.05889344,0.05613272,0.03649503,0.02790242,0.06539714,-0.01213593,-0.03970904,0.0579472,0.01574872,-0.03373457,0.02208437,0.01743058,0.0116921,0.01303696,0.01989785,0.04700507,0.03728797,-0.04500757,-0.01565532,0.00369596,0.01767757,0.01331215,0.01382872,-0.05695891,0.06210534,0.00274992,-0.01777143,0.00250548,-0.06194298,0.00521881,-0.00191015,0.01531277,0.02709184,-0.04815053,-0.01477762,0.01967046,0.01159789,-0.02090022,-0.01378209,-0.00067912,-0.03103907,0.00665256,-0.0006339,-0.00907353,0.04317911,0.0220456,-0.00209731,0.01519621,0.02233066,-0.01596239,0.01588852,0.02292522,0.0366682,-0.04503475,-0.05472037,0.03712684,-0.04072934,-0.05421329,0.00884166,0.02037526,0.04829701,0.0496053,-0.03194584,0.00218084,0.01348487,-0.00495389,-0.05421855,0.01292312,0.03602514,-0.09386425,0.00649542,0.04223088,-0.01429677,0.02300651,0.02960226,-0.03110319,0.0214239,-0.02115435,-0.03402172,-0.00376063,-0.00246699,-0.07738937,0.03683627,-0.0267871,0.00941077,0.00097179,-0.00802732,0.01677081,-0.05060112,-0.01923238,-0.04967777,0.07694538,0.00212544,-0.00678479,-0.02071477,-0.02471596,8.3e-7,-0.08142298,0.0060884,0.03476381,-0.0325981,-0.02068665,-0.05187404,-0.09182456,-0.01193322,0.03454528],"last_embed":{"tokens":362,"hash":"kfhi8e"}}},"last_read":{"hash":"kfhi8e","at":1752940644398},"class_name":"SmartSource","outlinks":[{"title":"防火墙","target":"防火墙","line":33},{"title":"Knock","target":"Knock","line":46}],"blocks":{"#实现原理":[2,43],"#实现原理#流程可视化":[4,43],"#实现原理#流程可视化#{1}":[5,30],"#实现原理#流程可视化#{2}":[31,31],"#实现原理#流程可视化#{3}":[32,33],"#实现原理#流程可视化#{4}":[34,34],"#实现原理#流程可视化#{5}":[35,43],"#---frontmatter---":[38,42],"#相关的工具":[44,60],"#相关的工具#[[Knock]]":[46,49],"#相关的工具#[[Knock]]#{1}":[47,47],"#相关的工具#[[Knock]]#{2}":[48,49],"#相关的工具#存在的问题":[50,60],"#相关的工具#存在的问题#{1}":[51,54],"#相关的工具#存在的问题#{2}":[55,60]},"last_import":{"mtime":1719830624857,"size":1603,"at":1749024987320,"hash":"kfhi8e"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理","lines":[2,43],"size":636,"outlinks":[{"title":"防火墙","target":"防火墙","line":32}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化","lines":[4,43],"size":628,"outlinks":[{"title":"防火墙","target":"防火墙","line":30}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{1}","lines":[5,30],"size":440,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{2}","lines":[31,31],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{3}","lines":[32,33],"size":56,"outlinks":[{"title":"防火墙","target":"防火墙","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{4}","lines":[34,34],"size":18,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{5}","lines":[35,43],"size":78,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#---frontmatter---","lines":[38,42],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具","lines":[44,60],"size":191,"outlinks":[{"title":"Knock","target":"Knock","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]","lines":[46,49],"size":49,"outlinks":[{"title":"Knock","target":"Knock","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]#{1}","lines":[47,47],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]#{2}","lines":[48,49],"size":20,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题","lines":[50,60],"size":132,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题#{1}","lines":[51,54],"size":110,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题#{2}","lines":[55,60],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md","last_embed":{"hash":"kfhi8e","at":1752940872656},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07396318,-0.03973293,-0.03339858,-0.02422358,-0.01535518,-0.01289813,0.00031753,0.08146851,0.01720657,0.0225245,-0.01065998,-0.06218088,0.00869697,0.07668775,0.03175303,0.00774071,-0.025794,0.01336595,-0.04133448,0.01093959,0.09617749,-0.0772938,-0.03581835,-0.05531349,-0.00695237,0.03364505,0.01509626,-0.01496399,-0.01383581,-0.18310998,0.00629972,0.02373311,0.00922241,0.02908129,-0.04841499,0.00677347,0.00086434,0.06711993,-0.02084977,0.02703261,-0.02424177,-0.0066238,0.05078385,-0.05116918,-0.00023424,-0.0607822,-0.04510811,0.01386999,-0.01042176,-0.02295169,-0.05863037,-0.06463706,-0.00766142,-0.04313604,-0.04002631,0.01698551,0.00186204,-0.00972056,0.04616061,-0.02577691,0.04505663,-0.00438929,-0.21285087,0.04511909,-0.0081196,-0.00047855,-0.03243151,-0.00781482,0.05183474,0.00032098,-0.06568044,-0.00254789,0.02420522,0.05131244,0.10146967,0.00692079,0.02997287,-0.04634042,-0.04741961,-0.02657185,-0.01506368,0.03366069,-0.01506691,-0.02624076,0.05606214,0.03185466,0.00251846,-0.02943302,-0.00374222,-0.02697565,-0.0304666,-0.05726863,-0.01805578,0.04659118,-0.00197909,0.01776018,0.04910061,0.00483542,-0.03403967,0.14109683,-0.0536232,0.03226839,0.0028621,-0.02067925,0.02774336,-0.06045868,0.00075021,-0.05311964,-0.02556562,-0.04420264,-0.07481512,-0.04969751,0.03293942,0.00825322,0.00007157,0.05297357,0.03819921,0.01033903,-0.01774008,-0.00241883,-0.00422088,-0.0029695,0.05737972,-0.05933704,-0.02258041,-0.04611864,0.06621666,0.08259637,0.0263697,-0.00695904,0.07931743,-0.04774447,-0.07523135,0.00596421,-0.00559898,-0.02077597,-0.02437455,-0.0084269,-0.0045283,-0.02378349,0.01321249,-0.07985704,0.00596258,-0.10157003,-0.0582345,0.03140577,-0.06374563,0.0363169,0.02942263,-0.00107018,0.00341887,0.06701856,-0.00130869,-0.0363576,0.0109321,0.01881693,0.0362261,0.12733562,-0.03928606,-0.0045216,0.01001384,0.05476858,-0.0234011,0.13801681,0.00641155,-0.03569042,0.0260225,0.01424416,0.00096657,-0.01750144,0.01392394,0.02055545,0.00871844,0.04439993,0.08832166,0.00908139,-0.00508072,-0.02082121,0.02542246,0.05208154,0.05545031,-0.08530837,-0.03629429,0.05781969,0.0550509,-0.1126908,-0.02313225,-0.03593553,0.00086539,-0.06533597,-0.01169123,-0.01014913,0.0067555,0.04774579,-0.06481603,-0.09230085,0.03412278,-0.00934865,0.01611495,-0.01935288,0.05358056,0.00491751,-0.0311306,0.01337099,0.02427549,-0.00031302,0.02824807,-0.0353264,0.03757401,0.0050081,-0.009943,0.00321109,-0.02447401,0.02749999,-0.00253727,0.06776065,0.02994085,0.02851601,0.03056616,0.04773133,0.02749359,-0.03755425,-0.08395042,-0.23008537,-0.07012238,0.04279108,-0.05251354,-0.01606564,-0.00771215,0.01007673,-0.01795154,0.08035249,0.0568598,0.08502348,0.02717802,-0.04760175,-0.03132628,0.00528032,0.02179401,0.0212726,-0.02315811,-0.01101372,0.05215707,-0.02476403,0.05634585,-0.00748488,-0.0286407,0.02337048,-0.02963678,0.16405176,0.05602226,0.00953295,0.06369708,0.01725614,-0.01762411,-0.05002233,-0.05378165,0.013495,0.05983201,-0.05455565,-0.04473857,-0.0160548,-0.0428457,-0.05891382,0.03097655,-0.0602239,-0.0789161,-0.05519959,-0.03195256,-0.07622287,0.04449959,0.01431242,0.05960337,-0.02938898,0.05732375,0.00829166,-0.01795705,0.02066318,-0.00697115,-0.08155377,-0.05380967,-0.05831492,0.02768962,0.0017631,-0.00164376,-0.01445606,0.07241251,0.00139098,-0.02689779,-0.02910265,0.00296029,-0.03244057,0.03516234,-0.03998102,0.12049491,0.02003881,-0.04927512,0.0849724,-0.0070089,-0.03548899,-0.06067116,-0.00824459,0.0096848,0.05367906,0.01208502,0.00593924,0.05993926,0.0089921,0.03919571,0.01772881,-0.00201324,0.00961416,-0.04022918,-0.04631404,0.03813946,-0.06616069,0.06249639,0.00983455,0.02645529,-0.28976157,0.0414755,-0.02401465,0.03060916,0.07087858,0.0275175,0.06385811,0.02217874,-0.03479819,0.0456688,-0.07707819,0.03695739,0.02369981,-0.02117387,0.01050388,-0.04199247,0.01310979,0.02319918,0.08885539,-0.02313002,0.02541082,0.09580693,0.21243422,-0.01379917,0.03722062,-0.00089667,0.02525306,0.07621601,0.05095452,0.04164776,0.01702415,-0.00936368,0.03412317,-0.05825689,0.01005967,0.01623245,-0.04198705,-0.00666449,0.03232052,-0.02303234,-0.03865467,0.03828693,-0.06341287,0.02591329,0.12938343,0.07156785,0.00105577,-0.05154462,-0.03794846,0.06658951,-0.02658204,0.05121671,-0.0247258,-0.03926047,0.00620285,0.06548078,-0.00365567,-0.02411537,-0.03088675,-0.04896791,-0.00079583,0.01455737,0.04698129,0.11235534,0.04051978],"last_embed":{"hash":"899c341ff9d726e16f54deae21ee0481c7218667fed9715980182f0c95fc50d3","tokens":434}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06539217,-0.06652012,0.02770454,-0.02680176,-0.01654894,0.04226364,0.03853914,-0.01756942,-0.00914642,-0.07787655,0.03075417,0.04145346,-0.00267192,-0.06064465,0.03330358,-0.02714958,0.00263589,0.01700492,0.02530101,-0.01721691,-0.03203565,0.00538184,-0.04227775,0.01483802,0.06668475,0.00505494,0.04479863,-0.05348925,0.03821336,0.0246966,0.04036816,-0.02198392,0.03517796,-0.04002578,-0.01671578,0.04176306,-0.01151362,-0.00567592,0.04266566,-0.02467958,-0.00526644,0.02357373,-0.0260487,0.01019398,-0.02433452,0.03206105,0.00149493,-0.039062,-0.00246239,-0.03161505,0.04738355,-0.06341997,0.01985957,0.02396874,-0.02270126,0.04402975,-0.00347507,-0.08833523,-0.03162691,-0.00282302,-0.00795982,-0.01494063,0.05296407,-0.01446144,0.0321852,0.00568397,-0.00505704,0.00737976,-0.00288309,-0.03396548,-0.00004901,-0.06240332,0.03886204,-0.00818443,-0.00450833,0.00079453,0.02440715,-0.00223883,-0.03763016,0.08946002,-0.03907002,-0.00332774,-0.01067187,0.07937079,0.03645374,-0.0177131,0.03511203,-0.04844286,-0.04326883,0.00951483,0.04119848,-0.04033311,-0.04792371,0.02408108,0.03531813,0.00119355,0.0254897,0.02354341,0.01138297,-0.09875432,0.04493836,0.00786138,-0.06870781,-0.00640564,0.01109622,0.02478088,0.01654132,0.05952875,-0.02279794,-0.04262063,-0.00065914,0.03508429,-0.05722586,-0.05443183,-0.06160453,0.01613475,0.01391756,-0.04270358,-0.02957839,0.01533325,0.08951539,0.05234273,-0.02263741,-0.06014356,-0.04571627,0.06207382,-0.00499879,-0.01233272,-0.001509,-0.03409643,-0.04873897,0.03464208,-0.07902753,-0.00937741,0.0538195,-0.0039107,0.07657377,-0.01606704,-0.05744538,-0.00291154,0.04335165,-0.01966395,-0.01805814,0.00682485,-0.02229537,0.06370348,-0.02499566,-0.06613442,0.03499963,-0.00583954,0.07359818,0.02560039,-0.01168037,-0.02135474,-0.01334586,-0.02479704,0.08270616,0.03600474,-0.06856611,-0.03525216,0.00068844,0.00313058,-0.02701954,-0.00967533,0.00301601,-0.02084896,-0.00554097,-0.00602682,-0.03596416,-0.05925905,0.00198927,0.00441501,-0.02032009,0.00034692,0.04059397,0.01554202,0.02879409,0.05433811,-0.06981496,0.0166671,0.03035056,-0.01752505,-0.0104967,-0.00041802,0.04661132,0.029398,-0.00926926,0.00743684,0.08685976,-0.02548329,0.01203777,0.01133028,0.02824655,0.00460412,0.01130921,0.05048688,-0.02997356,0.00596789,-0.00033562,0.00069174,-0.05913064,-0.02142666,-0.01619559,-0.08065568,0.04011217,-0.01409371,-0.03093979,-0.02848987,-0.00617454,0.08907138,-0.01597003,-0.00833501,0.00461787,0.02131006,-0.04239767,0.01672002,0.00662209,0.07278546,-0.00863776,-0.00088261,-0.02820937,0.02803502,-0.07763145,-0.07561261,-0.02349912,-0.02574603,0.00600882,0.01842556,0.05039242,0.03134418,0.04628631,0.10092668,-0.05800023,-0.06034302,-0.05152763,0.0220044,0.00526238,0.09408663,-0.03602685,0.03021132,0.00659401,-0.03328976,-0.02222274,0.02271979,-0.02257491,-0.00974242,-0.02076025,-0.00963689,0.02805382,0.03397033,0.04764519,0.00843172,0.02429217,0.03772042,0.02339451,-0.01953232,0.00624185,0.03142221,-0.02976011,-0.04562612,0.03231752,-0.07381285,-0.02454112,0.03890914,-0.00983407,-0.00380197,-0.01892374,-0.01156348,-0.06214764,0.05538744,0.02571189,-0.01135681,-0.05091932,0.04579848,0.04241952,0.03306745,0.01640806,-0.02426077,-0.00683659,-0.0005495,0.00745537,-0.02837777,0.02103854,-0.02164104,-0.02642016,0.01781992,-0.01903039,-0.04267947,0.02374232,0.0488673,0.00167148,0.00403363,-0.04141307,0.00429099,-0.01851903,-0.00822217,0.07738791,-0.02246557,0.00198095,-0.06533038,-0.00043526,-0.04043866,0.00879121,-0.04157678,0.0183507,0.05930639,0.00208669,-0.06947626,-0.05988842,-0.02593534,-0.05829924,-0.04740202,0.00591405,-0.0136107,0.01718318,0.0410938,0.01350949,0.01185947,-0.01854953,-0.07277467,-0.00528165,-0.00811003,-0.03612067,0.0378492,-0.01609866,-0.0923249,-0.01417662,0.0428583,0.02988185,0.06890529,0.00118838,0.0097891,-0.04923366,-0.00203251,0.02539853,0.01922631,-0.01576575,-0.07892989,0.01360514,-0.06246264,0.01674148,-0.03939148,0.01203988,-0.0189894,-0.03755227,0.04651152,0.02023895,-0.00864888,-0.00459362,-0.04606526,0.00678026,0.00972735,-0.02579303,-0.01607803,-0.03008067,0.07598882,-0.00579343,-0.02612584,-0.02322733,0.02769657,0.05433214,-0.00020861,0.06645168,-0.00606279,-0.03309376,0.00216008,0.03183752,0.06306442,-0.0017985,-0.01921044,-0.02994554,-0.00273123,-0.02708224,-0.03236299,0.04826419,-0.0089031,-0.01971026,0.0559743,0.02692381,0.01394977,-0.01734267,0.01774964,-0.02784144,-0.02937977,-0.07485639,-0.01382348,-0.06280208,0.00567662,-0.0134737,-0.0556891,0.04060879,0.01784843,-0.05094153,-0.0342465,0.02710222,0.0096503,0.02968493,0.02040444,-0.00358147,0.02716432,0.08466564,-0.02315816,0.02059919,0.02281221,-0.03590835,0.06396119,-0.04371132,0.0548597,-0.00756623,-0.05289852,-0.00882871,0.02264633,-0.03087712,0.01015522,-0.03092804,-0.02640175,0.01223711,-0.04633418,-0.02510452,0.00120154,-0.0117284,-0.00826104,0.02223714,0.0529084,-0.02881948,-0.0027431,-0.0140497,-0.04095189,0.03077041,-0.03876122,-0.00491565,-0.01255875,-0.11005432,-0.01431409,-0.02562712,-0.00675966,0.04164753,0.00888881,0.02487898,-0.00230232,-0.01351117,-0.01308455,0.03273831,-0.03149609,-0.00166879,-0.02370978,0.00339386,-0.06382195,-0.01462182,0.05049086,0.01405128,-0.01197703,-0.05848613,0.0403281,0.02997801,-0.0183324,-0.03523437,0.03961735,0.04013556,0.05217457,-0.12284144,0.00815227,0.02204335,0.00868364,0.0173774,0.04145585,0.00483475,-0.01697745,-0.03790171,-0.00374097,-0.03703902,-0.01723476,0.04548965,-0.04662428,0.10126803,-0.00647883,0.00952572,0.0451945,-0.0424144,0.0110069,0.01506229,0.0001691,-0.06021212,-0.02545095,-0.02399736,0.01874328,0.00647651,-0.00861632,0.02422385,-0.0408854,0.03026218,-0.00451992,-0.00171538,-0.01477279,-0.04717473,0.03689504,0.01223294,0.06264263,-0.00514967,0.03692916,-0.05742654,0.05965344,-0.03837433,0.03056933,0.03621041,-0.00060926,-0.03597677,0.01649265,0.00782452,-0.07557435,0.0519898,-0.02634036,0.08100718,-0.00046225,-0.00332711,-0.00364622,0.0274088,0.04485271,0.03809351,0.0100931,0.06213751,0.0234837,-0.00815255,-0.00178935,0.02274334,-0.01915917,-0.02801371,-0.02114262,0.00489659,-0.02321696,-0.01451635,0.0149986,0.03013005,-0.02254025,0.01584754,-0.02066164,0.03706279,-0.04680432,0.01060508,0.04872549,0.04082453,-0.00397387,0.02110932,-0.00413597,-0.00184204,0.01535843,0.01146877,-0.00884488,-0.002995,-0.01406773,0.01433093,-0.02917519,-0.00616594,-0.03696937,0.01758924,0.04874681,0.04086097,-0.07306278,-0.0169501,0.03403466,0.05087816,0.05322848,0.02042315,-0.02423423,-0.03601697,0.01745627,-0.07610234,0.03319521,-0.00025005,-0.02570913,-0.11440623,-0.00848128,0.04773896,0.04646105,-0.0449676,0.02544009,-0.02598554,-0.02404546,0.04393701,0.03186266,0.02212893,0.01946394,0.05861926,-0.02547158,0.00618902,-0.02779781,-0.02221841,0.04561125,0.03857617,-0.04418453,0.05319859,-0.02319342,-0.04376291,-0.00654434,-0.03237062,0.0299857,-0.03497091,0.02104718,-0.00120676,-0.01182727,0.02323142,-0.0152568,-0.05016034,-0.02682798,0.00180466,-0.03982282,-0.01463744,-0.009633,-0.00634922,0.02886372,0.02412816,0.03337887,-0.0076007,-0.04774027,0.03703655,-0.02298554,0.01921502,0.04139204,-0.00266589,0.03705035,-0.03440771,-0.00603555,-0.0653548,-0.00604837,-0.04910081,0.02342197,0.01687061,0.029729,0.00913577,-0.00989559,-0.00217897,-0.02612118,-0.01854324,0.01832309,0.05596972,0.00437281,-0.01686834,-0.01635402,0.02037401,0.04887003,-0.0083257,-0.04046054,0.06204359,-0.00070129,0.02907374,-0.02358775,-0.03559195,-0.00117056,-0.02382561,-0.04376504,0.04989391,-0.01481303,0.05487145,0.0003494,-0.00275954,0.04331346,0.03053154,-0.03630996,0.03413258,0.08317757,-0.02549859,-0.01341112,0.05889344,0.05613272,0.03649503,0.02790242,0.06539714,-0.01213593,-0.03970904,0.0579472,0.01574872,-0.03373457,0.02208437,0.01743058,0.0116921,0.01303696,0.01989785,0.04700507,0.03728797,-0.04500757,-0.01565532,0.00369596,0.01767757,0.01331215,0.01382872,-0.05695891,0.06210534,0.00274992,-0.01777143,0.00250548,-0.06194298,0.00521881,-0.00191015,0.01531277,0.02709184,-0.04815053,-0.01477762,0.01967046,0.01159789,-0.02090022,-0.01378209,-0.00067912,-0.03103907,0.00665256,-0.0006339,-0.00907353,0.04317911,0.0220456,-0.00209731,0.01519621,0.02233066,-0.01596239,0.01588852,0.02292522,0.0366682,-0.04503475,-0.05472037,0.03712684,-0.04072934,-0.05421329,0.00884166,0.02037526,0.04829701,0.0496053,-0.03194584,0.00218084,0.01348487,-0.00495389,-0.05421855,0.01292312,0.03602514,-0.09386425,0.00649542,0.04223088,-0.01429677,0.02300651,0.02960226,-0.03110319,0.0214239,-0.02115435,-0.03402172,-0.00376063,-0.00246699,-0.07738937,0.03683627,-0.0267871,0.00941077,0.00097179,-0.00802732,0.01677081,-0.05060112,-0.01923238,-0.04967777,0.07694538,0.00212544,-0.00678479,-0.02071477,-0.02471596,8.3e-7,-0.08142298,0.0060884,0.03476381,-0.0325981,-0.02068665,-0.05187404,-0.09182456,-0.01193322,0.03454528],"last_embed":{"tokens":362,"hash":"kfhi8e"}}},"last_read":{"hash":"kfhi8e","at":1752940872656},"class_name":"SmartSource","outlinks":[{"title":"防火墙","target":"防火墙","line":33},{"title":"Knock","target":"Knock","line":46}],"blocks":{"#实现原理":[2,43],"#实现原理#流程可视化":[4,43],"#实现原理#流程可视化#{1}":[5,30],"#实现原理#流程可视化#{2}":[31,31],"#实现原理#流程可视化#{3}":[32,33],"#实现原理#流程可视化#{4}":[34,34],"#实现原理#流程可视化#{5}":[35,43],"#---frontmatter---":[38,42],"#相关的工具":[44,60],"#相关的工具#[[Knock]]":[46,49],"#相关的工具#[[Knock]]#{1}":[47,47],"#相关的工具#[[Knock]]#{2}":[48,49],"#相关的工具#存在的问题":[50,60],"#相关的工具#存在的问题#{1}":[51,54],"#相关的工具#存在的问题#{2}":[55,60]},"last_import":{"mtime":1719830624857,"size":1603,"at":1749024987320,"hash":"kfhi8e"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口敲门技术/端口敲门技术(分析报告).md"},