"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md","embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08590443,-0.00680313,0.01976715,-0.03167345,-0.00991172,0.05201938,0.031176,-0.04821992,-0.02702717,-0.02857957,-0.01348792,0.01960083,0.04319253,-0.10887487,0.02838521,0.02511551,0.03738071,0.03573252,0.04224574,-0.06915133,-0.04137732,0.0419645,-0.03353788,0.01148116,-0.01156237,-0.04492094,-0.05542105,-0.01308208,0.08678748,0.02957509,0.07351552,0.03329866,0.02524018,-0.01911609,-0.01459664,0.02635002,0.02176182,0.0174127,-0.00218794,0.00296882,-0.01283686,0.01707668,-0.04474539,0.01817231,-0.06857547,0.02377453,0.00829967,-0.04344629,0.02197371,0.01984487,-0.00432334,-0.0369474,0.03299122,-0.04304232,-0.01962126,0.01411328,-0.05700644,-0.08662359,-0.00152012,-0.00311631,0.00602956,0.00587956,0.02152153,-0.02953749,0.05314839,0.00088835,-0.04336061,-0.04195584,-0.00320415,-0.02181721,-0.03070161,-0.02992741,0.06257666,0.02690436,-0.00036697,0.02975574,0.00722269,-0.00637176,-0.03593872,0.04521797,-0.0371435,0.03010975,0.02270534,0.07351577,0.03288681,-0.02253642,0.04692659,-0.03958705,-0.00474813,0.0142287,-0.02600315,0.0237089,-0.00393189,-0.03131561,0.06768774,-0.03355675,-0.02414175,0.04709046,0.03728198,-0.03529143,0.0099648,-0.01272465,0.0084923,0.01723466,0.04130235,-0.06100123,-0.00709515,0.03650473,-0.05402977,-0.03891629,0.02534151,0.02118073,0.0148762,-0.00608843,-0.05505812,-0.00456316,0.00293109,-0.00357249,-0.03052904,0.03998698,0.02254428,0.02853222,-0.02585623,0.01637698,-0.0764773,-0.03728888,-0.02604901,-0.03447783,0.03725778,-0.02777324,0.00117805,0.04548163,-0.02900071,-0.01319002,-0.00602325,0.00218753,0.07357199,-0.02463489,-0.0374934,-0.03017515,0.00037912,0.04497698,-0.00883514,-0.0077162,-0.00180049,0.00876838,-0.0352785,0.0085042,0.0409294,0.04027924,0.0504517,0.00360834,0.01901575,-0.06429814,-0.05487348,-0.01190734,0.03637994,0.03503052,-0.03361312,-0.02579403,0.04821967,-0.02573156,-0.00872221,-0.00258671,-0.01878832,0.01273473,-0.01538196,0.03853394,-0.01826195,0.01589465,-0.02621496,0.05660452,-0.00204615,0.00767602,0.01729104,-0.00257668,0.0651153,-0.00038333,-0.03917966,0.0580903,0.06354623,-0.0320968,0.03932172,0.03860922,-0.0102809,0.02104159,-0.0320316,0.02652862,0.01218927,-0.00463347,0.06403136,0.02315842,0.02959926,-0.0096643,0.0239449,0.06174217,-0.02337287,-0.05230367,-0.04199409,0.01558845,0.00392084,-0.01651171,-0.02374998,-0.13890132,0.04017038,0.01613889,0.04375022,-0.06195973,0.00456729,0.0516927,-0.03504279,0.02632841,0.01778635,-0.04143286,-0.02203691,-0.00556513,-0.02081642,0.01831384,0.00436402,-0.03630424,-0.03065303,0.03033683,0.0156651,0.07076459,-0.02470001,-0.02559744,0.01670202,-0.00330266,0.01095866,0.02808251,-0.02029303,-0.00564645,0.02568814,-0.00829976,-0.0343077,-0.0310499,0.02160499,0.055775,0.03043801,0.00322619,-0.02335219,0.0189184,0.03335806,0.02457939,-0.00151609,-0.03431947,-0.06246052,0.00304465,0.05140033,0.01118381,0.03831327,0.0650356,0.00301793,0.04852004,0.00594387,-0.06416828,-0.05954247,-0.02047482,0.03578395,-0.02823924,0.05455249,0.02491527,0.02382761,0.06034889,-0.01122428,-0.02854536,0.00192974,0.01023902,0.0184359,-0.0087348,0.05772205,-0.02013107,0.04100144,0.00702976,0.0124989,-0.02004095,0.04033316,0.03684084,0.03149127,-0.01208172,-0.00306849,-0.02552122,-0.00195037,0.00183817,-0.01482875,0.13295297,0.0107558,-0.00173493,0.04197023,-0.01368261,0.00344693,-0.03632707,-0.05848137,-0.01129257,-0.02233611,0.028351,-0.00387891,0.01263846,0.0438747,-0.04776357,0.0042624,0.02519941,0.01275186,-0.00695222,0.02750807,0.02561477,0.00192264,-0.00701132,0.04591447,-0.02198983,-0.01922381,-0.05221209,-0.03664175,-0.00058984,-0.0350286,0.03243685,0.01407427,0.02777289,-0.02014245,0.00584343,-0.01147379,0.01190892,-0.01348698,-0.03724663,0.02511688,-0.03608545,0.0285601,0.04419394,0.06209993,0.06112614,-0.04041312,0.00347591,0.00580412,-0.05964128,0.0257167,0.02421827,0.01618395,-0.07991306,-0.00440727,-0.05284583,0.02989164,-0.0295558,-0.02286645,-0.0789744,0.02311409,0.03251335,0.00947791,0.01853941,-0.04680636,-0.042968,-0.02873811,0.00909065,-0.00562618,-0.01098983,0.01864745,0.01344027,-0.06707581,-0.04387132,-0.06227474,0.00798658,0.04289059,0.00770016,-0.06512216,-0.01506547,-0.04895714,-0.02696088,0.01196322,-0.02741879,0.00128275,0.03480371,0.03410126,0.01395544,-0.04297862,0.00998316,0.01191373,-0.01112732,-0.03092965,0.03015425,0.02776937,-0.02660383,-0.0236283,-0.0465971,-0.0056449,0.0384103,-0.01721899,-0.01100169,-0.03633524,0.03195225,0.04823156,-0.11027773,0.02834985,0.01621742,-0.09738465,-0.02325594,0.02609498,-0.00439649,-0.00293399,-0.02336331,-0.02295253,0.03676492,0.01842697,0.00587411,0.00776474,-0.02494455,-0.04681759,0.05627834,-0.06878335,0.02869798,0.00179218,-0.02627768,0.03003873,0.04694769,-0.01679192,-0.00276303,0.0015858,-0.01958493,0.01901948,-0.03233118,0.02236659,-0.02063938,0.05352432,-0.04263544,0.06136789,0.03574826,-0.00271573,0.01322064,-0.02087747,-0.0368472,-0.0024465,0.02115145,-0.01863368,-0.03353653,-0.02473838,-0.00151946,0.03608535,0.02589698,0.01050018,0.00016224,0.03067401,0.00224729,-0.06470011,-0.01773114,0.01261531,-0.01455193,-0.06519769,0.01485265,0.01839688,-0.03091325,-0.02114294,-0.04105747,0.01542685,-0.0521322,-0.07163849,-0.04105624,0.05153235,0.05258579,0.00554275,-0.01576649,0.03824725,0.02344962,-0.07377152,0.04483489,0.0183373,-0.02705438,0.01755653,-0.01233387,0.04518937,-0.01284471,-0.02393496,-0.03318186,-0.05785493,0.02644814,0.01156146,-0.01773172,0.06505718,-0.05337274,0.00384865,-0.03042691,-0.0321086,-0.00424445,0.0225295,0.02440587,-0.00973281,-0.01763969,-0.01000679,0.01107067,0.00229257,0.04336216,0.03115787,-0.04509379,0.01676594,0.0257079,0.01784915,0.03539039,-0.00396331,0.06852528,-0.00542161,0.04868899,0.06369758,0.02327737,0.02947479,0.03860786,-0.02205856,0.01133976,0.04310095,0.02526354,-0.02398984,-0.01705587,0.01479577,-0.0128126,-0.02458773,-0.04231618,0.04036286,0.02211362,0.00629335,-0.01748694,0.01768122,0.03474546,0.00050535,-0.00632084,0.06451602,0.04662393,-0.05051613,-0.01428086,0.03805585,-0.07806588,-0.02274989,0.04023032,0.04518588,-0.00865454,0.02634409,-0.02582369,0.05525722,-0.01746034,-0.00508488,0.0068723,0.03929126,-0.0366186,-0.05780414,0.0172576,0.02231751,0.00366684,-0.00149356,0.05837017,0.00361053,-0.03005787,0.00566785,-0.04672013,0.01761667,0.01266548,0.01332165,0.072103,0.03976624,-0.00620445,0.01259904,0.03728781,0.03925816,-0.00564604,-0.05060172,0.05429279,-0.00101231,0.00864908,0.0398315,0.01900903,-0.05222023,0.03672735,-0.03959242,-0.01865857,0.02898198,-0.01089548,-0.11431174,-0.02141231,0.03487159,0.03151727,-0.00959152,-0.01343694,-0.04563653,-0.02639474,0.0045186,-0.00596501,0.03653499,0.02025039,0.06726199,-0.0359228,-0.02966779,-0.03219088,-0.00400955,0.02092918,0.00082668,-0.00865839,0.06437066,-0.05483436,-0.00909344,0.03882753,-0.00276644,0.01871577,-0.00512482,0.03135317,0.00362282,-0.01935209,-0.00310791,-0.01076726,-0.02318654,-0.04691092,0.02132883,-0.0349649,0.04042242,-0.08702923,-0.02086056,0.04183732,-0.04879936,0.06662759,-0.0413867,-0.05505836,0.01650784,0.00417427,0.04987224,-0.00673047,0.025475,0.03034259,-0.03941299,-0.04444521,0.01747924,0.01656552,0.01472886,0.00177066,0.02550113,0.03958242,0.04666489,-0.01535963,-0.04497988,-0.01764532,-0.01483906,-0.00433127,0.04888844,0.02129884,-0.09911643,0.00756954,-0.01089318,-0.00968285,0.08327635,-0.07431071,0.03534872,0.03553287,-0.01510676,0.01235254,-0.05874953,-0.09496178,-0.04188482,0.05354246,0.02440233,0.01478811,0.03674571,-0.02686301,0.01875567,0.02490859,-0.00131724,0.00646176,0.00803474,0.09223153,-0.02691452,-0.01952058,-0.00494299,0.01700663,-0.04358216,-0.01055176,0.03849217,-0.02203316,-0.03416022,-0.06737603,0.0543176,-0.04675319,0.01028131,-0.05119257,0.00309312,-0.00595245,-0.00414952,0.0348798,-0.02678034,-0.04311019,-0.01668713,-0.05453486,-0.00203386,0.01350043,0.04732339,-0.0595169,-0.02674578,-0.02751101,-0.07907905,-0.04567967,-0.05030759,0.01778796,0.00619755,0.00751591,0.03076988,-0.02328719,-0.00549679,0.01356262,-0.02092345,0.00016905,-0.01217946,-0.01737471,0.01378321,0.03078313,-0.05251591,0.01356293,-0.01634223,0.00880675,0.0564303,-0.00118986,-0.02156938,-0.0664907,0.02443626,-0.10528222,0.02765264,-0.00468274,0.00924726,0.02059406,-0.09516468,0.00764687,0.01573291,0.04430909,0.11674554,0.05772138,-0.08425044,0.01282897,0.00133291,-0.00368528,-0.02045278,0.01923319,-0.04197538,-0.07549578,-0.02424588,0.04585432,-0.00452308,0.00328461,0.00345472,-0.0211221,0.01852958,-0.04639996,0.041583,-0.04014487,0.02170255,-0.06128997,-0.00529185,0.00849472,0.00581803,-0.01225756,0.06294911,0.0128591,-0.07562893,-0.00544685,-0.01122872,0.06284288,0.04019428,0.03457275,-0.00827649,-0.07401089,7.5e-7,-0.03259349,-0.01589772,0.03025767,-0.03074128,-0.03358672,-0.00542141,-0.03388653,0.01478123,-0.00383678],"last_embed":{"hash":"a3fvwg","tokens":1200}}},"last_read":{"hash":"a3fvwg","at":1752940659207},"class_name":"SmartSource","last_import":{"mtime":1735626285365,"size":5180,"at":1749002741368,"hash":"a3fvwg"},"blocks":{"#---frontmatter---":[1,4],"#身份验证协议":[5,7],"#身份验证协议#{1}":[7,7],"#简介":[8,29],"#简介#{1}":[10,17],"#简介#{2}":[18,29],"#简介#{3}":[20,29],"#验证流程图":[30,67],"#验证流程图#{1}":[31,50],"#验证流程图#{2}":[51,52],"#验证流程图#{3}":[53,55],"#验证流程图#{4}":[56,56],"#验证流程图#{5}":[57,59],"#验证流程图#{6}":[60,60],"#验证流程图#{7}":[61,65],"#验证流程图#{8}":[66,67],"#Kerberos 认证的基本原理":[68,92],"#Kerberos 认证的基本原理#{1}":[70,79],"#Kerberos 认证的基本原理#{2}":[80,90],"#Kerberos 认证的基本原理#{3}":[91,92],"#Kerberos 的工作流程":[93,133],"#Kerberos 的工作流程#{1}":[95,113],"#Kerberos 的工作流程#{2}":[114,118],"#Kerberos 的工作流程#{3}":[119,125],"#Kerberos 的工作流程#{4}":[126,131],"#Kerberos 的工作流程#{5}":[132,133],"#攻击的逻辑":[134,145],"#攻击的逻辑#{1}":[135,145]},"outlinks":[{"title":"Windows Active Directory","target":"微软活动目录","line":15},{"title":"中间人攻击","target":"中间人攻击","line":19},{"title":"域控制器","target":"域控制器","line":57},{"title":"NTLM","target":"NTLM","line":61},{"title":"域控制器","target":"域控制器","line":73}],"metadata":{"cssclasses":["editor-full"]},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md","last_embed":{"hash":"a3fvwg","at":1752940659207}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06423741,0.00573261,0.00404459,-0.07604551,-0.01518549,-0.05809299,0.01968065,0.04534198,-0.00577037,-0.00840584,-0.01917129,-0.08351358,-0.00779442,0.04432294,0.06099791,0.02694504,0.02540754,-0.01024875,-0.03067927,0.0226512,0.07342084,-0.0834242,-0.01317375,-0.04225095,-0.01792133,0.00601463,-0.00858077,-0.02296299,-0.01758607,-0.14222661,-0.00063986,-0.01965292,0.03155555,0.09907249,-0.02040212,-0.01798114,-0.01473424,0.06139485,-0.01796605,0.02336176,-0.00376307,-0.02796252,0.01510786,-0.02104583,0.03349802,-0.05652781,-0.035837,-0.02767511,-0.02372452,-0.02822607,-0.02980969,-0.04140542,-0.04552953,0.01426321,-0.03994443,0.03039641,-0.00083472,0.03135328,0.05599168,-0.01806339,0.04861532,0.01729322,-0.22826847,0.04869394,-0.01279942,-0.00977298,-0.00169042,-0.01788131,-0.03741169,0.02398376,-0.06895731,0.02436892,0.00590826,0.0110625,0.08630552,-0.01862479,0.00675524,-0.01719671,-0.02712805,-0.03726442,-0.0594129,0.03804552,-0.01571041,-0.02383651,-0.05965446,0.01378032,-0.03723254,-0.03627309,0.02027545,-0.00534257,-0.03053175,-0.03876108,0.00454839,-0.00784118,-0.03771506,-0.00590537,0.05435291,0.06675991,-0.08307963,0.11601685,-0.01512765,0.00371966,-0.03735309,-0.02054016,-0.01114648,-0.06511503,-0.01317312,-0.06314108,-0.01706743,0.0269027,-0.06913852,-0.02349312,0.00044133,-0.01320927,-0.01503274,0.0734788,-0.00247276,0.0144835,0.00094532,0.02125486,-0.03031409,0.02988215,0.07121703,-0.05240788,0.02212824,-0.03194115,0.05408484,0.09054528,-0.00264309,0.02147558,0.03705839,-0.01911932,-0.06712393,0.01360451,-0.00766724,-0.0082504,-0.02917326,-0.00509434,-0.03876285,-0.03304001,-0.00184534,-0.05291488,0.0407853,-0.06560759,-0.03404769,0.0592332,-0.02241326,0.04032591,0.00432088,-0.01229105,0.00174029,0.05482009,0.00122599,-0.04465815,-0.02265811,-0.01103074,0.07042057,0.14007762,-0.03358205,0.0297899,0.05736566,0.02862326,-0.02986401,0.18571678,0.02155821,-0.08440642,0.00801555,0.02931607,0.01958429,-0.02888189,0.03366828,0.01133632,-0.00264316,0.00955328,0.09682799,-0.00830254,-0.07370611,0.00469543,0.02359888,-0.00695954,0.04511417,-0.0190488,-0.07944556,0.02832283,0.02830048,-0.0933896,0.01550023,-0.03539496,0.02090387,-0.07702142,-0.07090941,0.01087869,-0.01597986,-0.02101793,0.00735655,-0.08876963,0.04076881,-0.01454924,0.04629853,-0.04727354,0.07571176,0.01594135,-0.05832025,0.00748638,-0.0023438,0.02027305,-0.00468545,-0.04158215,0.06346999,0.00487528,0.02515227,0.01276406,-0.01695874,0.04693737,-0.0122158,0.04091674,0.0261867,0.06511401,0.015807,0.03608511,0.03993773,0.04339939,-0.03606058,-0.24687994,-0.02272235,0.00345436,-0.00295993,-0.04865637,-0.03284704,0.05263984,-0.03061885,0.05655468,0.03229767,0.09741132,0.02979494,-0.02582953,-0.02653099,0.00555758,0.0112044,0.00182586,-0.02432126,-0.02009593,0.00229196,-0.01636863,0.03614561,-0.01004223,-0.0174892,0.03324258,0.005985,0.15382005,0.01222799,0.04662066,0.04047413,-0.00328954,0.03988325,-0.01651904,-0.12423466,0.04174878,0.06050206,-0.02076732,-0.02367087,0.00270802,-0.01888123,-0.00287261,0.0356524,-0.0627102,-0.11688124,-0.0616165,-0.05290493,-0.03697826,0.04621368,-0.01497288,0.04704184,-0.02088673,0.01557448,0.0059955,0.00156097,0.03313294,-0.02324692,-0.0533215,-0.07197195,-0.04172298,0.03261221,0.02776525,-0.00486337,-0.00120897,0.05445253,0.04404726,-0.05148697,-0.00416923,0.03146206,0.02762511,0.00222009,-0.03622947,0.11094075,-0.01296009,-0.01624299,0.1030834,0.00284955,-0.0081051,-0.075292,-0.03187669,0.0128537,0.06941466,-0.02951273,0.05983977,-0.00689892,0.02748064,0.0319346,0.03511423,0.05971389,0.00201282,-0.070824,-0.06031121,0.00033659,-0.05737573,0.03903017,0.05070598,0.05714455,-0.29039061,0.0376934,-0.04017347,0.04104374,0.02083619,0.029204,0.05787002,0.01518007,-0.04987934,-0.04315066,-0.03063233,0.05186787,-0.0162572,-0.04396348,-0.01450395,-0.02451533,0.05287114,-0.00413608,0.08405139,-0.05449447,0.00616632,0.07013379,0.21566293,0.00054969,0.04184705,0.0143868,0.05604535,0.07434296,0.09166434,0.0569289,0.00385157,-0.00339296,0.04468962,-0.04177286,0.00469173,0.0020251,-0.03686363,-0.04845347,0.02654716,0.00966894,-0.03878616,0.06689837,-0.05118637,0.0052842,0.12622978,0.04910906,-0.00188199,-0.07776318,-0.01892572,0.03994127,0.00171448,-0.00884556,0.00918493,-0.00813157,0.03040603,0.0803915,0.02500499,-0.0022411,-0.09343515,-0.02167659,-0.00399969,-0.02029011,0.05791729,0.06480408,0.05335616],"last_embed":{"hash":"badb92da0e2f1119bfbf5e1871f20109e027db6cc32f5d3ab8853628f482cd7a","tokens":373}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.11127152,-0.00578102,0.00823156,-0.031827,-0.02031638,0.03628453,0.0063459,-0.07064686,0.01229248,-0.02600484,-0.01199611,0.02793507,0.02901034,-0.10693432,0.0281015,0.04760331,0.00770981,0.01011337,0.03133558,-0.06394725,-0.00246589,0.0480167,-0.05355786,0.00035283,-0.04024231,-0.03263237,-0.04489738,-0.02461065,0.03345818,0.04680436,0.09211561,0.00599871,0.0026684,-0.01318063,-0.0207465,0.02571641,0.0128466,0.06457133,-0.02902197,-0.01082567,-0.01240214,0.01709278,-0.02674309,0.03229506,-0.09101956,0.04207211,0.02103027,-0.03144339,0.01283908,0.0136516,-0.00996581,-0.03433079,0.02435523,-0.02753547,-0.02561108,0.02065362,-0.0238911,-0.06812987,-0.01343499,0.0198736,0.00691554,-0.01033341,0.03125759,-0.00043356,0.03541199,-0.02113824,-0.0384144,-0.03228163,0.00676771,-0.02870024,-0.0476045,-0.03902001,0.07379346,0.00335453,-0.02406604,0.02530763,-0.01008522,-0.00443671,-0.02390588,0.04011058,-0.01552759,0.02675998,-0.00895001,0.03893112,0.03235486,-0.03090887,0.03171658,-0.04220867,-0.0210966,0.01025638,0.02858713,0.0014234,0.01261722,-0.04498709,0.06997994,-0.03311069,0.00135099,0.04336116,0.03593718,-0.03666741,-0.00881096,-0.03107812,0.00225093,0.02334317,0.0425664,-0.03232851,0.00404358,0.04429894,-0.04033974,-0.04230048,0.05488029,0.00617992,0.02371993,-0.02305282,-0.05431417,0.00697098,0.03475444,0.02825029,-0.02756372,0.030566,0.04088594,0.03185623,-0.01965276,0.03328759,-0.07595123,-0.0289236,-0.01809737,-0.04049711,0.01612129,-0.00638737,-0.00446003,0.01749959,-0.01906157,-0.00562585,-0.01182101,-0.01237899,0.09058957,0.01372982,-0.04205155,-0.05244966,-0.01439849,0.02432755,0.00024289,0.01437947,0.02387512,0.00604145,-0.05692695,0.01188675,0.06938464,0.00431234,0.06773559,0.00538293,-0.01438567,-0.05431436,-0.07545421,-0.02538382,0.03397346,0.00602399,-0.05431171,-0.00013415,0.02889347,-0.02306539,-0.03184321,-0.01531703,-0.01823613,0.01530272,-0.03553758,0.04160844,-0.00849771,0.02054448,-0.04641973,0.07174094,-0.00278056,-0.01320017,-0.01822453,0.00640475,0.06195351,-0.01103693,-0.04860588,0.02556419,0.04624894,-0.06288538,0.02153336,0.02755536,0.01606102,0.03077434,-0.02402703,0.01295399,-0.00055799,0.0189574,0.05942534,-0.00609695,0.02436754,0.01864639,0.02246866,0.05527437,-0.01219744,-0.03644319,-0.01900754,0.00508646,-0.0103292,-0.00199007,-0.00869546,-0.12539242,0.03004226,0.02042915,0.05158213,-0.06620795,0.00440992,0.06459997,-0.00131152,0.03176225,0.04486964,-0.01029552,-0.00831517,0.00943947,-0.01421345,0.02800849,0.03297092,-0.0442475,-0.02231123,0.01878548,0.05246974,0.06607129,-0.01173303,-0.02679923,0.01569048,-0.01355686,0.00160416,0.03591494,-0.02419907,0.00764378,0.01778262,0.00504936,-0.03603047,-0.04064234,0.02233756,0.04610291,0.0169345,0.01685387,-0.01237857,-0.01465304,0.03165756,0.00150313,-0.00790803,-0.0349872,-0.03219241,-0.0021,0.03667647,0.00080774,0.02981478,0.04815089,-0.01530688,0.05939853,0.00134601,-0.05841522,-0.04404162,-0.0035325,0.04237214,-0.00618021,0.04099492,0.04804974,0.03374172,0.07372191,0.00099919,-0.04406361,-0.00778869,0.02881496,0.05778879,-0.00729656,0.03924242,-0.00407723,0.00255857,-0.00030773,-0.02878671,-0.0045901,0.03080037,0.05081892,0.02816154,0.0122274,0.03665848,0.00378757,-0.02103301,0.00695428,0.0045097,0.12298634,0.02157286,0.02660588,0.04716815,-0.0322886,0.01074245,-0.00597447,-0.03351742,-0.04292877,-0.00249168,0.02866164,-0.00231855,0.01498471,0.03184844,-0.03415048,-0.02379964,0.03196827,0.01480965,0.03971142,0.04352205,0.03990448,0.00092031,-0.01919871,0.043501,-0.01421922,0.00332785,-0.0602958,-0.0535948,0.00676876,-0.02177924,0.01997301,0.0268394,0.04018481,-0.01714288,0.00814937,0.01411462,0.01761367,-0.03991209,-0.01818925,0.00479191,-0.06397486,0.00239039,0.06175127,0.05014479,0.08624837,-0.02807406,0.01094244,0.00936308,-0.04046683,0.00384427,0.01186536,0.04096387,-0.0813913,-0.02504377,-0.05872764,-0.00999616,0.00439766,0.00056048,-0.04873968,0.03882115,0.04430807,-0.00847006,0.02280798,-0.05400058,-0.04496396,-0.0130442,0.03354293,-0.03526696,0.01037443,0.0296673,0.00774217,-0.06909732,-0.03532824,-0.04918882,-0.0037353,0.03346109,-0.00114787,-0.05864565,-0.014771,-0.03281477,-0.03533479,0.00061183,-0.00753224,0.01845115,0.01564336,0.02205641,0.0069037,-0.02878709,-0.01861851,-0.00150716,-0.00780334,-0.04880123,0.03736454,0.01319466,-0.00556434,-0.01923871,-0.02674828,0.00498879,0.04128687,-0.01446019,0.00245608,-0.04156661,0.007412,0.05584965,-0.1062154,-0.00148288,-0.01258776,-0.09790871,-0.00168288,0.02125056,-0.0064492,0.01685316,0.01387581,-0.03327548,0.03855358,0.03646484,0.01978771,0.00364313,-0.01481078,-0.04849831,0.02641213,-0.05115198,0.03886581,-0.04071954,0.00999357,0.03609227,0.03731727,-0.04990609,0.00173316,0.00049395,-0.02880628,-0.00115153,-0.05036636,0.0031266,-0.01435671,0.0137094,-0.06687238,0.04529548,0.01558513,0.00115121,0.00160699,-0.03271996,-0.0330057,-0.01261363,0.00586931,-0.03544099,-0.03190377,-0.03219187,-0.01508198,0.04297328,0.00817824,0.01264337,-0.00257199,0.01610126,-0.00282739,-0.06342163,-0.01124259,-0.01206829,-0.02447888,-0.06351384,0.02503416,0.02326713,-0.00697561,-0.02688297,-0.05831609,-0.00057987,-0.03322451,-0.07102293,-0.05243798,0.05108853,0.03383114,0.01839297,-0.02186363,0.03231981,0.01927927,-0.07442993,0.05623195,0.04736729,-0.01530187,0.03299298,-0.0554333,0.02590783,-0.01636764,0.00507631,-0.05521171,-0.03568994,0.04199598,-0.00388488,-0.00411218,0.07663935,-0.03682475,0.00692663,-0.01934481,-0.01248697,-0.00501227,0.00235721,-0.00963093,-0.02564578,-0.03882075,-0.01696951,0.01042722,-0.00536627,0.02514477,0.03037759,-0.04037746,0.04306549,0.04383999,0.01318692,0.00493533,0.0215141,0.06699783,-0.00489747,0.05562323,0.03185538,0.03023967,0.03613792,0.03034014,-0.03527635,0.03704122,0.03457949,0.04315582,-0.0373214,-0.01824928,0.03309561,-0.02028249,-0.03869968,-0.05014989,0.0386254,0.01046181,0.00755521,0.03674684,0.02414091,0.03769062,-0.01525766,-0.00716533,0.0468165,0.02921045,-0.07194679,-0.00729451,0.05953603,-0.05009919,-0.03541938,0.03105931,0.06224388,0.02929098,0.05497205,-0.02163991,0.0706109,0.01006906,-0.01398705,0.01349145,0.01992057,-0.02844049,-0.00856821,0.0599806,0.01563252,0.01491992,-0.00272362,0.03565671,0.00634871,-0.04928698,0.01794823,-0.04874792,-0.0125616,0.02016679,0.00983589,0.03682794,0.05113728,-0.0236053,0.00735132,0.02935512,0.03683339,-0.04619056,-0.04681829,0.04588369,-0.009998,-0.02112679,0.04155231,0.00957177,-0.06083668,0.03505615,-0.03579364,-0.00698701,-0.02384938,-0.01374106,-0.12576772,-0.02992041,0.03493709,0.05705573,-0.01476731,-0.0090464,-0.03247826,-0.03204081,-0.00286312,0.00054596,0.04568879,-0.00501713,0.05796314,-0.049486,-0.03905996,-0.04084916,0.01877026,0.00788799,0.01066013,0.00701889,0.07882936,-0.0623311,0.02337143,-0.01753726,0.0028334,0.03881814,-0.01190827,0.04027221,0.00047333,-0.0120322,-0.00013329,0.00124891,0.00345577,-0.0340149,0.03970779,-0.0413448,0.0338167,-0.10078544,-0.03281306,0.05607232,-0.03699127,0.08003689,-0.04852606,-0.05163752,0.00641673,0.01324107,0.05560619,-0.01549412,0.04729489,0.02025834,-0.0391157,-0.03072173,0.01526508,0.02215671,0.04001652,-0.02250634,0.0224665,0.0132541,0.04810261,0.01369721,-0.02446681,0.00179767,-0.01609573,0.00864583,0.05886291,0.02527516,-0.07102453,-0.01770835,-0.01574958,-0.02670829,0.06976649,-0.04727794,0.02538788,0.03468623,-0.04150478,-0.00224435,-0.03531785,-0.06780523,-0.03201499,0.03428028,0.01162727,0.03735201,0.03066159,-0.00154843,0.02753069,-0.00511985,0.00130685,0.00419544,0.02025948,0.08275523,-0.01649149,-0.0101962,-0.00386018,0.02944612,-0.05290002,-0.02232358,0.03121402,-0.00260446,0.00192938,-0.07148339,0.03823437,-0.04822618,0.02151373,-0.04630383,0.00864041,-0.01350455,0.00498921,0.04120493,-0.02831325,-0.05030966,0.00151802,-0.06949462,-0.01029789,-0.0260142,0.05384133,-0.02620071,-0.02333849,-0.03219728,-0.0790316,-0.0529849,-0.05994304,0.01038906,-0.01336709,-0.00053291,0.04247233,-0.03018298,-0.00306674,0.00618152,-0.02047469,0.00303305,0.00091763,-0.02465094,0.00805381,0.01469127,-0.06590959,-0.0058792,-0.02867898,0.02049962,0.07170779,-0.00228115,-0.04226909,-0.0791747,0.01386196,-0.05666075,-0.0051796,0.01512434,0.04390934,0.02144249,-0.06340367,0.01469207,0.0169019,0.043104,0.13455118,0.05039222,-0.07632134,-0.0029772,-0.00226014,0.00409697,-0.01141507,-0.00220783,-0.06762103,-0.08549772,-0.01457095,0.0480666,0.0200179,0.01319289,-0.0092781,-0.02821223,0.02730937,-0.03620635,0.07894141,-0.04373766,0.01682208,-0.02941346,0.01299841,0.04087855,-0.00309171,-0.01774458,0.06057204,0.0207509,-0.05593747,-0.01964794,-0.02304953,0.02406245,0.03925628,0.02412137,-0.02276415,-0.07506821,7.5e-7,-0.05941677,-0.03425163,0.01061278,-0.01719079,-0.00727341,-0.03649799,-0.01902064,0.01607507,0.00364111],"last_embed":{"hash":"ft3crl","tokens":457}}},"text":null,"length":0,"last_read":{"hash":"ft3crl","at":1748397845892},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图","lines":[30,67],"size":1011,"outlinks":[{"title":"域控制器","target":"域控制器","line":28},{"title":"NTLM","target":"NTLM","line":32}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04182538,0.00660326,-0.00782659,-0.07005209,-0.04453841,-0.04558567,0.04000385,0.03767452,0.003931,-0.01472187,-0.01084458,-0.09460337,0.00963106,0.05804186,0.04325342,-0.00260888,0.02728359,-0.00948896,-0.02811745,0.0199,0.0761805,-0.05340619,-0.02097679,-0.05167117,-0.00249971,0.02437649,0.00666492,-0.01400093,-0.00364599,-0.13855796,0.00293334,-0.01653081,0.00688358,0.086471,-0.03319578,-0.01996997,-0.0081841,0.06447851,-0.02619239,0.0197573,-0.00265706,-0.00474006,-0.0106576,-0.05443904,0.03122531,-0.08902667,-0.01655723,-0.00536781,-0.05345694,-0.02881365,-0.04254373,-0.0365888,-0.01147885,0.04060134,-0.04424755,0.02116398,-0.00885768,0.00804548,0.06921057,-0.02345864,0.04362449,0.02879779,-0.22070234,0.04763444,-0.02863349,-0.01545641,-0.00855955,0.04329331,-0.01379469,0.01277473,-0.04786488,0.00182302,0.02720135,0.02845215,0.08002818,0.01101833,0.02608234,-0.0111884,-0.03726666,-0.0514889,-0.07245929,-0.00060784,-0.00657503,-0.01421073,-0.03343503,0.00286544,-0.01198376,-0.03636334,0.00531742,0.01525613,-0.02760216,-0.03887121,-0.01496663,-0.00416561,-0.04421867,-0.01840463,0.05756792,0.04999407,-0.06958861,0.12123944,-0.02097874,0.04198395,-0.04068707,-0.00264126,0.02137127,-0.08465748,-0.00522419,-0.05477844,-0.02453137,0.00908981,-0.07031561,-0.03481453,0.01605656,0.01965909,-0.03773503,0.07675385,-0.00043213,0.04403239,-0.00591091,0.00003512,-0.03209972,0.01635213,0.04419852,-0.04214764,-0.02031893,-0.00948418,0.0543133,0.10613901,0.00393697,0.01927682,0.05340591,-0.02758007,-0.09877137,0.01379932,-0.02615148,-0.03055704,-0.03734864,0.000473,-0.03908858,-0.03531567,0.01248452,-0.06402219,0.03602629,-0.07509013,-0.03823501,0.05371992,-0.04300038,0.05090044,-0.0013411,-0.01835886,-0.01287608,0.0651425,-0.01167077,-0.03888644,-0.00969233,0.00733062,0.05867083,0.13682556,-0.04013733,0.02655752,0.05128958,0.02453909,-0.01157481,0.18289877,0.03742322,-0.09952886,0.01446307,0.02430666,-0.00115427,-0.07409538,0.00291944,0.00078462,-0.00439277,0.00485269,0.08476187,-0.01573632,-0.03530675,-0.00590292,0.00536836,-0.01625154,0.05323284,-0.02626133,-0.09945479,0.02555598,0.01762832,-0.0695843,0.00861906,-0.02865223,-0.00923988,-0.07084377,-0.06244627,0.01966411,0.01648849,-0.0273719,0.02346981,-0.1030249,0.02226952,-0.03071858,0.055312,-0.02747986,0.08642421,0.00565509,-0.0568727,0.00153179,0.00989045,0.03478329,0.00140575,-0.06453668,0.05034262,0.00249997,0.03391401,0.01267073,-0.00810294,0.05231304,-0.00681132,0.06714527,0.0307884,0.0542208,0.0199325,0.03603022,0.02359025,0.03511648,-0.05004727,-0.24076667,-0.00543135,0.0112779,-0.02484321,-0.04565344,-0.00930449,0.01049765,-0.04242114,0.06401724,0.04174919,0.10771616,0.02937543,-0.02739179,-0.00043748,0.00612119,-0.02573358,-0.01846826,0.00600055,0.02116149,0.00534727,-0.01575575,0.03541326,0.01310658,-0.01314596,0.01032491,-0.00641372,0.14733352,0.03304359,0.02403863,-0.00214162,0.0127466,0.03265832,-0.01410397,-0.12402968,0.04769202,0.0493172,-0.04040289,-0.00874337,0.0237066,-0.03736683,-0.01594621,0.03997795,-0.06976349,-0.09999887,-0.03621802,-0.05516187,-0.01734459,0.05825595,0.00689424,0.02871932,-0.00594381,0.01997661,0.021928,-0.03268484,0.0421339,-0.04503407,-0.06338667,-0.07212176,-0.04947548,0.04161359,0.01869508,0.00415797,0.00519897,0.05974271,0.01817996,-0.01469436,-0.0257424,0.0183047,0.0046071,-0.00273764,-0.04537185,0.1160403,-0.01419957,-0.03035119,0.09189976,0.00638266,-0.00433746,-0.09104514,-0.02248936,0.0232276,0.07497316,-0.00090163,0.02297833,0.00218787,-0.0053646,0.06173554,0.04256489,0.05468675,0.0061837,-0.0508904,-0.04402085,-0.02057991,-0.02423288,0.07811704,0.05066382,0.02707905,-0.28893107,0.04012206,-0.0372708,0.03687581,0.0332918,0.01406522,0.05889509,0.02122987,-0.04949889,-0.05195065,-0.02978042,0.04517198,0.01251467,-0.0452701,0.01810769,0.01924803,0.05704629,-0.04152903,0.0503852,-0.05159831,-0.01387171,0.06556457,0.20948805,0.01174575,0.0443211,0.02632192,0.04198539,0.06333263,0.09959404,0.04962383,-0.00306355,-0.02720107,0.06597479,-0.01642538,0.00067825,0.00125639,-0.02397692,-0.06355759,-0.00059925,-0.00607766,-0.05035217,0.03984312,-0.06533725,0.03003111,0.12019937,0.03440014,-0.01365522,-0.07363891,-0.01260892,0.0561283,0.00746513,-0.00274868,0.00587829,0.00596776,0.02855986,0.10328164,0.04221558,0.01898645,-0.10643273,0.0191856,-0.02056573,-0.01515927,0.04474913,0.07522865,0.06442819],"last_embed":{"hash":"f076166b68523f37211e691dd7392f7ce9e2f3eb00b6436b5a1477ebd3970389","tokens":454}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08946756,-0.01255037,0.00167829,-0.04535961,-0.00732064,0.04363302,-0.00948648,-0.07892469,0.00249791,-0.03992259,-0.0152851,0.03235367,0.02737399,-0.13029827,0.02134843,0.01161003,-0.00786801,0.00022641,0.02941476,-0.03314759,-0.02309748,0.05192037,-0.0394338,0.01154762,-0.03062949,-0.02322224,-0.05430131,-0.01718647,0.05534412,0.03253885,0.08954396,0.00841515,0.02032196,-0.00515135,-0.04275783,0.00712978,0.02455357,0.06452309,-0.01934692,-0.02644434,-0.0368217,0.01966261,-0.03921096,0.02972174,-0.07614874,0.03883968,-0.00453368,-0.05321651,0.02471505,-0.00080798,-0.00284301,0.0106115,0.01762899,-0.03371691,-0.0337638,0.00470268,-0.02230138,-0.05201908,-0.01980121,-0.00780892,0.01731004,-0.01941627,0.01110617,0.01276552,0.01663246,-0.03061394,-0.02680574,-0.02944149,-0.01243765,-0.02218976,-0.0500939,-0.04701521,0.05171146,0.02833763,0.00144867,0.02018983,0.00618362,-0.01010147,-0.04269112,0.06041323,-0.00303311,0.044872,-0.0134889,0.05573199,0.02258995,-0.03154961,0.02419544,-0.04108297,-0.03391592,0.00257257,0.01538904,0.00132237,-0.01063912,-0.04018012,0.07706277,-0.02871893,-0.00874821,0.03747149,0.02692479,-0.04939294,0.01238741,-0.03105198,0.00485306,0.03307945,0.02747311,-0.02240157,-0.00267706,0.04091936,-0.03153474,-0.01377825,0.0485858,0.01287071,0.03468265,0.00420955,-0.04587524,-0.01391109,0.0272141,0.01793446,-0.05259471,0.01259937,0.03722442,0.02765,-0.0310726,0.04174089,-0.09373203,-0.04238152,-0.02111572,-0.03523161,0.01344975,-0.01539088,-0.01079492,0.03262647,-0.02962696,0.00038704,-0.00723574,-0.01011223,0.07148235,-0.00583508,-0.02802373,-0.04796829,-0.01464882,0.0105012,-0.01589379,0.01662888,0.03660586,0.00545338,-0.03159344,0.03655875,0.03528119,0.01694578,0.07344848,0.02808533,-0.03363343,-0.05296174,-0.09165151,-0.01646706,0.03575583,0.0250078,-0.05081399,-0.00862315,0.0165337,-0.0300957,-0.04224909,-0.01415285,-0.01131344,0.02077531,-0.04085711,0.05367965,-0.00786985,0.01172035,-0.00285437,0.05528544,0.00861194,-0.02667478,0.01043428,-0.00171551,0.04286822,0.00062366,-0.05678836,0.03946817,0.05603354,-0.05302231,0.03158038,0.03538234,0.00575748,0.02603389,-0.06098373,0.00774789,-0.02705382,0.00896529,0.05120197,0.00012064,0.03268017,0.01030731,0.02797867,0.07654011,-0.00097682,-0.02191872,-0.01948875,0.02681122,-0.01071716,-0.0076883,-0.00173127,-0.11932494,0.02851846,0.00403267,0.06119156,-0.05705568,0.01696883,0.03917833,-0.02783036,0.01318201,0.03678359,-0.00000874,-0.01309633,0.02924601,-0.01855612,0.02847336,0.02482834,-0.02412585,-0.02928638,0.02012064,0.01225948,0.06666519,-0.02699099,-0.0262028,0.01626564,-0.02466656,0.02681782,0.03187342,-0.01955041,0.00138494,0.02819726,-0.00366289,-0.02905473,-0.03277621,0.01427841,0.03619726,0.02090133,0.0264789,-0.00936325,-0.00717645,0.02609629,-0.00592932,-0.01544911,-0.04050303,-0.05994051,0.02946382,0.02449726,0.00100818,0.02887209,0.06926547,-0.01492514,0.05393219,0.00258531,-0.05477237,-0.03659104,-0.00900928,0.02531424,-0.02418068,0.03634728,0.01920381,0.04054597,0.06930665,-0.00004363,-0.0169427,0.0037315,0.0474542,0.04094704,0.00947589,0.01196504,-0.00142844,0.01405941,0.00291716,-0.02218887,0.01974163,0.02629532,0.07419279,0.0435054,0.02671128,0.02672728,0.01403789,-0.02759519,0.01803441,-0.00939635,0.12270366,0.0090725,0.01712638,0.05153296,-0.0277077,-0.01617778,-0.01945208,-0.03169292,-0.03393548,-0.00366353,0.00518068,-0.01391515,0.02441264,0.02930847,-0.05649671,-0.03839342,0.02217148,0.01505728,0.02389702,0.05471031,0.05590012,0.001616,-0.02228391,0.03361175,-0.01378694,0.01863512,-0.07244157,-0.0372747,0.0097848,-0.03209344,0.00360185,0.02901926,0.04914159,-0.03382676,0.02734639,0.02937328,0.02868943,-0.0458452,-0.03511619,0.02239322,-0.06157242,0.01999471,0.04848988,0.02209672,0.07324252,-0.02899964,-0.0129258,0.0187156,-0.0366563,-0.00652667,0.00457325,0.01432174,-0.08985382,-0.02370601,-0.05259493,0.01169805,-0.01564624,-0.00205409,-0.05350224,0.02049754,0.0733005,-0.00782936,0.02845192,-0.05852966,-0.04656781,-0.01890978,0.03720755,-0.0216171,0.03331058,0.02168513,-0.0083171,-0.05210522,-0.04275303,-0.05382646,0.00718789,0.01038193,-0.04351029,-0.06377819,-0.03198545,-0.02509715,-0.03391776,-0.00306072,0.01971095,0.02773127,-0.00258067,0.02583709,-0.00823354,-0.0215021,-0.02295924,-0.00603165,-0.00831461,-0.04471598,0.05944905,0.03743187,0.00051269,-0.01495738,-0.04200597,0.00144918,0.02888094,0.00579085,0.00993367,-0.04736165,0.03049403,0.01540512,-0.11931793,-0.00140635,0.01598669,-0.09992594,-0.00396919,0.02332671,-0.01380379,0.0031369,0.00854925,-0.02697059,0.02251383,0.05144994,-0.00629,0.01286949,-0.03780182,-0.0436918,0.01730688,-0.0461305,0.01545985,-0.03730292,0.01836479,0.04460343,0.04254549,-0.04516511,-0.02380556,0.01465918,-0.03115464,0.01377876,-0.0462023,0.01585651,-0.02898067,0.00224646,-0.0448296,0.07170945,0.02800856,-0.00362696,0.01497262,-0.04459011,-0.02573671,-0.02502774,-0.01104371,-0.02407335,-0.01672467,-0.03197644,-0.014266,0.05378404,0.02655084,0.01886701,0.00275195,0.03722608,-0.0073087,-0.06416759,-0.00226117,0.01503108,-0.00568395,-0.06136857,0.02958136,0.01223695,0.01005415,-0.03267872,-0.0458513,-0.00785726,-0.02675537,-0.10231183,-0.06565103,0.05766757,0.02927298,0.02108403,-0.02048234,0.00735383,0.00324548,-0.08462646,0.04690488,0.05111677,-0.00950914,0.01417167,-0.0563057,0.02015759,-0.01254402,-0.00920662,-0.04680735,-0.07883358,0.05262485,0.00575044,-0.00566151,0.0834057,-0.03362965,-0.00646735,-0.01232627,-0.02422034,-0.02753543,0.01441088,0.02174519,-0.01235104,-0.04360424,-0.00926863,0.00886457,0.0034422,0.0315211,0.04300378,-0.03741292,0.05023561,0.04475205,0.00050361,-0.01124901,0.01488487,0.05859788,-0.00768089,0.05546167,0.04015288,0.02984651,0.01577601,0.04346868,-0.00621527,0.02652516,0.03846885,0.03587943,-0.04062866,-0.00327099,0.02332126,0.01025653,-0.04745258,-0.05473453,0.01641604,0.03292937,0.00447971,0.0333835,0.0582484,0.00193617,-0.01006899,-0.01630553,0.04068075,0.02738634,-0.01553699,0.00666124,0.05254528,-0.0293863,-0.03300705,0.02608473,0.03901917,0.00071133,0.06826513,-0.03630358,0.04908368,-0.00116957,-0.01593962,0.01704111,0.00701125,-0.03376562,-0.02929772,0.04487913,0.03635396,0.01271393,-0.01065574,0.02783298,0.0053258,-0.07322247,0.01541593,-0.03381961,0.00055605,0.00691448,0.01382423,0.03102719,0.03502979,-0.01776363,0.01397081,0.03685356,0.04925897,-0.05750693,-0.04127783,0.06935026,-0.00365748,-0.00847207,0.05664792,0.00299611,-0.05382588,0.03505262,-0.03824627,-0.02162768,-0.01435375,0.02947913,-0.10845967,-0.02317417,0.03731847,0.04449356,-0.00949974,-0.00996817,-0.02226418,-0.04366327,-0.00404387,-0.01188478,0.02676657,-0.00737818,0.05187606,-0.03086977,-0.05494248,-0.06151938,0.02225031,0.02332143,0.0137097,-0.02216004,0.07740123,-0.05175194,0.02099191,-0.0172042,-0.02170186,0.05343807,0.00245588,0.02781464,0.0045376,-0.01055016,-0.00043885,0.0016754,-0.01681205,-0.0470019,0.03198583,-0.02376289,0.03249083,-0.09631897,-0.01000118,0.04398447,-0.04317622,0.0750157,-0.04798026,-0.03866202,0.01732219,-0.00499263,0.04759583,0.00738792,0.06258004,0.03282151,-0.06174034,-0.01842608,0.03800313,0.03300156,0.02404761,-0.02028834,0.01909195,0.01477988,0.03667344,0.00197759,-0.00456661,0.00835155,-0.00749587,0.00250367,0.06165481,0.04040498,-0.03957038,-0.02387434,-0.01280017,-0.03071436,0.07210364,-0.08059932,0.01850495,0.03433966,-0.04265323,0.01888399,-0.04159467,-0.08022422,-0.03661644,0.04320421,0.02170839,0.03510204,0.02470302,-0.01145413,0.05422072,0.01436095,-0.00254861,-0.00410566,0.0324913,0.08869687,-0.01364252,-0.02905444,0.00899071,0.02592378,-0.0517456,-0.01991708,0.02674321,-0.01515588,-0.02322245,-0.08299726,0.0480212,-0.06869176,0.01754867,-0.02452959,0.01329546,-0.0092876,0.01210395,0.05287197,0.00625758,-0.04709231,-0.00029333,-0.07984126,-0.0189594,-0.02258584,0.0513644,-0.04615853,-0.01648017,-0.03358145,-0.04540221,-0.0483645,-0.04233889,0.0055067,-0.01523034,0.00226862,0.02937914,-0.03163784,-0.00596461,0.01534639,-0.02827145,0.01108658,-0.00475402,-0.01124484,-0.00070106,0.00400835,-0.06623719,-0.00471745,-0.03292396,0.03166838,0.0535614,-0.00099756,-0.01463523,-0.06015483,0.02246412,-0.08285982,0.01393971,-0.00617546,0.0343961,0.01301156,-0.05971632,0.01366789,0.02661047,0.04656473,0.11848228,0.04908377,-0.07966352,-0.0042724,0.02380376,-0.00813178,-0.02617393,0.0040877,-0.02522375,-0.0650446,-0.01951486,0.03783891,0.00587543,0.00495606,-0.00507589,-0.01591819,0.02372984,-0.02760339,0.06387225,-0.03878384,0.00540919,-0.02312481,0.00445672,0.0073963,0.00698501,-0.01409362,0.04856026,0.04217336,-0.0524493,0.0028921,-0.017051,0.0426988,0.01676049,0.02979119,0.01208153,-0.07079186,8.5e-7,-0.04438112,-0.0239589,0.04416211,-0.01577496,-0.01414325,-0.05046352,-0.04602969,0.01360912,0.00529764],"last_embed":{"hash":"h62xr","tokens":403}}},"text":null,"length":0,"last_read":{"hash":"h62xr","at":1748397845938},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程","lines":[93,133],"size":1023,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#---frontmatter---","lines":[1,4],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#身份验证协议": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#身份验证协议","lines":[5,7],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#身份验证协议#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#身份验证协议#{1}","lines":[7,7],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#简介","lines":[8,29],"size":337,"outlinks":[{"title":"Windows Active Directory","target":"微软活动目录","line":8},{"title":"中间人攻击","target":"中间人攻击","line":12}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#简介#{1}","lines":[10,17],"size":194,"outlinks":[{"title":"Windows Active Directory","target":"微软活动目录","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#简介#{2}","lines":[18,29],"size":136,"outlinks":[{"title":"中间人攻击","target":"中间人攻击","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#简介#{3}","lines":[20,29],"size":64,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{1}","lines":[31,50],"size":490,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{2}","lines":[51,52],"size":78,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{3}","lines":[53,55],"size":61,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{4}","lines":[56,56],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{5}","lines":[57,59],"size":173,"outlinks":[{"title":"域控制器","target":"域控制器","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{6}","lines":[60,60],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{7}","lines":[61,65],"size":147,"outlinks":[{"title":"NTLM","target":"NTLM","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#验证流程图#{8}","lines":[66,67],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 认证的基本原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 认证的基本原理","lines":[68,92],"size":373,"outlinks":[{"title":"域控制器","target":"域控制器","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 认证的基本原理#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 认证的基本原理#{1}","lines":[70,79],"size":163,"outlinks":[{"title":"域控制器","target":"域控制器","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 认证的基本原理#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 认证的基本原理#{2}","lines":[80,90],"size":184,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 认证的基本原理#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 认证的基本原理#{3}","lines":[91,92],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程#{1}","lines":[95,113],"size":464,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程#{2}","lines":[114,118],"size":183,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程#{3}","lines":[119,125],"size":204,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程#{4}","lines":[126,131],"size":146,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#Kerberos 的工作流程#{5}","lines":[132,133],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#攻击的逻辑": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#攻击的逻辑","lines":[134,145],"size":123,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#攻击的逻辑#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md#攻击的逻辑#{1}","lines":[135,145],"size":115,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md","embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08590443,-0.00680313,0.01976715,-0.03167345,-0.00991172,0.05201938,0.031176,-0.04821992,-0.02702717,-0.02857957,-0.01348792,0.01960083,0.04319253,-0.10887487,0.02838521,0.02511551,0.03738071,0.03573252,0.04224574,-0.06915133,-0.04137732,0.0419645,-0.03353788,0.01148116,-0.01156237,-0.04492094,-0.05542105,-0.01308208,0.08678748,0.02957509,0.07351552,0.03329866,0.02524018,-0.01911609,-0.01459664,0.02635002,0.02176182,0.0174127,-0.00218794,0.00296882,-0.01283686,0.01707668,-0.04474539,0.01817231,-0.06857547,0.02377453,0.00829967,-0.04344629,0.02197371,0.01984487,-0.00432334,-0.0369474,0.03299122,-0.04304232,-0.01962126,0.01411328,-0.05700644,-0.08662359,-0.00152012,-0.00311631,0.00602956,0.00587956,0.02152153,-0.02953749,0.05314839,0.00088835,-0.04336061,-0.04195584,-0.00320415,-0.02181721,-0.03070161,-0.02992741,0.06257666,0.02690436,-0.00036697,0.02975574,0.00722269,-0.00637176,-0.03593872,0.04521797,-0.0371435,0.03010975,0.02270534,0.07351577,0.03288681,-0.02253642,0.04692659,-0.03958705,-0.00474813,0.0142287,-0.02600315,0.0237089,-0.00393189,-0.03131561,0.06768774,-0.03355675,-0.02414175,0.04709046,0.03728198,-0.03529143,0.0099648,-0.01272465,0.0084923,0.01723466,0.04130235,-0.06100123,-0.00709515,0.03650473,-0.05402977,-0.03891629,0.02534151,0.02118073,0.0148762,-0.00608843,-0.05505812,-0.00456316,0.00293109,-0.00357249,-0.03052904,0.03998698,0.02254428,0.02853222,-0.02585623,0.01637698,-0.0764773,-0.03728888,-0.02604901,-0.03447783,0.03725778,-0.02777324,0.00117805,0.04548163,-0.02900071,-0.01319002,-0.00602325,0.00218753,0.07357199,-0.02463489,-0.0374934,-0.03017515,0.00037912,0.04497698,-0.00883514,-0.0077162,-0.00180049,0.00876838,-0.0352785,0.0085042,0.0409294,0.04027924,0.0504517,0.00360834,0.01901575,-0.06429814,-0.05487348,-0.01190734,0.03637994,0.03503052,-0.03361312,-0.02579403,0.04821967,-0.02573156,-0.00872221,-0.00258671,-0.01878832,0.01273473,-0.01538196,0.03853394,-0.01826195,0.01589465,-0.02621496,0.05660452,-0.00204615,0.00767602,0.01729104,-0.00257668,0.0651153,-0.00038333,-0.03917966,0.0580903,0.06354623,-0.0320968,0.03932172,0.03860922,-0.0102809,0.02104159,-0.0320316,0.02652862,0.01218927,-0.00463347,0.06403136,0.02315842,0.02959926,-0.0096643,0.0239449,0.06174217,-0.02337287,-0.05230367,-0.04199409,0.01558845,0.00392084,-0.01651171,-0.02374998,-0.13890132,0.04017038,0.01613889,0.04375022,-0.06195973,0.00456729,0.0516927,-0.03504279,0.02632841,0.01778635,-0.04143286,-0.02203691,-0.00556513,-0.02081642,0.01831384,0.00436402,-0.03630424,-0.03065303,0.03033683,0.0156651,0.07076459,-0.02470001,-0.02559744,0.01670202,-0.00330266,0.01095866,0.02808251,-0.02029303,-0.00564645,0.02568814,-0.00829976,-0.0343077,-0.0310499,0.02160499,0.055775,0.03043801,0.00322619,-0.02335219,0.0189184,0.03335806,0.02457939,-0.00151609,-0.03431947,-0.06246052,0.00304465,0.05140033,0.01118381,0.03831327,0.0650356,0.00301793,0.04852004,0.00594387,-0.06416828,-0.05954247,-0.02047482,0.03578395,-0.02823924,0.05455249,0.02491527,0.02382761,0.06034889,-0.01122428,-0.02854536,0.00192974,0.01023902,0.0184359,-0.0087348,0.05772205,-0.02013107,0.04100144,0.00702976,0.0124989,-0.02004095,0.04033316,0.03684084,0.03149127,-0.01208172,-0.00306849,-0.02552122,-0.00195037,0.00183817,-0.01482875,0.13295297,0.0107558,-0.00173493,0.04197023,-0.01368261,0.00344693,-0.03632707,-0.05848137,-0.01129257,-0.02233611,0.028351,-0.00387891,0.01263846,0.0438747,-0.04776357,0.0042624,0.02519941,0.01275186,-0.00695222,0.02750807,0.02561477,0.00192264,-0.00701132,0.04591447,-0.02198983,-0.01922381,-0.05221209,-0.03664175,-0.00058984,-0.0350286,0.03243685,0.01407427,0.02777289,-0.02014245,0.00584343,-0.01147379,0.01190892,-0.01348698,-0.03724663,0.02511688,-0.03608545,0.0285601,0.04419394,0.06209993,0.06112614,-0.04041312,0.00347591,0.00580412,-0.05964128,0.0257167,0.02421827,0.01618395,-0.07991306,-0.00440727,-0.05284583,0.02989164,-0.0295558,-0.02286645,-0.0789744,0.02311409,0.03251335,0.00947791,0.01853941,-0.04680636,-0.042968,-0.02873811,0.00909065,-0.00562618,-0.01098983,0.01864745,0.01344027,-0.06707581,-0.04387132,-0.06227474,0.00798658,0.04289059,0.00770016,-0.06512216,-0.01506547,-0.04895714,-0.02696088,0.01196322,-0.02741879,0.00128275,0.03480371,0.03410126,0.01395544,-0.04297862,0.00998316,0.01191373,-0.01112732,-0.03092965,0.03015425,0.02776937,-0.02660383,-0.0236283,-0.0465971,-0.0056449,0.0384103,-0.01721899,-0.01100169,-0.03633524,0.03195225,0.04823156,-0.11027773,0.02834985,0.01621742,-0.09738465,-0.02325594,0.02609498,-0.00439649,-0.00293399,-0.02336331,-0.02295253,0.03676492,0.01842697,0.00587411,0.00776474,-0.02494455,-0.04681759,0.05627834,-0.06878335,0.02869798,0.00179218,-0.02627768,0.03003873,0.04694769,-0.01679192,-0.00276303,0.0015858,-0.01958493,0.01901948,-0.03233118,0.02236659,-0.02063938,0.05352432,-0.04263544,0.06136789,0.03574826,-0.00271573,0.01322064,-0.02087747,-0.0368472,-0.0024465,0.02115145,-0.01863368,-0.03353653,-0.02473838,-0.00151946,0.03608535,0.02589698,0.01050018,0.00016224,0.03067401,0.00224729,-0.06470011,-0.01773114,0.01261531,-0.01455193,-0.06519769,0.01485265,0.01839688,-0.03091325,-0.02114294,-0.04105747,0.01542685,-0.0521322,-0.07163849,-0.04105624,0.05153235,0.05258579,0.00554275,-0.01576649,0.03824725,0.02344962,-0.07377152,0.04483489,0.0183373,-0.02705438,0.01755653,-0.01233387,0.04518937,-0.01284471,-0.02393496,-0.03318186,-0.05785493,0.02644814,0.01156146,-0.01773172,0.06505718,-0.05337274,0.00384865,-0.03042691,-0.0321086,-0.00424445,0.0225295,0.02440587,-0.00973281,-0.01763969,-0.01000679,0.01107067,0.00229257,0.04336216,0.03115787,-0.04509379,0.01676594,0.0257079,0.01784915,0.03539039,-0.00396331,0.06852528,-0.00542161,0.04868899,0.06369758,0.02327737,0.02947479,0.03860786,-0.02205856,0.01133976,0.04310095,0.02526354,-0.02398984,-0.01705587,0.01479577,-0.0128126,-0.02458773,-0.04231618,0.04036286,0.02211362,0.00629335,-0.01748694,0.01768122,0.03474546,0.00050535,-0.00632084,0.06451602,0.04662393,-0.05051613,-0.01428086,0.03805585,-0.07806588,-0.02274989,0.04023032,0.04518588,-0.00865454,0.02634409,-0.02582369,0.05525722,-0.01746034,-0.00508488,0.0068723,0.03929126,-0.0366186,-0.05780414,0.0172576,0.02231751,0.00366684,-0.00149356,0.05837017,0.00361053,-0.03005787,0.00566785,-0.04672013,0.01761667,0.01266548,0.01332165,0.072103,0.03976624,-0.00620445,0.01259904,0.03728781,0.03925816,-0.00564604,-0.05060172,0.05429279,-0.00101231,0.00864908,0.0398315,0.01900903,-0.05222023,0.03672735,-0.03959242,-0.01865857,0.02898198,-0.01089548,-0.11431174,-0.02141231,0.03487159,0.03151727,-0.00959152,-0.01343694,-0.04563653,-0.02639474,0.0045186,-0.00596501,0.03653499,0.02025039,0.06726199,-0.0359228,-0.02966779,-0.03219088,-0.00400955,0.02092918,0.00082668,-0.00865839,0.06437066,-0.05483436,-0.00909344,0.03882753,-0.00276644,0.01871577,-0.00512482,0.03135317,0.00362282,-0.01935209,-0.00310791,-0.01076726,-0.02318654,-0.04691092,0.02132883,-0.0349649,0.04042242,-0.08702923,-0.02086056,0.04183732,-0.04879936,0.06662759,-0.0413867,-0.05505836,0.01650784,0.00417427,0.04987224,-0.00673047,0.025475,0.03034259,-0.03941299,-0.04444521,0.01747924,0.01656552,0.01472886,0.00177066,0.02550113,0.03958242,0.04666489,-0.01535963,-0.04497988,-0.01764532,-0.01483906,-0.00433127,0.04888844,0.02129884,-0.09911643,0.00756954,-0.01089318,-0.00968285,0.08327635,-0.07431071,0.03534872,0.03553287,-0.01510676,0.01235254,-0.05874953,-0.09496178,-0.04188482,0.05354246,0.02440233,0.01478811,0.03674571,-0.02686301,0.01875567,0.02490859,-0.00131724,0.00646176,0.00803474,0.09223153,-0.02691452,-0.01952058,-0.00494299,0.01700663,-0.04358216,-0.01055176,0.03849217,-0.02203316,-0.03416022,-0.06737603,0.0543176,-0.04675319,0.01028131,-0.05119257,0.00309312,-0.00595245,-0.00414952,0.0348798,-0.02678034,-0.04311019,-0.01668713,-0.05453486,-0.00203386,0.01350043,0.04732339,-0.0595169,-0.02674578,-0.02751101,-0.07907905,-0.04567967,-0.05030759,0.01778796,0.00619755,0.00751591,0.03076988,-0.02328719,-0.00549679,0.01356262,-0.02092345,0.00016905,-0.01217946,-0.01737471,0.01378321,0.03078313,-0.05251591,0.01356293,-0.01634223,0.00880675,0.0564303,-0.00118986,-0.02156938,-0.0664907,0.02443626,-0.10528222,0.02765264,-0.00468274,0.00924726,0.02059406,-0.09516468,0.00764687,0.01573291,0.04430909,0.11674554,0.05772138,-0.08425044,0.01282897,0.00133291,-0.00368528,-0.02045278,0.01923319,-0.04197538,-0.07549578,-0.02424588,0.04585432,-0.00452308,0.00328461,0.00345472,-0.0211221,0.01852958,-0.04639996,0.041583,-0.04014487,0.02170255,-0.06128997,-0.00529185,0.00849472,0.00581803,-0.01225756,0.06294911,0.0128591,-0.07562893,-0.00544685,-0.01122872,0.06284288,0.04019428,0.03457275,-0.00827649,-0.07401089,7.5e-7,-0.03259349,-0.01589772,0.03025767,-0.03074128,-0.03358672,-0.00542141,-0.03388653,0.01478123,-0.00383678],"last_embed":{"hash":"a3fvwg","tokens":1200}}},"last_read":{"hash":"a3fvwg","at":1752940887955},"class_name":"SmartSource","last_import":{"mtime":1735626285365,"size":5180,"at":1749002741368,"hash":"a3fvwg"},"blocks":{"#---frontmatter---":[1,4],"#身份验证协议":[5,7],"#身份验证协议#{1}":[7,7],"#简介":[8,29],"#简介#{1}":[10,17],"#简介#{2}":[18,29],"#简介#{3}":[20,29],"#验证流程图":[30,67],"#验证流程图#{1}":[31,50],"#验证流程图#{2}":[51,52],"#验证流程图#{3}":[53,55],"#验证流程图#{4}":[56,56],"#验证流程图#{5}":[57,59],"#验证流程图#{6}":[60,60],"#验证流程图#{7}":[61,65],"#验证流程图#{8}":[66,67],"#Kerberos 认证的基本原理":[68,92],"#Kerberos 认证的基本原理#{1}":[70,79],"#Kerberos 认证的基本原理#{2}":[80,90],"#Kerberos 认证的基本原理#{3}":[91,92],"#Kerberos 的工作流程":[93,133],"#Kerberos 的工作流程#{1}":[95,113],"#Kerberos 的工作流程#{2}":[114,118],"#Kerberos 的工作流程#{3}":[119,125],"#Kerberos 的工作流程#{4}":[126,131],"#Kerberos 的工作流程#{5}":[132,133],"#攻击的逻辑":[134,145],"#攻击的逻辑#{1}":[135,145]},"outlinks":[{"title":"Windows Active Directory","target":"微软活动目录","line":15},{"title":"中间人攻击","target":"中间人攻击","line":19},{"title":"域控制器","target":"域控制器","line":57},{"title":"NTLM","target":"NTLM","line":61},{"title":"域控制器","target":"域控制器","line":73}],"metadata":{"cssclasses":["editor-full"]},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/应用层协议/通用性协议/基于HTTP协议/Kerberos认证.md","last_embed":{"hash":"a3fvwg","at":1752940887955}},