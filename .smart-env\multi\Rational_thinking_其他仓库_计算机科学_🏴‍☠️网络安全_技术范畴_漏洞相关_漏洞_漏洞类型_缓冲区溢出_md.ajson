"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0461838,-0.00274189,-0.02199685,-0.04470513,-0.03017068,-0.04614545,-0.01196642,0.04920425,0.07103954,0.02253387,-0.02402047,-0.06799546,0.06822003,0.05415864,0.02082461,-0.01243906,-0.04065274,0.02353906,-0.02546018,-0.03868915,0.11008323,-0.01231254,-0.03617636,-0.06231146,-0.01035492,0.02622501,0.02511355,-0.05176922,-0.01981676,-0.18026844,-0.00158275,0.01099165,0.01150957,0.01529999,-0.00423032,-0.0479856,0.04805989,0.07513773,-0.04011566,0.04993212,-0.02418675,0.05057062,0.01247941,-0.03067714,-0.00957435,-0.06226625,-0.04584324,-0.04948943,0.04519634,-0.01579539,-0.04625257,-0.00005093,-0.05075879,-0.01476438,-0.01222109,-0.02171587,0.03891407,0.0204885,0.02445892,-0.0668862,0.05130322,0.01622037,-0.15557732,0.05199774,0.03021844,-0.02012822,-0.00766649,0.02350715,0.05657705,0.02283059,-0.06257658,0.01559906,-0.03025308,0.07867838,0.07876388,-0.05515834,0.01213406,-0.02260651,-0.04574742,-0.03701198,0.00719296,0.04942654,-0.01890303,0.00249479,0.01621857,0.05219874,-0.04946313,-0.00338684,0.01177576,-0.04278421,0.00439114,-0.03603595,-0.01005459,0.063337,-0.04079016,-0.01631194,0.0615123,0.01268657,-0.09081045,0.11958212,-0.06504564,-0.0067594,0.00009016,-0.05996686,-0.00802415,-0.02706067,-0.03083397,-0.03361129,-0.0169986,-0.06648839,-0.09213717,-0.01255106,0.07830983,-0.06300535,0.06179465,0.02528769,-0.01039217,-0.02804229,-0.04589161,-0.00451035,-0.03714488,0.0289895,0.0282781,-0.01167067,-0.00026732,-0.03034195,0.03585761,0.07927065,0.03690768,0.02093661,0.07343122,-0.02543072,-0.03833255,-0.02762516,-0.03265972,0.00621371,-0.07910024,0.0049021,0.04566521,-0.06472556,-0.02506721,-0.0417273,0.00178213,-0.08682694,-0.03370698,0.08137024,-0.05311951,-0.00614116,0.02034965,-0.05335691,0.02095062,0.05744825,-0.00178327,-0.02747458,-0.01124413,0.05713055,0.042567,0.12982111,-0.03729015,-0.01598075,-0.00912705,0.05208766,-0.091777,0.12105513,0.01092552,-0.02893831,-0.04640137,0.01237723,-0.02036674,-0.05092897,0.02463586,-0.03652956,-0.00172624,-0.01006327,0.00544989,0.00658534,-0.05837354,-0.05394645,0.00841349,0.04727371,0.04754327,-0.02638385,-0.07167307,0.04532145,-0.00841423,-0.11129761,-0.06410926,-0.03973044,0.03756155,0.00144729,-0.10238885,0.00871968,-0.06011502,0.01972975,-0.08612575,-0.06249085,0.05955077,0.03432608,0.06392421,-0.0495928,0.11534521,0.03517161,-0.0609397,0.02020439,-0.05078443,-0.04974384,0.00477072,-0.0395665,0.05527198,0.04960049,0.0396667,-0.00059566,0.01401067,0.03223147,-0.02115851,-0.01039063,0.013232,0.01614195,0.0002928,0.02863437,0.05468257,-0.0494956,-0.07625353,-0.2061471,-0.02126672,0.00707428,-0.01113535,0.01797102,-0.05927936,0.04155757,0.01091439,0.04147553,0.0630215,0.06483214,0.03934876,-0.07122668,-0.0544774,0.00695895,0.04735356,0.02623982,-0.03104624,0.00619486,0.02183265,-0.00420007,0.05042104,-0.03207514,0.0365756,0.06678475,-0.04128463,0.12717184,0.00957683,0.04292069,0.03142485,0.02562122,0.0229693,0.01052539,-0.06660139,0.04159882,0.02379375,-0.06940729,-0.00927257,-0.02989867,-0.00874255,-0.00394973,-0.00560016,-0.04661097,-0.06455172,0.01441326,0.02875617,-0.04455047,-0.00949244,-0.00124713,0.04539343,-0.0109953,-0.00217593,0.01498364,0.06278999,0.05001899,-0.05349023,-0.05508645,0.0192656,-0.04664729,0.01177729,0.03207121,-0.04168102,0.01263687,0.00220348,-0.02379224,0.02189212,0.01144663,-0.02959988,-0.026433,0.01039071,0.01787273,0.16854863,0.00540913,-0.03496137,0.05744988,0.00375885,0.03411511,-0.04124651,0.02347772,-0.04990785,0.06414218,0.01663418,0.03773975,0.01063055,0.02439204,0.00763524,0.01906705,-0.01454581,0.08456564,-0.05058772,-0.01887418,-0.04043246,-0.02800091,0.0252518,0.0641445,-0.01797515,-0.30583885,0.03178049,-0.02943906,-0.02437197,0.04719183,0.02759861,0.03031594,-0.02890761,-0.05896657,0.05168657,-0.04875587,0.05323872,0.00207232,-0.00392949,-0.00647695,-0.00441165,0.05062455,0.02149446,0.05682566,-0.00013378,0.02473451,0.07078078,0.20653014,-0.03748899,0.0406225,-0.03533135,-0.00681211,0.0438638,0.09694631,0.00158024,-0.00702327,-0.02185855,0.07360091,-0.04471236,0.05426955,0.05524519,-0.05678009,0.03111137,0.02942555,0.00204887,0.0134477,0.05503752,-0.08712357,-0.00266057,0.1003153,0.04146019,0.01362396,-0.02550087,0.02165955,0.01598855,0.01372222,0.05778955,0.01537703,-0.01342292,0.01061008,0.04900962,0.00255095,-0.03643294,-0.04512302,0.00401524,0.0551789,0.01001693,0.10402035,0.12402262,0.04028007],"last_embed":{"hash":"1243b643db3ffb6c910769cfd375d5c80ff44eee7782ccc5261c4de5f6543702","tokens":471}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.05865461,0.02009557,-0.03211268,-0.00005838,0.00115518,0.02026048,0.07880132,-0.00236846,0.00978539,-0.05829299,-0.02779472,0.05193603,-0.06639756,-0.03571007,0.03102661,0.00595714,-0.01953643,0.04601772,0.04133528,0.00421455,0.04132469,-0.00954064,0.07204503,0.03765522,0.00056869,0.00731429,0.00305828,-0.04529138,0.01856631,0.06247523,0.07933568,-0.02029778,0.03645151,0.01028831,-0.03655231,0.00378105,0.02064244,-0.02957934,-0.01041503,0.0165671,-0.00691801,0.00596381,-0.02015154,0.03214618,-0.05768918,-0.06299464,0.00844587,-0.05617921,-0.00539835,0.01316509,0.04508773,-0.02842272,-0.02986398,0.02495408,-0.00177379,0.02462755,-0.01853289,-0.0054816,0.00201603,-0.00010421,0.02889414,-0.07205514,0.0755136,0.03041812,0.00040061,0.03974997,0.02639017,0.04203739,-0.0236064,-0.02532255,-0.03811979,0.00673701,0.01422874,0.02209667,0.01614373,0.01463014,0.01720345,-0.00031427,-0.02417595,-0.00153762,-0.07948606,0.02587155,0.05490405,-0.02436633,0.04059456,-0.03055129,-0.02563528,-0.00054173,0.0215556,-0.01360899,0.062267,-0.02240405,0.02881703,0.02381451,-0.00258032,-0.03264421,-0.01744949,0.0160866,0.00377504,-0.01064877,-0.0071347,0.0246403,-0.08710308,-0.01828579,-0.02128394,0.0018452,-0.0929847,0.01059581,0.00038196,-0.00139746,-0.02015712,-0.02018485,-0.03065418,-0.04608583,-0.07768523,0.04922669,-0.06207984,0.01648181,0.02916432,0.0120648,0.02867686,0.02580168,0.04740402,-0.05290977,-0.00138982,0.00964815,0.02302698,-0.0107385,0.02195107,-0.05710646,-0.01067723,0.02410668,-0.03529553,-0.00019736,0.04911144,0.03366139,0.01364429,-0.039896,0.02846101,-0.05432124,0.02934127,-0.02746371,0.01529866,0.04032904,0.00088941,0.04836394,-0.0129625,0.0019779,-0.00795058,0.00188251,0.0176766,-0.00410922,-0.00580037,-0.00755581,0.01893316,-0.0187172,0.03821386,0.04347498,-0.0212943,-0.02782032,-0.04980555,0.04644594,0.0111993,0.01900592,0.03275651,-0.02019871,-0.03470637,0.02231103,0.01200375,0.00654532,0.01270965,-0.03001994,-0.0111437,0.04460713,-0.02555411,-0.02465832,0.04206304,0.1225248,-0.03635602,-0.0466008,-0.00142572,-0.00872576,-0.01269647,0.00620082,0.01846492,0.00884943,-0.00277,-0.05513386,0.01233409,0.03138088,0.03222,0.00953634,-0.04349956,0.0063131,-0.00398217,0.00453401,-0.02209217,-0.06878763,-0.00244793,0.04233611,0.02343728,-0.00319864,0.02530605,-0.03564205,0.01153522,0.014032,0.0149573,0.02473517,-0.05074537,0.10560504,0.028432,0.0302544,-0.01364571,0.03579022,-0.03471755,0.07675571,-0.01988005,0.00299618,0.01990288,0.05390475,0.03694755,0.00079934,-0.0256276,-0.09280248,-0.01624911,-0.0506504,-0.02586143,-0.00089641,0.01430239,0.01604416,0.01146965,0.06690572,-0.03267156,-0.00258935,-0.02655882,0.05358931,-0.00798834,0.08963231,0.01265741,0.05367727,0.00829675,-0.00043288,0.0045939,0.02217046,-0.00589467,-0.02016609,-0.01145116,0.01935641,0.00671855,0.01750448,0.03652468,-0.04239953,-0.00302574,0.04101517,-0.01604403,-0.04533535,-0.00093722,-0.02909846,-0.02261146,-0.00120429,-0.00880265,0.04462188,-0.05995642,0.01823854,0.00997571,-0.01599118,-0.01009392,0.06776414,-0.04790707,0.0482352,-0.04194435,0.00179565,0.05156102,-0.0220723,-0.04429708,0.02518795,-0.05708078,-0.01016288,-0.07421687,-0.02471193,-0.00345208,-0.03084598,0.01700474,-0.03716512,-0.04276166,0.02899066,0.0165397,-0.02922896,0.01708952,-0.03529521,0.08037068,-0.04381737,-0.02668175,0.04369196,-0.08047846,-0.05484424,-0.02080395,-0.01226396,-0.01144138,-0.00425863,-0.02399329,0.0237011,0.0233303,0.00306629,-0.00814174,0.03670942,0.00275734,0.04010713,-0.07788097,-0.04806714,0.01897762,-0.0209019,0.07178938,-0.02213362,0.0286129,0.05088205,-0.00013892,-0.04281049,-0.03993541,0.065685,-0.06773979,-0.00093952,0.02055035,0.0075473,0.02401766,-0.06410988,-0.00284681,0.00368032,0.05581282,0.06152018,-0.04941298,-0.00404312,0.02026797,-0.01575121,0.0271781,-0.01243827,-0.02526403,-0.05435972,0.0231814,-0.04365746,0.0179317,-0.0468494,0.01183827,-0.00485165,-0.0079297,0.0262654,-0.01414315,-0.00495373,-0.00670786,-0.02115584,0.01319541,0.00158846,-0.05464793,-0.07153786,0.02183936,-0.00285716,-0.01106182,-0.044076,0.03098834,0.02153983,-0.01551922,-0.04937238,0.03451399,-0.01187207,-0.00286815,0.02611874,0.0424377,0.03133274,-0.02652257,-0.05731727,0.01382497,0.00383549,-0.01766245,0.01107574,0.0308145,-0.00966046,0.01125748,0.02434231,0.03283923,0.0260244,0.01615953,-0.02414205,-0.0033336,-0.02557209,-0.04775422,-0.03241652,-0.03471689,-0.01629417,-0.01646392,-0.05422227,-0.03603279,0.00166038,-0.07900312,0.05411187,0.03277019,0.03584019,0.02991994,-0.01154838,-0.0017917,-0.04624443,0.01594555,-0.01088978,-0.00340102,0.0033494,-0.00126595,0.09373632,-0.01891667,-0.00267216,-0.09554698,-0.00623754,0.02180309,0.09253392,-0.0121706,-0.02498881,0.02132643,0.02496026,-0.01912983,-0.0707342,-0.06419162,0.00952289,-0.01426118,-0.024766,-0.01307788,0.03207041,-0.02347562,-0.03989465,0.00089968,-0.02577221,0.02824429,-0.01195988,-0.03288449,-0.00587367,-0.05659702,0.01212011,0.01683423,-0.0372206,0.04922377,-0.0293069,-0.00918351,0.01857849,-0.05430292,-0.0173346,-0.0001226,-0.00854597,0.08338011,0.10646164,-0.01101975,-0.07187533,0.02633175,0.03137947,-0.01234373,0.02398758,-0.03296621,0.01597684,0.01541878,-0.04583437,-0.00918559,-0.04099549,-0.03487722,0.01437325,-0.03418394,-0.05813826,0.02611333,0.02186957,0.01185703,0.04010773,0.05328314,0.01111157,-0.04829095,0.04755511,-0.03705669,-0.04190328,0.01368205,0.00558024,0.01530984,-0.00366445,-0.06467873,0.0430002,-0.04519108,0.02166518,0.02963319,0.01883766,0.00582379,0.00049509,0.00478649,0.00469448,-0.06292294,0.02750579,0.02117691,-0.04818247,-0.0305181,0.08517485,-0.01282153,0.0284451,0.02707029,-0.05443315,-0.00873095,0.05943435,0.00067344,0.03084877,0.02040225,-0.03940631,-0.04542575,0.02702897,0.0230836,0.04000421,-0.04581069,0.01322062,0.03703499,-0.02273467,-0.0061834,-0.02205444,0.08935267,0.01025518,-0.02160131,-0.07212637,-0.00555329,-0.00344949,-0.01357736,-0.02846785,-0.00513715,0.05422938,-0.03879489,-0.01227187,0.00707487,-0.03022867,-0.02546483,-0.0072449,0.02467934,0.01881978,-0.07181871,-0.00843545,0.01329915,0.01035045,0.04621276,-0.0209905,0.06031208,0.01090279,-0.0362728,0.00936632,0.00432351,-0.01024563,0.00072483,0.04571752,-0.05614648,-0.00242103,0.02651901,-0.01913858,0.04316731,-0.04147367,-0.00198905,-0.00047484,0.00001316,-0.00987299,-0.08200572,0.0169882,0.0040805,-0.07815675,0.00377901,0.04672381,-0.03143922,0.04081997,-0.01040753,0.00076963,0.02264437,0.01950853,-0.0782582,-0.00478009,-0.02360661,0.03661911,-0.08073947,-0.00040219,0.07805542,-0.03550223,-0.01882648,-0.04076313,0.00015873,0.03287151,0.06688379,-0.03400186,-0.00175597,0.00937653,0.05913967,-0.01101913,-0.01648906,0.04428772,0.03187713,0.01087149,0.04055593,-0.01600422,-0.03407151,-0.05554657,-0.00781427,-0.06355276,-0.01006895,0.01401982,-0.01813909,0.04564292,0.01189919,-0.03888111,0.01459026,-0.00750158,-0.02590359,-0.05040927,-0.02596473,-0.06796237,-0.05049768,-0.0307397,0.01481789,0.03825928,-0.01791557,-0.01597252,-0.00893612,-0.01055566,0.0187954,0.06696302,0.01894181,0.01483776,-0.0530544,-0.03425399,-0.02068729,-0.09470735,-0.08577991,-0.02514671,-0.00117949,0.11463808,-0.01279508,0.01218397,0.08888562,-0.04151708,-0.04175333,-0.05412721,0.03894814,0.03488837,0.03973304,-0.02136919,-0.02360803,-0.01459195,0.01004127,0.01533478,0.00567334,0.05457772,0.02155652,0.06281586,0.04421268,-0.0099768,-0.01998843,0.02925222,0.02411273,-0.01392806,-0.04877265,-0.03119443,0.00101811,0.03418398,0.00237572,0.04359891,0.02191813,-0.01238352,0.00352727,0.09728193,-0.01176655,-0.03651661,-0.00337958,-0.03521206,-0.03127373,-0.00167928,0.05267496,0.00971174,-0.06843086,0.06647534,-0.06933431,-0.01609429,-0.02187769,0.03995942,-0.00534699,0.03856955,-0.00367003,0.05018545,0.00934367,-0.02288664,-0.01073576,0.00090045,0.00683222,-0.00261469,-0.03664368,0.00449341,0.0162391,-0.03468947,0.00243426,0.0473865,-0.04649103,0.07372218,-0.00648144,-0.00863578,-0.02196459,-0.07329975,0.03301912,0.00709444,0.05895918,-0.01233762,-0.02276641,-0.02371211,-0.00680841,-0.00754467,-0.03890532,0.00892004,0.01862151,-0.01615103,0.06361258,-0.00909684,-0.02682213,0.00560427,-0.01616064,0.035615,0.02595366,-0.05428139,-0.00154697,0.00284572,-0.07522666,0.02101639,-0.06447466,-0.05110036,0.03674073,0.00203073,-0.06933658,-0.0341991,-0.04017815,0.01016472,-0.01953159,0.00783836,0.02670069,-0.06882548,0.0424523,0.0382065,0.02622904,0.00797587,-0.01235982,0.0169415,0.0499935,-0.02592527,0.06291573,0.0135239,0.00005484,-0.01720033,-0.04749304,0.05948328,-0.00413597,0.0163424,0.01867772,0.08327839,-0.01277243,0.0227988,-0.04545294,0.06091839,0.03069864,-0.00535601,-0.0513961,-0.06624417,8.1e-7,0.0188263,0.01294203,0.03891958,-0.00611292,0.02061767,0.01221581,-0.03940738,-0.00995456,0.02215385],"last_embed":{"tokens":338,"hash":"5g15x9"}}},"last_read":{"hash":"5g15x9","at":1752940658643},"class_name":"SmartSource","outlinks":[{"title":"C语言","target":"C语言","line":8},{"title":"内核漏洞","target":"内核漏洞","line":10},{"title":"内存","target":"内存","line":17}],"metadata":{"aliases":["Buffer Overflow"],"英文":"Buffer Overflow","tags":["网络安全/漏洞/缓冲区溢出"],"基础知识":["[[C语言]]"],"进阶知识":["[[内核漏洞]]"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,13],"#简介":[14,22],"#简介#{1}":[15,15],"#简介#{2}":[16,19],"#简介#{3}":[20,20],"#简介#{4}":[21,21],"#简介#{5}":[22,22],"#相关实例":[23,42],"#相关实例#{1}":[25,40],"#相关实例#{2}":[41,41],"#相关实例#{3}":[42,42]},"last_import":{"mtime":1747561128776,"size":1265,"at":1749024987637,"hash":"5g15x9"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md","last_embed":{"hash":"5g15x9","at":1752940658643}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#---frontmatter---","lines":[1,13],"size":149,"outlinks":[{"title":"C语言","target":"C语言","line":8},{"title":"内核漏洞","target":"内核漏洞","line":10}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介","lines":[14,22],"size":211,"outlinks":[{"title":"内存","target":"内存","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介#{1}","lines":[15,15],"size":37,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介#{2}","lines":[16,19],"size":135,"outlinks":[{"title":"内存","target":"内存","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介#{3}","lines":[20,20],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介#{4}","lines":[21,21],"size":24,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#简介#{5}","lines":[22,22],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#相关实例": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#相关实例","lines":[23,42],"size":412,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#相关实例#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#相关实例#{1}","lines":[25,40],"size":310,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#相关实例#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#相关实例#{2}","lines":[41,41],"size":60,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#相关实例#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md#相关实例#{3}","lines":[42,42],"size":32,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0461838,-0.00274189,-0.02199685,-0.04470513,-0.03017068,-0.04614545,-0.01196642,0.04920425,0.07103954,0.02253387,-0.02402047,-0.06799546,0.06822003,0.05415864,0.02082461,-0.01243906,-0.04065274,0.02353906,-0.02546018,-0.03868915,0.11008323,-0.01231254,-0.03617636,-0.06231146,-0.01035492,0.02622501,0.02511355,-0.05176922,-0.01981676,-0.18026844,-0.00158275,0.01099165,0.01150957,0.01529999,-0.00423032,-0.0479856,0.04805989,0.07513773,-0.04011566,0.04993212,-0.02418675,0.05057062,0.01247941,-0.03067714,-0.00957435,-0.06226625,-0.04584324,-0.04948943,0.04519634,-0.01579539,-0.04625257,-0.00005093,-0.05075879,-0.01476438,-0.01222109,-0.02171587,0.03891407,0.0204885,0.02445892,-0.0668862,0.05130322,0.01622037,-0.15557732,0.05199774,0.03021844,-0.02012822,-0.00766649,0.02350715,0.05657705,0.02283059,-0.06257658,0.01559906,-0.03025308,0.07867838,0.07876388,-0.05515834,0.01213406,-0.02260651,-0.04574742,-0.03701198,0.00719296,0.04942654,-0.01890303,0.00249479,0.01621857,0.05219874,-0.04946313,-0.00338684,0.01177576,-0.04278421,0.00439114,-0.03603595,-0.01005459,0.063337,-0.04079016,-0.01631194,0.0615123,0.01268657,-0.09081045,0.11958212,-0.06504564,-0.0067594,0.00009016,-0.05996686,-0.00802415,-0.02706067,-0.03083397,-0.03361129,-0.0169986,-0.06648839,-0.09213717,-0.01255106,0.07830983,-0.06300535,0.06179465,0.02528769,-0.01039217,-0.02804229,-0.04589161,-0.00451035,-0.03714488,0.0289895,0.0282781,-0.01167067,-0.00026732,-0.03034195,0.03585761,0.07927065,0.03690768,0.02093661,0.07343122,-0.02543072,-0.03833255,-0.02762516,-0.03265972,0.00621371,-0.07910024,0.0049021,0.04566521,-0.06472556,-0.02506721,-0.0417273,0.00178213,-0.08682694,-0.03370698,0.08137024,-0.05311951,-0.00614116,0.02034965,-0.05335691,0.02095062,0.05744825,-0.00178327,-0.02747458,-0.01124413,0.05713055,0.042567,0.12982111,-0.03729015,-0.01598075,-0.00912705,0.05208766,-0.091777,0.12105513,0.01092552,-0.02893831,-0.04640137,0.01237723,-0.02036674,-0.05092897,0.02463586,-0.03652956,-0.00172624,-0.01006327,0.00544989,0.00658534,-0.05837354,-0.05394645,0.00841349,0.04727371,0.04754327,-0.02638385,-0.07167307,0.04532145,-0.00841423,-0.11129761,-0.06410926,-0.03973044,0.03756155,0.00144729,-0.10238885,0.00871968,-0.06011502,0.01972975,-0.08612575,-0.06249085,0.05955077,0.03432608,0.06392421,-0.0495928,0.11534521,0.03517161,-0.0609397,0.02020439,-0.05078443,-0.04974384,0.00477072,-0.0395665,0.05527198,0.04960049,0.0396667,-0.00059566,0.01401067,0.03223147,-0.02115851,-0.01039063,0.013232,0.01614195,0.0002928,0.02863437,0.05468257,-0.0494956,-0.07625353,-0.2061471,-0.02126672,0.00707428,-0.01113535,0.01797102,-0.05927936,0.04155757,0.01091439,0.04147553,0.0630215,0.06483214,0.03934876,-0.07122668,-0.0544774,0.00695895,0.04735356,0.02623982,-0.03104624,0.00619486,0.02183265,-0.00420007,0.05042104,-0.03207514,0.0365756,0.06678475,-0.04128463,0.12717184,0.00957683,0.04292069,0.03142485,0.02562122,0.0229693,0.01052539,-0.06660139,0.04159882,0.02379375,-0.06940729,-0.00927257,-0.02989867,-0.00874255,-0.00394973,-0.00560016,-0.04661097,-0.06455172,0.01441326,0.02875617,-0.04455047,-0.00949244,-0.00124713,0.04539343,-0.0109953,-0.00217593,0.01498364,0.06278999,0.05001899,-0.05349023,-0.05508645,0.0192656,-0.04664729,0.01177729,0.03207121,-0.04168102,0.01263687,0.00220348,-0.02379224,0.02189212,0.01144663,-0.02959988,-0.026433,0.01039071,0.01787273,0.16854863,0.00540913,-0.03496137,0.05744988,0.00375885,0.03411511,-0.04124651,0.02347772,-0.04990785,0.06414218,0.01663418,0.03773975,0.01063055,0.02439204,0.00763524,0.01906705,-0.01454581,0.08456564,-0.05058772,-0.01887418,-0.04043246,-0.02800091,0.0252518,0.0641445,-0.01797515,-0.30583885,0.03178049,-0.02943906,-0.02437197,0.04719183,0.02759861,0.03031594,-0.02890761,-0.05896657,0.05168657,-0.04875587,0.05323872,0.00207232,-0.00392949,-0.00647695,-0.00441165,0.05062455,0.02149446,0.05682566,-0.00013378,0.02473451,0.07078078,0.20653014,-0.03748899,0.0406225,-0.03533135,-0.00681211,0.0438638,0.09694631,0.00158024,-0.00702327,-0.02185855,0.07360091,-0.04471236,0.05426955,0.05524519,-0.05678009,0.03111137,0.02942555,0.00204887,0.0134477,0.05503752,-0.08712357,-0.00266057,0.1003153,0.04146019,0.01362396,-0.02550087,0.02165955,0.01598855,0.01372222,0.05778955,0.01537703,-0.01342292,0.01061008,0.04900962,0.00255095,-0.03643294,-0.04512302,0.00401524,0.0551789,0.01001693,0.10402035,0.12402262,0.04028007],"last_embed":{"hash":"1243b643db3ffb6c910769cfd375d5c80ff44eee7782ccc5261c4de5f6543702","tokens":471}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.05865461,0.02009557,-0.03211268,-0.00005838,0.00115518,0.02026048,0.07880132,-0.00236846,0.00978539,-0.05829299,-0.02779472,0.05193603,-0.06639756,-0.03571007,0.03102661,0.00595714,-0.01953643,0.04601772,0.04133528,0.00421455,0.04132469,-0.00954064,0.07204503,0.03765522,0.00056869,0.00731429,0.00305828,-0.04529138,0.01856631,0.06247523,0.07933568,-0.02029778,0.03645151,0.01028831,-0.03655231,0.00378105,0.02064244,-0.02957934,-0.01041503,0.0165671,-0.00691801,0.00596381,-0.02015154,0.03214618,-0.05768918,-0.06299464,0.00844587,-0.05617921,-0.00539835,0.01316509,0.04508773,-0.02842272,-0.02986398,0.02495408,-0.00177379,0.02462755,-0.01853289,-0.0054816,0.00201603,-0.00010421,0.02889414,-0.07205514,0.0755136,0.03041812,0.00040061,0.03974997,0.02639017,0.04203739,-0.0236064,-0.02532255,-0.03811979,0.00673701,0.01422874,0.02209667,0.01614373,0.01463014,0.01720345,-0.00031427,-0.02417595,-0.00153762,-0.07948606,0.02587155,0.05490405,-0.02436633,0.04059456,-0.03055129,-0.02563528,-0.00054173,0.0215556,-0.01360899,0.062267,-0.02240405,0.02881703,0.02381451,-0.00258032,-0.03264421,-0.01744949,0.0160866,0.00377504,-0.01064877,-0.0071347,0.0246403,-0.08710308,-0.01828579,-0.02128394,0.0018452,-0.0929847,0.01059581,0.00038196,-0.00139746,-0.02015712,-0.02018485,-0.03065418,-0.04608583,-0.07768523,0.04922669,-0.06207984,0.01648181,0.02916432,0.0120648,0.02867686,0.02580168,0.04740402,-0.05290977,-0.00138982,0.00964815,0.02302698,-0.0107385,0.02195107,-0.05710646,-0.01067723,0.02410668,-0.03529553,-0.00019736,0.04911144,0.03366139,0.01364429,-0.039896,0.02846101,-0.05432124,0.02934127,-0.02746371,0.01529866,0.04032904,0.00088941,0.04836394,-0.0129625,0.0019779,-0.00795058,0.00188251,0.0176766,-0.00410922,-0.00580037,-0.00755581,0.01893316,-0.0187172,0.03821386,0.04347498,-0.0212943,-0.02782032,-0.04980555,0.04644594,0.0111993,0.01900592,0.03275651,-0.02019871,-0.03470637,0.02231103,0.01200375,0.00654532,0.01270965,-0.03001994,-0.0111437,0.04460713,-0.02555411,-0.02465832,0.04206304,0.1225248,-0.03635602,-0.0466008,-0.00142572,-0.00872576,-0.01269647,0.00620082,0.01846492,0.00884943,-0.00277,-0.05513386,0.01233409,0.03138088,0.03222,0.00953634,-0.04349956,0.0063131,-0.00398217,0.00453401,-0.02209217,-0.06878763,-0.00244793,0.04233611,0.02343728,-0.00319864,0.02530605,-0.03564205,0.01153522,0.014032,0.0149573,0.02473517,-0.05074537,0.10560504,0.028432,0.0302544,-0.01364571,0.03579022,-0.03471755,0.07675571,-0.01988005,0.00299618,0.01990288,0.05390475,0.03694755,0.00079934,-0.0256276,-0.09280248,-0.01624911,-0.0506504,-0.02586143,-0.00089641,0.01430239,0.01604416,0.01146965,0.06690572,-0.03267156,-0.00258935,-0.02655882,0.05358931,-0.00798834,0.08963231,0.01265741,0.05367727,0.00829675,-0.00043288,0.0045939,0.02217046,-0.00589467,-0.02016609,-0.01145116,0.01935641,0.00671855,0.01750448,0.03652468,-0.04239953,-0.00302574,0.04101517,-0.01604403,-0.04533535,-0.00093722,-0.02909846,-0.02261146,-0.00120429,-0.00880265,0.04462188,-0.05995642,0.01823854,0.00997571,-0.01599118,-0.01009392,0.06776414,-0.04790707,0.0482352,-0.04194435,0.00179565,0.05156102,-0.0220723,-0.04429708,0.02518795,-0.05708078,-0.01016288,-0.07421687,-0.02471193,-0.00345208,-0.03084598,0.01700474,-0.03716512,-0.04276166,0.02899066,0.0165397,-0.02922896,0.01708952,-0.03529521,0.08037068,-0.04381737,-0.02668175,0.04369196,-0.08047846,-0.05484424,-0.02080395,-0.01226396,-0.01144138,-0.00425863,-0.02399329,0.0237011,0.0233303,0.00306629,-0.00814174,0.03670942,0.00275734,0.04010713,-0.07788097,-0.04806714,0.01897762,-0.0209019,0.07178938,-0.02213362,0.0286129,0.05088205,-0.00013892,-0.04281049,-0.03993541,0.065685,-0.06773979,-0.00093952,0.02055035,0.0075473,0.02401766,-0.06410988,-0.00284681,0.00368032,0.05581282,0.06152018,-0.04941298,-0.00404312,0.02026797,-0.01575121,0.0271781,-0.01243827,-0.02526403,-0.05435972,0.0231814,-0.04365746,0.0179317,-0.0468494,0.01183827,-0.00485165,-0.0079297,0.0262654,-0.01414315,-0.00495373,-0.00670786,-0.02115584,0.01319541,0.00158846,-0.05464793,-0.07153786,0.02183936,-0.00285716,-0.01106182,-0.044076,0.03098834,0.02153983,-0.01551922,-0.04937238,0.03451399,-0.01187207,-0.00286815,0.02611874,0.0424377,0.03133274,-0.02652257,-0.05731727,0.01382497,0.00383549,-0.01766245,0.01107574,0.0308145,-0.00966046,0.01125748,0.02434231,0.03283923,0.0260244,0.01615953,-0.02414205,-0.0033336,-0.02557209,-0.04775422,-0.03241652,-0.03471689,-0.01629417,-0.01646392,-0.05422227,-0.03603279,0.00166038,-0.07900312,0.05411187,0.03277019,0.03584019,0.02991994,-0.01154838,-0.0017917,-0.04624443,0.01594555,-0.01088978,-0.00340102,0.0033494,-0.00126595,0.09373632,-0.01891667,-0.00267216,-0.09554698,-0.00623754,0.02180309,0.09253392,-0.0121706,-0.02498881,0.02132643,0.02496026,-0.01912983,-0.0707342,-0.06419162,0.00952289,-0.01426118,-0.024766,-0.01307788,0.03207041,-0.02347562,-0.03989465,0.00089968,-0.02577221,0.02824429,-0.01195988,-0.03288449,-0.00587367,-0.05659702,0.01212011,0.01683423,-0.0372206,0.04922377,-0.0293069,-0.00918351,0.01857849,-0.05430292,-0.0173346,-0.0001226,-0.00854597,0.08338011,0.10646164,-0.01101975,-0.07187533,0.02633175,0.03137947,-0.01234373,0.02398758,-0.03296621,0.01597684,0.01541878,-0.04583437,-0.00918559,-0.04099549,-0.03487722,0.01437325,-0.03418394,-0.05813826,0.02611333,0.02186957,0.01185703,0.04010773,0.05328314,0.01111157,-0.04829095,0.04755511,-0.03705669,-0.04190328,0.01368205,0.00558024,0.01530984,-0.00366445,-0.06467873,0.0430002,-0.04519108,0.02166518,0.02963319,0.01883766,0.00582379,0.00049509,0.00478649,0.00469448,-0.06292294,0.02750579,0.02117691,-0.04818247,-0.0305181,0.08517485,-0.01282153,0.0284451,0.02707029,-0.05443315,-0.00873095,0.05943435,0.00067344,0.03084877,0.02040225,-0.03940631,-0.04542575,0.02702897,0.0230836,0.04000421,-0.04581069,0.01322062,0.03703499,-0.02273467,-0.0061834,-0.02205444,0.08935267,0.01025518,-0.02160131,-0.07212637,-0.00555329,-0.00344949,-0.01357736,-0.02846785,-0.00513715,0.05422938,-0.03879489,-0.01227187,0.00707487,-0.03022867,-0.02546483,-0.0072449,0.02467934,0.01881978,-0.07181871,-0.00843545,0.01329915,0.01035045,0.04621276,-0.0209905,0.06031208,0.01090279,-0.0362728,0.00936632,0.00432351,-0.01024563,0.00072483,0.04571752,-0.05614648,-0.00242103,0.02651901,-0.01913858,0.04316731,-0.04147367,-0.00198905,-0.00047484,0.00001316,-0.00987299,-0.08200572,0.0169882,0.0040805,-0.07815675,0.00377901,0.04672381,-0.03143922,0.04081997,-0.01040753,0.00076963,0.02264437,0.01950853,-0.0782582,-0.00478009,-0.02360661,0.03661911,-0.08073947,-0.00040219,0.07805542,-0.03550223,-0.01882648,-0.04076313,0.00015873,0.03287151,0.06688379,-0.03400186,-0.00175597,0.00937653,0.05913967,-0.01101913,-0.01648906,0.04428772,0.03187713,0.01087149,0.04055593,-0.01600422,-0.03407151,-0.05554657,-0.00781427,-0.06355276,-0.01006895,0.01401982,-0.01813909,0.04564292,0.01189919,-0.03888111,0.01459026,-0.00750158,-0.02590359,-0.05040927,-0.02596473,-0.06796237,-0.05049768,-0.0307397,0.01481789,0.03825928,-0.01791557,-0.01597252,-0.00893612,-0.01055566,0.0187954,0.06696302,0.01894181,0.01483776,-0.0530544,-0.03425399,-0.02068729,-0.09470735,-0.08577991,-0.02514671,-0.00117949,0.11463808,-0.01279508,0.01218397,0.08888562,-0.04151708,-0.04175333,-0.05412721,0.03894814,0.03488837,0.03973304,-0.02136919,-0.02360803,-0.01459195,0.01004127,0.01533478,0.00567334,0.05457772,0.02155652,0.06281586,0.04421268,-0.0099768,-0.01998843,0.02925222,0.02411273,-0.01392806,-0.04877265,-0.03119443,0.00101811,0.03418398,0.00237572,0.04359891,0.02191813,-0.01238352,0.00352727,0.09728193,-0.01176655,-0.03651661,-0.00337958,-0.03521206,-0.03127373,-0.00167928,0.05267496,0.00971174,-0.06843086,0.06647534,-0.06933431,-0.01609429,-0.02187769,0.03995942,-0.00534699,0.03856955,-0.00367003,0.05018545,0.00934367,-0.02288664,-0.01073576,0.00090045,0.00683222,-0.00261469,-0.03664368,0.00449341,0.0162391,-0.03468947,0.00243426,0.0473865,-0.04649103,0.07372218,-0.00648144,-0.00863578,-0.02196459,-0.07329975,0.03301912,0.00709444,0.05895918,-0.01233762,-0.02276641,-0.02371211,-0.00680841,-0.00754467,-0.03890532,0.00892004,0.01862151,-0.01615103,0.06361258,-0.00909684,-0.02682213,0.00560427,-0.01616064,0.035615,0.02595366,-0.05428139,-0.00154697,0.00284572,-0.07522666,0.02101639,-0.06447466,-0.05110036,0.03674073,0.00203073,-0.06933658,-0.0341991,-0.04017815,0.01016472,-0.01953159,0.00783836,0.02670069,-0.06882548,0.0424523,0.0382065,0.02622904,0.00797587,-0.01235982,0.0169415,0.0499935,-0.02592527,0.06291573,0.0135239,0.00005484,-0.01720033,-0.04749304,0.05948328,-0.00413597,0.0163424,0.01867772,0.08327839,-0.01277243,0.0227988,-0.04545294,0.06091839,0.03069864,-0.00535601,-0.0513961,-0.06624417,8.1e-7,0.0188263,0.01294203,0.03891958,-0.00611292,0.02061767,0.01221581,-0.03940738,-0.00995456,0.02215385],"last_embed":{"tokens":338,"hash":"5g15x9"}}},"last_read":{"hash":"5g15x9","at":1752940887353},"class_name":"SmartSource","outlinks":[{"title":"C语言","target":"C语言","line":8},{"title":"内核漏洞","target":"内核漏洞","line":10},{"title":"内存","target":"内存","line":17}],"metadata":{"aliases":["Buffer Overflow"],"英文":"Buffer Overflow","tags":["网络安全/漏洞/缓冲区溢出"],"基础知识":["[[C语言]]"],"进阶知识":["[[内核漏洞]]"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,13],"#简介":[14,22],"#简介#{1}":[15,15],"#简介#{2}":[16,19],"#简介#{3}":[20,20],"#简介#{4}":[21,21],"#简介#{5}":[22,22],"#相关实例":[23,42],"#相关实例#{1}":[25,40],"#相关实例#{2}":[41,41],"#相关实例#{3}":[42,42]},"last_import":{"mtime":1747561128776,"size":1265,"at":1749024987637,"hash":"5g15x9"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/缓冲区溢出.md","last_embed":{"hash":"5g15x9","at":1752940887353}},