/* @settings

name: Task Genius
id: task-genius
settings:
    - 
        id: task-colors-heading
        title: Checkbox Status Colors
        type: heading
        level: 1
    - 
        id: task-completed-color
        title: Completed Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#4caf50'
        default-dark: '#4caf50'
    - 
        id: task-doing-color
        title: Doing Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#80dee5'
        default-dark: '#379fa7'
    - 
        id: task-in-progress-color
        title: In Progress Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#f9d923'
        default-dark: '#ffc107'
    - 
        id: task-abandoned-color
        title: Abandoned Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#eb5353'
        default-dark: '#f44336'
    - 
        id: task-planned-color
        title: Planned Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#9c27b0'
        default-dark: '#ce93d8'
    - 
        id: task-question-color
        title: Question Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#2196f3'
        default-dark: '#42a5f5'
    - 
        id: task-important-color
        title: Important Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#f44336'
        default-dark: '#ef5350'
    - 
        id: task-star-color
        title: Star Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#ffc107'
        default-dark: '#ffd54f'
    - 
        id: task-quote-color
        title: Quote Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#607d8b'
        default-dark: '#90a4ae'
    - 
        id: task-location-color
        title: Location Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#795548'
        default-dark: '#8d6e63'
    - 
        id: task-bookmark-color
        title: Bookmark Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#ff9800'
        default-dark: '#ffb74d'
    - 
        id: task-information-color
        title: Information Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#00bcd4'
        default-dark: '#26c6da'
    - 
        id: task-idea-color
        title: Idea Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#9c27b0'
        default-dark: '#ce93d8'
    - 
        id: task-pros-color
        title: Pros Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#4caf50'
        default-dark: '#66bb6a'
    - 
        id: task-cons-color
        title: Cons Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#f44336'
        default-dark: '#ef5350'
    - 
        id: task-fire-color
        title: Fire Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#ff5722'
        default-dark: '#ff7043'
    - 
        id: task-key-color
        title: Key Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#ffd700'
        default-dark: '#ffd700'
    - 
        id: task-win-color
        title: Win Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#66bb6a'
        default-dark: '#81c784'
    - 
        id: task-up-color
        title: Up Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#4caf50'
        default-dark: '#66bb6a'
    - 
        id: task-down-color
        title: Down Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#f44336'
        default-dark: '#ef5350'
    - 
        id: task-note-color
        title: Note Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#9e9e9e'
        default-dark: '#bdbdbd'
    - 
        id: task-amount-color
        title: Amount/Savings Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#8bc34a'
        default-dark: '#aed581'
    - 
        id: task-speech-color
        title: Speech Bubble Task Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#03a9f4'
        default-dark: '#29b6f6'
    - 
        id: progress-bar-colors
        title: Progress Bar Colors
        type: heading
        level: 1
    - 
        id: progress-0-color
        title: 0% Progress Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#ae431e'
        default-dark: '#ae431e'
    - 
        id: progress-25-color
        title: 25% Progress Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#e5890a'
        default-dark: '#e5890a'
    - 
        id: progress-50-color
        title: 50% Progress Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#b4c6a6'
        default-dark: '#b4c6a6'
    - 
        id: progress-75-color
        title: 75% Progress Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#6bcb77'
        default-dark: '#6bcb77'
    - 
        id: progress-100-color
        title: 100% Progress Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#4d96ff'
        default-dark: '#4d96ff'
    - 
        id: progress-background-color
        title: Progress Bar Background Color
        type: variable-themed-color
        opacity: false
        format: hex
        default-light: '#f1f1f1'
		default-dark: '#f1f1f1'
*/

.cm-task-progress-bar{display:inline-block;position:relative;margin-left:5px;margin-bottom:1px}.no-progress-bar .cm-task-progress-bar{display:none!important}.HyperMD-header .cm-task-progress-bar{display:inline-block;position:relative;margin-left:5px;margin-bottom:5px}.progress-bar-inline{height:8px;position:relative}.progress-bar-inline-empty{background-color:var(--progress-background-color)}.progress-bar-inline-0{background-color:var(--progress-0-color)}.progress-bar-inline-1{background-color:var(--progress-25-color)}.progress-bar-inline-2{background-color:var(--progress-50-color)}.progress-bar-inline-3{background-color:var(--progress-75-color)}.progress-bar-inline-complete{background-color:var(--progress-100-color)}.progress-completed{background-color:var(--task-completed-color);z-index:3}.progress-in-progress{background-color:var(--task-in-progress-color);z-index:2;position:absolute;top:0;height:100%}.progress-abandoned{background-color:var(--task-abandoned-color);z-index:1;position:absolute;top:0;height:100%}.progress-planned{background-color:var(--task-planned-color);z-index:1;position:absolute;top:0;height:100%}.progress-bar-inline-background{color:#000!important;background-color:var(--progress-background-color);border-radius:10px;flex-direction:row;justify-content:flex-start;align-items:center;width:85px;position:relative;overflow:hidden}.progress-bar-inline-background.hidden{display:none}.cm-task-progress-bar .task-status-indicator{display:inline-block;margin-right:2px}.cm-task-progress-bar .completed-indicator{color:var(--task-completed-color)}.cm-task-progress-bar .in-progress-indicator{color:var(--task-in-progress-color)}.cm-task-progress-bar .abandoned-indicator{color:var(--task-abandoned-color)}.cm-task-progress-bar .planned-indicator{color:var(--task-planned-color)}.cm-task-progress-bar.with-number{display:inline-flex;align-items:center}.HyperMD-header .cm-task-progress-bar.with-number .progress-bar-inline-background,.HyperMD-header .cm-task-progress-bar.with-number .progress-status{margin-bottom:5px}.cm-task-progress-bar.with-number .progress-bar-inline-background{margin-bottom:-2px;width:42px}.cm-task-progress-bar.with-number .progress-status{font-size:13px;margin-left:3px}.theme-dark .progress-completed{background-color:var(--task-completed-color)}.theme-dark .progress-in-progress{background-color:var(--task-in-progress-color)}.theme-dark .progress-abandoned{background-color:var(--task-abandoned-color)}.theme-dark .progress-planned{background-color:var(--task-planned-color)}.task-progress-bar-popover{width:400px}.task-states-container{margin:10px 0;border:1px solid var(--background-modifier-border);border-radius:5px;padding:10px}.task-state-row{margin-bottom:8px}.task-state-row .setting-item{border:none;padding:6px;border-radius:4px}.task-state-row .setting-item-info{margin-right:10px}.task-state-row .setting-item-control{display:flex;align-items:center;justify-content:flex-end;flex-wrap:nowrap}.task-state-row .setting-item-control input[type=text]{margin-right:8px}.task-state-row .extra-setting-button{padding:4px;width:24px;height:24px;border-radius:4px;margin-left:4px;display:flex;align-items:center;justify-content:center}.task-state-row .setting-item-control button{white-space:nowrap}.task-state-container{margin-inline-start:calc(var(--checkbox-size) * -1)}.task-state-container .task-state{padding-inline-start:var(--size-2-1);padding-inline-end:var(--size-2-2);text-decoration:none!important;cursor:pointer}.task-states-container{margin:10px 0;border:1px solid var(--background-modifier-border);border-radius:5px;padding:10px}.task-state-row{margin-bottom:8px}.task-state-row .setting-item{border:none;padding:6px;border-radius:4px}.task-state-row .setting-item-info{margin-right:10px}.task-state-row .setting-item-control{display:flex;align-items:center;justify-content:flex-end;flex-wrap:nowrap}.task-state-row .setting-item-control input[type=text]{margin-right:8px}.task-state-row .extra-setting-button{padding:4px;width:24px;height:24px;border-radius:4px;margin-left:4px;display:flex;align-items:center;justify-content:center}.task-state-row .setting-item-control button{white-space:nowrap}.task-state-container{margin-inline-start:calc(var(--checkbox-size) * -1)}.task-state-container .task-state{padding-inline-start:var(--size-2-1);padding-inline-end:var(--size-2-2);text-decoration:none!important;cursor:pointer}.task-genius-settings .settings-tabs-categorized-container{margin-top:var(--size-4-4);margin-bottom:var(--size-4-4);display:flex;flex-direction:column;gap:var(--size-4-6)}.task-genius-settings .settings-category-section{display:flex;flex-direction:column;gap:var(--size-4-2)}.task-genius-settings .settings-category-header{font-size:var(--font-ui-small);font-weight:var(--font-weight-semibold);color:var(--text-muted);text-transform:uppercase;letter-spacing:.05em;padding:0 var(--size-4-2);border-bottom:1px solid var(--background-modifier-border);padding-bottom:var(--size-4-1)}.task-genius-settings .settings-category-tabs{display:grid;grid-template-columns:repeat(3,minmax(200px,1fr));gap:var(--size-4-2)}@media (max-width: 1200px){.task-genius-settings .settings-category-tabs{grid-template-columns:repeat(2,minmax(200px,1fr))}}@media (max-width: 768px){.task-genius-settings .settings-category-tabs{grid-template-columns:1fr}}.task-genius-settings .settings-tabs-container{display:grid;grid-template-columns:repeat(2,1fr);grid-auto-rows:var(--size-4-18);margin-top:var(--size-4-4);margin-bottom:var(--size-4-4);height:fit-content;gap:var(--size-4-4)}@media (max-width: 768px){.task-genius-settings .settings-tabs-container{grid-template-columns:repeat(1,1fr)}}.task-genius-settings .settings-tab{padding:var(--size-4-3) var(--size-4-4);border-radius:var(--radius-m);cursor:pointer;display:flex;align-items:center;gap:var(--size-4-2);min-height:var(--size-4-12);border:1px solid var(--background-modifier-border);background:var(--background-primary);position:relative;overflow:hidden;transition:all .2s ease}.task-genius-settings .settings-tab:after{content:"";position:absolute;top:10px;right:-80px;width:200px;height:200px;background-color:var(--background-secondary-alt);transform:rotate(-15deg);z-index:0;opacity:.7;transition:all .3s ease;border-radius:var(--radius-m)}.task-genius-settings .settings-tab:hover:after{transform:rotate(-10deg);opacity:.9}.task-genius-settings .settings-tab-active:after{background-color:var(--interactive-accent);opacity:.3}.task-genius-settings .settings-tab-icon,.task-genius-settings .settings-tab span,.task-genius-settings .settings-tab-label{position:relative;z-index:1}.task-genius-settings .settings-category-tabs .settings-tab-icon{display:flex;align-items:center;justify-content:center;width:var(--size-4-4);height:var(--size-4-4);flex-shrink:0}.task-genius-settings .settings-category-tabs .settings-tab-icon svg{width:var(--icon-s);height:var(--icon-s)}.task-genius-settings .settings-category-tabs .settings-tab-label{font-size:var(--font-ui-small);font-weight:var(--font-weight-medium);flex:1;text-align:left}.task-genius-settings .settings-category-tabs .settings-tab:hover{background:var(--background-modifier-hover);border-color:var(--background-modifier-border-hover);transform:translateY(-1px);box-shadow:var(--shadow-m)}.task-genius-settings .settings-category-tabs .settings-tab-active{background:var(--interactive-accent);color:var(--text-on-accent);border-color:var(--interactive-accent);box-shadow:var(--shadow-m);font-weight:var(--font-weight-semibold)}.task-genius-settings .settings-category-tabs .settings-tab-active:hover{background:var(--interactive-accent-hover);border-color:var(--interactive-accent-hover);transform:translateY(-1px)}.task-genius-settings .settings-tab:hover{background-color:var(--background-modifier-hover)}.task-genius-settings .settings-tab-active{background-color:var(--background-modifier-border-hover);font-weight:bold}.task-genius-settings .settings-tab-sections{overflow:hidden}.task-genius-settings .settings-tab-section{display:none}.task-genius-settings .settings-tab-section-active{display:block}.task-genius-settings .settings-tab-section-header{display:flex;align-items:center;justify-content:flex-end;margin-top:var(--size-4-2);margin-bottom:var(--size-4-2)}.task-genius-settings .settings-tab-section-header .header-button{display:flex;align-items:center;justify-content:center;gap:4px;font-size:var(--font-ui-small)}.task-genius-settings .settings-tab-section-header .header-button-icon{--icon-size: 16px;display:flex;align-items:center;justify-content:center}.task-genius-settings .settings-tab[data-tab-id=general]{display:none}.task-genius-settings .settings-tabs-categorized-container{display:flex}.task-genius-settings:has(.settings-tab-section-active:not([data-tab-id="general"])) .settings-tabs-categorized-container{display:none}.task-genius-settings .settings-tabs-container{display:none}.task-genius-settings:has(.settings-tab-active[data-tab-id="general"]) .settings-tabs-container{display:grid}.task-genius-settings-header{display:block}.task-genius-settings:has(.settings-tab-section-active:not([data-tab-id="general"])) .task-genius-settings-header{display:none}.expression-examples{margin-top:8px;border-radius:5px}.expression-example-item{margin-bottom:var(--size-4-3);padding:var(--size-4-2);padding-left:var(--size-4-3);padding-right:var(--size-4-3);border-radius:var(--radius-s);display:flex;flex-direction:column;gap:6px;border:1px solid var(--background-modifier-border)}.expression-example-name{font-weight:bold}.expression-example-code{padding:4px 8px;background-color:var(--background-secondary);border-radius:4px;font-family:var(--font-monospace);font-size:.9em;overflow-wrap:break-word;user-select:text}.expression-example-use{align-self:flex-end;margin-top:4px}.custom-format-textarea{height:200px;width:100%;font-family:var(--font-monospace);resize:vertical}.custom-format-preview-container{margin-bottom:var(--size-4-3);padding:var(--size-4-3);border-radius:var(--radius-s);background-color:var(--background-secondary);display:flex;flex-direction:column}.custom-format-preview-label{font-weight:bold;margin-bottom:var(--size-4-2);color:var(--text-muted)}.custom-format-preview-content{padding:var(--size-4-2);background-color:var(--background-primary);border-radius:var(--radius-s);font-family:var(--font-interface)}.custom-format-placeholder-info{margin-top:var(--size-4-2);margin-bottom:var(--size-4-2);user-select:text}.custom-format-preview-error,.expression-preview-error{color:var(--text-error)}.expression-example-preview{margin-top:var(--size-4-2);padding:var(--size-4-2);background-color:var(--background-primary-alt);border-radius:var(--radius-s);font-size:.9em}.preset-filters-container{margin-top:10px;padding:8px;border-radius:5px;border:1px solid var(--background-modifier-border)}.preset-filter-row{margin-bottom:5px;border-radius:4px;padding-top:var(--size-4-2);padding-left:var(--size-4-2);padding-right:var(--size-4-2);transition:background-color .2s ease}.preset-filter-row:hover{background-color:var(--background-secondary-alt)}.no-presets-message{font-style:italic;color:var(--text-muted);text-align:center;padding:15px}.preset-saved-message{color:var(--text-accent);font-weight:bold;text-align:center;padding:5px;margin-top:5px;animation:fadeIn .3s ease-in-out}.task-filter-save-preset{margin-top:15px;padding:10px;border-radius:5px;background-color:var(--background-secondary-alt)}.tg-modal-button-container{display:flex;justify-content:flex-end;gap:10px;margin-top:20px}.tg-modal-button-container button{padding:6px 12px;border-radius:4px;font-size:14px;font-weight:500;cursor:pointer}.tg-modal-button-container button.mod-warning{background-color:var(--background-modifier-error);color:#fff}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}.modal-workflow-definition{max-width:800px;width:90vw}.modal-stage-definition{max-width:800px;width:90vw}.workflow-container{border:1px solid var(--background-modifier-border);border-radius:5px;padding:15px;max-height:500px;overflow-y:auto;background-color:var(--background-primary);box-shadow:0 1px 4px #0000000d}.workflow-row{margin-bottom:15px;padding:12px;border-radius:6px;background-color:var(--background-secondary-alt);box-shadow:0 1px 3px #00000014;border-left:3px solid var(--interactive-accent)}.workflow-row .setting-item{border:none;padding:0}.workflow-row .setting-item-info{padding:0!important}.workflow-row .setting-item-name{font-size:16px;font-weight:600;color:var(--text-normal)}.workflow-row .setting-item-description{font-size:13px;color:var(--text-muted);margin-top:4px}.workflow-stages-info{margin-top:12px;padding:8px 0 0;border-top:1px solid var(--background-modifier-border)}.workflow-stages-list{list-style-type:none;display:flex;flex-wrap:wrap;gap:var(--size-2-2);padding:0;margin:0}.workflow-stage-item{padding:4px 8px;border-radius:4px;font-size:12px;display:inline-flex;align-items:center;background-color:var(--background-modifier-border)}.workflow-stage-cycle{background-color:var(--task-in-progress-color);color:var(--text-on-accent)}.workflow-stage-terminal{background-color:var(--task-completed-color);color:var(--text-on-accent)}.no-workflows-message{font-style:italic;color:var(--text-muted);text-align:center;padding:15px}.workflow-form{margin-bottom:20px}.workflow-stages-section{margin-top:20px;border-top:1px solid var(--background-modifier-border);padding-top:15px}.workflow-stages-section h2{margin-top:0;margin-bottom:15px;font-size:1.3em;color:var(--text-normal)}.workflow-stages-container{margin-top:15px}.workflow-stages-container .workflow-stages-list{display:block;flex-wrap:unset;gap:unset}.workflow-stages-container .workflow-stage-item{display:block;margin-bottom:10px;padding:0;background-color:transparent}.workflow-buttons{display:flex;justify-content:flex-end;gap:10px;margin-top:20px;padding-top:10px;border-top:1px solid var(--background-modifier-border)}.workflow-save-button,.workflow-cancel-button,.workflow-add-stage-button{padding:6px 12px;border-radius:4px;cursor:pointer}.workflow-save-button.mod-cta{background-color:var(--interactive-accent);color:var(--text-on-accent)}.workflow-cancel-button{background-color:var(--background-modifier-border);color:var(--text-normal)}.workflow-add-stage-button{background-color:var(--interactive-accent);color:var(--text-on-accent);margin-top:10px}.no-stages-message{font-style:italic;color:var(--text-muted);text-align:center;padding:15px}.workflow-stage-header{display:flex;align-items:center;justify-content:space-between;padding:8px 12px;background-color:var(--background-secondary);border-radius:4px;margin-bottom:8px;box-shadow:0 1px 3px #0000001a}.workflow-stage-name{font-weight:600;flex:1;margin-right:10px}.workflow-stage-actions{display:flex;gap:5px}.workflow-stage-edit,.workflow-stage-move-up,.workflow-stage-move-down,.workflow-stage-delete{padding:3px 8px;border-radius:3px;background-color:var(--background-modifier-border);cursor:pointer;font-size:12px;border:none}.workflow-stage-edit:hover,.workflow-stage-move-up:hover,.workflow-stage-move-down:hover{background-color:var(--interactive-accent);color:var(--text-on-accent)}.workflow-stage-type-badge{display:inline-block;padding:2px 6px;margin-left:8px;border-radius:3px;font-size:10px;text-transform:uppercase;font-weight:600}.workflow-stage-type-linear{background-color:var(--background-modifier-border)}.workflow-stage-type-cycle{background-color:var(--task-in-progress-color);color:var(--text-on-accent)}.workflow-stage-type-terminal{background-color:var(--task-completed-color);color:var(--text-on-accent)}.workflow-substages-list{padding:0 0 0 var(--size-4-6);margin-top:var(--size-4-2);margin-bottom:var(--size-4-2);border-left:2px solid var(--background-modifier-border)}.substage-settings-container{width:100%}.stage-type-settings{margin-top:20px;border:1px solid var(--background-modifier-border);border-radius:4px;padding:15px;background-color:var(--background-primary)}.substages-section,.can-proceed-to-section{margin-top:20px;padding-top:15px;border-top:1px solid var(--background-modifier-border)}.substages-container,.can-proceed-to-container{margin-top:15px;padding:10px;border-radius:4px}.substages-list,.can-proceed-list{list-style-type:none;padding:0;margin:0}.substage-name-container{display:flex;gap:10px;align-items:center;flex:1}.substage-name-container input{padding:4px 8px;border-radius:3px;border:1px solid var(--background-modifier-border);background-color:var(--background-primary)}.substage-next-container{display:flex;align-items:center;gap:5px;margin-left:10px}.substage-remove-button,.can-proceed-remove-button{color:var(--text-normal);border-radius:3px;padding:2px 5px;cursor:pointer;border:none}.substage-remove-button:hover,.can-proceed-remove-button:hover{background-color:var(--background-modifier-error);color:var(--text-on-accent)}.add-substage-button,.add-can-proceed-button{background-color:var(--interactive-accent);color:var(--text-on-accent);padding:4px 10px;border-radius:4px;margin-top:10px;cursor:pointer;border:none}.add-can-proceed-container{display:flex;gap:10px;align-items:flex-end}.add-can-proceed-select{flex:1;padding:4px 8px;border-radius:3px;border:1px solid var(--background-modifier-border)}.stage-buttons{display:flex;justify-content:flex-end;gap:10px;margin-top:20px;padding-top:10px;border-top:1px solid var(--background-modifier-border)}.stage-save-button,.stage-cancel-button{padding:6px 12px;border-radius:4px;cursor:pointer;border:none}.stage-save-button.mod-cta{background-color:var(--interactive-accent);color:var(--text-on-accent)}.stage-cancel-button{background-color:var(--background-modifier-border);color:var(--text-normal)}.stage-error-message{color:var(--background-modifier-error);font-weight:bold;text-align:center;margin-top:10px;padding:8px;border-radius:4px}.task-workflow-tag{display:inline-block;padding:2px 5px;border-radius:3px;margin-left:5px;font-size:12px;background-color:var(--background-secondary-alt)}.task-workflow-stage{margin-left:5px;color:var(--text-accent)}.task-workflow-substage{font-size:11px;color:var(--text-muted)}.task-workflow-history{margin-left:20px;font-size:12px;color:var(--text-muted)}.task-workflow-timestamp{color:var(--text-faint)}.setting-item-control span[class^=workflow-stage-name-]{display:inline-block;padding:2px 6px;border-radius:3px;font-size:12px;font-weight:500;margin-right:5px}.setting-item-control .workflow-stage-name-cycle{background-color:var(--task-in-progress-color);color:var(--text-on-accent)}.setting-item-control .workflow-stage-name-terminal{background-color:var(--task-completed-color);color:var(--text-on-accent)}.workflow-stage-item{margin-right:4px}.workflow-stages-container .workflow-stage-header{padding:8px 12px;background-color:var(--background-secondary);border-radius:4px;box-shadow:0 1px 3px #0000001a;margin-bottom:8px}.workflow-stages-container .workflow-stage-type-badge{display:inline-block;padding:2px 6px;margin-left:8px;border-radius:3px;font-size:10px;text-transform:uppercase;font-weight:600}.workflow-substages-list{list-style-type:none;padding:0 0 0 20px;margin:5px 0 10px;border-left:2px solid var(--background-modifier-border)}.workflow-add-stage-button,.stage-save-button.mod-cta,.workflow-save-button.mod-cta{background-color:var(--interactive-accent);color:var(--text-on-accent);padding:6px 15px;border-radius:4px;font-weight:500;border:none;cursor:pointer;box-shadow:0 2px 4px #0000001a;transition:all .2s ease;text-align:center}.workflow-add-stage-button:hover,.stage-save-button.mod-cta:hover,.workflow-save-button.mod-cta:hover{background-color:var(--interactive-accent-hover);box-shadow:0 3px 6px #00000026;transform:translateY(-1px)}.workflow-stage-move-up,.workflow-stage-move-down,.workflow-stage-edit,.workflow-stage-delete{border:none;background-color:var(--background-modifier-border);padding:3px 8px;border-radius:3px;font-size:12px;cursor:pointer;transition:all .2s ease}.workflow-stage-move-up:hover,.workflow-stage-move-down:hover,.workflow-stage-edit:hover{background-color:var(--interactive-accent);color:var(--text-on-accent)}.workflow-stage-delete:hover{background-color:var(--background-modifier-error);color:var(--text-on-accent)}.substage-item{display:flex;justify-content:flex-end;align-items:center;padding:6px 0;margin-bottom:5px;border-radius:4px}.substage-name-container input{background-color:var(--background-primary);border:1px solid var(--background-modifier-border);padding:4px 8px;border-radius:3px;font-size:13px}.substage-name-container input:focus{border-color:var(--interactive-accent);outline:none}.no-stages-message,.no-workflows-message,.no-substages-message,.no-can-proceed-message{font-style:italic;color:var(--text-muted);padding:15px;text-align:center;background-color:var(--background-secondary-alt);border-radius:5px;margin:10px 0}.rewards-levels-container,.rewards-items-container{margin-top:10px;padding:15px;border-radius:5px;border:1px solid var(--background-modifier-border);background-color:var(--background-secondary)}.rewards-level-row .setting-item-info,.rewards-item-row .setting-item-info{display:none}.rewards-item-row.setting-item{border-top:0}.rewards-level-row .setting-item-control,.rewards-item-row .setting-item-control{display:flex;flex-wrap:wrap;gap:10px;align-items:center}.rewards-level-row .setting-item-control input[type=text]{flex:1;min-width:100px}.rewards-item-row .setting-item-control .input-container{flex:1;min-width:150px}.rewards-item-row .setting-item-control textarea{width:100%;min-height:40px;resize:vertical}.rewards-item-row .setting-item-control .dropdown{min-width:120px}.rewards-level-row .setting-item-control button,.rewards-item-row .setting-item-control button{margin-left:auto}.rewards-item-divider{border:none;height:1px;background-color:var(--background-modifier-border);margin-top:15px;margin-bottom:15px}.setting-item.sort-criterion-row .setting-item-info{display:none}.setting-item.sort-criterion-row select.dropdown{flex:1}.view-management-list{margin:10px 0;border:1px solid var(--background-modifier-border);border-radius:5px;padding:10px}.view-edit-button,.view-copy-button,.view-order-button,.view-delete-button{padding:4px;width:24px;height:24px;border-radius:4px;margin-left:4px;display:flex;align-items:center;justify-content:center}.view-copy-button{color:var(--interactive-accent)}.view-copy-button:hover{background-color:var(--interactive-accent);color:var(--text-on-accent)}.view-delete-button{color:var(--text-error)}.view-delete-button:hover{background-color:var(--background-modifier-error);color:var(--text-on-accent)}.view-icon{margin-right:8px;--icon-size: 16px}.copy-mode-info{margin:10px 0;padding:12px;background-color:var(--background-secondary-alt);border-radius:5px;border-left:3px solid var(--interactive-accent)}.copy-mode-info p{margin:4px 0}.tasks-compatibility-warning{display:flex;align-items:flex-start;gap:var(--size-4-3);padding:var(--size-4-4);margin-bottom:var(--size-4-4);background-color:hsl(var(--accent-h),var(--accent-s),var(--accent-l),.5);border:1px solid hsl(var(--accent-h),var(--accent-s),var(--accent-l),.5);border-radius:var(--radius-m);color:var(--text-on-accent)}.tasks-warning-icon{font-size:20px;line-height:1;flex-shrink:0}.tasks-warning-content{flex:1;display:flex;flex-direction:column;gap:var(--size-2-2)}.tasks-warning-title{font-weight:600;font-size:var(--font-ui-medium)}.tasks-warning-text{color:var(--text-on-accent);font-size:var(--font-ui-small);line-height:1.4}.tasks-warning-text a{color:var(--text-on-accent);text-decoration:underline}.tasks-warning-text a:hover{color:var(--text-on-accent)}.task-genius-format-examples{display:flex;flex-direction:column;gap:var(--size-2-3);padding:var(--size-4-3);margin:var(--size-4-3) 0;border-radius:var(--radius-m);background-color:var(--background-secondary-alt);border:1px solid var(--background-modifier-border)}.task-genius-format-examples strong{font-size:var(--font-ui-medium);font-weight:600;color:var(--text-normal);margin-bottom:var(--size-2-1)}.task-genius-format-examples span{font-family:var(--font-monospace);font-size:var(--font-ui-smaller);line-height:1.5;color:var(--text-muted);padding:var(--size-2-1) var(--size-2-3);background-color:var(--background-primary);border-radius:var(--radius-s);border:1px solid var(--background-modifier-border);margin:var(--size-2-1) 0}.task-genius-format-examples span:first-of-type{margin-top:0}.task-genius-format-examples span:last-of-type{margin-bottom:0}.project-path-mappings-container,.project-metadata-mappings-container{margin-top:10px}.project-path-mapping-row,.project-metadata-mapping-row{border:1px solid var(--background-modifier-border);border-radius:6px;margin-bottom:10px;padding:10px}.no-mappings-message{color:var(--text-muted);font-style:italic;text-align:center;padding:20px}.task-project-tg{opacity:.8;font-style:italic;border-left:2px solid var(--color-accent);padding-left:4px}.task-project-tg:before{content:"\1f517";margin-right:2px;font-size:.8em}.project-readonly{opacity:.8}.project-readonly input{background-color:var(--background-modifier-border);cursor:not-allowed}.project-source-indicator{font-size:var(--font-ui-smaller);color:var(--text-muted);font-style:italic;margin-top:4px}.tg-status-icon{display:inline-flex;align-items:center;vertical-align:middle;margin-right:var(--size-2-3);margin-top:calc(-1 * var(--size-2-1))}.tg-icons-container{display:flex;gap:var(--size-2-2);flex-wrap:wrap;align-items:center;justify-content:center}.tg-icons-container .tg-status-icon{margin-right:0;margin-top:0}.global-filter-container{margin-bottom:20px;padding:10px;border:1px solid var(--background-modifier-border);border-radius:6px;background-color:var(--background-secondary)}.beta-test-warning-banner{display:flex;align-items:flex-start;gap:12px;padding:16px;margin-bottom:20px;background-color:var(--background-modifier-warning);border:1px solid var(--color-orange);border-radius:8px}.beta-warning-icon{font-size:20px;line-height:1;flex-shrink:0;margin-top:2px}.beta-warning-content{flex:1;min-width:0}.beta-warning-title{font-weight:600;font-size:14px;color:var(--text-normal);margin-bottom:8px}.beta-warning-text{font-size:13px;line-height:1.4;color:var(--text-muted)}.confirm-modal-buttons{display:flex;gap:var(--size-4-3);justify-content:flex-end;margin-top:var(--size-4-3)}.habit-edit-dialog{max-width:600px;width:100%}.habit-edit-dialog .modal-content{padding:20px}.habit-edit-dialog .habit-type-selector{margin-bottom:20px}.habit-edit-dialog .habit-type-description{font-weight:600;margin-bottom:10px}.habit-edit-dialog .habit-type-grid{display:grid;grid-template-columns:repeat(2,1fr);gap:10px}@media (max-width: 500px){.habit-edit-dialog .habit-type-grid{grid-template-columns:1fr}}.habit-edit-dialog .habit-type-item{display:flex;padding:12px;border-radius:var(--radius-m);border:1px solid var(--background-modifier-border);background-color:var(--background-secondary);cursor:pointer;transition:all .2s ease}.habit-edit-dialog .habit-type-item:hover{background-color:var(--background-modifier-hover)}.habit-edit-dialog .habit-type-item.selected{border-color:var(--interactive-accent);background-color:var(--interactive-accent-hover)}.habit-edit-dialog .habit-type-icon{display:flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background-color:var(--background-primary);margin-right:10px}.habit-edit-dialog .habit-type-icon svg{width:20px;height:20px;color:var(--text-normal)}.habit-edit-dialog .habit-type-text{flex:1;display:flex;flex-direction:column}.habit-edit-dialog .habit-type-name{font-weight:600;margin-bottom:4px}.habit-edit-dialog .habit-type-desc{font-size:.85em;color:var(--text-muted)}.habit-edit-dialog .habit-common-form,.habit-edit-dialog .habit-type-form{margin-bottom:20px}.habit-edit-dialog .habit-icon-preview{display:flex;align-items:center;justify-content:center;width:30px;height:30px;margin-left:10px;background-color:var(--background-primary);border-radius:50%}.habit-edit-dialog .habit-icon-preview svg{width:18px;height:18px}.habit-edit-dialog .habit-mapping-container{border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);padding:10px;margin-bottom:10px;margin-top:5px}.habit-edit-dialog .habit-mapping-row{display:flex;align-items:center;margin-bottom:8px}.habit-edit-dialog .habit-mapping-key{width:80px;margin-right:5px;font-size:.9em}.habit-edit-dialog .habit-mapping-arrow{margin:0 10px;color:var(--text-muted)}.habit-edit-dialog .habit-mapping-value{flex:1;font-size:.9em;margin-right:var(--size-4-4)}.habit-edit-dialog .habit-mapping-delete{background:none;border:none;color:var(--text-error);cursor:pointer;font-size:1.2em;padding:0 8px}.habit-edit-dialog .habit-add-mapping-button{background-color:var(--interactive-accent);color:var(--text-on-accent);border:none;border-radius:var(--radius-s);padding:6px 12px;cursor:pointer;font-size:.9em}.habit-edit-dialog .habit-events-container{border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);padding:10px;margin-bottom:10px;margin-top:5px}.habit-edit-dialog .habit-event-row{display:flex;margin-bottom:8px;gap:5px}.habit-edit-dialog .habit-event-name{width:120px;font-size:.9em}.habit-edit-dialog .habit-event-details{flex:1;font-size:.9em}.habit-edit-dialog .habit-event-property{width:120px;font-size:.9em}.habit-edit-dialog .habit-event-delete{background:none;border:none;color:var(--text-error);cursor:pointer;font-size:1.2em;padding:0 8px}.habit-edit-dialog .habit-add-event-button{background-color:var(--interactive-accent);color:var(--text-on-accent);border:none;border-radius:var(--radius-s);padding:6px 12px;cursor:pointer;font-size:.9em}.habit-edit-dialog .habit-edit-buttons{display:flex;justify-content:flex-end;gap:10px;margin-top:20px}.habit-edit-dialog .habit-cancel-button{background-color:var(--background-modifier-hover);color:var(--text-normal);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);padding:8px 16px;cursor:pointer}.habit-edit-dialog .habit-save-button{background-color:var(--interactive-accent);color:var(--text-on-accent);border:none;border-radius:var(--radius-s);padding:8px 16px;cursor:pointer}.habit-edit-dialog input[type=text],.habit-edit-dialog input[type=number]{background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);padding:6px;color:var(--text-normal)}.habit-edit-dialog .habit-type-item.selected .habit-type-desc,.habit-edit-dialog .habit-type-item.selected .habit-type-name{color:var(--text-on-accent)}.habit-list-container{padding:12px;width:100%}.habit-settings-container{padding-top:12px;border-top:1px solid var(--background-modifier-border)}.habit-add-button-container{display:flex;justify-content:flex-end;margin-bottom:16px}.habit-add-button{display:flex;align-items:center;gap:6px;padding:6px 12px;background-color:var(--interactive-accent);color:var(--text-on-accent);border-radius:var(--radius-s);cursor:pointer;font-size:14px}.habit-add-button svg{width:16px;height:16px}.habit-empty-state{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:200px;text-align:center;padding:20px;border:1px dashed var(--background-modifier-border);border-radius:var(--radius-m);background-color:var(--background-secondary)}.habit-empty-state h2{margin:0 0 10px;font-size:1.2em;color:var(--text-normal)}.habit-empty-state p{margin:0;color:var(--text-muted)}.habit-items-container{display:flex;flex-direction:column;gap:10px}.habit-item{display:flex;align-items:center;padding:12px;border-radius:var(--radius-m);background-color:var(--background-secondary);border:1px solid var(--background-modifier-border);transition:background-color .2s ease;cursor:pointer;height:7.5rem}.habit-item:hover{background-color:var(--background-modifier-hover)}.habit-item-icon{--icon-size: 20px;display:flex;align-items:center;justify-content:center;width:48px;height:48px;border-radius:50%;background-color:var(--background-primary);margin-right:12px}.habit-item-icon svg{color:var(--text-normal)}.habit-item-info{flex:1;min-width:0}.habit-item-name{font-weight:600;margin-bottom:4px;font-size:16px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.habit-item-description{color:var(--text-muted);font-size:12px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-bottom:4px}.habit-item-type{display:inline-block;font-size:11px;padding:2px 6px;border-radius:var(--radius-s);background-color:var(--background-modifier-border);color:var(--text-muted)}.habit-item-actions{display:flex;gap:8px;margin-left:12px}.habit-edit-button,.habit-delete-button{display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:50%;background-color:var(--background-primary);cursor:pointer;padding:0;border:1px solid var(--background-modifier-border)}.habit-edit-button:hover,.habit-delete-button:hover{background-color:var(--background-modifier-hover)}.habit-edit-button svg,.habit-delete-button svg{width:16px;height:16px;color:var(--text-muted)}.habit-delete-button:hover svg{color:var(--text-error)}.habit-delete-modal-buttons{display:flex;justify-content:flex-end;gap:10px;margin-top:20px}.habit-delete-button-confirm{background-color:var(--text-error);color:#fff;border:none;border-radius:var(--radius-s);padding:8px 16px;cursor:pointer}.ics-settings-container{max-width:800px;margin:0 auto}.ics-header-container{margin-bottom:2rem;border-bottom:1px solid var(--background-modifier-border);padding-bottom:1rem}.ics-back-button{background:var(--interactive-accent);color:var(--text-on-accent);border:none;padding:.5rem 1rem;border-radius:6px;cursor:pointer;margin-bottom:1rem;font-size:.9em;transition:all .2s ease}.ics-back-button:hover{background:var(--interactive-accent-hover);transform:translateY(-1px)}.ics-description{color:var(--text-muted);margin-top:.5rem;line-height:1.5}.ics-global-settings{margin-bottom:2rem;padding:1.5rem;border:1px solid var(--background-modifier-border);border-radius:8px;background:var(--background-secondary)}.ics-sources-list{margin-top:1.5rem}.ics-sources-list h3{margin-bottom:1rem;color:var(--text-normal)}.ics-source-item{margin-bottom:1rem;padding:1.5rem;border:1px solid var(--background-modifier-border);border-radius:8px;background:var(--background-primary);transition:all .2s ease}.ics-source-item:hover{border-color:var(--interactive-accent);box-shadow:0 2px 8px #0000001a}.ics-source-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.ics-source-title strong{font-size:1.1em;color:var(--text-normal)}.ics-source-status{padding:.3rem .8rem;border-radius:12px;font-size:.75em;font-weight:600;text-transform:uppercase;letter-spacing:.5px}.status-enabled{background:var(--color-green);color:#fff}.status-disabled{background:var(--color-red);color:#fff}.ics-source-details{margin-bottom:1.5rem;font-size:.9em;color:var(--text-muted);line-height:1.4}.ics-source-details div{margin-bottom:.4rem}.ics-source-actions{display:flex;justify-content:space-between;align-items:center;gap:1rem}.primary-actions,.secondary-actions{display:flex;gap:.5rem}.ics-source-actions button{padding:.5rem 1rem;border:1px solid var(--background-modifier-border);border-radius:6px;background:var(--background-secondary);color:var(--text-normal);font-size:.85em;cursor:pointer;transition:all .2s ease;min-width:80px;white-space:nowrap}.ics-source-actions button:hover{background:var(--background-modifier-hover);border-color:var(--interactive-accent);transform:translateY(-1px)}.ics-source-actions button.mod-cta{background:var(--interactive-accent);color:var(--text-on-accent);border-color:var(--interactive-accent)}.ics-source-actions button.mod-cta:hover{background:var(--interactive-accent-hover)}.ics-source-actions button.mod-warning{background:var(--color-red);color:#fff;border-color:var(--color-red)}.ics-source-actions button.mod-warning:hover{background:var(--color-red);opacity:.8}.ics-source-actions button:disabled{opacity:.5;cursor:not-allowed;transform:none}.ics-source-actions button.syncing{color:var(--interactive-accent)}.ics-source-actions button.success{background:var(--color-green);color:#fff;border-color:var(--color-green)}.ics-source-actions button.error{background:var(--color-red);color:#fff;border-color:var(--color-red)}.ics-add-source-container{margin-top:2rem;text-align:center;padding:2rem;border:2px dashed var(--background-modifier-border);border-radius:8px;background:var(--background-secondary);transition:all .2s ease}.ics-add-source-container:hover{border-color:var(--interactive-accent);background:var(--background-modifier-hover)}.ics-add-source-container button{background:var(--interactive-accent);color:var(--text-on-accent);border:none;padding:.8rem 1.5rem;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s ease;font-size:.95em}.ics-add-source-container button:hover{background:var(--interactive-accent-hover);transform:translateY(-2px)}.ics-test-container{margin-top:1rem;text-align:center;padding:1rem;border:1px solid var(--background-modifier-border);border-radius:8px;background:var(--background-modifier-form-field)}.ics-test-button{background:var(--color-orange);color:#fff;border:none;padding:.6rem 1.2rem;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s ease;font-size:.9em}.ics-test-button:hover{background:var(--color-orange);opacity:.8;transform:translateY(-1px)}.ics-empty-state{text-align:center;padding:3rem 2rem;color:var(--text-muted);font-style:italic;background:var(--background-secondary);border-radius:8px;border:1px solid var(--background-modifier-border)}.ics-source-modal .modal-content{max-width:600px;max-height:80vh;overflow-y:auto}.auth-field{margin-top:.5rem}.modal-button-container{display:flex;gap:.5rem;justify-content:flex-end;margin-top:1.5rem;padding-top:1rem;border-top:1px solid var(--background-modifier-border)}.modal-button-container button{padding:.5rem 1rem;border-radius:6px;font-size:.9em;min-width:80px}@media (max-width: 768px){.ics-source-header{flex-direction:column;align-items:flex-start;gap:.5rem}.ics-source-actions{flex-direction:column;gap:.5rem}.primary-actions,.secondary-actions{width:100%;justify-content:space-between}.ics-source-actions button{flex:1;min-width:auto}}@media (max-width: 480px){.ics-source-item{padding:1rem}.primary-actions,.secondary-actions{flex-direction:column}.ics-source-actions button{width:100%;margin-bottom:.3rem}.modal-button-container{flex-direction:column}.modal-button-container button{width:100%}}.text-replacements-list{margin:1rem 0}.text-replacements-empty{text-align:center;padding:2rem;color:var(--text-muted);font-style:italic;background:var(--background-secondary);border-radius:6px;border:1px dashed var(--background-modifier-border)}.text-replacement-rule{margin-bottom:1rem;padding:1rem;border:1px solid var(--background-modifier-border);border-radius:6px;background:var(--background-primary);transition:all .2s ease}.text-replacement-rule:hover{border-color:var(--interactive-accent);box-shadow:0 2px 4px #0000001a}.text-replacement-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:.8rem}.text-replacement-header strong{color:var(--text-normal);font-size:1em}.text-replacement-status{padding:.2rem .6rem;border-radius:10px;font-size:.7em;font-weight:600;text-transform:uppercase;letter-spacing:.5px}.text-replacement-status.enabled{background:var(--color-green);color:#fff}.text-replacement-status.disabled{background:var(--color-red);color:#fff}.text-replacement-details{margin-bottom:1rem;font-size:.85em;color:var(--text-muted);line-height:1.4}.text-replacement-details div{margin-bottom:.3rem}.text-replacement-pattern{font-family:var(--font-monospace);background:var(--background-modifier-form-field);padding:.2rem .4rem;border-radius:3px;display:inline-block;margin-left:.5rem}.text-replacement-replacement{font-family:var(--font-monospace);background:var(--background-modifier-form-field);padding:.2rem .4rem;border-radius:3px;display:inline-block;margin-left:.5rem}.text-replacement-actions{display:flex;gap:.5rem;flex-wrap:wrap}.text-replacement-actions button{padding:.4rem .8rem;border:1px solid var(--background-modifier-border);border-radius:4px;background:var(--background-secondary);color:var(--text-normal);font-size:.8em;cursor:pointer;transition:all .2s ease}.text-replacement-actions button:hover{background:var(--background-modifier-hover);border-color:var(--interactive-accent)}.text-replacement-actions button.mod-cta{background:var(--interactive-accent);color:var(--text-on-accent);border-color:var(--interactive-accent)}.text-replacement-actions button.mod-warning{background:var(--color-red);color:#fff;border-color:var(--color-red)}.text-replacement-add{margin-top:1rem;text-align:center}.text-replacement-add button{background:var(--interactive-accent);color:var(--text-on-accent);border:none;padding:.6rem 1.2rem;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s ease}.text-replacement-add button:hover{background:var(--interactive-accent-hover);transform:translateY(-1px)}.text-replacement-modal .modal-content{max-width:700px;max-height:85vh;overflow-y:auto}.test-output{margin-top:.5rem;padding:.8rem;background:var(--background-modifier-form-field);border-radius:4px;border:1px solid var(--background-modifier-border);font-family:var(--font-monospace);font-size:.9em}.test-result{font-weight:500}.text-replacement-modal ul{margin:.5rem 0;padding-left:1.5rem}.text-replacement-modal li{margin-bottom:.5rem;line-height:1.4}.text-replacement-modal code{background:var(--background-modifier-form-field);padding:.1rem .3rem;border-radius:3px;font-family:var(--font-monospace);font-size:.85em}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.ics-source-actions button.syncing:before{content:"";display:inline-block;margin-right:.3rem;animation:spin 1s linear infinite}.ics-text-replacement-modal,.ics-source-modal{max-width:1000px;max-height:90vh;padding-right:0}.ics-text-replacement-modal .modal-content,.ics-source-modal .modal-content{padding-right:var(--size-4-2)}.task-filter-panel{padding:var(--size-4-4) var(--size-4-4);padding-bottom:var(--size-2-2);padding-left:var(--size-4-8);background-color:var(--background-primary);border-top:1px solid var(--background-modifier-border);display:flex;flex-direction:column;max-height:300px;overflow-y:auto}.task-filter-active{color:var(--color-accent-2);font-weight:bold}.task-filter-panel>.setting-item{border-top:unset}.task-filter-header-container{display:flex;align-items:center;justify-content:flex-end}.task-filter-title{font-size:var(--font-ui-small);color:var(--text-normal)}.task-filter-options{display:flex;flex-direction:column;gap:10px}.task-filter-section{display:flex;flex-direction:column}.task-filter-section h3{font-size:14px;margin:5px 0;color:var(--text-muted)}.task-filter-section:last-child{border-bottom:unset}.task-filter-option{display:flex;align-items:center;gap:6px}.task-filter-option input[type=checkbox]{margin:0}.task-filter-option label{font-size:13px;color:var(--text-normal)}.task-filter-buttons{display:flex;justify-content:flex-end;gap:8px;margin-top:8px;padding-top:8px;border-top:1px solid var(--background-modifier-border)}.task-filter-apply,.task-filter-close{padding:6px 12px;border-radius:4px;font-size:12px;cursor:pointer}.task-filter-apply{background-color:var(--interactive-accent);color:var(--text-on-accent)}.task-filter-reset{background-color:var(--background-modifier-border);color:var(--text-normal)}.task-filter-close{background-color:var(--background-secondary);color:var(--text-normal)}.task-filter-query-input{width:100%;min-width:250px;border-radius:4px;padding:8px 12px;font-family:var(--font-monospace);font-size:14px}.task-filter-query-input:focus{box-shadow:0 0 0 2px var(--interactive-accent);outline:none}.task-filter-section .setting-item-description{margin-top:5px;margin-bottom:10px;font-size:12px;color:var(--text-muted);line-height:1.4}.task-filter-options{max-height:70vh;overflow-y:auto;padding-right:5px}.task-filter-options{margin-bottom:10px;padding-top:var(--size-4-4)}.filter-group-separator{display:flex;align-items:center;justify-content:center;margin:var(--size-2-2) 0;color:var(--text-muted);font-size:var(--font-ui-smaller)}.filter-group-separator:before,.filter-group-separator:after{content:"";flex-grow:1;height:1px;background-color:var( --background-modifier-border );margin:0 var(--size-2-1)}.drag-handle{cursor:grab;display:flex;align-items:center;justify-content:center}.compact-btn{padding:var(--size-2-1) var(--size-2-2);box-shadow:unset!important;border:unset!important;--icon-size: var(--size-4-4);display:flex;justify-content:center;-webkit-app-region:no-drag;display:inline-flex;overflow:hidden;align-items:center;color:var(--text-muted);font-size:var(--font-ui-small);border-radius:var(--button-radius);padding:var(--size-2-2);font-weight:var(--input-font-weight);cursor:var(--cursor);font-family:inherit;gap:var(--size-2-2);min-height:30px}.compact-btn:hover{box-shadow:none;opacity:var(--icon-opacity-hover);background-color:var(--background-modifier-hover);color:var(--text-normal)}.compact-input,.compact-select{font-size:var(--font-ui-smaller);height:var(--input-height);border:1px solid var(--background-modifier-border);box-shadow:none}.compact-select:hover{box-shadow:none}.compact-text{font-size:var(--font-ui-smaller)}.dragging-placeholder{opacity:.5;background-color:var( --background-modifier-hover )}.task-filter-root-container.task-popover-content{padding:var(--size-2-2);max-width:100%;max-height:100%}.task-filter-main-panel{max-width:100%;padding:var(--size-2-2);border-radius:var(--radius-m)}.filter-menu{z-index:50;min-width:600px;background-color:var(--background-primary);border-radius:var(--radius-m);box-shadow:var(--shadow-s);border:1px solid var(--background-modifier-border)}.root-filter-setup-section{display:flex;flex-direction:column;gap:.75rem}.root-condition-section{display:flex;align-items:center;gap:.5rem;padding:.5rem;background-color:var( --background-secondary-alt, var(--background-modifier-hover) );border-radius:var(--radius-m);border:1px solid var(--background-modifier-border)}.root-condition-label{font-weight:500;color:var(--text-normal)}.root-condition-select{width:auto;border:1px solid var(--input-border-color, var(--background-modifier-border))}.root-condition-select:focus{border-color:var(--interactive-accent);box-shadow:0 0 0 1px var(--interactive-accent)}.root-condition-span{color:var(--text-normal)}.filter-groups-container{display:flex;flex-direction:column;gap:var(--size-2-3);max-height:50vh;overflow:auto}.filter-group{padding:var(--size-2-3);border:1px solid var(--background-modifier-border);border-radius:var(--radius-m);background-color:var(--background-primary);display:flex;flex-direction:column;gap:var(--size-4-2)}.filter-group-header{display:flex;align-items:center;justify-content:space-between}.filter-group-header-left{display:flex;align-items:center;gap:.375rem}.filter-group-header-left .drag-handle-container .svg-icon{color:var(--text-faint)}.filter-group-header-left .drag-handle-container:hover .svg-icon{color:var(--text-muted)}.filter-group-header-left .drag-handle-container{padding-right:var(--size-2-1)}.filter-group-header-left>.compact-text,.filter-group-header-left>span.compact-text{font-weight:500;color:var(--text-normal)}.filter-group-header-left .group-condition-select.compact-select{border:1px solid var(--input-border-color, var(--background-modifier-border))}.filter-group-header-left .group-condition-select.compact-select:focus{border-color:var(--interactive-accent);box-shadow:0 0 0 1px var(--interactive-accent)}.filter-group-header-right{display:flex;align-items:center;gap:.25rem}.filter-group-header-right .duplicate-group-btn.compact-icon-btn,.filter-group-header-right .remove-group-btn.compact-icon-btn{border-radius:var(--radius-s)}.filter-group-header-right .duplicate-group-btn.compact-icon-btn .svg-icon{color:var(--text-muted)}.filter-group-header-right .duplicate-group-btn.compact-icon-btn:hover .svg-icon{color:var(--interactive-accent)}.filter-group-header-right .duplicate-group-btn.compact-icon-btn:hover{background-color:var(--background-modifier-hover)}.filter-group-header-right .remove-group-btn.compact-icon-btn .svg-icon{color:var(--text-muted)}.filter-group-header-right .remove-group-btn.compact-icon-btn:hover .svg-icon{color:var(--text-error)}.filter-group-header-right .remove-group-btn.compact-icon-btn:hover{background-color:var( --background-error-hover, var(--background-modifier-error-hover) )}.filters-list{display:flex;flex-direction:column;gap:var(--size-2-2);padding-left:1rem;border-left:2px solid var(--background-modifier-border);margin-left:var(--size-4-2)}.filters-list:empty{display:none}.group-footer{padding-left:.375rem;margin-top:.375rem}.add-filter-btn-icon{display:flex;align-items:center;justify-content:center}.filter-item{display:flex;align-items:center;gap:var(--size-2-2);padding:var(--size-4-2);padding-top:0;padding-bottom:0}.filter-item .filter-conjunction{font-size:var(--font-ui-smaller);font-weight:600;color:var(--text-faint);align-self:center}.filter-item .filter-property-select.compact-select{flex-basis:30%;flex-grow:0;flex-shrink:0;border:1px solid var(--input-border-color, var(--background-modifier-border));box-shadow:none}.filter-item .filter-property-select.compact-select:focus{border-color:var(--interactive-accent);box-shadow:0 0 0 1px var(--interactive-accent)}.filter-item .filter-condition-select.compact-select{width:auto;border:1px solid var(--input-border-color, var(--background-modifier-border));box-shadow:none}.filter-item .filter-condition-select.compact-select:focus{border-color:var(--interactive-accent);box-shadow:0 0 0 1px var(--interactive-accent)}.filter-item .filter-value-input.compact-input{flex-grow:1;border:1px solid var(--input-border-color, var(--background-modifier-border));width:100%}.filter-item .filter-value-input.compact-input:focus{border-color:var(--interactive-accent);box-shadow:0 0 0 1px var(--interactive-accent)}.filter-item .remove-filter-btn.compact-icon-btn .svg-icon{color:var(--text-muted)}.filter-item .remove-filter-btn.compact-icon-btn:hover .svg-icon{color:var(--text-error)}.filter-item .remove-filter-btn.compact-icon-btn:hover{background-color:var( --background-error-hover, var(--background-modifier-error-hover) )}.add-group-section{margin-top:var(--size-2-1);margin-bottom:var(--size-2-1);margin-left:var(--size-2-1);display:flex;justify-content:space-between}.add-filter-group-btn-icon{display:flex;align-items:center;justify-content:center}.filter-config-section{display:flex;gap:var(--size-4-2)}.save-filter-config-btn,.load-filter-config-btn{flex:1}.save-filter-config-btn-icon,.load-filter-config-btn-icon{display:flex;align-items:center;justify-content:center}.save-filter-config-btn:hover{background-color:var(--interactive-accent-hover);color:var(--text-on-accent)}.load-filter-config-btn:hover{background-color:var(--background-modifier-hover)}.filter-config-details{margin-top:var(--size-4-3);padding:var(--size-4-3);border:1px solid var(--background-modifier-border);border-radius:var(--radius-l);background:linear-gradient(135deg,var(--background-secondary) 0%,var(--background-primary-alt) 100%);box-shadow:0 2px 8px #0000001a;transition:all .2s ease-in-out}.filter-config-details:hover{box-shadow:0 4px 12px #00000026;transform:translateY(-1px)}.filter-config-details h3{margin:0 0 var(--size-4-2) 0;font-size:var(--font-ui-medium);font-weight:600;color:var(--text-accent);display:flex;align-items:center;gap:var(--size-2-2)}.filter-config-details p{margin:var(--size-2-2) 0;line-height:1.5;color:var(--text-normal)}.filter-config-meta{font-size:var(--font-ui-smaller);color:var(--text-muted);margin:var(--size-2-1) 0;padding:var(--size-2-1) var(--size-2-2);background-color:var(--background-modifier-form-field);border-radius:var(--radius-s);border-left:3px solid var(--interactive-accent)}.filter-config-summary{margin-top:var(--size-4-3);padding:var(--size-4-2) 0 0 0;border-top:2px solid var(--background-modifier-border)}.filter-config-summary h4{margin:0 0 var(--size-2-3) 0;font-size:var(--font-ui-small);font-weight:600;color:var(--text-normal);display:flex;align-items:center;gap:var(--size-2-1)}.filter-config-summary p{margin:var(--size-2-1) 0;font-size:var(--font-ui-smaller);color:var(--text-muted);padding:var(--size-2-1) var(--size-2-2);background-color:var(--background-primary-alt);border-radius:var(--radius-s)}.filter-config-buttons{margin-top:var(--size-4-3);padding-top:var(--size-4-2)}.filter-config-name-highlight{background-color:var(--text-accent);color:var(--text-on-accent);padding:.125rem .25rem;border-radius:var(--radius-s);font-weight:500}.advanced-filter-container{margin-top:var(--size-4-2);padding:var(--size-4-3);border:1px solid var(--background-modifier-border);border-radius:var(--radius-m);background-color:var(--background-secondary)}.advanced-filter-container .task-filter-root-container{background-color:transparent;border:none;padding:0}.advanced-filter-container .task-filter-main-panel{background-color:transparent;border:none;padding:0}.task-genius-view-config-modal .advanced-filter-container .filter-group{padding:var(--size-4-2);margin-bottom:var(--size-4-2)}.task-genius-view-config-modal .advanced-filter-container .filter-item{padding:var(--size-2-2);gap:var(--size-2-2)}.task-genius-view-config-modal .advanced-filter-container .compact-btn{padding:var(--size-2-1) var(--size-2-2);min-height:26px}.task-genius-view-config-modal .advanced-filter-container .compact-select,.task-genius-view-config-modal .advanced-filter-container .compact-input{font-size:var(--font-ui-smaller);height:28px}.file-filter-rules-container{margin-top:1rem;border:1px solid var(--background-modifier-border);border-radius:6px;padding:1rem;background:var(--background-secondary)}.file-filter-rule{display:flex;align-items:center;gap:1rem;padding:.75rem;margin-bottom:.5rem;border:1px solid var(--background-modifier-border);border-radius:4px;background:var(--background-primary)}.file-filter-rule:last-child{margin-bottom:0}.file-filter-rule-type,.file-filter-rule-path,.file-filter-rule-enabled{display:flex;flex-direction:column;gap:.25rem}.file-filter-rule-type{min-width:80px}.file-filter-rule-path{flex:1}.file-filter-rule-enabled{min-width:60px}.file-filter-rule label{font-size:.8rem;font-weight:500;color:var(--text-muted)}.file-filter-rule input[type=text]{padding:.25rem .5rem;border:1px solid var(--background-modifier-border);border-radius:3px;background:var(--background-primary);color:var(--text-normal);font-size:.9rem}.file-filter-rule input[type=checkbox]{width:16px;height:16px}.file-filter-rule-delete{padding:.25rem;border:none;border-radius:3px;background:var(--interactive-accent);color:var(--text-on-accent);cursor:pointer;display:flex;align-items:center;justify-content:center;min-width:28px;height:28px}.file-filter-add-rule{margin-top:1rem}.file-filter-add-rule .setting-item{border:none;padding:0}.file-filter-add-rule .setting-item-control{gap:.5rem}.file-filter-add-rule+.setting-item{border-top:none}.file-filter-stats{margin-top:1.5rem;padding:1rem;border:1px solid var(--background-modifier-border);border-radius:6px;background:var(--background-secondary)}.file-filter-stat{display:flex;justify-content:space-between;align-items:center;padding:.25rem 0}.file-filter-stat:not(:last-child){border-bottom:1px solid var(--background-modifier-border);margin-bottom:.25rem;padding-bottom:.5rem}.stat-label{font-weight:500;color:var(--text-normal)}.stat-value{font-weight:600;color:var(--interactive-accent)}.file-filter-stat.error{background-color:var(--background-modifier-error);border-left:3px solid var(--text-error)}.file-filter-stat.error .stat-label{color:var(--text-error)}.setting-item .setting-item-control button[aria-label*=refresh]{transition:transform .2s ease}.setting-item .setting-item-control button[aria-label*=refresh]:hover{transform:rotate(90deg)}@keyframes refresh-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.setting-item .setting-item-control button[disabled] .lucide-refresh-cw{animation:refresh-spin 1s linear infinite}@media (max-width: 768px){.file-filter-rule{flex-direction:column;align-items:stretch;gap:.5rem}.file-filter-rule-type,.file-filter-rule-path,.file-filter-rule-enabled{min-width:auto}.file-filter-rule-delete{align-self:flex-end;margin-top:.5rem}}.theme-dark .file-filter-rule input[type=text]{background:var(--background-primary-alt);border-color:var(--background-modifier-border-hover)}.theme-dark .file-filter-rule input[type=text]:focus{border-color:var(--interactive-accent);box-shadow:0 0 0 2px var(--interactive-accent-hover)}.file-filter-rules-container:empty:before{content:"No filter rules configured. Add rules below to start filtering files and folders.";display:block;text-align:center;color:var(--text-muted);font-style:italic;padding:2rem}.file-filter-preset-container{margin-top:1rem;padding:1rem;border:1px solid var(--background-modifier-border);border-radius:6px;background:var(--background-secondary)}.file-filter-preset-container .setting-item{border:none;padding:.5rem 0}.file-filter-preset-container .setting-item:not(:last-child){border-bottom:1px solid var(--background-modifier-border)}.file-filter-preset-container button{position:relative;transition:all .2s ease}.file-filter-preset-container button:disabled{opacity:.6;cursor:not-allowed;background:var(--background-modifier-border);color:var(--text-muted)}.file-filter-preset-container button:not(:disabled):hover{transform:translateY(-1px);box-shadow:0 2px 8px #0000001a}.file-filter-preset-container button[disabled]{background:var(--color-green);color:var(--text-on-accent);border-color:var(--color-green)}.theme-dark .file-filter-preset-container button[disabled]{background:var(--color-green-rgb);opacity:.8}.cm-workflow-stage-indicator{display:inline-block;margin-left:4px;font-size:12px;cursor:pointer;opacity:.7;transition:opacity .2s ease;user-select:none;align-items:center;vertical-align:middle}.cm-workflow-stage-indicator span{display:inline-flex;justify-content:center;align-items:center}.cm-workflow-stage-indicator:hover{opacity:1}.cm-workflow-stage-indicator[data-stage-type=linear]{color:var(--text-accent)}.cm-workflow-stage-indicator[data-stage-type=cycle]{color:var(--task-in-progress-color)}.cm-workflow-stage-indicator[data-stage-type=terminal]{color:var(--task-completed-color)}.theme-dark .cm-workflow-stage-indicator[data-stage-type=linear]{color:var(--text-accent)}.theme-dark .cm-workflow-stage-indicator[data-stage-type=cycle]{color:var(--task-in-progress-color)}.theme-dark .cm-workflow-stage-indicator[data-stage-type=terminal]{color:var(--task-completed-color)}.date-picker-root-container{display:flex;flex-direction:column;width:100%;min-width:500px;max-width:600px}.date-picker-root-container .date-picker-main-panel{display:flex;gap:var(--size-2-3);padding:var(--size-2-3)}.date-picker-root-container .date-picker-left-panel{flex:1;min-width:200px;border-right:1px solid var(--background-modifier-border)}.date-picker-root-container .date-picker-right-panel{flex:1;min-width:250px}.date-picker-root-container .date-picker-section-title{font-size:var(--font-ui-medium);font-weight:var(--font-bold);margin-bottom:var(--size-4-2);color:var(--text-normal)}.date-picker-root-container .quick-options-container{display:flex;flex-direction:column;gap:var(--size-2-1);max-height:195px;overflow:auto;overflow-x:hidden}.date-picker-root-container .quick-option-item{display:flex;justify-content:space-between;align-items:center;padding:var(--size-2-2) var(--size-4-2);cursor:pointer;transition:background-color .2s ease}.date-picker-root-container .quick-option-item:hover{background-color:var(--background-modifier-hover)}.date-picker-root-container .quick-option-item.selected{background-color:var(--interactive-accent);color:var(--text-on-accent)}.date-picker-root-container .quick-option-item.clear-option{border-top:1px solid var(--background-modifier-border);margin-top:var(--size-2-2);padding-top:var(--size-2-3);color:var(--text-error)}.date-picker-root-container .quick-option-item.clear-option:hover{color:var(--text-on-accent);background-color:var(--background-modifier-error-hover)}.date-picker-root-container .quick-option-label{font-size:var(--font-ui-small);font-weight:var(--font-medium)}.date-picker-root-container .quick-option-date{font-size:var(--font-ui-smaller);color:var(--text-muted);font-family:var(--font-monospace)}.date-picker-root-container .quick-option-item.selected .quick-option-date{color:var(--text-on-accent)}.date-picker-root-container .calendar-container{display:flex;flex-direction:column}.date-picker-root-container .calendar-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--size-4-2);padding:0 var(--size-2-2)}.date-picker-root-container .calendar-nav-btn{display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:var(--radius-s);cursor:pointer;transition:background-color .2s ease}.date-picker-root-container .calendar-nav-btn:hover{background-color:var(--background-modifier-hover)}.date-picker-root-container .calendar-month-year{font-size:var(--font-ui-medium);font-weight:var(--font-bold);color:var(--text-normal)}.date-picker-root-container .calendar-grid{display:grid;grid-template-columns:repeat(7,1fr);gap:1px;background-color:var(--background-modifier-border);border-radius:var(--radius-s);overflow:hidden}.date-picker-root-container .calendar-day-header{background-color:var(--background-secondary);padding:var(--size-2-2);text-align:center;font-size:var(--font-ui-smaller);font-weight:var(--font-bold);color:var(--text-muted)}.date-picker-root-container .calendar-day{background-color:var(--background-primary);padding:var(--size-2-2);text-align:center;font-size:var(--font-ui-small);cursor:pointer;transition:background-color .2s ease;min-height:32px;display:flex;align-items:center;justify-content:center}.date-picker-root-container .calendar-day:hover{background-color:var(--background-modifier-hover)}.date-picker-root-container .calendar-day.other-month{color:var(--text-faint);background-color:var(--background-secondary)}.date-picker-root-container .calendar-day.today{background-color:var(--interactive-accent-hover);color:var(--text-on-accent);font-weight:var(--font-bold)}.date-picker-root-container .calendar-day.selected{background-color:var(--interactive-accent);color:var(--text-on-accent);font-weight:var(--font-bold)}.date-picker-root-container .calendar-day.today.selected{background-color:var(--interactive-accent);box-shadow:inset 0 0 0 2px var(--text-on-accent)}.date-picker-popover.tg-menu{z-index:20;position:fixed;background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);box-shadow:var(--shadow-l);max-height:80vh;overflow:auto}.date-picker-popover.tg-menu .date-picker-popover-content{padding:0}@media (max-width: 768px){.date-picker-root-container .date-picker-main-panel{flex-direction:column;gap:var(--size-4-2)}.date-picker-root-container .date-picker-left-panel{border-right:none;border-bottom:1px solid var(--background-modifier-border);padding-right:0;padding-bottom:var(--size-4-2)}.date-picker-root-container{min-width:300px;max-width:400px}.date-picker-root-container .calendar-day{min-height:40px;font-size:var(--font-ui-medium)}}.date-picker-root-container .date-picker-widget-error{color:var(--text-error);background-color:var(--background-modifier-error);padding:var(--size-2-1) var(--size-2-2);border-radius:var(--radius-s);font-size:var(--font-ui-smaller)}.quick-capture-panel{padding:var(--size-4-2);background-color:var(--background-primary);border-top:1px solid var(--background-modifier-border);display:flex;flex-direction:column;gap:var(--size-4-2)}.quick-capture-modal.minimal{max-width:600px;min-width:500px;max-height:200px}.quick-capture-minimal-editor-container{padding:var(--size-4-2);min-height:50px}.quick-capture-minimal-editor-container .cm-editor{font-size:var(--font-text-size);min-height:40px;border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);padding:var(--size-2-1)}.quick-capture-minimal-editor-container .cm-editor.cm-focused{border-color:var(--interactive-accent);box-shadow:0 0 0 2px var(--interactive-accent-alpha)}.quick-capture-minimal-buttons{display:flex;justify-content:space-between;align-items:center;padding:var(--size-4-2)}.quick-actions-left{display:flex;gap:var(--size-2-1)}.quick-actions-right{display:flex;gap:var(--size-2-1)}.quick-action-button.active{background-color:var(--interactive-accent);color:var(--text-on-accent);border-color:var(--interactive-accent)}.quick-action-save{padding:var(--size-2-1) var(--size-4-2);min-width:80px;height:32px;border-radius:var(--radius-s)}.quick-capture-tag-input{position:absolute;bottom:60px;left:50%;transform:translate(-50%);width:300px;padding:var(--size-2-1);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);background-color:var(--background-primary);color:var(--text-normal);font-size:var(--font-text-size);z-index:1000}.minimal-quick-capture-suggestion{padding:var(--size-2-1) var(--size-4-2);border-radius:var(--radius-s);cursor:pointer;transition:background-color .2s ease;min-height:40px;display:flex;align-items:center}.minimal-quick-capture-suggestion:hover{background-color:var(--background-modifier-hover)}.minimal-quick-capture-suggestion.is-selected{background-color:var(--interactive-accent);color:var(--text-on-accent)}.minimal-quick-capture-suggestion.is-selected .suggestion-label{color:var(--text-on-accent)}.minimal-quick-capture-suggestion.is-selected .suggestion-description{color:var(--text-on-accent);opacity:.8}.suggestion-container{display:flex;align-items:center;gap:var(--size-2-1)}.suggestion-icon{font-size:16px;min-width:20px;text-align:center}.suggestion-content{flex:1}.suggestion-label{font-size:var(--font-text-size);font-weight:500;color:var(--text-normal)}.suggestion-description{font-size:var(--font-ui-small);color:var(--text-muted);margin-top:2px}.quick-capture-header-container{display:flex;align-items:center;margin-bottom:var(--size-4-2);gap:var(--size-4-2);font-size:var(--font-ui-medium);font-weight:bold;color:var(--text-normal);padding:var(--size-2-1) var(--size-4-2)}.quick-capture-title{color:var(--text-normal);white-space:nowrap}.quick-capture-target{flex:1;border-radius:var(--radius-s);color:var(--text-accent);font-size:var(--font-text-size);font-weight:normal;min-width:100px;max-width:500px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.quick-capture-target:focus{outline:none}.quick-capture-hint{font-size:12px;color:var(--text-muted);margin-bottom:8px;margin-top:-4px;text-align:right}.quick-capture-editor{min-height:200px;background-color:var(--background-primary)}.quick-capture-file-suggest{max-width:500px}.quick-capture-buttons{display:flex;justify-content:flex-end;gap:8px}.quick-capture-submit,.quick-capture-cancel{padding:6px 12px;border-radius:4px;cursor:pointer}.quick-capture-submit{background-color:var(--interactive-accent);color:var(--text-on-accent)}.quick-capture-cancel{background-color:var(--background-modifier-border);color:var(--text-normal)}.quick-capture-modal .modal-title{display:flex;align-items:center;flex-direction:row;gap:10px;font-size:var(--font-ui-medium);font-weight:bold}.quick-capture-modal-editor{min-height:150px;margin-bottom:20px}.quick-capture-modal-buttons{display:flex;justify-content:flex-end;gap:10px}.quick-capture-modal.full{width:80vw;max-width:900px}.quick-capture-layout{display:flex;height:100%;gap:16px;margin-bottom:16px}.quick-capture-config-panel{flex:1;border-right:1px solid var(--background-modifier-border);padding-right:16px;overflow-y:auto;max-width:40%}.quick-capture-editor-panel{flex:1.5;display:flex;flex-direction:column}.quick-capture-section-title{font-weight:bold;margin-bottom:8px;font-size:var(--font-ui-medium);color:var(--text-normal)}.quick-capture-target-container{margin-bottom:16px}.quick-capture-modal.full .quick-capture-modal-editor{min-height:200px;flex:1;overflow-y:auto;border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);padding:8px;margin-top:8px}@media (max-width: 768px){.quick-capture-modal.full{width:95vw}.quick-capture-layout{flex-direction:column}.quick-capture-config-panel{max-width:100%;border-right:none;border-bottom:1px solid var(--background-modifier-border);padding-right:0;padding-bottom:16px;margin-bottom:16px;max-height:40%}}.quick-capture-config-panel .details-status-selector{display:flex;flex-direction:row;justify-content:space-between;margin-bottom:var(--size-4-2);margin-top:var(--size-4-2)}.quick-capture-config-panel .quick-capture-status-selector{display:flex;flex-direction:row;justify-content:space-between;gap:var(--size-4-3)}.task-details .panel-toggle-container{left:10px}.task-details{width:300px;flex-shrink:0;border-left:1px solid var(--background-modifier-border);height:100%;overflow-y:auto;display:flex;flex-direction:column;transition:all .3s ease-in-out;position:relative;min-width:250px;max-width:400px;background-color:var(--background-secondary);order:1}.task-genius-container.details-hidden .task-details{width:0;opacity:0;margin-right:-300px;overflow:hidden}.task-genius-container.details-visible .task-details{width:350px;opacity:1;margin-right:0}.is-phone .task-details{position:absolute;right:0;top:0;height:100%;width:100%;max-width:100%;z-index:10;transform:translate(100%)}.is-phone .task-genius-container.details-hidden .task-details{width:100%;margin-right:0;transform:translate(100%)}.is-phone .task-genius-container.details-visible .task-details{width:calc(100% - var(--size-4-12));transform:translate(0)}.is-phone .task-genius-container.details-visible:before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--background-modifier-cover);opacity:.5;z-index:5;transition:opacity .3s ease-in-out}.is-phone .details-close-btn{width:24px;height:24px;display:flex;align-items:center;justify-content:center}.is-phone .details-header{padding:var(--size-4-4)}.details-empty{display:flex;height:100%;align-items:center;justify-content:center;text-align:center;color:var(--text-muted);padding:20px}.details-header{padding:var(--size-4-4);padding-bottom:var(--size-4-3);padding-top:var(--size-4-3);font-weight:600;border-bottom:1px solid var(--background-modifier-border);display:flex;justify-content:space-between;align-items:center;font-size:1.1em}.details-content{padding:var(--size-4-4);display:flex;flex-direction:column;gap:var(--size-4-2);overflow-y:auto;padding-bottom:max(var(--safe-area-inset-bottom),var(--size-4-8))}.details-name{margin:0 0 8px;padding:0;font-size:1.3em;line-height:1.3}.details-status-container{display:flex;justify-content:space-between;align-items:center}.details-status-label{text-transform:uppercase;font-size:var(--font-ui-small)}.details-status{display:inline-block;padding:4px 8px;border-radius:4px;background-color:var(--color-accent);color:var(--text-on-accent);font-size:var(--font-ui-small)}.details-status-selector{display:flex;justify-content:space-evenly;align-items:center}.menu-item-title:has(.status-option){display:flex;align-items:center;gap:4px}.menu-item:has(.status-option-checkbox) .menu-item-icon{display:none}.menu-item:has(.status-option-icon) .menu-item-icon{display:none}.status-option-icon{display:flex;align-items:center;justify-content:center;margin-right:var(--size-2-2)}.status-option-checkbox{display:flex;align-items:center;justify-content:center}.status-option{display:flex;justify-content:center;text-transform:uppercase}.status-option.current{outline-offset:2px;outline:1px solid hsl(var(--accent-h),var(--accent-s),var(--accent-l),.3);outline-style:dashed}.status-option:not(.current){opacity:.8}.status-option:not(.current):hover{opacity:1}.status-option input.task-list-item-checkbox{margin-inline-end:0}.details-metadata{display:flex;flex-direction:column;gap:var(--size-4-2);margin-top:var(--size-4-2);margin-bottom:var(--size-4-2)}.metadata-field{display:flex;flex-direction:column;gap:2px}.metadata-label{font-size:.8em;color:var(--text-muted)}.metadata-value{word-break:break-word;font-size:.95em}.details-actions{display:flex;align-items:center;justify-content:flex-start;gap:8px;margin-bottom:var(--size-4-4)}.details-edit-btn,.details-toggle-btn{background-color:var(--interactive-normal);border:1px solid var(--background-modifier-border);border-radius:4px;padding:6px 12px;color:var(--text-normal);cursor:pointer;font-size:var(--font-ui-small)}.details-edit-btn:hover,.details-toggle-btn:hover{background-color:var(--interactive-hover)}.details-toggle-btn{background-color:var(--interactive-accent);color:var(--text-on-accent)}.details-edit-form{display:flex;flex-direction:column;gap:12px}.details-form-field{display:flex;flex-direction:column;gap:4px}.details-form-label{font-size:.8em;color:var(--text-muted);font-weight:500}.details-form-input{width:100%}.details-edit-content{font-weight:500}.details-form-input input,.details-form-input select{width:100%;padding:6px 8px;border-radius:4px;border:1px solid var(--background-modifier-border);background-color:var(--background-primary)}.date-input{width:100%;padding:6px 8px;border-radius:4px;border:1px solid var(--background-modifier-border);background-color:var(--background-primary);color:var(--text-normal)}.field-description{font-size:.7em;color:var(--text-muted);margin-top:2px}.details-form-buttons{display:flex;justify-content:space-between;margin-top:16px;gap:8px}.details-form-buttons button{flex:1;justify-content:center}.details-form-error{color:var(--text-error);font-size:.8em;margin-top:8px;padding:8px;background-color:var(--background-modifier-error);border-radius:4px}.details-edit-file-btn{background-color:var(--interactive-normal);border:1px solid var(--background-modifier-border);border-radius:4px;padding:6px 12px;color:var(--text-normal);cursor:pointer;font-size:var(--font-ui-small)}.details-edit-file-btn:hover{background-color:var(--interactive-hover)}@media screen and (max-width: 768px){.task-omnifocus-container{flex-direction:column}.task-sidebar{width:100%;max-width:100%;height:auto;border-right:none;border-bottom:1px solid var(--background-modifier-border)}.task-content{width:100%;flex:1}.task-details{width:100%;max-width:100%;border-left:none}}.project-source-indicator{display:flex;align-items:center;gap:4px;margin-top:4px;padding:4px 8px;border-radius:4px;font-size:.85em;line-height:1.2}.project-source-indicator .indicator-icon{font-size:.9em}.project-source-indicator .indicator-text{color:var(--text-muted)}.project-source-indicator.readonly-indicator{border:1px solid var(--background-modifier-error)}.project-source-indicator.readonly-indicator .indicator-text{color:var(--text-error);font-weight:500}.project-source-indicator.override-indicator{border:1px solid var(--background-modifier-accent)}.project-source-indicator.override-indicator .indicator-text{color:var(--text-accent)}.field-description.readonly-description{color:var(--text-error);font-size:.8em;margin-top:4px;font-style:italic}.field-description.override-description{color:var(--text-accent);font-size:.8em;margin-top:4px;font-style:italic}.project-source-indicator.inline-indicator{position:absolute;top:100%;left:0;right:0;z-index:10;margin-top:2px;padding:2px 6px;font-size:.75em;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.project-source-indicator.table-indicator{position:absolute;top:2px;right:2px;padding:1px 3px;font-size:.7em;border-radius:2px;z-index:5}.project-source-indicator.table-indicator .indicator-icon{font-size:.8em}.task-table-cell.readonly-cell{background-color:var(--background-modifier-error-hover);opacity:.8}.project-container.project-readonly{position:relative}.project-container.project-readonly .project-source-indicator{margin-top:8px}.oncompletion-configurator{display:flex;flex-direction:column;gap:12px;padding:12px;border:1px solid var(--background-modifier-border);border-radius:6px;background-color:var(--background-secondary)}.oncompletion-action-type{display:flex;flex-direction:column;gap:6px}.oncompletion-label{font-weight:600;color:var(--text-normal);font-size:.9em}.oncompletion-config{display:flex;flex-direction:column;gap:10px;margin-top:8px;padding-top:8px;border-top:1px solid var(--background-modifier-border-hover)}.oncompletion-field{display:flex;flex-direction:column;gap:4px}.oncompletion-description{font-size:.8em;color:var(--text-muted);font-style:italic;margin-top:2px}.oncompletion-action-type .dropdown{width:100%}.oncompletion-field .text-input{width:100%;padding:6px 8px;border:1px solid var(--background-modifier-border);border-radius:4px;background-color:var(--background-primary);color:var(--text-normal)}.oncompletion-field .text-input:focus{border-color:var(--interactive-accent);outline:none;box-shadow:0 0 0 2px var(--interactive-accent-hover)}.oncompletion-field .checkbox-container{display:flex;align-items:center;gap:8px}.task-id-suggestion{font-weight:600;color:var(--text-accent)}.task-content-preview{font-size:.85em;color:var(--text-muted);margin-top:2px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:300px}.file-name{font-weight:500;color:var(--text-normal)}.file-path{font-size:.8em;color:var(--text-muted);margin-top:2px}.action-type-suggestion{font-weight:600;color:var(--text-accent)}.action-description{font-size:.8em;color:var(--text-muted);margin-top:2px}.oncompletion-configurator.invalid{border-color:var(--text-error);background-color:var(--background-modifier-error)}.oncompletion-configurator.valid{border-color:var(--text-success)}.oncompletion-validation-message{font-size:.8em;margin-top:4px;padding:4px 6px;border-radius:3px}.oncompletion-validation-message.error{color:var(--text-error);background-color:var(--background-modifier-error)}.oncompletion-validation-message.success{color:var(--text-success);background-color:var(--background-modifier-success)}.task-details .oncompletion-configurator{margin-top:8px;border:none;background-color:transparent;padding:0}.task-details .oncompletion-field{margin-bottom:8px}@media (max-width: 768px){.oncompletion-configurator{padding:8px;gap:8px}.oncompletion-config{gap:8px}.task-content-preview{max-width:200px}}.theme-dark .oncompletion-configurator{background-color:var(--background-primary-alt)}.theme-dark .oncompletion-field .text-input{background-color:var(--background-secondary);border-color:var(--background-modifier-border-hover)}@media (prefers-contrast: high){.oncompletion-configurator{border-width:2px}.oncompletion-field .text-input{border-width:2px}.oncompletion-field .text-input:focus{box-shadow:0 0 0 3px var(--interactive-accent-hover)}}.oncompletion-config{transition:all .2s ease-in-out}.oncompletion-field{opacity:1;transform:translateY(0);transition:opacity .2s ease-in-out,transform .2s ease-in-out}.oncompletion-field.entering{opacity:0;transform:translateY(-10px)}.oncompletion-field.exiting{opacity:0;transform:translateY(10px)}.oncompletion-modal{--dialog-width: 600px;--dialog-max-width: 90vw;--dialog-max-height: 80vh}.oncompletion-modal .modal-content{padding:0;max-height:var(--dialog-max-height);overflow-y:auto}.oncompletion-modal-content{padding:20px;max-height:60vh;overflow-y:auto}.oncompletion-modal-buttons{display:flex;justify-content:flex-end;gap:8px;padding:16px 20px;border-top:1px solid var(--background-modifier-border);background-color:var(--background-secondary)}.oncompletion-modal-buttons button{min-width:80px}.inline-oncompletion-button-container{display:inline-flex;align-items:center}.inline-oncompletion-config-button{padding:4px 8px;border:1px solid var(--background-modifier-border);border-radius:4px;background-color:var(--background-primary);color:var(--text-normal);font-family:inherit;font-size:var(--font-ui-small);cursor:pointer;transition:all .15s ease;min-width:100px;text-align:left}.inline-oncompletion-config-button:hover{background-color:var(--background-modifier-hover);border-color:var(--interactive-accent)}.inline-oncompletion-config-button:focus{outline:none;border-color:var(--interactive-accent);box-shadow:0 0 0 2px var(--interactive-accent-hover)}.inline-oncompletion-config-button:active{background-color:var(--background-modifier-active);transform:scale(.98)}@media (max-width: 768px){.oncompletion-modal{--dialog-width: 95vw;--dialog-max-height: 85vh}.oncompletion-modal-content{padding:16px;max-height:65vh}.oncompletion-modal-buttons{padding:12px 16px;flex-direction:column-reverse}.oncompletion-modal-buttons button{width:100%;min-width:unset}}.universal-suggest-item{display:flex;align-items:center;cursor:pointer;border-radius:4px;transition:background-color .1s ease}.universal-suggest-item:hover{background-color:var(--background-modifier-hover)}.universal-suggest-item.is-selected{background-color:var(--background-modifier-active-hover)}.universal-suggest-container{display:flex;flex-direction:row;align-items:center;justify-content:flex-start;overflow:hidden}.universal-suggest-icon{display:flex;align-items:center;justify-content:center;width:20px;height:20px;margin-right:12px;color:var(--text-muted);flex-shrink:0}.universal-suggest-content{flex:1;min-width:0}.universal-suggest-label{font-weight:500;color:var(--text-normal);margin-bottom:2px}.universal-suggest-description{font-size:.85em;color:var(--text-muted);line-height:1.3}.cm-editor .cm-line .universal-suggest-trigger{background-color:var(--background-modifier-accent);color:var(--text-accent);border-radius:2px;padding:1px 2px}.suggestion-container .universal-suggest-item{border-bottom:1px solid var(--background-modifier-border)}.suggestion-container .universal-suggest-item:last-child{border-bottom:none}.theme-dark .universal-suggest-item:hover{background-color:var(--background-modifier-hover)}.theme-dark .universal-suggest-item.is-selected{background-color:var(--background-modifier-active-hover)}@media (prefers-contrast: high){.universal-suggest-item{border:1px solid var(--background-modifier-border);margin-bottom:2px}.universal-suggest-item:hover,.universal-suggest-item.is-selected{border-color:var(--text-accent)}}.task-list{flex:1;overflow-y:auto;padding:0}.task-item{display:flex;align-items:flex-start;padding:8px 16px;border-bottom:1px solid var(--background-modifier-border);cursor:pointer;gap:var(--size-2-3)}.task-item:hover{background-color:var(--background-secondary-alt)}.task-children-container .task-item:hover{background-color:var(--background-secondary)}.task-item.selected{background-color:var(--background-secondary-alt)}.task-item.task-completed .task-item-content{text-decoration:line-through;color:var(--text-muted)}.task-item .markdown-block.markdown-renderer>p:only-child{padding:0;margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-checkbox{width:16px;height:16px;display:flex;align-items:center;justify-content:center;color:var(--text-normal);cursor:pointer;flex-shrink:0}.task-item.task-completed .task-checkbox{color:var(--text-on-accent)}.task-item-content{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-item-container{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-item-metadata{display:flex;align-items:center;gap:var(--size-4-2);margin-top:var(--size-2-2)}.task-item-metadata:empty{display:none}.task-date{font-size:var(--font-ui-small);color:var(--text-faint);white-space:nowrap;background-color:var(--background-modifier-active-hover);padding:var(--size-2-1) var(--size-2-3);border-radius:var(--radius-s);opacity:.8}.task-item:hover .task-date{opacity:1}.task-date:before{display:inline-block;margin-right:var(--size-2-2);font-size:xx-small;display:inline-flex;transform:translateY(-1px)}.tg-kanban-view .task-date:before{transform:translateY(0)}.task-date.task-due-date:before{content:"\1f4c5"}.task-date.task-overdue{color:var(--text-error);font-weight:600}.task-date.task-due-today{color:var(--task-doing-color);font-weight:600}.task-date.task-due-soon{color:var(--text-warning);font-weight:600}.task-date.task-start-date:before{content:"\1f6eb"}.task-date.task-created-date:before{content:"\2795"}.task-date.task-scheduled-date:before{content:"\23f3"}.task-date.task-done-date:before{content:"\2705"}.task-date.task-cancelled-date:before{content:"\274c"}.task-date.task-recurrence:before{content:"\1f501"}.task-date.task-on-completion:before{content:"\1f3c1"}.task-project{font-size:var(--font-ui-small);color:var(--text-on-accent);background-color:var(--color-accent);border-radius:var(--radius-s);padding:var(--size-2-1) var(--size-2-3);white-space:nowrap;opacity:.5}.task-project:has(input){background-color:var(--background-modifier-active-hover);color:var(--text-normal)}.task-item:hover .task-project{opacity:1}.task-project:before{content:"\1f5c2\fe0f";margin-right:var(--size-4-2);display:inline-flex;align-items:center;justify-content:center;font-size:var(--font-ui-small)}.task-project:hover{background-color:var(--background-modifier-active-hover);color:var(--text-accent-hover)}.task-priority{margin-left:8px;font-size:.9em;white-space:nowrap}.task-priority.priority-5{color:var(--text-error);font-weight:600}.task-priority.priority-4{color:var(--text-warning);font-weight:600}.task-priority.priority-3{color:var(--text-warning);font-weight:600}.task-priority.priority-2{color:var(--text-warning)}.task-priority.priority-1{color:var(--text-accent)}.task-oncompletion{display:inline-flex;align-items:center;padding:2px 6px;margin-left:4px;border-radius:3px;font-size:var(--font-ui-small);color:var(--text-muted);white-space:nowrap}.task-oncompletion:hover{color:var(--text-normal)}.task-dependson{display:inline-flex;align-items:center;padding:2px 6px;margin-left:4px;background-color:var(--background-modifier-error);border-radius:3px;font-size:var(--font-ui-small);color:var(--text-error);white-space:nowrap}.task-dependson:hover{background-color:var(--background-modifier-error-hover);color:var(--text-error)}.task-id{display:inline-flex;align-items:center;padding:2px 6px;margin-left:4px;background-color:var(--background-modifier-accent);border-radius:3px;font-size:var(--font-ui-small);color:var(--text-accent);white-space:nowrap}.task-id:hover{background-color:var(--background-modifier-accent-hover);color:var(--text-accent-hover)}.task-tags-container{display:flex;flex-wrap:wrap;gap:var(--size-2-2)}.task-tags-container:empty{display:none}.task-tag{font-size:var(--font-ui-small);color:var(--text-normal);background-color:var(--background-modifier-hover);border-radius:var(--radius-s);padding:var(--size-2-1) var(--size-2-3);white-space:nowrap;opacity:.75}.task-item:hover .task-tag{opacity:1}.task-item-content p:has(img) img{display:block;width:min(50%,200px)}.inline-editor{position:relative;display:inline-block;width:100%}.inline-content-editor{width:100%;min-height:18px;border:none;border-bottom:1px solid var(--interactive-accent);border-radius:0;padding:2px 4px;background-color:transparent;color:var(--text-normal);font-family:inherit;font-size:inherit;line-height:inherit;resize:none;outline:none;transition:border-color .15s ease,background-color .15s ease}.inline-content-editor:focus{border-bottom-color:var(--interactive-accent-hover);background-color:var(--background-primary-alt);box-shadow:0 1px 0 0 var(--interactive-accent-hover)}.inline-embedded-editor-container{width:100%;min-height:18px;border:none;border-radius:0;background-color:transparent}.inline-embedded-editor{width:100%;min-height:18px;background-color:transparent}.inline-embedded-editor .cm-editor{border:none!important;outline:none!important;background-color:transparent!important;border-bottom:1px solid var(--interactive-accent)!important}.inline-embedded-editor .cm-focused{outline:none!important;border-bottom-color:var(--interactive-accent-hover)!important;background-color:var(--background-primary-alt)!important}.inline-embedded-editor .cm-content{padding:2px 4px;min-height:18px;font-family:inherit;font-size:inherit;line-height:inherit}.inline-embedded-editor .cm-line{padding:0}.inline-metadata-editor{display:inline-flex;align-items:center;gap:4px;padding:2px 6px;background-color:var(--background-primary-alt);border:1px solid var(--interactive-accent);border-radius:var(--radius-s);box-shadow:0 1px 3px #0000001a;min-width:120px;max-width:300px;position:relative;z-index:100}.inline-metadata-editor input{border:unset;outline:unset;padding:0;height:var(--line-height);background-color:transparent;background:transparent;border-radius:var(--radius-s)}.inline-metadata-editor input:focus{outline:unset;padding:0;background-color:transparent}.inline-metadata-editor:has(input){outline:unset;border:0;padding:0;background-color:transparent;border-radius:unset}.inline-project-input,.inline-tags-input,.inline-context-input,.inline-date-input,.inline-recurrence-input{flex:1;padding:2px 4px;border:none;border-radius:2px;background-color:transparent;color:var(--text-normal);font-family:inherit;font-size:var(--font-ui-small);outline:none;min-width:80px;transition:background-color .15s ease}.inline-project-input:focus,.inline-tags-input:focus,.inline-context-input:focus,.inline-date-input:focus,.inline-recurrence-input:focus{background-color:var(--background-primary);box-shadow:inset 0 0 0 1px var(--interactive-accent)}.inline-priority-select{padding:2px 4px;border:none;border-radius:2px;background-color:transparent;color:var(--text-normal);font-family:inherit;font-size:var(--font-ui-small);outline:none;cursor:pointer;min-width:80px}.inline-priority-select:focus{background-color:var(--background-primary);box-shadow:inset 0 0 0 1px var(--interactive-accent)}.add-metadata-container{display:inline-flex;align-items:center;margin-left:4px}.task-list .task-item:not(.tree-task-item):hover .add-metadata-btn{opacity:1}.tree-task-item .task-item-container:hover .add-metadata-btn{opacity:1}.add-metadata-btn{display:inline-flex;align-items:center;justify-content:center;width:22px;height:22px;border:none;border-radius:2px;background-color:var(--background-secondary);color:var(--text-muted);cursor:pointer;transition:all .15s ease;--icon-size: 10px;opacity:0;padding:0;margin:0}.add-metadata-btn:hover{background-color:var(--background-modifier-hover);color:var(--text-normal);opacity:1}.add-metadata-btn:active{background-color:var(--background-modifier-active);transform:scale(.95)}.add-metadata-btn svg{width:10px;height:10px}.inline-editor *{transition:border-color .15s ease,background-color .15s ease,box-shadow .15s ease}.inline-editor input:focus,.inline-editor textarea:focus,.inline-editor select:focus{outline:none}.task-item-metadata .task-date,.task-item-metadata .task-project,.task-item-metadata .task-tag{cursor:pointer;transition:background-color .15s ease,transform .15s ease;position:relative}.task-item-metadata .task-date:hover,.task-item-metadata .task-project:hover,.task-item-metadata .task-tag:hover{background-color:var(--background-modifier-hover);transform:none}.task-item-metadata .task-date:hover:after,.task-item-metadata .task-project:hover:after,.task-item-metadata .task-tag:hover:after{display:none}.task-item-content{cursor:pointer;transition:background-color .15s ease}.inline-metadata-editor{animation:fadeInScale .15s ease-out}@keyframes fadeInScale{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}.inline-editor-placeholder{min-height:1em;display:inline-block}@media (max-width: 768px){.inline-project-input,.inline-tags-input,.inline-context-input,.inline-recurrence-input{min-width:100px;font-size:var(--font-ui-smaller)}.inline-metadata-editor{max-width:250px}}@media (prefers-contrast: high){.inline-content-editor,.inline-embedded-editor .cm-editor{border-bottom-width:2px}.inline-metadata-editor{border-width:2px}}@media (prefers-reduced-motion: reduce){.inline-editor *,.task-item-metadata .task-date,.task-item-metadata .task-project,.task-item-metadata .task-tag,.task-item-content,.add-metadata-btn{transition:none}.inline-metadata-editor{animation:none}}.inline-dependson-input,.inline-id-input{width:100%;min-width:200px;padding:4px 8px;border:1px solid var(--background-modifier-border);border-radius:4px;background-color:var(--background-primary);color:var(--text-normal);font-family:inherit;font-size:var(--font-ui-small);outline:none;transition:border-color .15s ease,box-shadow .15s ease}.inline-dependson-input:focus,.inline-id-input:focus{border-color:var(--interactive-accent);box-shadow:0 0 0 2px var(--interactive-accent-hover)}.inline-dependson-input::placeholder,.inline-id-input::placeholder{color:var(--text-faint)}.tree-task-item{position:relative;display:flex;flex-direction:column;padding:8px 16px;transition:background-color .2s ease}.task-children-container .task-item.tree-task-item{border-bottom:unset;padding-top:var(--size-2-2);padding-bottom:var(--size-2-2);gap:0}.task-item.tree-task-item{gap:0}.tree-task-item:hover{background-color:var(--background-secondary-alt)}.tree-task-item.selected{background-color:var(--background-modifier-active)}.tree-task-item.completed{opacity:.7}.tree-task-item>div:first-of-type{width:100%;display:flex;align-items:flex-start;gap:6px}.task-indent{flex-shrink:0}.task-item.tree-task-item .task-expand-toggle{padding-top:var(--size-2-2)}.task-item .task-checkbox{padding-top:var(--size-2-2)}.task-expand-toggle{cursor:pointer;display:flex;align-items:center;justify-content:center;width:16px;height:16px;flex-shrink:0;color:var(--text-muted)}.task-expand-toggle:hover{color:var(--text-normal)}.task-item.tree-task-item .task-checkbox{cursor:pointer;flex-shrink:0;color:var(--text-muted);width:16px;height:16px;display:flex;align-items:center;justify-content:center}.task-item.tree-task-item .task-checkbox:hover{color:var(--text-accent)}.task-item.tree-task-item .task-checkbox.checked{color:var(--text-accent)}.task-content{flex-grow:1;line-height:1.4}.tree-task-item.completed .task-content{text-decoration:line-through;color:var(--text-muted)}.task-metadata{display:flex;gap:8px;margin-top:4px;font-size:.85em;color:var(--text-muted)}.task-metadata:empty{display:none}.task-due-date.overdue{color:var(--text-error);font-weight:bold}.task-item.tree-task-item .task-project{display:inline-block;padding:1px 6px;border-radius:4px}.task-priority.priority-3{color:var(--text-error)}.task-priority.priority-2{color:var(--text-warning)}.task-priority.priority-1{color:var(--text-accent)}.tree-task-item .task-oncompletion{display:inline-flex;align-items:center;padding:2px 6px;margin-left:4px;background-color:var(--background-modifier-border);border-radius:3px;font-size:var(--font-ui-small);color:var(--text-muted);white-space:nowrap}.tree-task-item .task-oncompletion:hover{color:var(--text-normal)}.tree-task-item .task-dependson{display:inline-flex;align-items:center;padding:2px 6px;margin-left:4px;background-color:var(--background-modifier-error);border-radius:3px;font-size:var(--font-ui-small);color:var(--text-error);white-space:nowrap}.tree-task-item .task-dependson:hover{background-color:var(--background-modifier-error-hover);color:var(--text-error)}.tree-task-item .task-id{display:inline-flex;align-items:center;padding:2px 6px;margin-left:4px;background-color:var(--background-modifier-accent);border-radius:3px;font-size:var(--font-ui-small);color:var(--text-accent);white-space:nowrap}.tree-task-item .task-id:hover{background-color:var(--background-modifier-accent-hover);color:var(--text-accent-hover)}.task-children-container{margin-top:4px;width:100%}.view-toggle-btn{cursor:pointer;display:flex;align-items:center;justify-content:center;width:24px;height:24px;color:var(--text-muted);border-radius:4px}.view-toggle-btn:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}.task-children-container:empty{display:none!important}.forecast-container{display:flex;flex-direction:column;height:100%;overflow:hidden;flex:1}.forecast-header{display:flex;justify-content:space-between;align-items:center;padding:15px;border-bottom:1px solid var(--background-modifier-border)}.forecast-title-container{display:flex;flex-direction:column}.forecast-title{font-weight:600;font-size:1.2em}.forecast-count{font-size:.8em;color:var(--text-muted);margin-top:4px}.forecast-actions{display:flex;gap:var(--size-4-2);align-items:center;justify-content:center}.forecast-settings{cursor:pointer;opacity:.7;transition:opacity .2s ease;display:flex;align-items:center;justify-content:center}.forecast-settings:hover{opacity:1}.forecast-focus-bar{display:flex;padding:10px 15px;border-bottom:1px solid var(--background-modifier-border);gap:10px;align-items:center}.focus-input{flex:1;padding:6px 12px;border-radius:4px;border:1px solid var(--interactive-accent);background-color:var(--background-primary);color:var(--text-normal)}.unfocus-button{padding:6px 12px;border-radius:4px;background-color:var(--interactive-accent);color:var(--text-on-accent);cursor:pointer;border:none}.unfocus-button:hover{background-color:var(--interactive-accent-hover)}.forecast-content{display:flex;flex:1;overflow:hidden}.forecast-left-column{width:360px;min-width:360px;border-right:1px solid var(--background-modifier-border);display:flex;flex-direction:column;overflow-y:auto;background-color:var(--background-secondary-alt)}.forecast-right-column{flex:1;display:flex;flex-direction:column;background-color:var(--background-primary)}.forecast-task-list{overflow-y:auto}.forecast-calendar-section{padding:10px 0;margin-top:var(--size-4-4);flex-shrink:0;border-top:1px solid var(--background-modifier-border)}.forecast-stats{display:flex}.stat-item{flex:1;display:flex;flex-direction:column;align-items:center;padding:10px;cursor:pointer;transition:background-color .2s ease;position:relative}.stat-item:after{content:"";position:absolute;bottom:0;left:10%;width:80%;height:3px;background-color:transparent;transition:background-color .2s ease}.stat-item:hover{background-color:var(--background-modifier-hover)}.stat-item.active:after{background-color:var(--interactive-accent);animation:color-pulse 1.5s infinite alternate}@keyframes color-pulse{0%{background-color:var(--color-accent-1)!important;opacity:.7}to{background-color:var(--color-accent-2)!important;opacity:1}}.stat-item.tg-past-due:after{background-color:var(--text-error);opacity:.7}.stat-item.tg-today:after{background-color:var(--interactive-accent);opacity:.7}.stat-item.tg-future:after{background-color:var(--text-accent);opacity:.7}.stat-count{font-size:1.5em;font-weight:600}.stat-item.tg-past-due .stat-count{color:var(--text-error)}.stat-label{font-size:.8em;color:var(--text-muted)}.forecast-due-soon-section{display:flex;flex-direction:column;padding-bottom:var(--size-4-3)}.due-soon-header{font-size:.8em;font-weight:600;padding:5px 15px;color:var(--text-muted);text-transform:uppercase;letter-spacing:.05em}.due-soon-item{display:flex;justify-content:space-between;padding:8px 15px;cursor:pointer;border-left:3px solid transparent;transition:background-color .2s ease}.due-soon-item:hover{background-color:var(--background-modifier-hover);border-left-color:var(--interactive-accent)}.due-soon-date{font-size:.9em}.due-soon-count{font-size:.8em;background-color:var(--background-modifier-border);padding:2px 6px;border-radius:10px;color:var(--text-muted)}.due-soon-empty{text-align:center;padding:15px;color:var(--text-muted);font-style:italic;font-size:.9em}.date-section-header{display:flex;align-items:center;padding:8px 15px;cursor:pointer;border-bottom:1px solid var(--background-modifier-border);background-color:var(--background-secondary-alt)}.date-section-header .section-toggle{margin-right:8px;display:flex;align-items:center;justify-content:center}.date-section-header .section-title{flex:1;font-weight:500}.date-section-header .section-count{font-size:.8em;color:var(--text-muted);background-color:var(--background-modifier-border);border-radius:10px;height:var(--size-4-5);width:var(--size-4-5);display:inline-flex;align-items:center;justify-content:center}.task-date-section.overdue .date-section-header{border-left:3px solid var(--text-error)}.task-date-section.overdue .section-title{color:var(--text-error)}.task-date-section.overdue .section-count{background-color:var(--text-error);color:#fff}.section-tasks{display:flex;flex-direction:column}.forecast-empty-state{display:flex;height:100px;align-items:center;justify-content:center;color:var(--text-muted);font-style:italic}.forecast-sidebar-toggle{position:absolute}.is-phone .forecast-header:has(.forecast-sidebar-toggle) .forecast-title-container{padding-left:var(--size-4-10)}.is-phone .forecast-container{position:relative;overflow:hidden}.is-phone .forecast-left-column{position:absolute;left:0;top:0;height:100%;z-index:10;background-color:var(--background-secondary);width:100%;transform:translate(-100%);transition:transform .3s ease-in-out;border-right:1px solid var(--background-modifier-border)}.is-phone .forecast-left-column.is-visible{transform:translate(0)}.is-phone .forecast-sidebar-toggle{display:flex;align-items:center;justify-content:center;margin-right:8px}.is-phone .forecast-sidebar-close{position:absolute;top:10px;right:10px;z-index:15;width:28px;height:28px;display:flex;align-items:center;justify-content:center;border-radius:50%;background-color:var(--background-primary);box-shadow:0 2px 4px #0000001a}.is-phone .task-genius-container:has(.forecast-left-column.is-visible):before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--background-modifier-cover);opacity:.5;z-index:5;transition:opacity .3s ease-in-out}.task-genius-view .mini-calendar-container{display:flex;flex-direction:column;width:100%;border-bottom:1px solid var(--background-modifier-border);padding-bottom:10px}.task-genius-view .mini-calendar-container .calendar-header{display:flex;justify-content:space-between;align-items:center;padding:10px 15px;margin-bottom:10px}.task-genius-view .mini-calendar-container .calendar-title{font-weight:600;display:flex;gap:5px}.task-genius-view .mini-calendar-container .calendar-month{margin-right:5px}.task-genius-view .mini-calendar-container .calendar-year{color:var(--text-muted)}.task-genius-view .mini-calendar-container .calendar-nav{display:flex;align-items:center;gap:8px}.task-genius-view .mini-calendar-container .calendar-nav-btn{display:flex;align-items:center;justify-content:center;width:24px;height:24px;border-radius:4px;background-color:var(--background-modifier-hover);cursor:pointer;opacity:.7;transition:opacity .2s ease}.task-genius-view .mini-calendar-container .calendar-nav-btn:hover{opacity:1;background-color:var(--background-modifier-border-hover)}.task-genius-view .mini-calendar-container .calendar-today-btn{padding:2px 8px;border-radius:4px;background-color:var(--background-modifier-hover);cursor:pointer;font-size:.8em;transition:background-color .2s ease}.task-genius-view .mini-calendar-container .calendar-today-btn:hover{background-color:var(--background-modifier-border-hover)}.task-genius-view .mini-calendar-container .calendar-grid{display:grid;grid-template-columns:repeat(7,1fr);gap:2px;padding:0 10px}.task-genius-view .mini-calendar-container .calendar-day-header{text-align:center;font-size:.8em;color:var(--text-muted);padding:5px 0;border-bottom:1px solid var(--background-modifier-border);margin-bottom:5px}.task-genius-view .mini-calendar-container .calendar-day-header.calendar-weekend{color:var(--text-accent)}.task-genius-view .mini-calendar-container.hide-weekends .calendar-grid{grid-template-columns:repeat(5,1fr)}.task-genius-view .mini-calendar-container .calendar-day{aspect-ratio:1;border-radius:4px;padding:2px;cursor:pointer;position:relative;display:flex;flex-direction:column;transition:background-color .2s ease}.task-genius-view .mini-calendar-container .calendar-day:hover{background-color:var(--background-modifier-hover)}.task-genius-view .mini-calendar-container .calendar-day.selected{background-color:var(--background-modifier-border-hover)}.task-genius-view .mini-calendar-container .calendar-day.today{background-color:var(--interactive-accent-hover);color:var(--text-on-accent)}.task-genius-view .mini-calendar-container .calendar-day.past-due{color:var(--text-error)}.task-genius-view .mini-calendar-container .calendar-day.other-month{opacity:.5}.task-genius-view .mini-calendar-container .calendar-day-number{text-align:center;font-size:.9em;font-weight:500;padding:2px}.task-genius-view .mini-calendar-container .calendar-day-count{background-color:var(--background-modifier-border);color:var(--text-normal);border-radius:10px;font-size:.7em;padding:1px 5px;margin:2px auto;text-align:center;width:fit-content}.task-genius-view .mini-calendar-container .calendar-day-count.has-priority{background-color:var(--text-accent);color:var(--text-on-accent)}@media (max-width: 1400px){.task-genius-container:has(.task-details.visible) .mini-calendar-container .forecast-left-column{display:none}}.tags-container{display:flex;flex-direction:column;height:100%;width:100%;overflow:hidden;flex:1}.task-genius-view:has(.task-details.visible) .tags-left-column{display:none}.tags-content{display:flex;flex-direction:row;flex:1;overflow:hidden}.multi-select-mode .tags-multi-select-btn{color:var(--color-accent)}.tags-left-column{width:max(120px,30%);min-width:min(120px,30%);max-width:400px;display:flex;flex-direction:column;border-right:1px solid var(--background-modifier-border);overflow:hidden}.tags-right-column{flex:1;display:flex;flex-direction:column;overflow:hidden}.tags-sidebar-header{display:flex;justify-content:space-between;align-items:center;padding:var(--size-4-2) var(--size-4-4);border-bottom:1px solid var(--background-modifier-border);height:var(--size-4-10)}.tags-sidebar-title{font-weight:600;font-size:14px}.tags-multi-select-btn{cursor:pointer;color:var(--text-muted);display:flex;align-items:center;justify-content:center}.tags-multi-select-btn:hover{color:var(--text-normal)}.tags-sidebar-list{flex:1;overflow-y:auto;padding:var(--size-4-2);display:flex;flex-direction:column;gap:var(--size-2-1)}.tag-list-item{display:flex;align-items:center;padding:4px 12px;cursor:pointer;position:relative;border-radius:var(--radius-s)}.tag-list-item:hover{background-color:var(--background-modifier-hover)}.tag-list-item.selected{background-color:var(--background-modifier-active)}.tag-indent{flex-shrink:0}.tag-icon{margin-right:var(--size-2-2);color:var(--text-muted);display:flex;--icon-size: var(--size-4-4)}.tag-name{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.tag-count{margin-left:8px;font-size:.8em;color:var(--text-muted);background-color:var(--background-modifier-border);border-radius:10px;padding:1px 6px}.tag-children{width:100%}.tags-task-header{display:flex;justify-content:space-between;align-items:center;padding:var(--size-4-2) var(--size-4-4);border-bottom:1px solid var(--background-modifier-border);height:var(--size-4-10)}.tags-task-title{font-weight:600;font-size:16px}.tags-task-count{color:var(--text-muted)}.tags-task-list{flex:1;overflow-y:auto}.tags-empty-state{display:flex;align-items:center;justify-content:center;height:100%;color:var(--text-muted);font-style:italic;padding:16px}.tag-section-header{display:flex;align-items:center;padding:8px 15px;cursor:pointer;border-bottom:1px solid var(--background-modifier-border);background-color:var(--background-secondary-alt)}.tag-section-header .section-toggle{margin-right:8px;display:flex;align-items:center;justify-content:center}.tag-section-header .section-title{flex:1;font-weight:500}.tag-section-header .section-count{font-size:.8em;color:var(--text-muted);background-color:var(--background-modifier-border);padding:2px 6px;border-radius:10px;height:var(--size-4-5);width:var(--size-4-5)}.is-phone .tags-container{position:relative;overflow:hidden}.is-phone .tags-left-column{position:absolute;left:0;top:0;height:100%;z-index:10;background-color:var(--background-secondary);width:100%;transform:translate(-100%);transition:transform .3s ease-in-out;border-right:1px solid var(--background-modifier-border)}.is-phone .tags-left-column.is-visible{transform:translate(0)}.is-phone .tags-sidebar-toggle{display:flex;align-items:center;justify-content:center;margin-right:8px}.is-phone .tags-sidebar-close{--icon-size: var(--size-4-4);position:absolute;top:var(--size-4-2);right:10px;z-index:15;display:flex;align-items:center;justify-content:center}.is-phone .tags-container:has(.tags-left-column.is-visible):before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--background-modifier-cover);opacity:.5;z-index:5;transition:opacity .3s ease-in-out}.is-phone .tags-sidebar-header:has(.tags-sidebar-close){padding-right:var(--size-4-12)}.projects-container{display:flex;flex-direction:column;height:100%;width:100%;overflow:hidden}.projects-content{display:flex;flex-direction:row;flex:1;overflow:hidden}.projects-left-column{width:max(120px,30%);min-width:min(120px,30%);max-width:300px;display:flex;flex-direction:column;border-right:1px solid var(--background-modifier-border);overflow:hidden}.is-phone .projects-left-column{max-width:100%}.projects-right-column{flex:1;display:flex;flex-direction:column;overflow:hidden}.projects-sidebar-header{display:flex;justify-content:space-between;align-items:center;padding:var(--size-4-2) var(--size-4-4);border-bottom:1px solid var(--background-modifier-border);height:var(--size-4-10)}.projects-sidebar-title{font-weight:600;font-size:14px}.multi-select-mode .projects-multi-select-btn{color:var(--color-accent)}.projects-multi-select-btn{cursor:pointer;color:var(--text-muted);display:flex;align-items:center;justify-content:center}.projects-multi-select-btn:hover{color:var(--text-normal)}.projects-sidebar-list{flex:1;overflow-y:auto;padding:var(--size-4-2)}.project-list-item{display:flex;align-items:center;padding:4px 12px;cursor:pointer;border-radius:var(--radius-s)}.project-list-item:hover{background-color:var(--background-modifier-hover)}.project-list-item.selected{background-color:var(--background-modifier-active)}.project-icon{margin-right:8px;color:var(--text-muted);display:flex;align-items:center;justify-content:center}.project-name{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.project-count{margin-left:8px;font-size:.8em;color:var(--text-muted);background-color:var(--background-modifier-border);border-radius:10px;padding:1px 6px}.projects-task-header{display:flex;justify-content:space-between;align-items:center;padding:var(--size-4-2) var(--size-4-4);border-bottom:1px solid var(--background-modifier-border);height:var(--size-4-10)}.projects-task-title{font-weight:600;font-size:16px}.projects-task-count{color:var(--text-muted)}.projects-task-list{flex:1;overflow-y:auto}.projects-empty-state{display:flex;align-items:center;justify-content:center;height:100%;color:var(--text-muted);font-style:italic;padding:16px}.is-phone .projects-left-column{position:absolute;left:0;top:0;height:100%;z-index:10;background-color:var(--background-secondary);width:100%;transform:translate(-100%);transition:transform .3s ease-in-out;border-right:1px solid var(--background-modifier-border)}.is-phone .projects-left-column.is-visible{transform:translate(0)}.is-phone .projects-sidebar-toggle{display:flex;align-items:center;justify-content:center;margin-right:8px}.is-phone .projects-sidebar-close{--icon-size: var(--size-4-4);position:absolute;top:var(--size-4-2);right:10px;z-index:15;display:flex;align-items:center;justify-content:center}.is-phone .projects-container:has(.projects-left-column.is-visible):before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--background-modifier-cover);opacity:.5;z-index:5;transition:opacity .3s ease-in-out}.is-phone .projects-container{position:relative;overflow:hidden}.is-phone .projects-sidebar-header:has(.projects-sidebar-close){padding-right:var(--size-4-12)}.review-container{display:flex;flex-direction:column;height:100%;width:100%;overflow:hidden}.review-content{display:flex;flex-direction:row;flex:1;overflow:hidden}.review-left-column{width:250px;min-width:200px;max-width:300px;display:flex;flex-direction:column;border-right:1px solid var(--background-modifier-border);overflow:hidden}.is-phone .review-left-column{max-width:100%}.review-right-column{flex:1;display:flex;flex-direction:column;overflow:hidden}.review-sidebar-header{display:flex;justify-content:space-between;align-items:center;padding:var(--size-4-2) var(--size-4-4);border-bottom:1px solid var(--background-modifier-border);height:var(--size-4-10)}.review-sidebar-title{font-weight:600;font-size:14px}.review-multi-select-btn{cursor:pointer;color:var(--text-muted);display:flex;align-items:center;justify-content:center}.review-multi-select-btn:hover{color:var(--text-normal)}.review-sidebar-list{flex:1;overflow-y:auto;padding:var(--size-4-2)}.review-projects-group-header{font-size:10px;font-weight:600;color:var(--text-faint);text-transform:uppercase;padding:4px 8px;margin-top:12px;letter-spacing:.5px}.review-projects-group-header:first-child{margin-top:4px}.review-project-item{--icon-size: var(--size-4-4);display:flex;align-items:center;padding:4px 8px;cursor:pointer;border-radius:var(--radius-s);margin-bottom:2px}.review-project-item:hover{background-color:var(--background-modifier-hover)}.review-project-item.selected{background-color:var(--background-modifier-active)}.review-project-item.has-review-settings .review-project-icon{color:var(--text-accent)}.review-project-item.has-review-settings .review-project-name{font-weight:500}.review-project-item:not(.has-review-settings) .review-project-icon{color:var(--text-muted)}.review-project-icon{margin-right:8px;display:flex;align-items:center;justify-content:center}.review-project-name{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.review-task-header{display:flex;flex-direction:column;padding:var(--size-4-4);border-bottom:1px solid var(--background-modifier-border)}.is-phone .review-task-header{flex-direction:row;align-items:flex-start}.review-header-content h3{margin:0 0 8px;padding:0}.review-info{display:flex;align-items:center;color:var(--text-muted);font-size:.9em}.review-separator{margin:0 8px}.review-frequency{color:var(--text-accent)}.review-frequency:hover{color:var(--text-normal);text-decoration:underline}.review-last-date{color:var(--text-normal)}.review-no-settings{font-style:italic}.review-filter-info{margin-top:10px;padding:6px 10px;background-color:var(--background-secondary);border-radius:var(--radius-s);font-size:.85em;color:var(--text-muted);border-left:3px solid var(--text-accent)}.review-filter-toggle{cursor:pointer;text-decoration:underline;color:var(--text-accent);margin-left:5px}.review-filter-toggle:hover{color:var(--text-accent-hover)}.review-task-list{flex:1;overflow-y:auto;padding:var(--size-4-2)}.review-empty-state{display:flex;align-items:center;justify-content:center;height:100%;color:var(--text-muted);font-style:italic;padding:16px;text-align:center}.review-button-container{margin-top:12px;display:flex;justify-content:flex-start}.review-complete-btn,.review-configure-btn{padding:6px 12px;border-radius:var(--radius-s);cursor:pointer;font-size:.9em;border:1px solid var(--background-modifier-border);background-color:var(--background-secondary)}.review-complete-btn{color:var(--text-accent)}.review-complete-btn:hover{background-color:var(--background-modifier-hover);color:var(--text-accent)}.review-configure-btn{color:var(--text-muted)}.review-configure-btn:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}.review-edit-btn{color:var(--text-accent-hover);margin-left:8px}.review-edit-btn:hover{background-color:var(--background-modifier-hover);color:var(--text-accent-hover)}.review-modal-title{margin-top:0;margin-bottom:20px;font-size:1.5em;color:var(--text-normal);border-bottom:1px solid var(--background-modifier-border);padding-bottom:10px}.review-modal-form{margin-bottom:20px}.review-modal-field{margin-bottom:16px}.review-modal-label{display:block;font-weight:600;margin-bottom:4px;color:var(--text-normal)}.review-modal-description{font-size:.9em;color:var(--text-muted);margin-bottom:8px}.review-modal-select{width:100%;border-radius:var(--radius-s);border:1px solid var(--background-modifier-border);background-color:var(--background-primary);color:var(--text-normal);font-size:14px}.review-modal-custom-frequency{margin-top:8px}.review-modal-input{width:100%;padding:8px;border-radius:var(--radius-s);border:1px solid var(--background-modifier-border);background-color:var(--background-primary);color:var(--text-normal);font-size:14px}.review-modal-last-reviewed{padding:8px;font-size:14px;color:var(--text-normal);background-color:var(--background-secondary);border-radius:var(--radius-s)}.review-modal-buttons{display:flex;justify-content:flex-end;margin-top:24px;border-top:1px solid var(--background-modifier-border);padding-top:16px}.review-modal-button{padding:8px 16px;border-radius:var(--radius-s);font-size:14px;cursor:pointer;border:1px solid var(--background-modifier-border)}.review-modal-button-cancel{background-color:var(--background-secondary);color:var(--text-muted);margin-right:8px}.review-modal-button-cancel:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}.review-modal-button-save{background-color:var(--interactive-accent);color:var(--text-on-accent)}.review-modal-button-save:hover{background-color:var(--interactive-accent-hover)}.is-phone .review-container{position:relative;overflow:hidden}.is-phone .review-left-column{position:absolute;left:0;top:0;height:100%;z-index:10;background-color:var(--background-secondary);width:100%;transform:translate(-100%);transition:transform .3s ease-in-out;border-right:1px solid var(--background-modifier-border)}.is-phone .review-left-column.is-visible{transform:translate(0)}.is-phone .review-sidebar-toggle{display:flex;align-items:center;justify-content:center;margin-right:8px}.is-phone .review-sidebar-close{position:absolute;top:var(--size-2-2);right:10px;z-index:15;display:flex;align-items:center;justify-content:center}.is-phone .review-container:has(.review-left-column.is-visible):before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--background-modifier-cover);opacity:.5;z-index:5;transition:opacity .3s ease-in-out}.task-sidebar.collapsed{width:48px;overflow:hidden}.panel-toggle-btn{display:flex;align-items:center;justify-content:center;border-radius:4px;cursor:pointer;opacity:.6;transition:opacity .2s ease}.panel-toggle-btn:hover{opacity:1}.task-sidebar.collapsed .sidebar-nav{align-items:center}.sidebar-nav{display:flex;flex-direction:column;padding:20px 0 10px;gap:5px}.sidebar-nav-spacer{height:1px;background-color:var(--background-modifier-border);margin:auto 15px 8px;opacity:.7}.sidebar-nav-item{display:flex;align-items:center;padding:8px 15px;cursor:pointer;border-radius:4px;margin:0 5px;transition:background-color .2s ease}.sidebar-nav-item:hover{background-color:var(--background-modifier-active)}.sidebar-nav-item.is-active{font-weight:600;--background-modifier-hover: var(--interactive-accent);--icon-color: var(--text-on-accent);background-color:var(--interactive-accent);color:var(--text-on-accent)}.nav-item-icon{--icon-size: var(--size-4-4);display:flex;align-items:center;justify-content:center;margin-right:var(--size-4-2)}.nav-item-label{flex:1;font-size:var(--font-ui-medium);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.nav-item-label.hidden{opacity:0;width:0;overflow:hidden;margin:0}.task-sidebar.collapsed .sidebar-nav-item{padding:8px 10px;justify-content:center;width:var(--size-4-9);flex-shrink:0;transition:width .3s ease-in-out,flex-shrink .3s ease-in-out}.task-sidebar.collapsed .nav-item-icon{margin-right:0}.task-content{flex:1;display:flex;flex-direction:column;overflow:hidden;min-width:0;transition:margin .3s ease}.task-sidebar.collapsed .task-content{margin-left:-200px;transition:margin .3s ease}.task-genius-view .project-tree{padding:10px 0;transition:opacity .3s ease}.task-genius-view .tree-root{display:flex;flex-direction:column}.task-genius-view .task-genius-view .tree-item{display:flex;align-items:center;padding:6px 8px;cursor:pointer;transition:background-color .2s ease;border-radius:4px;margin:0 5px}.task-genius-view .tree-item.selected{background-color:var(--background-modifier-border-hover)}.task-genius-view .tree-item-icon{display:flex;align-items:center;justify-content:center;width:20px;height:20px;margin-right:8px;color:var(--text-muted)}.task-genius-view .tree-item-name{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.task-genius-view .tree-item-count{font-size:.8em;color:var(--text-muted);margin-left:5px;background-color:var(--background-modifier-hover);padding:1px 6px;border-radius:10px}.task-genius-view .tree-item-toggle,.task-genius-view .tree-item-indent{width:20px;display:flex;align-items:center;justify-content:center;margin-right:5px}.task-genius-view .tree-item-toggle{cursor:pointer}.content-header{padding:15px;border-bottom:1px solid var(--background-modifier-border);display:flex;align-items:center;flex-shrink:0}.task-count{font-size:.8em;color:var(--text-muted);margin-right:10px}.focus-filter{margin-left:10px}.workspace-leaf-content .task-genius-view{padding:0}.task-genius-container{display:flex;flex-direction:row;height:100%;width:100%;background-color:var(--background-primary);border-top:1px solid var(--background-modifier-border);color:var(--text-normal);position:relative;overflow:hidden}.task-sidebar{display:flex;flex-direction:column;border-right:1px solid var(--background-modifier-border);background-color:var(--background-secondary);overflow-y:auto;width:240px;transition:width .3s ease-in-out;position:relative}.task-content{display:flex;flex-direction:column;flex:1;min-width:300px;height:100%;overflow:hidden}.task-sidebar .sidebar-nav{display:flex;flex-direction:column;padding:8px 0;height:100%}.project-tree{display:flex;flex-direction:column;padding:8px 0;overflow-y:auto}.tree-root{display:flex;flex-direction:column}.task-genius-view .tree-item{display:flex;align-items:center;padding:4px 12px;cursor:pointer;border-radius:4px;margin:2px 8px}.task-genius-view .tree-item:hover{background-color:var(--background-modifier-border-hover)}.task-genius-view .tree-item.selected{background-color:var(--background-modifier-border-hover);color:var(--text-accent)}.task-genius-view .tree-item-toggle{width:16px;height:16px;display:flex;align-items:center;justify-content:center;margin-right:4px}.task-genius-view .tree-item-indent{width:16px;height:16px;margin-right:4px}.task-genius-view .tree-item-icon{margin-right:8px;width:16px;height:16px;display:flex;align-items:center;justify-content:center;color:var(--text-muted)}.task-genius-view .tree-item-name{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-genius-view .tree-item-count{font-size:.8em;color:var(--text-muted);background-color:var(--background-modifier-hover);border-radius:10px;padding:2px 6px;min-width:16px;text-align:center}.task-genius-view .tree-item.expanded>.tree-item-children{display:flex}.task-genius-view .tree-item-children{display:none;flex-direction:column;margin-left:16px;width:100%}.task-genius-view .content-header{display:flex;align-items:center;padding:10px 16px;border-bottom:1px solid var(--background-modifier-border);min-height:50px}.task-genius-view .content-title{font-size:1.2em;font-weight:600;margin-right:12px;flex:1}@media screen and (max-width: 768px){.task-genius-view .content-title{display:none}.task-genius-view .task-count{flex:1}.task-genius-view .focus-filter{flex:1}}.task-genius-view .content-filter{display:flex;align-items:center;margin-right:12px}.task-genius-view .filter-input{border:1px solid var(--background-modifier-border);border-radius:4px;padding:4px 8px;width:200px;background-color:var(--background-primary)}.task-genius-view .focus-button{background-color:var(--interactive-normal);border:1px solid var(--background-modifier-border);border-radius:4px;padding:4px 10px;color:var(--text-normal);cursor:pointer}.task-genius-view .focus-button:hover{background-color:var(--interactive-hover)}.task-genius-view .focus-button.focused{background-color:var(--interactive-accent);color:var(--text-on-accent)}.mod-root .task-genius-action-btn{--icon-size: 16px}.mod-left-split .task-genius-action-btn{display:none}.mod-left-split .workspace-tab-header-status-container:has(.task-genius-action-btn){display:none}.mod-right-split .workspace-tab-header-status-container:has(.task-genius-action-btn){display:none}.task-genius-view .task-empty-state{width:100%;height:100%;flex:1;display:flex;align-items:center;justify-content:center}.mod-root .task-genius-tab-header{container-type:inline-size!important}@container (max-width: 120px){.mod-root .task-genius-action-btn {display: none;}}.quick-workflow-modal{max-width:600px;min-height:400px}.workflow-template-section{margin-bottom:20px;padding:15px;border:1px solid var(--background-modifier-border);border-radius:8px}.template-description{margin-top:10px}.template-desc-text{font-style:italic;color:var(--text-muted);margin:0}.workflow-form-section{margin-bottom:20px}.workflow-stages-preview{margin-top:15px}.stages-preview-list{margin-top:10px}.stage-preview-item{display:flex;align-items:center;justify-content:space-between;padding:8px 12px;margin:4px 0;background:var(--background-secondary);border-radius:6px;border:1px solid var(--background-modifier-border)}.stage-info{display:flex;align-items:center;gap:8px}.stage-name{font-weight:500}.stage-type{color:var(--text-muted);font-size:.9em}.stage-actions{display:flex;gap:4px}.no-stages-message{text-align:center;color:var(--text-muted);font-style:italic;padding:20px;border:2px dashed var(--background-modifier-border);border-radius:8px;margin-top:10px}.workflow-modal-buttons{display:flex;justify-content:flex-end;gap:10px;margin-top:20px;padding-top:15px;border-top:1px solid var(--background-modifier-border)}.workflow-progress-indicator{background:var(--background-secondary);border:1px solid var(--background-modifier-border);border-radius:8px;padding:15px;margin:10px 0}.workflow-progress-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px}.workflow-name{font-weight:600;font-size:1.1em}.workflow-progress-text{color:var(--text-muted);font-size:.9em}.workflow-progress-bar-container{display:flex;align-items:center;gap:10px;margin-bottom:15px}.workflow-progress-bar{flex:1;height:8px;background:var(--background-modifier-border);border-radius:4px;overflow:hidden}.workflow-progress-fill{height:100%;background:var(--interactive-accent);transition:width .3s ease}.workflow-progress-percentage{font-size:.9em;font-weight:500;min-width:35px;text-align:right}.workflow-stage-list{display:flex;flex-direction:column;gap:8px}.workflow-stage-item{display:flex;align-items:flex-start;gap:12px;padding:10px;border-radius:6px;transition:background-color .2s ease}.workflow-stage-item.completed{background:var(--background-modifier-success)}.workflow-stage-item.current{background:var(--background-modifier-accent);border:1px solid var(--interactive-accent)}.workflow-stage-item.pending{background:var(--background-primary);opacity:.7}.workflow-stage-icon{width:20px;height:20px;display:flex;align-items:center;justify-content:center;margin-top:2px}.workflow-stage-icon.completed-icon{color:var(--text-success)}.workflow-stage-icon.current-icon{color:var(--interactive-accent)}.workflow-stage-icon.pending-icon{color:var(--text-muted)}.workflow-stage-content{flex:1}.workflow-stage-name{font-weight:500;margin-bottom:2px}.workflow-stage-type{font-size:.8em;color:var(--text-muted)}.workflow-stage-number{width:24px;height:24px;border-radius:50%;background:var(--background-modifier-border);display:flex;align-items:center;justify-content:center;font-size:.8em;font-weight:600;margin-top:2px}.workflow-stage-item.completed .workflow-stage-number{background:var(--text-success);color:var(--background-primary)}.workflow-stage-item.current .workflow-stage-number{background:var(--interactive-accent);color:var(--text-on-accent)}.workflow-substage-container{margin-top:8px;padding-left:16px;border-left:2px solid var(--background-modifier-border)}.workflow-substage-item{display:flex;align-items:center;gap:8px;padding:4px 0}.workflow-substage-icon{width:12px;height:12px;color:var(--text-muted)}.workflow-substage-name{font-size:.9em;color:var(--text-muted)}.full-calendar-container{container-type:inline-size;display:flex;flex-direction:column;height:100%;overflow:hidden;flex-grow:1}.full-calendar-container .calendar-header{display:flex;justify-content:space-between;align-items:center;padding:var(--size-2-3) var(--size-4-4);border-bottom:1px solid var(--background-modifier-border);flex-shrink:0;margin-bottom:0}.full-calendar-container .calendar-header button{margin:0 var(--size-4-1)}.full-calendar-container .calendar-nav,.full-calendar-container .calendar-view-switcher{display:flex;gap:var(--size-2-2)}.full-calendar-container .calendar-nav button{border-radius:var(--radius-s);text-transform:uppercase}.full-calendar-container .calendar-view-switcher button{border-radius:var(--radius-s);text-transform:uppercase}.full-calendar-container .calendar-view-switcher button:not(.is-active),.full-calendar-container .calendar-nav button:not(.is-active){box-shadow:var(--shadow-xs);border:1px solid var(--background-modifier-border)}.full-calendar-container .calendar-current-date{font-weight:var(--font-semibold);font-size:var(--font-ui-large);text-align:center;flex-grow:1;max-width:max(120px,40%);overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.full-calendar-container .calendar-view-switcher button.is-active{background-color:var(--interactive-accent);color:var(--text-on-accent);border-color:var(--interactive-accent-hover)}.full-calendar-container .calendar-view-container{flex-grow:1;overflow-y:auto;padding:var(--size-4-2);position:relative;display:flex;flex-direction:column}.full-calendar-container .calendar-weekday-header{display:grid;grid-template-columns:repeat(7,1fr);text-align:center;font-size:var(--font-ui-small);color:var(--text-muted);padding:var(--size-4-1) 0;border-bottom:1px solid var(--background-modifier-border);margin-bottom:-1px;background-color:var(--background-secondary)}.full-calendar-container .calendar-weekday{padding:var(--size-4-1)}.full-calendar-container .calendar-view-container.view-month{padding:0}.full-calendar-container .calendar-month-grid{display:grid;grid-template-columns:repeat(7,1fr);grid-auto-rows:minmax(100px,auto);gap:1px;background-color:var(--background-modifier-border);height:100%}.full-calendar-container .calendar-day-cell{background-color:var(--background-primary);padding:var(--size-4-1);position:relative;display:flex;flex-direction:column;min-width:0}.full-calendar-container .calendar-day-cell:hover{background-color:hsl(var(--color-accent-h),var(--color-accent-s),var(--color-accent-l),.8)!important}.full-calendar-container .calendar-day-cell.is-today{background-color:var(--background-secondary-alt)!important;border:1px solid hsl(var(--accent-h),var(--accent-s),var(--accent-l),.5)}.full-calendar-container .calendar-day-cell.is-today .calendar-day-number{color:hsl(var(--accent-h),var(--accent-s),var(--accent-l),1)}.full-calendar-container .calendar-day-header{width:100%;display:flex;flex-direction:row-reverse;justify-content:space-between;align-items:center;gap:var(--size-4-1)}.full-calendar-container .calendar-day-cell:not(.is-today){opacity:.7}.full-calendar-container .calendar-day-cell.is-other-month{background-color:var(--background-secondary);opacity:.7}.full-calendar-container .calendar-day-cell.is-weekend{background-color:var( --background-secondary )}.full-calendar-container .calendar-day-number{font-size:var(--font-ui-small);text-align:center;margin-bottom:var(--size-4-1);flex-shrink:0;align-self:flex-end}.full-calendar-container .calendar-events-container{flex-grow:1;overflow:hidden;position:relative}.full-calendar-container .calendar-event{background-color:var(--interactive-accent);color:var(--text-on-accent);border-radius:var(--radius-s);padding:2px 4px;font-size:var(--font-ui-smaller);margin-bottom:3px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;display:block}.full-calendar-container .calendar-event:has(.task-list-item-checkbox){display:flex;flex-direction:row;align-items:center}.full-calendar-container .calendar-event:has(.task-list-item-checkbox).calendar-event-week-allday{display:flex}.full-calendar-container .calendar-event:has(.task-list-item-checkbox).calendar-event-month{display:flex}.full-calendar-container .full-calendar-container .calendar-event:hover{opacity:.8}.full-calendar-container .calendar-event.is-completed{background-color:var( --background-modifier-success-hover );text-decoration:line-through;opacity:.7}.full-calendar-container .calendar-event.calendar-event-month{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:block;width:100%;box-sizing:border-box}.full-calendar-container .calendar-view-container.view-day{display:flex;flex-direction:column;padding:0}.full-calendar-container .calendar-timeline-section{flex-grow:1;border-top:1px solid var(--background-modifier-border);overflow-y:auto;padding:var(--size-4-4)}.full-calendar-container .calendar-timeline-events-container{display:flex;flex-direction:column;gap:var(--size-4-2)}.full-calendar-container .calendar-event-timed{border:1px solid var(--background-modifier-border);overflow:hidden;display:flex;flex-direction:column;width:100%}.full-calendar-container .calendar-event-time{font-size:var(--font-ui-smaller);font-weight:bold;padding:1px 3px;background-color:#0000001a}.full-calendar-container .calendar-event-title{font-size:var(--font-ui-small);padding:2px 3px;flex-grow:1;white-space:normal;word-wrap:break-word;display:flex;align-items:center}.full-calendar-container .calendar-view-container.view-week{display:flex;flex-direction:column;padding:0}.full-calendar-container .calendar-week-header{display:grid;grid-template-columns:repeat(7,1fr);border-bottom:1px solid var(--background-modifier-border);flex-shrink:0;text-align:center;background-color:var(--background-secondary);font-size:var(--font-ui-medium)}.full-calendar-container .calendar-header-cell{padding:var(--size-4-1) 0;border-left:1px solid var(--background-modifier-border-hover)}.full-calendar-container .calendar-header-cell:first-child{border-left:none}.full-calendar-container .calendar-header-cell.is-today .calendar-day-number{background-color:var(--interactive-accent);color:var(--text-on-accent);border-radius:50%;display:inline-block;width:1.5em;height:1.5em;line-height:1.5em;margin:auto;display:flex;align-items:center;justify-content:center}.full-calendar-container .calendar-weekday{font-size:var(--font-ui-small);color:var(--text-muted)}.full-calendar-container .calendar-day-number{font-size:var(--font-ui-medium)}.full-calendar-container .calendar-week-grid-section{flex-grow:1;display:flex;flex-direction:column;overflow-y:auto;border-bottom:1px solid var(--background-modifier-border)}.full-calendar-container .calendar-week-grid{flex-grow:1;display:grid;grid-template-columns:repeat(7,1fr);grid-template-rows:1fr;gap:1px;background-color:var(--background-modifier-border);border-top:1px solid var(--background-modifier-border)}.full-calendar-container .calendar-day-column{background-color:var(--background-primary);padding:var(--size-4-1);border-left:none;display:flex;flex-direction:column;gap:var(--size-4-1);overflow:hidden;min-width:0}.full-calendar-container .calendar-day-column.is-weekend{background-color:var(--background-secondary)}.full-calendar-container .calendar-view-container.hide-weekends .calendar-weekday-header{grid-template-columns:repeat(5,1fr)!important}.full-calendar-container .calendar-view-container.hide-weekends .calendar-month-grid{grid-template-columns:repeat(5,1fr)!important}.full-calendar-container .calendar-view-container.hide-weekends .calendar-week-header{grid-template-columns:repeat(5,1fr)!important}.full-calendar-container .calendar-view-container.hide-weekends .calendar-week-grid{grid-template-columns:repeat(5,1fr)!important}.full-calendar-container .calendar-view-container.hide-weekends .mini-month-grid{grid-template-columns:repeat(5,1fr)!important}.full-calendar-container .calendar-day-events-container{flex-grow:1;display:flex;flex-direction:column;gap:3px}.full-calendar-container .calendar-event.calendar-event-week-allday{display:block;width:100%;position:relative;left:auto;top:auto;height:auto;margin-bottom:3px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.full-calendar-container .calendar-view-container.view-year{padding:var(--size-4-4)}.full-calendar-container .calendar-year-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:var(--size-4-4)}.full-calendar-container .calendar-mini-month{border:1px solid var(--background-modifier-border);border-radius:var(--radius-m);background-color:var(--background-secondary);overflow:hidden}.full-calendar-container .mini-month-header{text-align:center;font-weight:var(--font-semibold);padding:var(--size-4-2);background-color:var(--background-secondary-alt);border-bottom:1px solid var(--background-modifier-border)}.full-calendar-container .mini-month-body{padding:var(--size-4-2)}.full-calendar-container .mini-month-grid{display:grid;grid-template-columns:repeat(7,1fr);gap:2px;text-align:center}.full-calendar-container .mini-weekday-header{display:contents;font-size:var(--font-ui-smaller);color:var(--text-faint);font-weight:bold}.full-calendar-container .mini-weekday{padding-bottom:var(--size-4-1)}.full-calendar-container .mini-day-cell{font-size:var(--font-ui-small);padding:1px;border-radius:var(--radius-s);line-height:1.5em}.full-calendar-container .mini-day-cell.is-other-month{color:var(--text-faint);opacity:.6}.full-calendar-container .mini-day-cell.is-today{font-weight:bold;background-color:var(--interactive-accent-hover);color:var(--text-on-accent)}.full-calendar-container .mini-day-cell.has-events{font-weight:bold}.agenda-day-section{display:flex;width:100%;border:1px solid var(--background-modifier-border);padding-top:var(--size-4-2);padding-bottom:var(--size-4-2);padding-left:var(--size-4-2);padding-right:var(--size-4-2)}.agenda-day-date-column{width:20%;display:flex;flex-direction:column;justify-content:flex-start;align-items:center}.agenda-day-events-column{flex:1}.full-calendar-container input.task-list-item-checkbox{scale:.9}.full-calendar-container .calendar-view-switcher-selector{display:none}.calendar-event-ghost{background-color:var(--background-secondary-alt)!important;border:2px dashed var(--background-modifier-border)!important;opacity:.5!important;box-shadow:none!important}.calendar-event-dragging{opacity:.9!important;box-shadow:var(--shadow-l)!important;transform:rotate(2deg)!important;z-index:1000!important}.calendar-events-container .calendar-event{cursor:grab;transition:transform .2s ease,box-shadow .2s ease}.calendar-events-container .calendar-event:hover{transform:translateY(-1px);box-shadow:var(--shadow-s)}.calendar-events-container .calendar-event:active{cursor:grabbing}.calendar-events-container,.calendar-day-events-container{min-height:20px;border-radius:var(--radius-s);transition:background-color .2s ease}@container (max-width: 600px){.full-calendar-container .calendar-view-switcher button {display: none;} .calendar-nav .prev-button {display: none;} .calendar-nav .next-button {display: none;} .full-calendar-container .calendar-view-switcher-selector {display: block;}}.full-calendar-container .calendar-event-title-container p{padding-inline-start:0;padding-inline-end:0;margin-block-start:0;margin-block-end:0}.full-calendar-container .calendar-event-title-container{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;max-width:100%}.full-calendar-container .calendar-event-title p{margin-block-start:0;margin-block-end:0}.calendar-badges-container{display:flex;flex-direction:row;gap:4px;pointer-events:none;z-index:10}.calendar-badge{color:var(--text-muted);display:flex;font-size:10px;padding:var(--size-2-1);border-radius:var(--radius-s);max-width:80px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.calendar-day-cell{position:relative}.tg-kanban-view{display:flex;flex-direction:column;height:100%;width:100%;overflow:hidden}.tg-kanban-filters{border-bottom:1px solid var(--background-modifier-border);flex-shrink:0;display:flex;flex-direction:row-reverse;gap:8px;padding:0 8px}.tg-kanban-controls-container{display:flex;align-items:center;gap:12px;flex-wrap:wrap}.tg-kanban-sort-container{display:flex;align-items:center;gap:4px}.tg-kanban-sort-button{padding:4px 8px;border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);background-color:var(--background-primary);color:var(--text-normal);cursor:pointer;display:flex;align-items:center;gap:4px;font-size:var(--font-ui-small)}.tg-kanban-sort-button:hover{background-color:var(--background-modifier-hover);border-color:var(--background-modifier-border-hover)}.tg-kanban-toggle-container{display:flex;align-items:center;gap:4px}.tg-kanban-toggle-label{display:flex;align-items:center;gap:6px;font-size:var(--font-ui-small);color:var(--text-normal);cursor:pointer}.tg-kanban-toggle-checkbox{margin:0}.tg-kanban-filter-input{flex-grow:1;padding:6px 10px;font-size:var(--font-ui-small);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);background-color:var(--background-primary);margin-right:10px}.tg-kanban-filter-input:focus{outline:none;border-color:var(--interactive-accent);box-shadow:0 0 0 1px var(--interactive-accent)}.tg-kanban-column-container{display:flex;flex-grow:1;overflow-x:auto;overflow-y:hidden;padding:10px;gap:10px;height:100%;-webkit-overflow-scrolling:touch;overscroll-behavior-x:auto;scroll-snap-type:x proximity;scroll-behavior:smooth}@media (hover: hover) and (pointer: fine){.tg-kanban-column-container{overscroll-behavior-x:none;scroll-snap-type:none}}.tg-kanban-column{flex:0 0 280px;display:flex;flex-direction:column;background-color:var(--background-secondary);border-radius:var(--radius-m);height:100%;max-height:100%;overflow:hidden;border:1px solid var(--background-modifier-border);scroll-snap-align:start}@media (hover: hover) and (pointer: fine){.tg-kanban-column{scroll-snap-align:none}}.tg-kanban-column-header{padding:8px 12px;font-size:var(--font-ui-mediumn);font-weight:600;border-bottom:1px solid var(--background-modifier-border);flex-shrink:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:uppercase;display:flex;align-items:center}.tg-kanban-column-content{flex-grow:1;overflow-y:auto;padding:8px;display:flex;flex-direction:column;gap:8px;background-color:var(--background-secondary-alt);-webkit-overflow-scrolling:touch;overscroll-behavior:contain;scroll-behavior:smooth}@media (hover: hover) and (pointer: fine){.tg-kanban-column-content{overscroll-behavior:none}}.tg-kanban-card{background-color:var(--background-primary);border-radius:var(--radius-s);padding:10px 12px;border:1px solid var(--background-modifier-border);font-size:var(--font-ui-small);cursor:grab;transition:box-shadow .2s ease-in-out,background-color .2s ease-in-out;max-width:100%;box-sizing:border-box;white-space:nowrap;text-overflow:ellipsis;touch-action:manipulation;user-select:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none}.tg-kanban-card .tg-kanban-card-content{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;max-width:100%}.tg-kanban-card:hover{border-color:var(--background-modifier-border-hover);box-shadow:var(--shadow-m)}.tg-kanban-card.task-completed{background-color:var(--background-secondary);opacity:.7}.tg-kanban-card.task-completed .tg-kanban-card-content{text-decoration:line-through;color:var(--text-muted)}.tg-kanban-card-container{display:flex;align-items:flex-start;margin-bottom:6px}.tg-kanban-card-content p:last-child{margin-bottom:0;margin-block-end:0;margin-block-start:0}.tg-kanban-card-metadata{display:flex;flex-wrap:wrap;gap:4px 8px;font-size:var(--font-ui-small);color:var(--text-muted)}.tg-kanban-card-metadata .task-date,.tg-kanban-card-metadata .task-tags-container,.tg-kanban-card-metadata .task-priority{display:flex;align-items:center;gap:4px;padding:2px 5px;background-color:var(--background-secondary);border-radius:var(--radius-s);margin-inline-start:0;margin-inline-end:0;margin-left:0;margin-right:0}.tg-kanban-card-metadata .task-tag{background-color:var( --background-modifier-accent-hover );color:var(--text-accent);padding:1px 4px;border-radius:var(--radius-s);font-size:calc(var(--font-ui-small) * .9)}.tg-kanban-card-metadata .task-due-date.task-overdue{color:var(--text-error);background-color:var(--background-error)}.tg-kanban-card-metadata .task-due-date.task-due-today{color:var(--text-warning);background-color:var(--background-warning)}.tg-kanban-card-metadata .task-priority.priority-1{color:var(--text-accent)}.tg-kanban-card-metadata .task-priority.priority-2{color:var(--text-warning)}.tg-kanban-card-metadata .task-priority.priority-3{color:var(--text-error);font-weight:bold}.tg-kanban-card-dragging{box-shadow:var(--shadow-l)}.tg-kanban-card-ghost{background-color:var(--background-secondary-alt);border:1px dashed var(--background-modifier-border);box-shadow:none}.tg-kanban-column-content.tg-kanban-drop-target-active{outline:2px dashed var(--background-modifier-accent-hover);outline-offset:-2px}.tg-kanban-column-content.tg-kanban-drop-target-hover{background-color:var(--background-modifier-accent-hover)}.tg-kanban-card--drop-indicator-before{margin-top:10px;border-top:2px dashed var(--interactive-accent);transition:margin-top .1s ease-out,border-top .1s ease-out}.tg-kanban-card--drop-indicator-after{margin-bottom:10px;border-bottom:2px dashed var(--interactive-accent);transition:margin-bottom .1s ease-out,border-bottom .1s ease-out}.tg-kanban-column-content--drop-indicator-empty{border:2px dashed var(--interactive-accent);min-height:50px;box-sizing:border-box;margin-top:5px;margin-bottom:5px}.tg-kanban-card{transition:margin .1s ease-out,padding .1s ease-out,border .1s ease-out,transform .2s ease-out,box-shadow .2s ease-in-out,background-color .2s ease-in-out}.drop-target-active{background-color:#00800033;outline:2px dashed green}.tg-kanban-add-card-container{padding:8px;border-top:1px solid var(--background-modifier-border);flex-shrink:0}.task-genius-add-card-container{padding:8px;margin-top:auto;text-align:center}.tg-kanban-add-card-button{--icon-size: 16px;width:100%;padding:6px 12px;border:none;background-color:transparent;color:var(--text-muted);border-radius:var(--radius-s);cursor:pointer;font-size:var(--font-ui-small);text-align:left;transition:background-color .2s ease-in-out,color .2s ease-in-out}.tg-kanban-add-card-button:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}.tg-kanban-column-dragging{transform:rotate(5deg);opacity:.8;box-shadow:var(--shadow-xl);z-index:1000}.tg-kanban-column-ghost{background-color:var(--background-modifier-border);border:2px dashed var(--background-modifier-accent);opacity:.5}.tg-kanban-column-header{cursor:grab}.tg-kanban-column-header:active{cursor:grabbing}.filter-component{display:flex;flex-wrap:wrap;align-items:center;gap:var(--size-4-2);padding:var(--size-4-2) var(--size-4-3);background-color:var(--background-primary);min-height:48px;flex:1}.filter-pills-container{display:flex;flex-wrap:wrap;gap:var(--size-4-2);flex:1}.filter-controls{display:flex;align-items:center;gap:var(--size-4-2);margin-left:auto}.filter-pill{display:flex;align-items:center;gap:var(--size-4-1);padding:5px 8px;border:1px solid var(--background-modifier-border);border-radius:var(--radius-m);font-size:var(--font-ui-small);animation:filter-pill-appear .2s ease-out;transition:background-color var(--duration-fast),transform var(--duration-fast)}.filter-pill-remove .clickable-icon:hover{background-color:unset}.filter-pill:hover{background-color:var(--background-tertiary)}.filter-pill-category{font-weight:500;color:var(--text-muted)}.filter-pill-value{color:var(--text-normal)}.filter-pill-remove{display:flex;align-items:center;justify-content:center;width:16px;height:16px;border-radius:50%;background:transparent;border:none;padding:0;margin-left:var(--size-4-1);cursor:pointer;color:var(--text-faint);font-size:14px;line-height:1;transition:background-color var(--duration-fast),color var(--duration-fast)}.filter-pill-remove:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}.filter-pill-remove-icon{font-size:16px;display:flex;align-items:center;justify-content:center}.filter-add-button,.filter-clear-all-button{display:flex;align-items:center;padding:6px 10px;font-size:var(--font-ui-small);cursor:pointer}.filter-add-button{gap:var(--size-4-1);color:var(--text-muted)}.filter-add-icon{font-weight:var(--font-bold);display:flex;align-items:center;justify-content:center}.filter-dropdown{position:fixed;width:220px;background-color:var(--background-primary);border-radius:var(--radius-m);box-shadow:var(--shadow-l);border:1px solid var(--background-modifier-border);z-index:var(--layer-popover);max-height:400px;display:flex;flex-direction:column;opacity:0;transform:translateY(-8px);transition:opacity var(--duration-normal),transform var(--duration-normal);overflow:hidden}.filter-dropdown-visible{opacity:1;transform:translateY(0)}.filter-dropdown-header{padding:var(--size-4-2);border-bottom:1px solid var(--background-modifier-border)}.filter-dropdown-search{width:100%;padding:var(--size-4-2);border:1px solid var(--background-modifier-border);border-radius:var(--radius-m);background-color:var(--background-secondary);font-size:var(--font-ui-small);outline:none}.filter-dropdown-search:focus{border-color:var(--interactive-accent);box-shadow:0 0 0 2px var(--focus-ring-color)}.filter-dropdown-list{overflow-y:auto;max-height:350px}.filter-dropdown-item{display:flex;align-items:center;padding:var(--size-4-2) var(--size-4-3);cursor:pointer;font-size:var(--font-ui-small);color:var(--text-normal);transition:background-color var(--duration-fast)}.filter-dropdown-item:hover{background-color:var(--background-secondary)}.filter-dropdown-item-label{flex:1}.filter-dropdown-item-arrow{color:var(--text-faint);font-size:18px}.filter-dropdown-item-arrow.back{margin-right:var(--size-4-2);display:flex;align-items:center;justify-content:center}.filter-dropdown-back{color:var(--text-muted)}.filter-dropdown-separator{height:1px;background-color:var(--divider-color);margin:var(--size-4-1) 0}.filter-dropdown-empty{padding:var(--size-4-4);text-align:center;color:var(--text-faint);font-size:var(--font-ui-small)}.filter-dropdown-value-item{padding-left:var(--size-4-4)}.filter-dropdown-category{padding:var(--size-4-2) 0;color:var(--text-muted);font-weight:500}.filter-dropdown-value-preview{padding:var(--size-4-1) var(--size-4-4);cursor:pointer;transition:background-color var(--duration-fast);font-size:var(--font-ui-small);color:var(--text-normal)}.filter-dropdown-value-preview:hover{background-color:var(--background-secondary)}@keyframes filter-pill-appear{0%{opacity:0;transform:scale(.9)}to{opacity:1;transform:scale(1)}}.filter-pill-removing{opacity:0;transform:scale(.9);transition:opacity .15s ease-out,transform .15s ease-out}.gantt-chart-container{width:100%;height:100%;overflow:auto;position:relative;background-color:var(--background-secondary);--gantt-header-height: 50px;--gantt-row-height: 40px;--gantt-bar-height: 20px;--gantt-bar-radius: 3px;--gantt-bg-color: var(--background-secondary);--gantt-grid-color: var(--background-modifier-border);--gantt-row-color: var(--background-secondary);--gantt-bar-color: var(--color-blue);--gantt-milestone-color: var(--color-purple);--gantt-progress-color: var(--color-blue);--gantt-today-color: var(--color-accent)}.gantt-svg{display:block;font-family:var(--font-interface);font-size:var(--font-ui-small);user-select:none}.gantt-header-bg{fill:var(--background-primary);stroke:var(--background-modifier-border);stroke-width:1px}.gantt-header-text{fill:var(--text-muted);font-weight:500}.gantt-grid-bg{fill:transparent;stroke:var(--background-modifier-border);stroke-width:0}.gantt-grid-line-vertical{stroke:var(--background-modifier-border);stroke-width:1px;stroke-dasharray:2,2}.gantt-task-item{cursor:pointer}.gantt-task-bar{fill:var(--interactive-accent);stroke:var(--interactive-accent-hover);stroke-width:1px;transition:fill .1s ease-in-out}.gantt-task-item:hover .gantt-task-bar{fill:var(--interactive-accent-hover)}.gantt-task-milestone{fill:var(--color-orange);stroke:var(--color-orange-border);stroke-width:1px}.gantt-task-label{fill:var(--text-on-accent);font-size:calc(var(--font-ui-small) * .9);pointer-events:none;white-space:pre}.gantt-task-bar.status-completed{fill:var(--color-green);stroke:var(--color-green-border)}.gantt-header{position:sticky;top:0;left:0;right:0;z-index:10;height:var(--gantt-header-height);border-bottom:1px solid var(--gantt-grid-color);user-select:none;background-color:var(--gantt-bg-color);pointer-events:none;width:100%;overflow:hidden}.gantt-header-row{position:relative;height:50%;width:100%}.gantt-header-row.primary{border-bottom:1px solid var(--gantt-grid-color);font-weight:600}.gantt-header-cell{position:absolute;height:100%;display:flex;align-items:center;justify-content:center;text-align:center;font-size:12px;color:var(--text-normal);border-right:1px solid var(--gantt-grid-color);box-sizing:border-box;background-color:var(--gantt-bg-color);pointer-events:auto}.gantt-body{position:relative;overflow:auto;height:100%;padding-top:var(--gantt-header-height);margin-top:calc(var(--gantt-header-height) * -1)}.gantt-grid{position:absolute;top:var(--gantt-header-height);left:0;height:calc(100% - var(--gantt-header-height));min-width:100%}.gantt-grid-column{position:absolute;top:0;height:100%;border-right:1px solid var(--gantt-grid-color);box-sizing:border-box}.gantt-grid-column.today{background-color:var(--gantt-today-color)}.gantt-grid-row{position:absolute;left:0;border-bottom:1px solid var(--gantt-grid-color);box-sizing:border-box;background-color:var(--gantt-row-color)}.gantt-grid-row:nth-child(odd){background-color:var(--gantt-bg-color)}.gantt-bars{position:absolute;top:var(--gantt-header-height);left:0;height:calc(100% - var(--gantt-header-height));min-width:100%;pointer-events:none}.gantt-task-container{position:absolute;box-sizing:border-box;pointer-events:auto;cursor:pointer;transition:transform .1s ease}.gantt-task-container:hover{z-index:10;transform:translateY(-2px)}.gantt-task-bar.milestone{background-color:var(--gantt-milestone-color);width:15px!important;height:15px!important;border-radius:50%;transform:rotate(45deg);top:50%;margin-top:-7.5px;left:50%;margin-left:-7.5px}.gantt-task-progress{position:absolute;top:0;left:0;height:100%;background-color:var(--gantt-progress-color);opacity:.7}.gantt-task-label{position:absolute;left:calc(100% + 8px);top:0;white-space:nowrap;font-size:12px;color:var(--text-normal);line-height:var(--gantt-bar-height)}.gantt-task-container.right-aligned .gantt-task-label{left:auto;right:calc(100% + 8px);text-align:right}@media (max-width: 680px){.gantt-header-cell{font-size:10px}.gantt-task-label{font-size:10px}}.gantt-chart-container{display:flex;flex-direction:column;height:100%;overflow:hidden;position:relative}.gantt-header-container{height:40px;flex-shrink:0;overflow:hidden;position:relative;border-bottom:1px solid var(--background-modifier-border);background-color:var(--background-secondary)}.gantt-header-svg{display:block}.gantt-header-tick-major,.gantt-header-tick-minor,.gantt-header-tick-day,.gantt-header-today-marker{stroke:var(--background-modifier-border);stroke-width:1}.gantt-header-tick-major{stroke-width:1.5}.gantt-header-today-marker{stroke:var(--color-orange);stroke-width:1.5;stroke-dasharray:4,2}.gantt-header-label-major,.gantt-header-label-minor,.gantt-header-label-day{font-size:var(--font-ui-small);fill:var(--text-muted);user-select:none;pointer-events:none}.gantt-header-label-major{font-weight:500;fill:var(--text-normal)}.gantt-scroll-container{flex-grow:1;overflow:auto;position:relative}.gantt-content-wrapper{position:relative;background:var(--background-primary)}.gantt-grid-line-major,.gantt-grid-line-minor{stroke:var(--background-modifier-border-hover);stroke-width:.5}.gantt-grid-line-major{stroke-width:1}.gantt-grid-line-horizontal{stroke:var(--background-modifier-border);stroke-width:1}.gantt-grid-today-marker{stroke:var(--color-orange);stroke-width:1;stroke-dasharray:4,2}.gantt-task-item{cursor:pointer}.gantt-task-bar{fill:var(--color-blue);stroke:var(--color-blue-hover);stroke-width:.5;transition:fill .1s ease}.gantt-task-item:hover .gantt-task-bar{fill:var(--color-accent)}.gantt-task-milestone{fill:var(--color-purple);stroke:var(--color-purple);stroke-width:1;transition:fill .1s ease}.gantt-task-item:hover .gantt-task-milestone{fill:var(--color-accent)}.gantt-task-item.status-done .gantt-task-bar,.gantt-task-item.status-done .gantt-task-milestone{fill:var(--color-green);stroke:var(--color-green);opacity:.7}.gantt-task-item.status-cancelled .gantt-task-bar,.gantt-task-item.status-cancelled .gantt-task-milestone{fill:var(--color-red);stroke:var(--color-red);opacity:.6;text-decoration:line-through}.gantt-task-label-fo{pointer-events:none;overflow:hidden;user-select:none}.gantt-task-label-markdown{color:var(--text-on-accent);font-size:var(--font-ui-smaller);line-height:1.3;padding:0 2px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:flex;align-items:center;height:100%}.gantt-task-label-markdown p{margin:0!important}.gantt-milestone-label-container p{margin-block-start:0;margin-block-end:0;margin-inline-start:0;margin-inline-end:0;color:var(--text-normal);font-size:var(--font-ui-smaller);line-height:1.3;padding:0 2px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:flex;align-items:center;height:100%}.gantt-task-item.status-done .gantt-task-label-markdown{color:var(--text-on-accent)}.gantt-task-item.status-cancelled .gantt-task-label-markdown{color:var(--text-on-accent);text-decoration:line-through}.gantt-milestone-label{fill:var(--text-normal)}.gantt-filter-area{display:flex;align-items:center;justify-content:flex-end;width:100%;padding-left:var(--size-2-2);padding-right:var(--size-4-2);background-color:var(--background-primary)}.gantt-filter-area .filter-component{flex:1}.gantt-offscreen-indicator{position:absolute;top:calc(50% + 20px);transform:translateY(-50%);width:8px;height:8px;background-color:#80808099;border-radius:50%;z-index:10;pointer-events:none;display:none;transition:opacity .2s ease-in-out;opacity:1}.gantt-offscreen-indicator[style*="display: none"]{opacity:0}.gantt-offscreen-indicator-left{left:5px}.gantt-offscreen-indicator-right{right:5px}.gantt-indicator-container{position:absolute;top:0;bottom:0;width:var(--size-4-3);z-index:10;pointer-events:none;overflow:hidden}.gantt-indicator-container-left{left:0}.gantt-indicator-container-right{right:0}.gantt-single-indicator{position:absolute;left:var(--size-2-1);width:var(--size-4-2);height:var(--size-4-2);border-radius:50%;background-color:var(--text-faint);pointer-events:auto;cursor:default}.gantt-single-indicator:hover{background-color:var(--text-accent)}.gantt-chart-container .gantt-indicator-container{top:calc(var(--header-height, 40px) + var(--filter-height, 0px));bottom:15px}.gantt-chart-container .gantt-indicator-container-right{right:15px}.gantt-task-label p{margin:0;line-height:var(--gantt-bar-height);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-property-container{display:flex;flex-direction:column;height:100%;width:100%;overflow:hidden}.task-property-content{display:flex;flex-direction:row;flex:1;overflow:hidden}.task-property-left-column{width:max(120px,30%);min-width:min(120px,30%);max-width:300px;display:flex;flex-direction:column;border-right:1px solid var(--background-modifier-border);overflow:hidden}.is-phone .task-property-left-column{max-width:100%}.task-property-right-column{flex:1;display:flex;flex-direction:column;overflow:hidden}.task-property-sidebar-header{display:flex;justify-content:space-between;align-items:center;padding:var(--size-4-2) var(--size-4-4);border-bottom:1px solid var(--background-modifier-border);height:var(--size-4-10)}.task-property-sidebar-title{font-weight:600;font-size:14px}.multi-select-mode .task-property-multi-select-btn{color:var(--color-accent)}.task-property-multi-select-btn{cursor:pointer;color:var(--text-muted);display:flex;align-items:center;justify-content:center}.task-property-multi-select-btn:hover{color:var(--text-normal)}.task-property-sidebar-list{flex:1;overflow-y:auto;padding:var(--size-4-2)}.task-property-list-item{display:flex;align-items:center;padding:4px 12px;cursor:pointer;border-radius:var(--radius-s)}.task-property-list-item:hover{background-color:var(--background-modifier-hover)}.task-property-list-item.selected{background-color:var(--background-modifier-active)}.task-property-icon{margin-right:8px;color:var(--text-muted);display:flex;align-items:center;justify-content:center}.task-property-name{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-property-count{margin-left:8px;font-size:.8em;color:var(--text-muted);background-color:var(--background-modifier-border);border-radius:10px;padding:1px 6px}.task-property-task-header{display:flex;justify-content:space-between;align-items:center;padding:var(--size-4-2) var(--size-4-4);border-bottom:1px solid var(--background-modifier-border);height:var(--size-4-10)}.task-property-task-title{font-weight:600;font-size:16px}.task-property-task-count{color:var(--text-muted)}.task-property-task-list{flex:1;overflow-y:auto}.task-property-empty-state{display:flex;align-items:center;justify-content:center;height:100%;color:var(--text-muted);font-style:italic;padding:16px}.is-phone .task-property-left-column{position:absolute;left:0;top:0;height:100%;z-index:10;background-color:var(--background-secondary);width:100%;transform:translate(-100%);transition:transform .3s ease-in-out;border-right:1px solid var(--background-modifier-border)}.is-phone .task-property-left-column.is-visible{transform:translate(0)}.is-phone .task-property-sidebar-toggle{display:flex;align-items:center;justify-content:center;margin-right:8px}.is-phone .task-property-sidebar-close{--icon-size: var(--size-4-4);position:absolute;top:var(--size-4-2);right:10px;z-index:15;display:flex;align-items:center;justify-content:center}.is-phone .task-property-container:has(.task-property-left-column.is-visible):before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--background-modifier-cover);opacity:.5;z-index:5;transition:opacity .3s ease-in-out}.is-phone .task-property-container{position:relative;overflow:hidden}.is-phone .task-property-sidebar-header:has(.task-property-sidebar-close){padding-right:var(--size-4-12)}.table-view-adapter{width:100%;display:flex;flex-direction:column;gap:0;height:100%;overflow:hidden}.task-table-container{display:flex;flex-direction:column;height:100%;overflow:hidden;position:relative;background-color:var(--background-primary)}.task-table{width:100%;border-collapse:collapse;table-layout:fixed;font-size:var(--font-ui-small);flex:1;min-height:0;min-width:max-content}.task-table-wrapper{flex:1;overflow:auto;min-height:0;position:relative;overflow-x:auto;overflow-y:auto;scroll-behavior:smooth}.task-table-header{position:sticky;top:0;z-index:10;background-color:var(--background-secondary);border-bottom:2px solid var(--background-modifier-border);min-width:max-content}.task-table-header-row{height:40px}.task-table-header-cell{padding:8px 12px;text-align:left;font-weight:600;color:var(--text-muted);border-right:1px solid var(--background-modifier-border);position:relative;user-select:none;background-color:var(--background-secondary);white-space:nowrap}.task-table-header-cell:last-child{border-right:none}.task-table-header-cell.sortable{cursor:pointer}.task-table-header-cell.sortable:hover{background-color:var(--background-modifier-hover)}.task-table-header-content{display:flex;align-items:center;justify-content:space-between;gap:4px}.task-table-header-title{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.task-table-sort-icon{font-size:12px;opacity:.5;transition:opacity .2s;display:flex;align-items:center;width:16px;height:16px}.task-table-sort-icon.asc,.task-table-sort-icon.desc{opacity:1;color:var(--text-accent)}.task-table-resize-handle{position:absolute;top:0;right:0;width:4px;height:100%;cursor:col-resize;background-color:transparent;transition:background-color .2s}.task-table-resize-handle:hover{background-color:var(--text-accent)}.task-table-body{background-color:var(--background-primary)}.task-table-row{height:40px;border-bottom:1px solid var(--background-modifier-border);transition:background-color .2s}.task-table-row:hover{background-color:var(--background-modifier-hover)}.task-table-row.selected{background-color:var(--background-modifier-active-hover)}.task-table-row:nth-child(even){background-color:var(--background-secondary-alt)}.task-table-row:nth-child(even):hover{background-color:var(--background-modifier-hover)}.task-table-row:nth-child(even).selected{background-color:var(--background-modifier-active-hover)}.task-table-cell{padding:8px 12px;vertical-align:middle;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.task-table-cell:last-child{border-right:none}.task-table-cell.editing{padding:0}.task-table-tree-indent{display:inline-flex;align-items:center;gap:4px}.task-table-cell:has(.task-table-expand-btn){padding-left:0}.task-table-row.task-table-subtask{background-color:var(--background-secondary)}.task-table-expand-btn{cursor:pointer;user-select:none;width:20px;height:20px;padding:0;display:flex;align-items:center;justify-content:center;border-radius:2px;font-size:10px;transition:background-color .2s}.task-table-expand-btn:hover{background-color:var(--background-modifier-hover)}.task-table-row-level-1 .task-table-cell:first-child{padding-left:32px}.task-table-row-level-2 .task-table-cell:first-child{padding-left:52px}.task-table-row-level-3 .task-table-cell:first-child{padding-left:72px}.task-table-row-level-4 .task-table-cell:first-child{padding-left:92px}.task-table-row-level-5 .task-table-cell:first-child{padding-left:112px}.task-table-text{color:var(--text-normal)}.task-table-number{text-align:right;color:var(--text-muted);font-variant-numeric:tabular-nums}.task-table-status{display:flex;align-items:center;gap:6px}.task-table-status-icon{font-size:14px;display:flex;align-items:center;width:16px;height:16px}.task-table-status-text{flex:1;overflow:hidden;text-overflow:ellipsis}.task-table-status.completed .task-table-status-icon{color:var(--text-success)}.task-table-status.in-progress .task-table-status-icon{color:var(--text-warning)}.task-table-status.abandoned .task-table-status-icon{color:var(--text-error)}.task-table-status.planned .task-table-status-icon{color:var(--text-muted)}.task-table-status.not-started .task-table-status-icon{color:var(--text-faint)}.task-table-priority{display:flex;align-items:center;gap:6px}.task-table-priority.clickable-priority{cursor:pointer;padding:4px;border-radius:4px;transition:background-color .2s}.task-table-priority.clickable-priority:hover{background-color:var(--background-modifier-hover)}.task-table-priority-icon{font-size:14px;display:flex;align-items:center;width:16px;height:16px}.task-table-priority-icon.high{color:var(--text-error)}.task-table-priority-icon.medium{color:var(--text-warning)}.task-table-priority-icon.low{color:var(--text-muted)}.task-table-priority-text{flex:1;overflow:hidden;text-overflow:ellipsis}.task-table-priority-empty{color:var(--text-faint);font-style:italic}.task-table-date{display:flex;flex-direction:column;gap:2px;cursor:pointer;transition:background-color .2s;padding:4px;border-radius:4px}.task-table-date:hover{background-color:var(--background-modifier-hover)}.task-table-date-text{font-size:var(--font-ui-small);color:var(--text-normal)}.task-table-date-relative{font-size:var(--font-ui-smaller);font-weight:500}.task-table-date-relative.today{color:var(--text-success)}.task-table-date-relative.tomorrow{color:var(--text-accent)}.task-table-date-relative.yesterday{color:var(--text-muted)}.task-table-date-relative.overdue{color:var(--text-error)}.task-table-date-relative.upcoming{color:var(--text-warning)}.task-table-date-empty{color:var(--text-faint);font-style:italic}.task-table-tags{display:flex;flex-wrap:wrap;gap:4px;align-items:center}.task-table-tag-chip{background-color:var(--background-modifier-accent);color:var(--text-accent);padding:2px 6px;border-radius:8px;font-size:var(--font-ui-smaller);font-weight:500;white-space:nowrap}.task-table-tags-empty{color:var(--text-faint);font-style:italic}.task-table-text-input,.task-table-tags-input{border:none!important;background:transparent!important;outline:none!important;width:100%!important;padding:0!important;font:inherit!important;color:var(--text-normal)!important}.task-table-text-input:focus,.task-table-tags-input:focus{background-color:var(--background-modifier-form-field)!important;border-radius:3px!important;padding:2px 4px!important}.task-count-icon{font-size:16px;display:flex;align-items:center;width:16px;height:16px}.task-table-empty-row{height:80px}.task-table-empty-cell{text-align:center;color:var(--text-muted);font-style:italic;vertical-align:middle}.task-table-loading{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);padding:20px;background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:8px;color:var(--text-muted);font-size:var(--font-ui-small);z-index:100}.task-table.resizing{user-select:none}.task-table.resizing *{cursor:col-resize!important}.virtual-scroll-spacer{pointer-events:none;visibility:hidden}@media (max-width: 768px){.task-table-container{font-size:var(--font-ui-smaller)}.task-table-wrapper{overflow-x:auto}.task-table{min-width:800px}.task-table-header-cell,.task-table-cell{padding:6px 8px}.task-table-row{height:36px}.task-table-header-row{height:36px}}.theme-dark .task-table-container{border-color:var(--background-modifier-border)}.theme-dark .task-table-row:nth-child(even){background-color:var(--background-primary-alt)}@media (prefers-contrast: high){.task-table-container{border-width:2px}.task-table-header-cell,.task-table-cell{border-width:1px}.task-table-row{border-bottom-width:1px}}@media print{.task-table-container{border:none;overflow:visible;height:auto}.task-table-header{position:static}.task-table-resize-handle{display:none}.task-table-expand-btn{display:none}}.virtual-scroll-spacer-top{pointer-events:none}.virtual-scroll-spacer-top td{padding:0!important;border:none!important;background:transparent!important}.task-table-context-menu{background:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:4px;box-shadow:0 2px 8px #00000026;z-index:1000;min-width:120px}.task-table-context-menu-item{padding:6px 12px;cursor:pointer;transition:background-color .1s ease}.task-table-context-menu-item:hover{background-color:var(--background-modifier-hover)}.task-table-date-input{cursor:pointer;background:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:3px;padding:4px 8px;width:100%}.task-table-date-input:hover{border-color:var(--background-modifier-border-hover)}.task-table-date-input:focus{border-color:var(--interactive-accent);outline:none}.task-table-project-input,.task-table-context-input,.task-table-tags-input{background:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:3px;padding:4px 8px;width:100%}.task-table-project-input:focus,.task-table-context-input:focus,.task-table-tags-input:focus{border-color:var(--interactive-accent);outline:none}.task-table-row.selected{background-color:var(--background-modifier-hover)}.task-table-row:hover{background-color:var(--background-modifier-hover-weak)}@media (max-width: 768px){.task-table{font-size:.9em}th[data-column-id=rowNumber]{max-width:40px!important;min-width:40px!important;width:40px!important}.task-table-tree-container{gap:0!important}.task-table-expand-btn{margin-right:0!important}td[data-column-id=rowNumber]{max-width:40px!important;min-width:40px!important;width:40px!important}.task-table-header-cell,.task-table-cell{padding:6px 4px}}.task-table-header-bar{display:flex;justify-content:space-between;align-items:center;padding:6px 8px;background-color:var(--background-secondary);border-bottom:1px solid var(--background-modifier-border);border-radius:6px 6px 0 0;margin-bottom:0;flex-shrink:0;min-height:40px}.table-header-left{display:flex;align-items:center;gap:12px}.table-header-right{display:flex;align-items:center;gap:8px}.task-count-container{display:flex;align-items:center;gap:8px;padding:6px 12px;background-color:var(--background-primary);border-radius:4px;border:1px solid var(--background-modifier-border)}.task-count-text{font-size:var(--font-ui-small);font-weight:500;color:var(--text-normal)}.table-controls-container{display:flex;align-items:center;gap:8px}.table-control-btn{display:flex;align-items:center;gap:6px;padding:8px 12px;background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:4px;cursor:pointer;font-size:var(--font-ui-small);color:var(--text-normal);transition:all .2s ease;box-shadow:unset!important}.table-control-btn:hover{background-color:var(--background-modifier-hover)}.table-control-btn:active{background-color:var(--background-modifier-active)}.tree-mode-btn.active{background-color:var(--text-accent);color:var(--text-on-accent);border-color:var(--text-accent)}.tree-mode-icon,.refresh-icon,.column-icon{font-size:14px;display:flex;align-items:center;justify-content:center}.tree-mode-text,.refresh-text,.column-text{font-weight:500}.dropdown-arrow{font-size:10px;transition:transform .2s ease}.column-dropdown{position:relative}.column-dropdown-menu{position:absolute;top:100%;right:0;margin-top:4px;background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:4px;box-shadow:var(--shadow-l);z-index:1000;min-width:200px;max-height:300px;overflow-y:auto}.column-toggle-item{display:flex;align-items:center;gap:8px;padding:8px 12px;cursor:pointer;transition:background-color .2s ease}.column-toggle-item:hover{background-color:var(--background-modifier-hover)}.column-toggle-checkbox{margin:0;cursor:pointer}.column-toggle-label{flex:1;font-size:var(--font-ui-small);color:var(--text-normal);cursor:pointer;margin:0}@media (max-width: 768px){.task-table-header-bar{flex-direction:column;gap:12px;align-items:stretch}.table-header-left{display:none}.table-header-left,.table-header-right{justify-content:center}.table-controls-container{justify-content:center;flex-wrap:wrap}.table-control-btn{flex:1;min-width:100px;justify-content:center}.column-dropdown-menu{right:auto;left:0;width:100%}}.theme-dark .task-table-header-bar{background-color:var(--background-secondary-alt)}.theme-dark .column-dropdown-menu{background-color:var(--background-primary-alt);border-color:var(--background-modifier-border-hover)}.custom-suggest-dropdown{background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:4px;box-shadow:var(--shadow-l);z-index:1000;position:absolute;max-height:200px;overflow-y:auto;min-width:150px}.custom-suggest-dropdown .suggestion-item{padding:8px 12px;cursor:pointer;border-bottom:1px solid var(--background-modifier-border);transition:background-color .2s;font-size:var(--font-ui-small);color:var(--text-normal)}.custom-suggest-dropdown .suggestion-item:last-child{border-bottom:none}.custom-suggest-dropdown .suggestion-item:hover,.custom-suggest-dropdown .suggestion-item.selected{background-color:var(--background-modifier-hover)}.custom-suggest-dropdown .suggestion-item.selected{color:var(--text-accent)}.task-table-subtask{border-left:2px solid var(--background-modifier-border-hover)}.task-table-parent .task-table-cell:first-child{font-weight:500}.task-table-subtask-cell{border-left:1px solid var(--background-modifier-border-focus)}.task-table-tree-container{display:flex;align-items:center;gap:6px;width:100%}.task-table-tree-structure{display:flex;align-items:center;gap:2px;flex-shrink:0}.task-table-tree-line{font-family:monospace;font-size:12px;color:var(--text-faint);line-height:1;width:16px;text-align:center}.task-table-tree-connector{color:var(--text-muted)}.task-table-tree-vertical{color:var(--text-faint)}.task-table-subtask-indicator{font-size:10px;color:var(--text-accent);margin-right:6px;margin-left:4px;flex-shrink:0;font-weight:bold}.task-table-top-level-expand{margin-right:6px}.task-table-content-wrapper{flex:1;min-width:0}.task-table-child-indicator{font-size:10px;color:var(--text-muted);margin-left:6px;flex-shrink:0}.task-table-status.clickable-status{cursor:pointer;padding:4px;border-radius:4px;transition:background-color .2s}.task-table-status.clickable-status:hover{background-color:var(--background-modifier-hover)}.task-table-priority-icon.highest{color:var(--text-error);filter:brightness(1.2)}.task-table-priority-icon.lowest{color:var(--text-faint)}.task-table-expand-btn.clickable-icon{opacity:.7;transition:opacity .2s,background-color .2s}.task-table-expand-btn.clickable-icon:hover{opacity:1}.task-table-row-level-1 .task-table-cell:first-child,.task-table-row-level-2 .task-table-cell:first-child,.task-table-row-level-3 .task-table-cell:first-child,.task-table-row-level-4 .task-table-cell:first-child,.task-table-row-level-5 .task-table-cell:first-child{padding-left:12px}.tg-quadrant-component-container{height:100%;display:flex;flex-direction:column;overflow:hidden;background:var(--background-primary);width:100%}.tg-quadrant-header{display:flex;align-items:center;justify-content:space-between;padding:var(--size-4-3) var(--size-4-4);background:var(--background-primary);flex-shrink:0}.tg-quadrant-title{font-size:var(--font-ui-medium);font-weight:var(--font-semibold);color:var(--text-normal);margin:0}.tg-quadrant-controls{display:flex;align-items:center;gap:var(--size-2-3)}.tg-quadrant-sort-select{padding:var(--size-2-2) var(--size-2-3);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);background:var(--background-primary);color:var(--text-normal);font-size:var(--font-ui-small);cursor:pointer;transition:border-color .2s ease}.tg-quadrant-sort-select:hover{border-color:var(--background-modifier-border-hover)}.tg-quadrant-sort-select:focus{border-color:var(--color-accent);outline:none}.tg-quadrant-toggle-empty{padding:var(--size-2-2);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);background:var(--background-primary);color:var(--text-muted);cursor:pointer;transition:all .2s ease;width:28px;height:28px;display:flex;align-items:center;justify-content:center}.tg-quadrant-toggle-empty:hover{background:var(--background-modifier-hover);color:var(--text-normal);border-color:var(--background-modifier-border-hover)}.tg-quadrant-filter-container{flex-shrink:0;border-bottom:1px solid var(--background-modifier-border)}.tg-quadrant-grid{display:grid;grid-template-columns:1fr 1fr;grid-template-rows:1fr 1fr;gap:1px;flex:1;background:var(--background-modifier-border);overflow:hidden}.tg-quadrant-column{display:flex;flex-direction:column;background:var(--background-primary);min-height:0;overflow:hidden;position:relative}.tg-quadrant-column--hidden{display:none}.tg-quadrant-column .tg-quadrant-header{padding:var(--size-4-2) var(--size-4-3);background:var(--background-secondary);border-bottom:1px solid var(--background-modifier-border);flex-shrink:0;position:relative;min-height:var(--size-4-12)}.tg-quadrant-title-container{display:flex;align-items:center;gap:var(--size-2-2);margin-bottom:var(--size-2-1)}.tg-quadrant-priority{font-size:var(--font-ui-medium);line-height:1;opacity:.8}.tg-quadrant-column .tg-quadrant-title{font-size:var(--font-ui-small);font-weight:var(--font-semibold);color:var(--text-normal);margin:0}.tg-quadrant-description{font-size:var(--font-ui-smaller);color:var(--text-muted);margin-bottom:var(--size-2-2);line-height:1.3}.tg-quadrant-count{font-size:var(--font-ui-smaller);color:var(--text-faint);background:var(--background-modifier-border);padding:var(--size-2-1) var(--size-2-2);border-radius:var(--radius-s);font-weight:var(--font-medium)}.tg-quadrant-column-content{flex:1;overflow-y:auto;padding:var(--size-2-3);min-height:100px}.tg-quadrant-column-content::-webkit-scrollbar{width:8px}.tg-quadrant-column-content::-webkit-scrollbar-track{background:transparent}.tg-quadrant-column-content::-webkit-scrollbar-thumb{background:var(--background-modifier-border);border-radius:var(--radius-s)}.tg-quadrant-column-content::-webkit-scrollbar-thumb:hover{background:var(--background-modifier-border-hover)}.tg-quadrant-column-content--drop-active{background:var(--background-modifier-hover);border:2px dashed var(--color-accent);border-radius:var(--radius-m)}.quadrant-urgent-important .tg-quadrant-header:before{content:"";position:absolute;top:0;left:0;right:0;height:3px;background:var(--text-error);opacity:.6}.quadrant-not-urgent-important .tg-quadrant-header:before{content:"";position:absolute;top:0;left:0;right:0;height:3px;background:var(--color-accent);opacity:.6}.quadrant-urgent-not-important .tg-quadrant-header:before{content:"";position:absolute;top:0;left:0;right:0;height:3px;background:var(--text-warning);opacity:.6}.quadrant-not-urgent-not-important .tg-quadrant-header:before{content:"";position:absolute;top:0;left:0;right:0;height:3px;background:var(--text-muted);opacity:.4}.tg-quadrant-card{background:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);margin-bottom:var(--size-2-3);padding:var(--size-4-2);cursor:pointer;transition:all .15s ease;position:relative}.tg-quadrant-card:hover{background:var(--background-modifier-hover);border-color:var(--background-modifier-border-hover);transform:translateY(-1px);box-shadow:var(--shadow-s)}.tg-quadrant-card:active{transform:translateY(0)}.tg-quadrant-card:last-child{margin-bottom:0}.tg-quadrant-card-header{display:flex;align-items:flex-start;justify-content:space-between;margin-bottom:var(--size-2-2);gap:var(--size-2-2)}.tg-quadrant-card-checkbox{flex-shrink:0;margin-top:2px}.tg-quadrant-card-actions{flex-shrink:0;opacity:0;transition:opacity .2s ease}.tg-quadrant-card:hover .tg-quadrant-card-actions{opacity:1}.tg-quadrant-card-more-btn{background:none;border:none;padding:var(--size-2-1);border-radius:var(--radius-s);color:var(--text-muted);cursor:pointer;transition:all .2s ease;width:24px;height:24px;display:flex;align-items:center;justify-content:center}.tg-quadrant-card-more-btn:hover{background:var(--background-modifier-hover);color:var(--text-normal)}.tg-quadrant-card-content{margin-bottom:var(--size-2-2)}.tg-quadrant-card-title{font-size:var(--font-ui-small);line-height:1.4;color:var(--text-normal);margin-bottom:var(--size-2-1);word-wrap:break-word;font-weight:var(--font-normal)}.tg-quadrant-card-priority{font-size:var(--font-ui-small);margin-left:var(--size-2-1);opacity:.8}.tg-quadrant-card-tags{display:flex;flex-wrap:wrap;gap:var(--size-2-1);margin-top:var(--size-2-2)}.tg-quadrant-card-tag{background:var(--background-modifier-border);color:var(--text-muted);padding:var(--size-2-1) var(--size-2-2);border-radius:var(--radius-s);font-size:var(--font-ui-smaller);font-weight:var(--font-medium);border:1px solid transparent;transition:all .2s ease}.tg-quadrant-card-tag:hover{background:var(--background-modifier-hover);color:var(--text-normal)}.tg-quadrant-tag--urgent{background:var(--background-modifier-error);color:var(--text-error);border-color:var(--text-error)}.tg-quadrant-tag--important{background:var(--background-modifier-accent);color:var(--text-accent);border-color:var(--color-accent)}.tg-quadrant-card-metadata{display:flex;align-items:center;justify-content:space-between;font-size:var(--font-ui-smaller);color:var(--text-faint);gap:var(--size-2-2)}.tg-quadrant-card-due-date{display:flex;align-items:center;gap:var(--size-2-1);background:var(--background-modifier-border);padding:var(--size-2-1) var(--size-2-2);border-radius:var(--radius-s);font-weight:var(--font-medium)}.tg-quadrant-card-due-date-icon{width:12px;height:12px;opacity:.7}.tg-quadrant-card-due-date--urgent{color:var(--text-warning)}.tg-quadrant-card-due-date--overdue{color:var(--text-error)}.tg-quadrant-card-file-info{display:flex;align-items:center;justify-content:space-between;gap:var(--size-4-2);opacity:.7;transition:opacity .2s ease}.tg-quadrant-card:hover .tg-quadrant-card-file-info{opacity:1}.tg-quadrant-card-file-icon{width:12px;height:12px}.tg-quadrant-card-file-name{font-size:var(--font-ui-smaller);max-width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.tg-quadrant-card-line{color:var(--text-faint);font-size:var(--font-ui-smaller);opacity:.6;font-weight:var(--font-medium)}.tg-quadrant-card--priority-highest{border-left:3px solid var(--text-error)}.tg-quadrant-card--priority-high{border-left:3px solid var(--text-warning)}.tg-quadrant-card--priority-medium{border-left:3px solid var(--color-accent)}.tg-quadrant-card--priority-low{border-left:3px solid var(--text-success)}.tg-quadrant-card--priority-lowest{border-left:3px solid var(--text-muted)}.tg-quadrant-card--dragging{box-shadow:var(--shadow-l)}.tg-quadrant-card--chosen{background:var(--background-modifier-hover);border-color:var(--color-accent);box-shadow:var(--shadow-s)}.tg-quadrant-card--drag{box-shadow:var(--shadow-l);z-index:1000;border-color:var(--color-accent)}.tg-quadrant-empty-state{display:flex;flex-direction:column;align-items:center;justify-content:center;height:120px;color:var(--text-faint);text-align:center;padding:var(--size-4-4);opacity:.8}.tg-quadrant-empty-icon{width:32px;height:32px;margin-bottom:var(--size-2-3);opacity:.5;color:var(--text-faint)}.tg-quadrant-empty-message{font-size:var(--font-ui-small);line-height:1.4;font-weight:var(--font-medium)}@media (max-width: 768px){.tg-quadrant-grid{grid-template-columns:1fr;grid-template-rows:repeat(4,1fr)}.tg-quadrant-header{padding:var(--size-2-3) var(--size-4-2)}.tg-quadrant-column .tg-quadrant-header{padding:var(--size-2-3) var(--size-4-2)}.tg-quadrant-card{padding:var(--size-2-3)}.tg-quadrant-card-title{font-size:var(--font-ui-smaller)}.tg-quadrant-controls{gap:var(--size-2-2)}}.tg-quadrant-card:focus{outline:2px solid var(--color-accent);outline-offset:2px}.tg-quadrant-card-more-btn:focus{outline:2px solid var(--color-accent);outline-offset:2px}@keyframes cardComplete{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}.tg-quadrant-card--completed{animation:cardComplete .3s ease-in-out}.tg-quadrant-card:hover .tg-quadrant-card-title{color:var(--text-normal)}.tg-quadrant-card:hover .tg-quadrant-card-priority{opacity:1}.tg-quadrant-card-content{position:relative}.tg-quadrant-loading{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;color:var(--text-muted);min-height:100px}.tg-quadrant-loading-spinner{margin-bottom:1rem}.tg-quadrant-spinner{width:24px;height:24px;color:var(--color-accent)}.tg-quadrant-loading-message{font-size:.9rem;opacity:.7}.tg-quadrant-dragging{cursor:grabbing!important}.tg-quadrant-dragging *{pointer-events:none}.tg-quadrant-card--ghost{opacity:.4;background:var(--background-modifier-border);border:2px dashed var(--color-accent)}.tg-quadrant-card--chosen{box-shadow:0 8px 25px #00000026;transform:scale(1.02);z-index:1000;background:var(--background-primary);border:2px solid var(--color-accent)}.tg-quadrant-card--drag{opacity:.8;box-shadow:0 12px 30px #0003}.tg-quadrant-card--fallback{opacity:.9;background:var(--background-primary);border:2px solid var(--color-accent);border-radius:var(--radius-m);box-shadow:0 8px 25px #00000026}.tg-quadrant-column--drag-target{background:var(--background-modifier-hover);border:2px dashed var(--color-accent);border-radius:var(--radius-m)}.tg-quadrant-column-content--drop-active{background:var(--background-modifier-active-hover);border:2px dashed var(--color-accent);border-radius:var(--radius-s);min-height:60px;position:relative}.tg-quadrant-column-content--drop-active:before{content:"Drop task here";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:var(--color-accent);font-size:.9rem;font-weight:500;opacity:.7;pointer-events:none;z-index:1}.tg-quadrant-update-feedback{position:fixed;top:20px;right:20px;z-index:10000;opacity:0;transform:translate(100%);transition:all .3s ease;pointer-events:none}.tg-quadrant-feedback--show{opacity:1;transform:translate(0)}.tg-quadrant-feedback--hide{opacity:0;transform:translate(100%)}.tg-quadrant-feedback-content{display:flex;align-items:center;gap:.5rem;padding:.75rem 1rem;background:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:var(--radius-m);box-shadow:0 4px 12px #0000001a;min-width:200px}.tg-quadrant-feedback--error .tg-quadrant-feedback-content{background:var(--background-modifier-error);border-color:var(--text-error);color:var(--text-error)}.tg-quadrant-feedback-icon{font-size:1.2rem;flex-shrink:0}.tg-quadrant-feedback-text{font-size:.9rem;font-weight:500}.tg-quadrant-card{transition:all .2s ease;cursor:grab}.tg-quadrant-card:hover{transform:translateY(-2px);box-shadow:0 4px 12px #0000001a}.tg-quadrant-card:active{cursor:grabbing}.tg-quadrant-empty-state{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem 1rem;text-align:center;color:var(--text-muted);min-height:120px;border:2px dashed var(--background-modifier-border);border-radius:var(--radius-m);margin:.5rem 0}.tg-quadrant-empty-icon{margin-bottom:.75rem;opacity:.5}.tg-quadrant-empty-message{font-size:.9rem;line-height:1.4;max-width:200px}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.tg-quadrant-spinner circle{animation:spin 2s linear infinite;transform-origin:center}@media (max-width: 768px){.tg-quadrant-update-feedback{top:10px;right:10px;left:10px;transform:translateY(-100%)}.tg-quadrant-feedback--show{transform:translateY(0)}.tg-quadrant-feedback--hide{transform:translateY(-100%)}.tg-quadrant-feedback-content{min-width:auto;width:100%}}.theme-dark .tg-quadrant-card--chosen{background:var(--background-primary-alt);box-shadow:0 8px 25px #0000004d}.theme-dark .tg-quadrant-card--fallback{background:var(--background-primary-alt);box-shadow:0 8px 25px #0000004d}.theme-dark .tg-quadrant-feedback-content{box-shadow:0 4px 12px #0000004d}@media (prefers-reduced-motion: reduce){.tg-quadrant-card,.tg-quadrant-update-feedback,.tg-quadrant-card--chosen,.tg-quadrant-card--drag{transition:none;animation:none}.tg-quadrant-spinner circle{animation:none}}.tg-quadrant-scroll-container{flex:1;overflow-y:auto;overflow-x:hidden;max-height:70vh;scrollbar-width:thin;scrollbar-color:var(--background-modifier-border) transparent}.tg-quadrant-scroll-container::-webkit-scrollbar{width:6px}.tg-quadrant-scroll-container::-webkit-scrollbar-track{background:transparent}.tg-quadrant-scroll-container::-webkit-scrollbar-thumb{background:var(--background-modifier-border);border-radius:3px}.tg-quadrant-scroll-container::-webkit-scrollbar-thumb:hover{background:var(--background-modifier-border-hover)}.tg-quadrant-load-more{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:1rem;color:var(--text-muted);border-top:1px solid var(--background-modifier-border);margin-top:.5rem}.tg-quadrant-load-more-spinner{margin-bottom:.5rem}.tg-quadrant-load-more-message{font-size:.8rem;opacity:.7}.tg-quadrant-column{display:flex;flex-direction:column;height:100%;min-height:400px;max-height:80vh}.tg-quadrant-column-content{flex:1;display:flex;flex-direction:column;gap:.5rem;padding:.5rem}.tg-quadrant-scroll-container{scroll-behavior:smooth}.tg-quadrant-column.loading-more .tg-quadrant-load-more{opacity:1;pointer-events:none}.tg-quadrant-load-more{min-height:40px;transition:opacity .2s ease}.tg-quadrant-column-content:empty:before{content:"";display:block;min-height:100px}.tg-quadrant-grid{display:grid;grid-template-columns:repeat(2,1fr);height:calc(100vh - 200px);min-height:400px}@media (max-width: 1200px){.tg-quadrant-scroll-container{max-height:60vh}.tg-quadrant-column{max-height:70vh}}@media (max-width: 768px){.tg-quadrant-scroll-container{max-height:50vh}.tg-quadrant-column{max-height:60vh;min-height:300px}.tg-quadrant-grid{grid-template-columns:1fr;height:auto}}.tg-quadrant-column-content{contain:layout style;will-change:contents}.tg-quadrant-card{contain:layout style paint}.tg-quadrant-scroll-container.has-scroll:before{content:"";position:sticky;top:0;height:1px;background:linear-gradient(to bottom,var(--background-primary),transparent);z-index:1}.tg-quadrant-scroll-container.has-scroll:after{content:"";position:sticky;bottom:0;height:1px;background:linear-gradient(to top,var(--background-primary),transparent);z-index:1}.tg-habit-component-container{width:100%;display:flex;flex-direction:column;gap:1rem;padding:1rem;height:100%;overflow-y:auto}.habit-list-container{display:grid;grid-template-columns:repeat(1,minmax(0,1fr));gap:1rem;width:100%}@media screen and (max-width: 480px){.habit-list-container{padding:.5rem;gap:.75rem}}@media screen and (min-width: 768px){.habit-list-container{margin-left:auto;margin-right:auto;max-width:400px;display:flex;flex-direction:column}}@media screen and (min-width: 1024px){.habit-list-container{max-width:500px}}.habit-card-wrapper{width:100%;min-height:fit-content}.habit-card{border:1px solid var(--background-modifier-border);border-radius:var(--radius-m);background-color:var(--background-secondary);color:var(--text-normal);overflow:hidden;display:flex;flex-direction:column;width:100%;height:100%;min-height:fit-content}.card-header{display:flex;align-items:center;justify-content:space-between;padding:.5rem 1rem;gap:.5rem}.card-title{display:flex;align-items:center;gap:.5rem;font-size:var(--font-ui-large);font-weight:600;flex-grow:1;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.habit-name.habit-name:hover{text-decoration:underline;cursor:pointer}.card-content-wrapper{padding:.75rem 1rem;flex-grow:1}.daily-habit-card .habit-checkbox{--checkbox-size: 1.25rem;cursor:pointer;accent-color:var(--interactive-accent)}.daily-habit-card .card-content-wrapper{padding:0 1rem .75rem}.count-habit-card .card-content-wrapper{display:flex;flex-direction:column;gap:.75rem;align-items:center}.count-habit-card .habit-icon-button{--icon-size: 2rem;height:4rem;width:4rem;aspect-ratio:1;padding:0;cursor:pointer;border-radius:var(--radius-s);display:flex;justify-content:center;align-items:center;font-size:1.5rem}.count-habit-card .habit-icon-button{color:var(--icon-color)}.count-habit-card .habit-icon-button:hover{background-color:var(--background-secondary)}.count-habit-card .habit-card-name{font-size:var(--font-ui-large);font-weight:600}.count-habit-card .habit-active-day{font-size:var(--font-ui-small);color:var(--text-muted);font-weight:400}.count-habit-card .habit-info{display:flex;flex-direction:column;align-items:center;text-align:center;flex-grow:1}.count-habit-card .habit-info h3{font-size:var(--font-ui-large);font-weight:600}.count-habit-card .habit-progress-area{width:100%;display:flex;flex-direction:column;align-items:center;gap:.5rem}@media (min-width: 640px){.count-habit-card .card-content-wrapper{flex-direction:row;align-items:center;gap:1rem}.count-habit-card .habit-progress-area{width:auto;min-width:150px;align-items:flex-end}.count-habit-card .habit-heatmap-small{width:100%}}.scheduled-habit-card .card-header{padding-bottom:.5rem}.scheduled-habit-card .card-content-wrapper{display:flex;flex-direction:column;gap:.75rem;align-items:center}.scheduled-habit-card .habit-heatmap-medium{width:100%}.scheduled-habit-card .habit-controls{width:100%;display:flex;flex-direction:column;gap:.5rem;align-items:center}.scheduled-habit-card .habit-event-dropdown{width:auto;margin-bottom:.5rem;width:100%}@media (min-width: 640px){.scheduled-habit-card .card-content-wrapper{flex-direction:row;align-items:flex-start;justify-content:space-between}.scheduled-habit-card .habit-heatmap-medium{width:auto;flex-grow:1;margin-right:1rem}.scheduled-habit-card .habit-controls{width:auto;min-width:150px;align-items:flex-start}}.mapping-habit-card .card-header{padding-bottom:.5rem}.mapping-habit-card .card-content-wrapper{display:flex;flex-direction:column;gap:.75rem;align-items:center;padding-top:0;padding-bottom:1.2rem}.mapping-habit-card .habit-heatmap-medium{width:100%}.mapping-habit-card .habit-controls{width:100%;display:flex;flex-direction:column;align-items:center;gap:.5rem}.mapping-habit-card .habit-mapping-button{display:flex;justify-content:center;align-items:center;font-size:1.75rem;padding:.5rem;width:100%;max-width:100px;height:3.5rem;border:1px solid var(--button-secondary-border-color);background-color:var(--button-secondary-bg);color:var(--text-normal);cursor:pointer;border-radius:var(--radius-s)}.mapping-habit-card .habit-mapping-button:hover{background-color:var(--button-secondary-hover-bg)}.mapping-habit-card .habit-slider-setting{width:100%;max-width:200px}.mapping-habit-card .habit-slider-setting .setting-item-info{display:none}.mapping-habit-card .habit-slider-setting .setting-item{width:100%;padding:0;border:none}.mapping-habit-card .habit-slider-setting .setting-item-control{width:100%}.mapping-habit-card .heatmap-md .heatmap-container-simple{gap:.5rem}@media (min-width: 640px){.mapping-habit-card .card-content-wrapper{flex-direction:row;align-items:center;justify-content:space-between}.mapping-habit-card .habit-heatmap-medium{width:auto;flex-grow:1;margin-right:1rem}.mapping-habit-card .habit-controls{width:auto;min-width:80px;flex-direction:column;align-items:center;gap:.75rem}.mapping-habit-card .habit-mapping-button{width:4rem;height:4rem}.mapping-habit-card .habit-slider-setting{width:100%;max-width:none}}.habit-progress-container{width:100%;height:.75rem;background-color:var(--background-modifier-border);border-radius:var(--radius-l);overflow:hidden;position:relative}.habit-progress-bar{height:100%;background-color:var(--interactive-accent);border-radius:var(--radius-l);transition:width .3s ease-in-out}.habit-progress-container.filled .habit-progress-text{mix-blend-mode:unset}.habit-progress-text{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;font-size:.6rem;line-height:1;color:var(--text-on-accent);mix-blend-mode:difference;font-weight:500}.tg-heatmap-root{width:100%}.heatmap-sm .heatmap-container-simple{display:grid;grid-template-columns:repeat(3,1fr);gap:3px;overflow-x:auto;padding-bottom:2px}.heatmap-md .heatmap-container-simple{display:grid;grid-template-columns:repeat(6,1fr);gap:3px;overflow-x:auto;padding-bottom:2px;justify-items:center}.heatmap-lg .heatmap-container-simple{display:grid;grid-template-columns:repeat(10,1fr);gap:var(--size-4-2);overflow-x:auto;padding-bottom:2px;justify-items:center}.heatmap-cell{border-radius:var(--radius-s);display:flex;justify-content:center;align-items:center;cursor:pointer;flex-shrink:0;background-color:var( --background-modifier-border );border:1px solid transparent}.heatmap-cell-dot{border-radius:50%}.heatmap-sm .heatmap-cell{width:.75rem;height:.75rem}.habit-heatmap-medium .heatmap-md .heatmap-cell{width:1.4rem;height:1.4rem;font-size:.7rem}.heatmap-md .heatmap-cell{width:1.1rem;height:1.1rem;font-size:.7rem}.heatmap-lg .heatmap-cell{width:1.25rem;height:1.25rem;font-size:.75rem}.heatmap-cell.filled{background-color:var(--interactive-accent);color:var(--text-on-accent)}.heatmap-cell.has-custom-content:has(.pie-dot-container){background:transparent;border:unset}.heatmap-cell.has-custom-content,.heatmap-cell.has-text-content{background-color:var(--background-secondary);border-color:var(--background-modifier-border);color:var(--text-normal)}.heatmap-cell.has-text-content{line-height:1}.pie-dot-container{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.pie-dot-container svg{display:block}.habit-empty-state{text-align:center;padding:2rem 1rem;color:var(--text-muted)}.habit-empty-state h2{font-size:var(--font-ui-large);font-weight:600;margin-bottom:.5rem}.habit-empty-state p{font-size:var(--font-ui-normal);color:var(--text-faint)}.habit-icon{display:inline-block;height:1em;line-height:1;text-align:center;color:var(--text-muted);font-style:italic;margin-right:.25em;--icon-size: 1.5rem}:root{--task-completed-color: #4caf50;--task-doing-color: #80dee5;--task-in-progress-color: #f9d923;--task-abandoned-color: #eb5353;--task-planned-color: #9c27b0;--task-question-color: #2196f3;--task-important-color: #f44336;--task-star-color: #ffc107;--task-quote-color: #607d8b;--task-location-color: #795548;--task-bookmark-color: #ff9800;--task-information-color: #00bcd4;--task-idea-color: #9c27b0;--task-pros-color: #4caf50;--task-cons-color: #f44336;--task-fire-color: #ff5722;--task-key-color: #ffd700;--task-win-color: #66bb6a;--task-up-color: #4caf50;--task-down-color: #f44336;--task-note-color: #9e9e9e;--task-amount-color: #8bc34a;--task-speech-color: #03a9f4;--progress-0-color: #ae431e;--progress-25-color: #e5890a;--progress-50-color: #b4c6a6;--progress-75-color: #6bcb77;--progress-100-color: #4d96ff;--progress-background-color: #f1f1f1}.theme-dark{--task-completed-color: #4caf50;--task-doing-color: #379fa7;--task-in-progress-color: #ffc107;--task-abandoned-color: #f44336;--task-planned-color: #ce93d8;--task-question-color: #42a5f5;--task-important-color: #ef5350;--task-star-color: #ffd54f;--task-quote-color: #90a4ae;--task-location-color: #8d6e63;--task-bookmark-color: #ffb74d;--task-information-color: #26c6da;--task-idea-color: #ce93d8;--task-pros-color: #66bb6a;--task-cons-color: #ef5350;--task-fire-color: #ff7043;--task-key-color: #ffd700;--task-win-color: #81c784;--task-up-color: #66bb6a;--task-down-color: #ef5350;--task-note-color: #bdbdbd;--task-amount-color: #aed581;--task-speech-color: #29b6f6;--progress-0-color: #ae431e;--progress-25-color: #e5890a;--progress-50-color: #b4c6a6;--progress-75-color: #6bcb77;--progress-100-color: #4d96ff;--progress-background-color: #f1f1f1}.task-genius-view-config-modal{width:max(70%,500px)}.task-genius-view-config-modal .setting-item{margin-bottom:15px}.task-genius-view-config-modal .setting-item:not(.setting-item-heading) .setting-item-info{width:120px}.task-genius-view-config-modal .setting-item-control input[type=text],.task-genius-view-config-modal .setting-item-control input[type=number]{width:100%}.task-genius-view-config-modal .setting-item-description{font-size:var(--font-ui-smaller);color:var(--text-muted);margin-top:2px}.view-management-list .setting-item{border-bottom:1px solid var(--background-modifier-border);padding:10px 0;display:flex;align-items:center}.view-management-list .setting-item-info{flex-grow:1;margin-right:10px}.view-management-list .setting-item-control{display:flex;align-items:center;gap:8px}.view-management-list .setting-item-control .button-component{padding:5px;height:auto}.view-management-list .view-order-button,.view-management-list .view-delete-button{margin-left:5px}.view-management-list .setting-item:last-child{border-bottom:none}.view-management-list .setting-item-control .checkbox-container{margin:0}.tg-icon-menu{position:absolute;z-index:100;background-color:var(--background-secondary);border:1px solid var(--background-modifier-border);border-radius:var(--radius-m);box-shadow:var(--shadow-l);padding:8px;max-height:300px;width:250px;display:flex;flex-direction:column;box-sizing:border-box}.tg-icon-menu .tg-menu-search{width:100%;padding:6px 8px;margin-bottom:8px;border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);background-color:var(--background-primary);color:var(--text-normal);box-sizing:border-box;flex-shrink:0}.tg-icon-menu .tg-menu-icons{flex-grow:1;overflow-y:auto;min-height:0;display:grid;grid-template-columns:repeat(auto-fill,minmax(32px,1fr));gap:4px}.tg-icon-menu .clickable-icon{display:flex;justify-content:center;align-items:center;padding:6px;border-radius:var(--radius-s);cursor:pointer;background-color:var(--background-primary);border:1px solid transparent;transition:background-color .1s ease-in-out,border-color .1s ease-in-out}.tg-icon-menu .clickable-icon:hover{background-color:var(--background-modifier-hover);border-color:var(--background-modifier-border-hover)}.tg-icon-menu .clickable-icon svg{width:20px;height:20px;color:var(--text-muted)}.task-status-widget{display:inline-flex;align-items:center;cursor:pointer;font-size:var(--font-ui-medium);font-weight:var(--font-bold)}.task-state.live-preview-mode{padding-inline-start:var(--size-4-2);padding-inline-end:var(--size-2-1)}.task-status-widget .list-bullet:after{background-color:var(--list-marker-color)!important}.task-state[data-task-state=" "]{color:var(--text-accent)}.task-state[data-task-state="/"]{color:var(--task-doing-color)}.task-state[data-task-state=">"]{color:var(--task-in-progress-color)}.task-state[data-task-state=x],.task-state[data-task-state=X]{color:var(--task-completed-color)}.task-state[data-task-state="-"]{color:var(--task-abandoned-color)}.task-state[data-task-state="<"]{color:var(--task-planned-color)}.task-state[data-task-state="?"]{color:var(--task-question-color)}.task-state[data-task-state="!"]{color:var(--task-important-color)}.task-state[data-task-state="*"]{color:var(--task-star-color)}.task-state[data-task-state='"']{color:var(--task-quote-color)}.task-state[data-task-state=l]{color:var(--task-location-color)}.task-state[data-task-state=b]{color:var(--task-bookmark-color)}.task-state[data-task-state=i]{color:var(--task-information-color)}.task-state[data-task-state=I]{color:var(--task-idea-color)}.task-state[data-task-state=p]{color:var(--task-pros-color)}.task-state[data-task-state=c]{color:var(--task-cons-color)}.task-state[data-task-state=f]{color:var(--task-fire-color)}.task-state[data-task-state=k]{color:var(--task-key-color)}.task-state[data-task-state=w]{color:var(--task-win-color)}.task-state[data-task-state=u]{color:var(--task-up-color)}.task-state[data-task-state=d]{color:var(--task-down-color)}.task-state[data-task-state=n]{color:var(--task-note-color)}.task-state[data-task-state=S]{color:var(--task-amount-color)}.task-state[data-task-state="0"],.task-state[data-task-state="1"],.task-state[data-task-state="2"],.task-state[data-task-state="3"],.task-state[data-task-state="4"],.task-state[data-task-state="5"],.task-state[data-task-state="6"],.task-state[data-task-state="7"],.task-state[data-task-state="8"],.task-state[data-task-state="9"]{color:var(--task-speech-color)}.task-fake-bullet{display:inline-block;width:5px;height:5px;border-radius:50%;background-color:var(--text-normal);margin-right:4px;vertical-align:middle}ol>.task-list-item .task-fake-bullet{display:none}ol>.task-list-item .task-state-container{margin-inline-start:0}div[data-type^=tg-timeline-sidebar-view] .timeline-sidebar-container{display:flex;flex-direction:column;height:100%;width:100%;background-color:var(--background-primary);overflow:hidden;font-family:var(--font-interface);padding:0!important}div[data-type^=tg-timeline-sidebar-view] .timeline-header{display:flex;justify-content:space-between;align-items:center;padding:var(--size-4-3) var(--size-4-4);border-bottom:1px solid var(--background-modifier-border);background:linear-gradient(135deg,var(--background-secondary) 0%,var(--background-modifier-hover) 100%);flex-shrink:0;backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px)}div[data-type^=tg-timeline-sidebar-view] .timeline-title{font-weight:600;font-size:var(--font-ui-medium);color:var(--text-normal);display:flex;align-items:center;gap:var(--size-4-2)}div[data-type^=tg-timeline-sidebar-view] .timeline-controls{display:flex;gap:var(--size-4-2)}div[data-type^=tg-timeline-sidebar-view] .timeline-btn{display:flex;align-items:center;justify-content:center;width:var(--size-4-8);height:var(--size-4-8);border-radius:var(--radius-s);cursor:pointer;color:var(--text-muted);background-color:transparent;transition:all .2s ease}div[data-type^=tg-timeline-sidebar-view] .timeline-btn:hover{color:var(--text-normal);background-color:var(--background-modifier-hover)}div[data-type^=tg-timeline-sidebar-view] .timeline-btn.is-active{color:var(--text-on-accent);background-color:var(--interactive-accent)}div[data-type^=tg-timeline-sidebar-view] .timeline-content{flex:1;overflow-y:auto;padding:var(--size-4-2) 0;position:relative}div[data-type^=tg-timeline-sidebar-view] .timeline-content.focus-mode .timeline-date-group:not(.is-today){opacity:.3;pointer-events:none}div[data-type^=tg-timeline-sidebar-view] .timeline-empty{display:flex;align-items:center;justify-content:center;height:100%;color:var(--text-muted);font-style:italic;text-align:center;padding:var(--size-4-8)}div[data-type^=tg-timeline-sidebar-view] .timeline-date-group{margin-bottom:var(--size-4-2);position:relative;border-radius:var(--radius-m);transition:all .3s ease}div[data-type^=tg-timeline-sidebar-view] .timeline-date-group.is-today{background-color:var(--background-secondary);border-radius:var(--radius-m);margin:0 var(--size-4-2) var(--size-4-2);padding:var(--size-4-2);box-shadow:0 2px 8px #0000001a;border:1px solid var(--interactive-accent)}div[data-type^=tg-timeline-sidebar-view] .timeline-date-header{display:flex;align-items:center;justify-content:space-between;padding:var(--size-4-2) var(--size-4-4);font-weight:600;font-size:var(--font-ui-small);color:var(--text-accent);border-bottom:1px solid var(--background-modifier-border);margin-bottom:var(--size-4-2);position:sticky;top:0;background-color:var(--background-primary);z-index:1}div[data-type^=tg-timeline-sidebar-view] .timeline-date-group.is-today .timeline-date-header{border-radius:var(--radius-s);margin:0 0 var(--size-4-2) 0}div[data-type^=tg-timeline-sidebar-view] .timeline-date-relative{font-size:var(--font-ui-smaller);color:var(--text-muted);font-weight:normal}div[data-type^=tg-timeline-sidebar-view] .timeline-events-list{display:flex;flex-direction:column;gap:var(--size-2-1);padding:0 var(--size-2-3)}div[data-type^=tg-timeline-sidebar-view] .timeline-event{display:flex;align-items:flex-start;gap:var(--size-4-3);padding:var(--size-4-3);border-radius:var(--radius-m);cursor:pointer;position:relative;border:1px solid transparent;margin-bottom:var(--size-4-2)}div[data-type^=tg-timeline-sidebar-view] .timeline-event:hover{background-color:var(--background-modifier-hover);border-color:var(--interactive-accent);box-shadow:0 2px 8px #0000000d;transform:translateY(-1px)}div[data-type^=tg-timeline-sidebar-view] .timeline-event:hover:has(.timeline-event-checkbox:hover){transform:none}div[data-type^=tg-timeline-sidebar-view] .timeline-event.is-completed{opacity:.6}div[data-type^=tg-timeline-sidebar-view] .timeline-event.is-completed .timeline-event-text{text-decoration:line-through;color:var(--text-muted)}div[data-type^=tg-timeline-sidebar-view] .timeline-event-time{font-size:var(--font-ui-smaller);color:var(--text-muted);font-family:var(--font-monospace);min-width:45px;text-align:center;margin-top:2px;flex-shrink:0;background-color:var(--background-modifier-border);border-radius:var(--radius-s);padding:var(--size-4-1) var(--size-4-2);font-weight:500}div[data-type^=tg-timeline-sidebar-view] .timeline-event-content{flex:1;display:flex;align-items:flex-start;gap:var(--size-4-2);min-width:0}div[data-type^=tg-timeline-sidebar-view] .timeline-event-checkbox{display:flex;align-items:center;margin-top:2px}div[data-type^=tg-timeline-sidebar-view] .timeline-event-checkbox input[type=checkbox]{margin:0;cursor:pointer}div[data-type^=tg-timeline-sidebar-view] .timeline-event-text{flex:1;font-size:var(--font-ui-small);line-height:1.4;word-wrap:break-word;color:var(--text-normal);display:flex;align-items:flex-start;gap:var(--size-4-2)}div[data-type^=tg-timeline-sidebar-view] .timeline-event-icon{font-size:var(--font-ui-medium);flex-shrink:0;margin-top:1px}div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text{flex:1;word-break:break-word}div[data-type^=tg-timeline-sidebar-view] .timeline-event-actions{display:flex;gap:var(--size-4-1);opacity:0;transition:opacity .2s ease}div[data-type^=tg-timeline-sidebar-view] .timeline-event:hover .timeline-event-actions{opacity:1}div[data-type^=tg-timeline-sidebar-view] .timeline-event-action{display:flex;align-items:center;justify-content:center;width:var(--size-4-6);height:var(--size-4-6);border-radius:var(--radius-s);cursor:pointer;color:var(--text-muted);background-color:transparent;transition:all .2s ease}div[data-type^=tg-timeline-sidebar-view] .timeline-event-action:hover{color:var(--text-normal);background-color:var(--background-modifier-border)}div[data-type^=tg-timeline-sidebar-view] .timeline-quick-input{flex-shrink:0;border-top:1px solid var(--background-modifier-border);background-color:var(--background-secondary);padding:var(--size-4-4);display:flex;flex-direction:column;gap:var(--size-4-3);padding-bottom:var(--size-4-12);position:relative;transition:all .3s cubic-bezier(.4,0,.2,1);overflow:hidden}div[data-type^=tg-timeline-sidebar-view] .timeline-quick-input.is-collapsed{padding:0;gap:0;height:auto}div[data-type^=tg-timeline-sidebar-view] .timeline-quick-input.is-collapsed .quick-input-header,div[data-type^=tg-timeline-sidebar-view] .timeline-quick-input.is-collapsed .quick-input-editor,div[data-type^=tg-timeline-sidebar-view] .timeline-quick-input.is-collapsed .quick-input-actions{display:none}div[data-type^=tg-timeline-sidebar-view] .timeline-quick-input.is-collapsing{overflow:hidden}div[data-type^=tg-timeline-sidebar-view] .timeline-quick-input.is-expanding{overflow:hidden}div[data-type^=tg-timeline-sidebar-view] .quick-input-header-collapsed{display:flex;align-items:center;justify-content:space-between;padding:var(--size-4-3) var(--size-4-4);background-color:var(--background-secondary);border-bottom:1px solid var(--background-modifier-border);cursor:pointer;transition:background-color .2s ease}div[data-type^=tg-timeline-sidebar-view] .quick-input-header-collapsed:hover{background-color:var(--background-modifier-hover)}div[data-type^=tg-timeline-sidebar-view] .collapsed-expand-btn{display:flex;align-items:center;justify-content:center;width:var(--size-4-6);height:var(--size-4-6);border-radius:var(--radius-s);color:var(--text-muted);transition:all .2s ease;cursor:pointer}div[data-type^=tg-timeline-sidebar-view] .collapsed-expand-btn:hover{color:var(--text-normal);background-color:var(--background-modifier-border)}div[data-type^=tg-timeline-sidebar-view] .collapsed-title{flex:1;font-weight:600;font-size:var(--font-ui-small);color:var(--text-normal);margin-left:var(--size-4-2)}div[data-type^=tg-timeline-sidebar-view] .collapsed-quick-actions{display:flex;gap:var(--size-4-2)}div[data-type^=tg-timeline-sidebar-view] .collapsed-quick-capture,div[data-type^=tg-timeline-sidebar-view] .collapsed-more-options{display:flex;align-items:center;justify-content:center;width:var(--size-4-7);height:var(--size-4-7);border-radius:var(--radius-s);color:var(--text-muted);cursor:pointer;transition:all .2s ease}div[data-type^=tg-timeline-sidebar-view] .collapsed-quick-capture:hover,div[data-type^=tg-timeline-sidebar-view] .collapsed-more-options:hover{color:var(--text-normal);background-color:var(--background-modifier-border)}div[data-type^=tg-timeline-sidebar-view] .collapsed-quick-capture:hover{color:var(--interactive-accent)}div[data-type^=tg-timeline-sidebar-view] .quick-input-header{display:flex;justify-content:space-between;align-items:flex-start;gap:var(--size-4-2);margin-bottom:var(--size-4-2)}div[data-type^=tg-timeline-sidebar-view] .quick-input-header-left{display:flex;align-items:center;gap:var(--size-4-2)}div[data-type^=tg-timeline-sidebar-view] .quick-input-collapse-btn{display:flex;align-items:center;justify-content:center;width:var(--size-4-6);height:var(--size-4-6);border-radius:var(--radius-s);color:var(--text-muted);cursor:pointer;transition:all .2s ease}div[data-type^=tg-timeline-sidebar-view] .quick-input-collapse-btn:hover{color:var(--text-normal);background-color:var(--background-modifier-border)}div[data-type^=tg-timeline-sidebar-view] .quick-input-collapse-btn svg{transition:transform .2s ease}div[data-type^=tg-timeline-sidebar-view] .timeline-quick-input.is-collapsed .quick-input-collapse-btn svg{transform:rotate(-90deg)}div[data-type^=tg-timeline-sidebar-view] .quick-input-title{font-weight:600;font-size:var(--font-ui-small);color:var(--text-normal)}div[data-type^=tg-timeline-sidebar-view] .quick-input-target-info{font-size:var(--font-ui-smaller);color:var(--text-muted);font-style:italic;padding:var(--size-4-1) var(--size-4-2);background-color:var(--background-modifier-hover);border-radius:var(--radius-s);word-break:break-all}div[data-type^=tg-timeline-sidebar-view] .quick-input-editor{min-height:80px;border:2px solid var(--background-modifier-border);border-radius:var(--radius-m);background-color:var(--background-primary);padding:var(--size-4-3);font-family:var(--font-text);font-size:var(--font-ui-small);resize:vertical;transition:all .3s ease}div[data-type^=tg-timeline-sidebar-view] .quick-input-editor:focus-within{border-color:var(--interactive-accent);box-shadow:0 0 0 2px rgba(var(--interactive-accent-rgb),.2)}div[data-type^=tg-timeline-sidebar-view] .quick-input-editor .cm-editor{background-color:transparent;border:none;outline:none}div[data-type^=tg-timeline-sidebar-view] .quick-input-editor .cm-focused{outline:none}div[data-type^=tg-timeline-sidebar-view] .quick-input-editor .cm-editor.cm-focused{outline:none}div[data-type^=tg-timeline-sidebar-view] .quick-input-actions{display:flex;gap:var(--size-4-2);justify-content:flex-end}div[data-type^=tg-timeline-sidebar-view] .quick-capture-btn,div[data-type^=tg-timeline-sidebar-view] .quick-modal-btn{padding:var(--size-4-3) var(--size-4-6);border-radius:var(--radius-m);font-size:var(--font-ui-small);font-weight:500;cursor:pointer;border:none;transition:all .3s ease;box-shadow:0 2px 4px #0000001a}div[data-type^=tg-timeline-sidebar-view] .quick-capture-btn{background-color:var(--interactive-accent);color:var(--text-on-accent)}div[data-type^=tg-timeline-sidebar-view] .quick-capture-btn:hover{background-color:var(--interactive-accent-hover);transform:translateY(-1px);box-shadow:0 4px 8px #00000026}div[data-type^=tg-timeline-sidebar-view] .quick-modal-btn{background-color:var(--background-modifier-border);color:var(--text-normal)}div[data-type^=tg-timeline-sidebar-view] .quick-modal-btn:hover{background-color:var(--background-modifier-border-hover);transform:translateY(-1px);box-shadow:0 4px 8px #00000026}@media (max-width: 768px){div[data-type^=tg-timeline-sidebar-view] .timeline-header{padding:var(--size-4-2) var(--size-4-3)}div[data-type^=tg-timeline-sidebar-view] .timeline-controls{gap:var(--size-4-1)}div[data-type^=tg-timeline-sidebar-view] .timeline-btn{width:var(--size-4-7);height:var(--size-4-7)}div[data-type^=tg-timeline-sidebar-view] .timeline-events-list{padding:0 var(--size-2-3)}div[data-type^=tg-timeline-sidebar-view] .timeline-event{padding:var(--size-4-2)}div[data-type^=tg-timeline-sidebar-view] .timeline-quick-input{padding:var(--size-4-3)}div[data-type^=tg-timeline-sidebar-view] .timeline-quick-input.is-collapsed{padding:0}div[data-type^=tg-timeline-sidebar-view] .quick-input-editor{min-height:60px}div[data-type^=tg-timeline-sidebar-view] .quick-input-header-collapsed{padding:var(--size-4-2) var(--size-4-3)}div[data-type^=tg-timeline-sidebar-view] .collapsed-quick-capture,div[data-type^=tg-timeline-sidebar-view] .collapsed-more-options{width:var(--size-4-6);height:var(--size-4-6)}}div[data-type^=tg-timeline-sidebar-view] .timeline-content::-webkit-scrollbar{width:6px}div[data-type^=tg-timeline-sidebar-view] .timeline-content::-webkit-scrollbar-track{background-color:var(--background-secondary)}div[data-type^=tg-timeline-sidebar-view] .timeline-content::-webkit-scrollbar-thumb{background-color:var(--background-modifier-border);border-radius:3px}div[data-type^=tg-timeline-sidebar-view] .timeline-content::-webkit-scrollbar-thumb:hover{background-color:var(--background-modifier-border-hover)}@keyframes fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}div[data-type^=tg-timeline-sidebar-view] .timeline-content.focus-mode{position:relative}div[data-type^=tg-timeline-sidebar-view] .timeline-content.focus-mode:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(to bottom,rgba(var(--background-primary-rgb),.9) 0%,rgba(var(--background-primary-rgb),.7) 50%,rgba(var(--background-primary-rgb),.9) 100%);pointer-events:none;z-index:0}div[data-type^=tg-timeline-sidebar-view] .timeline-content.focus-mode .timeline-date-group.is-today{position:relative;z-index:1}div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text .markdown-block{margin:0;padding:0;font-size:inherit;line-height:inherit}div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text .markdown-block p{margin:0;padding:0;font-size:inherit;line-height:inherit}div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text .markdown-block strong,div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text .markdown-block em,div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text .markdown-block code{font-size:inherit}div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text .markdown-block a{color:var(--link-color);text-decoration:none}div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text .markdown-block a:hover{text-decoration:underline}div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text .markdown-block ul,div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text .markdown-block ol{margin:0;padding-left:var(--size-4-4)}div[data-type^=tg-timeline-sidebar-view] .timeline-event-content-text .markdown-block li{margin:0;padding:0}.reward-modal-content{text-align:center}.reward-modal .modal-title{text-align:center}.reward-name{font-size:1.2em;font-weight:bold;margin-bottom:15px}.reward-image-container{margin-bottom:20px;display:flex;justify-content:center;align-items:center}.reward-image{max-width:80%;max-height:300px;border-radius:8px;box-shadow:0 2px 4px #0000001a}.reward-image-error{font-style:italic;color:var(--text-muted)}.reward-spacer{height:20px}.task-genius-reward-modal .setting-item-control{display:flex;justify-content:center;gap:10px}.markdown-source-view.mod-cm6 .cm-gutters.task-gutter{margin-inline-end:0!important;margin-inline-start:var(--file-folding-offset)}.is-mobile .markdown-source-view.mod-cm6 .cm-gutters.task-gutter{margin-inline-start:0!important}.task-details-popover.tg-menu{z-index:20;position:fixed;background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);padding:var(--size-4-3);box-shadow:var(--shadow-l)}.task-gutter{width:26px}.task-gutter-marker{cursor:pointer;font-size:var(--font-smaller);opacity:.1;transition:opacity .2s ease}.task-gutter-marker:hover{opacity:1}.task-popover-content{padding:var(--size-4-3);max-width:300px;max-height:400px;overflow:auto}.task-metadata-editor{display:flex;flex-direction:column;gap:var(--size-4-2);padding:var(--size-2-2);height:100%}.field-container{display:flex;flex-direction:column;margin-bottom:var(--size-2-2)}.field-label{font-size:var(--font-smallest);font-weight:var(--font-bold);margin-bottom:var(--size-2-1);color:var(--text-muted)}.action-buttons{display:flex;justify-content:space-between;margin-top:var(--size-4-2);gap:var(--size-4-2)}.action-button{padding:var(--size-2-2) var(--size-4-2);font-size:var(--font-smallest);border-radius:var(--radius-s);cursor:pointer}.task-gutter-marker.clickable-icon{width:24px;padding:var(--size-2-1);display:flex;justify-content:center;align-items:center}.task-details-popover .tabs-main-container{display:flex;flex-direction:column;width:100%}.task-details-popover .tabs-navigation{display:flex;margin-bottom:var(--size-4-2);gap:var(--size-4-2)}.task-details-popover .tab-button{padding:var(--size-2-2) var(--size-4-2);cursor:pointer;border:none;background:none;font-size:var(--font-ui-small);color:var(--text-muted);margin-bottom:-1px;transition:color .2s ease,border-color .2s ease}.task-details-popover .tab-button:hover{color:var(--text-normal)}.task-details-popover .tab-button.active{color:var(--text-on-accent);font-weight:var(--font-bold);background-color:var(--interactive-accent)}.task-details-popover .tab-pane{display:none;flex-direction:column;gap:var(--size-4-2)}.task-details-popover .tab-pane.active{display:flex}.task-details-popover .details-status-selector,.task-status-editor .details-status-selector{display:flex;flex-direction:row;justify-content:space-between;margin-bottom:var(--size-4-2);margin-top:var(--size-4-2)}.task-details-popover .quick-capture-status-selector,.task-status-editor .quick-capture-status-selector{display:flex;flex-direction:row;justify-content:space-between;gap:var(--size-4-3)}.task-details-popover .quick-capture-status-selector-label,.task-status-editor .quick-capture-status-selector-label{display:none}.modal-content.task-metadata-editor{display:flex;flex-direction:column;gap:var(--size-4-2)}.metadata-full-container{display:flex;flex-direction:column;gap:var(--size-4-2)}.metadata-full-container .dates-container{display:flex;flex-direction:column;gap:var(--size-4-2)}.internal-embed .task-genius-container{max-height:800px}.internal-embed .task-genius-container .task-sidebar{width:44px;min-width:44px;overflow:hidden}.internal-embed .task-genius-container .task-sidebar .sidebar-nav{align-items:center}.internal-embed .task-genius-container .task-sidebar .sidebar-nav-item{padding:8px 10px;justify-content:center;width:var(--size-4-9);flex-shrink:0;transition:width .3s ease-in-out,flex-shrink .3s ease-in-out}.internal-embed .task-genius-container .task-sidebar .nav-item-icon{margin-right:0}.internal-embed .task-genius-container .task-list{max-height:800px}.internal-embed .projects-container{flex:1;height:auto}.internal-embed .forecast-left-column{width:240px}.internal-embed .forecast-left-column .mini-calendar-container .calendar-grid{display:grid;grid-template-columns:repeat(7,1fr);gap:1px;padding:0 5px}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day-header{text-align:center;font-size:.7em;color:var(--text-muted);padding:3px 0;border-bottom:1px solid var(--background-modifier-border);margin-bottom:3px}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day-header.calendar-weekend{color:var(--text-accent)}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day{aspect-ratio:1;border-radius:3px;padding:1px;cursor:pointer;position:relative;display:flex;flex-direction:column;transition:background-color .2s ease}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day:hover{background-color:var(--background-modifier-hover)}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day.selected{background-color:var(--background-modifier-border-hover)}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day.today{background-color:var(--interactive-accent-hover);color:var(--text-on-accent)}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day.past-due{color:var(--text-error)}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day.other-month{opacity:.5}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day-number{text-align:center;font-size:.75em;font-weight:500;padding:1px}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day-count{background-color:var(--background-modifier-border);color:var(--text-normal);border-radius:8px;font-size:.6em;padding:1px 3px;margin:1px auto;text-align:center;width:fit-content}.internal-embed .forecast-left-column .mini-calendar-container .calendar-day-count.has-priority{background-color:var(--text-accent);color:var(--text-on-accent)}.internal-embed .tags-container{height:auto;max-height:100%}.internal-embed .task-genius-container:has(.task-details.visible) .tags-left-column{display:none}.internal-embed .task-genius-container:has(.task-details.visible) .projects-left-column{display:none}.internal-embed .full-calendar-container{height:auto}.internal-embed .tg-kanban-view{height:auto}.bases-view.task-genius-container{border-top:unset}.bases-update-error-notification{position:fixed;top:20px;right:20px;background:var(--background-modifier-error);border:1px solid var(--background-modifier-border);border-radius:6px;padding:12px 16px;max-width:400px;box-shadow:var(--shadow-s);z-index:1000;cursor:pointer;animation:slideInRight .3s ease-out}.bases-update-error-notification:hover{opacity:.8}.bases-update-error-notification .error-icon{font-size:16px;margin-bottom:8px}.bases-update-error-notification .error-message .error-title{font-weight:600;color:var(--text-error);margin-bottom:4px}.bases-update-error-notification .error-message .error-details{font-size:12px;color:var(--text-muted);line-height:1.4}@keyframes slideInRight{0%{transform:translate(100%);opacity:0}to{transform:translate(0);opacity:1}}
