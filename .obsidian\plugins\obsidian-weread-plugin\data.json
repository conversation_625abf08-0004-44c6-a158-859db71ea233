{"cookies": [{"name": "wr_fp", "value": "910152634"}, {"name": "wr_localvid", "value": "54e327b08175b4eb654eef0"}, {"name": "wr_name", "value": "NEO_@_01"}, {"name": "wr_avatar", "value": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEKBHbhPCnLoecjcNM0CpeN2OzJap3tBHCFmab4kQO5uIicYtTruch7NWSB06tLHg4q53Ad6zUdwfdhSGwombwVyYYMLdvbMIYLkmSPGYZYIuZA/132"}, {"name": "wr_gender", "value": "1"}, {"name": "wr_gid", "value": "241129475"}, {"name": "wr_skey", "value": "y6fZUymE"}, {"name": "wr_vid", "value": "391859894"}, {"name": "wr_rt", "value": "web@2G5BKmQAofqSf2gBDDu_AL"}], "noteLocation": "MyBook", "dailyNotesLocation": "/", "insertAfter": "<!-- start of weread -->", "insertBefore": "<!-- end of weread -->", "dailyNotesFormat": "YYYY-MM-DD", "lastCookieTime": 1752897073669, "isCookieValid": true, "user": "NEO_@_01", "userVid": "391859894", "template": "---\nisbn: {{metaData.isbn}}\nlastReadDate: {{metaData.lastReadDate}}\n出版社: {{metaData.publisher}}\n出版时间: {{metaData.publishTime}}\n分类: {{metaData.category}}\ncssclasses:\n  - editor-full\n作者: {{metaData.author}}\n进度: {{metaData.readInfo.readingProgress}}\n阅读状态: {{metaData.readInfo.markedStatus}}\n---\n\n\n{{progress}}\n\n# 元数据\n> [!abstract] {{metaData.title}}\n> - ![ {{metaData.title}}|200]({{metaData.cover}})\n> - 书名： {{metaData.title}}\n> - 作者： {{metaData.author}}\n> - 出版时间： {{metaData.publishTime}}\n> - ISBN： {{metaData.isbn}}\n> - 分类： {{metaData.category}}\n> - 出版社： {{metaData.publisher}}\n> - PC地址：{{metaData.pcUrl}}\n> - 简介:  {{metaData.intro | replace(\"\\n\", \" \")}}\n\n\n---\n\n# 高亮划线\n{% for chapter in chapterHighlights %}\n{% if chapter.level == 1 %}## {{chapter.chapterTitle}}{% elif chapter.level == 2 %}### {{chapter.chapterTitle}}{% elif chapter.level == 3 %}#### {{chapter.chapterTitle}}{% endif %}\n{% for highlight in chapter.highlights %}{% if highlight.reviewContent %}\n> 📌 {{ highlight.markText |trim }} ^{{highlight.bookmarkId}}\n- 💭 {{highlight.reviewContent}} - ⏱ {{highlight.createTime}} {% else %}\n> 📌 {{ highlight.markText |trim }} \n> ⏱ {{highlight.createTime}} ^{{highlight.bookmarkId}}{% endif %}\n{% endfor %}{% endfor %}\n\n---\n\n# 读书笔记\n{% for chapter in bookReview.chapterReviews %}{% if chapter.reviews or chapter.chapterReview %}\n## {{chapter.chapterTitle}}\n{% if chapter.chapterReviews %}{% for chapterReview in chapter.chapterReviews %}\n\n---\n\n### 章节评论 No.{{loop.index}}\n- {{chapterReview.content}} ^{{chapterReview.reviewId}}\n    - ⏱ {{chapterReview.createTime}} {% endfor %}{% endif %}{% if chapter.reviews %}{% for review in chapter.reviews %}\n\n---\n\n### 划线评论\n> 📌 {{review.abstract |trim }}  ^{{review.reviewId}}\n    - 💭 {{review.content}}\n    - ⏱ {{review.createTime}}\n{% endfor %} {% endif %} {% endif %} {% endfor %}\n\n---\n\n# 本书评论\n{% if bookReview.bookReviews %}{% for bookReview in bookReview.bookReviews %}\n\n---\n\n## 书评 No.{{loop.index}} \n{{bookReview.mdContent}} ^{{bookReview.reviewId}}\n⏱ {{bookReview.createTime}}\n{% endfor %}{% endif %}\n", "noteCountLimit": -1, "subFolderType": "category", "fileNameType": "BOOK_NAME", "dailyNotesToggle": false, "notesBlacklist": "", "showEmptyChapterTitleToggle": false, "convertTags": false, "saveArticleToggle": true, "saveReadingInfoToggle": true}